"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogBody } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel } from "@/components/ui/alert-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>Horizon<PERSON>, <PERSON><PERSON><PERSON>, Trash2, <PERSON>, Info, AlertTriangle } from "lucide-react"

/**
 * Premium Modal System Test - Notion/Linear/Airtable Grade
 * Tests the new bulletproof modal system with:
 * 1. Always centered positioning (never top-heavy, never full-screen)
 * 2. Glassmorphism backgrounds (no white layers)
 * 3. Floating buttons (no white button bars)
 * 4. Perfect device compatibility
 * 5. Premium animations and spacing
 */
export function DialogTest() {
  const [showDialog, setShowDialog] = useState(false)
  const [showLargeDialog, setShowLargeDialog] = useState(false)
  const [showAlertDialog, setShowAlertDialog] = useState(false)
  const [showDestructiveAlert, setShowDestructiveAlert] = useState(false)

  return (
    <div className="mx-auto max-w-4xl space-y-8 p-8">
      <div className="space-y-4 text-center">
        <h1 className="text-3xl font-bold">Premium Modal System Test</h1>
        <p className="text-gray-600">
          Notion/Linear/Airtable grade modal experience - Always centered, glassmorphic, floating buttons
        </p>
        <div className="flex justify-center gap-2">
          <Badge variant="secondary">Always Centered</Badge>
          <Badge variant="secondary">Glassmorphism</Badge>
          <Badge variant="secondary">No White Bars</Badge>
          <Badge variant="secondary">Premium Animations</Badge>
        </div>
      </div>

      {/* Test Buttons Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        
        {/* Standard Dialog Test */}
        <div className="space-y-4 rounded-xl border bg-white/50 p-6 backdrop-blur-sm">
          <h3 className="font-semibold">Standard Dialog</h3>
          <p className="text-sm text-gray-600">
            Always centered • max-w-md (~480px)<br/>
            Glassmorphic background • Floating buttons
          </p>
          <Button onClick={() => setShowDialog(true)} className="w-full">
            Open Standard Dialog
          </Button>
        </div>

        {/* Large Dialog Test */}
        <div className="space-y-4 rounded-xl border bg-white/50 p-6 backdrop-blur-sm">
          <h3 className="font-semibold">Large Dialog</h3>
          <p className="text-sm text-gray-600">
            Always centered • max-w-lg (~512px)<br/>
            Perfect for complex forms
          </p>
          <Button onClick={() => setShowLargeDialog(true)} className="w-full">
            Open Large Dialog
          </Button>
        </div>

        {/* Alert Dialog Test */}
        <div className="space-y-4 rounded-xl border bg-white/50 p-6 backdrop-blur-sm">
          <h3 className="font-semibold">Alert Dialog</h3>
          <p className="text-sm text-gray-600">
            Compact • max-w-xs (~320px)<br/>
            Perfect centering • No overlay dismiss
          </p>
          <div className="space-y-2">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  Info Alert
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Information</AlertDialogTitle>
                  <AlertDialogDescription>
                    This is an informational alert with perfect centering and glassmorphic background.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogAction>Got it</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="w-full">
                  Destructive Alert
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Account</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete your account and remove your data.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                    Delete Account
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* Dropdown Menu Test */}
        <div className="space-y-4 rounded-xl border p-6">
          <h3 className="font-semibold">Dropdown Menu</h3>
          <p className="text-sm text-gray-600">
            Enhanced positioning with overflow prevention
          </p>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full">
                <MoreHorizontal className="mr-2 size-4" />
                Open Menu
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="bottom"
              align="end"
              sideOffset={8}
              collisionPadding={12}
            >
              <DropdownMenuItem>
                <Edit className="mr-2 size-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 size-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600 focus:text-red-600 hover:text-red-600">
                <Trash2 className="mr-2 size-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Popover Test */}
        <div className="space-y-4 rounded-xl border p-6">
          <h3 className="font-semibold">Popover</h3>
          <p className="text-sm text-gray-600">
            Enhanced positioning with overflow prevention
          </p>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full">
                <Info className="mr-2 size-4" />
                Open Popover
              </Button>
            </PopoverTrigger>
            <PopoverContent>
              <div className="space-y-3">
                <h4 className="font-semibold">Information</h4>
                <p className="text-sm text-gray-600">
                  This is a popover with enhanced positioning that prevents overflow
                  on mobile devices and provides smooth animations.
                </p>
                <div className="flex gap-2">
                  <Badge variant="secondary">Enhanced</Badge>
                  <Badge variant="outline">Responsive</Badge>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Edge Case Test */}
        <div className="space-y-4 rounded-xl border p-6">
          <h3 className="font-semibold">Edge Cases</h3>
          <p className="text-sm text-gray-600">
            Test positioning near screen edges
          </p>
          <div className="space-y-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" className="w-full">
                  Bottom Right Menu
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="bottom"
                align="end"
                sideOffset={8}
                collisionPadding={12}
              >
                <DropdownMenuItem>Item 1</DropdownMenuItem>
                <DropdownMenuItem>Item 2</DropdownMenuItem>
                <DropdownMenuItem>Item 3</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Standard Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Profile</DialogTitle>
            <DialogDescription>
              Update your profile information. Changes will be saved automatically.
            </DialogDescription>
          </DialogHeader>

          <DialogBody>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" placeholder="Enter your full name" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" placeholder="Enter your email" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  placeholder="Tell us about yourself"
                  rows={3}
                />
              </div>
            </div>
          </DialogBody>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowDialog(false)}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Large Dialog */}
      <Dialog open={showLargeDialog} onOpenChange={setShowLargeDialog}>
        <DialogContent size="large">
          <DialogHeader>
            <DialogTitle>Edit Scoring Rule</DialogTitle>
            <DialogDescription>
              Configure how this question will be scored in your thesis. This is a complex form that demonstrates the large dialog size.
            </DialogDescription>
          </DialogHeader>

          <DialogBody>
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="rule-name">Rule Name</Label>
                  <Input id="rule-name" placeholder="Enter rule name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rule-type">Rule Type</Label>
                  <Input id="rule-type" placeholder="Scoring" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rule-description">Description</Label>
                <Textarea
                  id="rule-description"
                  placeholder="Describe how this rule works"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="weight">Weight</Label>
                  <Input id="weight" type="number" placeholder="1.0" step="0.1" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="threshold">Threshold</Label>
                  <Input id="threshold" type="number" placeholder="0.5" step="0.1" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Input id="priority" placeholder="High" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="conditions">Conditions</Label>
                <Textarea
                  id="conditions"
                  placeholder="Define the conditions for this rule"
                  rows={3}
                />
              </div>
            </div>
          </DialogBody>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLargeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowLargeDialog(false)}>
              Save Rule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
