import * as React from "react"

import { cn } from "@/lib/utils"


export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  hasLeftIcon?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type = "text", hasLeftIcon = false, ...props }, ref) => {
    return (
      <input
        type={type}
        ref={ref}
        className={cn(
          "flex w-full rounded-xl border border-input bg-transparent ring-offset-background",
          "file:border-0 file:bg-transparent file:font-medium",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "shadow-sm transition-all duration-200 focus-visible:shadow-md",
          "h-12 text-base",
           "pl-8 pr-2" ,
          "md:h-11 md:text-sm",
          className
        )}
        {...props}
      />
    )
  }
)

Input.displayName = "Input"

// export interface InputProps
//   extends React.InputHTMLAttributes<HTMLInputElement> {
//   hasLeftIcon?: boolean
// }
// export { Input }
// const Input = React.forwardRef<HTMLInputElement, InputProps>(
//   ({ className, type, hasLeftIcon, ...props }, ref) => {
//     return (
//       <input
//         type={type}
//         className={cn(
//           "flex w-full rounded-xl border border-input bg-transparent ring-offset-background",
//           "file:border-0 file:bg-transparent file:font-medium",
//           "placeholder:text-muted-foreground",
//           "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
//           "disabled:cursor-not-allowed disabled:opacity-50",
//           "shadow-sm transition-all duration-200 focus-visible:shadow-md",
//           "h-12 px-4 py-3 text-base",
//           "md:h-11 md:px-3 md:py-2 md:text-sm",
//           "touch-manipulation",
//           "active:scale-[0.99]",
//           hasLeftIcon && "pl-10",
//           className
//         )}
//         ref={ref}
//         {...props}
//       />
//     )
//   }
// )
// Input.displayName = "Input"

export { Input }
