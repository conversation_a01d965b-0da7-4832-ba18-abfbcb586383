"use client"

import * as React from "react"
import {
  Root as Popover,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>rigger,
  Content as <PERSON>overContent,
  Portal as PopoverPortal,
} from "@radix-ui/react-popover"

import { cn } from "@/lib/utils"

const PopoverContentComponent = React.forwardRef<
  React.ElementRef<typeof PopoverContent>,
  React.ComponentPropsWithoutRef<typeof PopoverContent>
>(({ className, align = "center", sideOffset = 8, collisionPadding = 12, ...props }, ref) => (
  <PopoverPortal>
    <PopoverContent
      ref={ref}
      align={align}
      sideOffset={sideOffset}
      collisionPadding={collisionPadding}
      className={cn(
        // Premium styling with proper anchoring
        "z-50 w-72 rounded-xl border bg-white shadow-xl drop-shadow-md outline-none",
        "p-4 text-popover-foreground backdrop-blur-sm",
        // Prevent overflow on mobile and ensure proper positioning
        "max-h-[calc(100vh-2rem)] max-w-[calc(100vw-2rem)]",
        // Enhanced animations with proper timing
        "animate-in duration-200 ease-in-out",
        "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
        "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        "data-[state=closed]:animate-out data-[state=closed]:duration-150",
        "data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
        "data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
        className
      )}
      {...props}
    />
  </PopoverPortal>
))
PopoverContentComponent.displayName = "PopoverContent"

export { Popover, PopoverTrigger, PopoverContentComponent as PopoverContent }
