"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from "@/components/ui/dropdown-menu"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip"
import { 
  MoreHorizontal, 
  Edit, 
  Eye, 
  Trash2, 
  Info,
  Settings,
  Download,
  Share
} from "lucide-react"

/**
 * Test component to verify dropdown, popover, and tooltip positioning fixes
 * This component demonstrates the improved anchoring and animations
 */
export function DropdownTest() {
  const [selectedItem, setSelectedItem] = useState<string | null>(null)

  return (
    <div className="p-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Dropdown, Popover & Tooltip Test</h1>
        <p className="text-muted-foreground">
          Testing improved positioning, anchoring, and animations
        </p>
      </div>

      {/* Grid of test cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Card 1: Top-left corner */}
        <Card className="relative">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Top Left Card</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="size-8 p-0">
                  <MoreHorizontal className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                side="bottom" 
                align="end" 
                sideOffset={8}
                collisionPadding={12}
                className="w-48"
              >
                <DropdownMenuItem onClick={() => setSelectedItem("edit-1")}>
                  <Edit className="mr-2 size-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedItem("preview-1")}>
                  <Eye className="mr-2 size-4" />
                  Preview
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => setSelectedItem("delete-1")}
                  className="text-red-600 focus:text-red-600 hover:text-red-600"
                >
                  <Trash2 className="mr-2 size-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card tests dropdown positioning in the top-left area.
              The dropdown should anchor properly to the trigger button.
            </p>
          </CardContent>
        </Card>

        {/* Card 2: Center with popover */}
        <Card className="relative">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Center Card</CardTitle>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="size-8 p-0">
                      <Info className="size-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent 
                    side="top" 
                    sideOffset={8}
                    collisionPadding={12}
                  >
                    <p>This is a tooltip with improved positioning</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="size-8 p-0">
                    <Settings className="size-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent 
                  side="bottom" 
                  align="end" 
                  sideOffset={8}
                  collisionPadding={12}
                  className="w-80"
                >
                  <div className="space-y-3">
                    <h4 className="font-semibold">Settings Panel</h4>
                    <p className="text-sm text-muted-foreground">
                      This popover demonstrates improved positioning and animations.
                      It should anchor properly to the trigger and handle screen edges gracefully.
                    </p>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">Cancel</Button>
                      <Button size="sm">Save</Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card tests popover and tooltip positioning in the center area.
            </p>
          </CardContent>
        </Card>

        {/* Card 3: Right edge */}
        <Card className="relative">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Right Edge Card</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="size-8 p-0">
                  <MoreHorizontal className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                side="bottom" 
                align="end" 
                sideOffset={8}
                collisionPadding={12}
                className="w-48"
              >
                <DropdownMenuItem>
                  <Download className="mr-2 size-4" />
                  Download
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Share className="mr-2 size-4" />
                  Share
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600 focus:text-red-600 hover:text-red-600">
                  <Trash2 className="mr-2 size-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card tests dropdown positioning near the right edge.
              The dropdown should flip to stay within viewport bounds.
            </p>
          </CardContent>
        </Card>

        {/* Card 4: Bottom area */}
        <Card className="relative">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Bottom Card</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="size-8 p-0">
                  <MoreHorizontal className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                side="top" 
                align="end" 
                sideOffset={8}
                collisionPadding={12}
                className="w-48"
              >
                <DropdownMenuItem>
                  <Edit className="mr-2 size-4" />
                  Edit Item
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Eye className="mr-2 size-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600 focus:text-red-600 hover:text-red-600">
                  <Trash2 className="mr-2 size-4" />
                  Remove
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card tests dropdown positioning in the bottom area.
              The dropdown should open upward when there's no space below.
            </p>
          </CardContent>
        </Card>

        {/* Card 5: Mobile test */}
        <Card className="relative md:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Mobile Responsive Test</CardTitle>
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Actions
                    <MoreHorizontal className="ml-2 size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  side="bottom" 
                  align="end" 
                  sideOffset={8}
                  collisionPadding={12}
                  className="w-48"
                >
                  <DropdownMenuItem>
                    <Edit className="mr-2 size-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Eye className="mr-2 size-4" />
                    Preview
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="mr-2 size-4" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-red-600 focus:text-red-600 hover:text-red-600">
                    <Trash2 className="mr-2 size-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card tests mobile responsiveness. On mobile devices, 
              dropdowns should stay within screen bounds and be easily tappable.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status display */}
      {selectedItem && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <p className="text-sm text-blue-800">
              Last action: <strong>{selectedItem}</strong>
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
