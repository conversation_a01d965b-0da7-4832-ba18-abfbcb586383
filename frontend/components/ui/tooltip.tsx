"use client"

import * as React from "react"
import {
  Provider as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>Trigger,
  Content as TooltipContent,
} from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipContentComponent = React.forwardRef<
  React.ElementRef<typeof TooltipContent>,
  React.ComponentPropsWithoutRef<typeof TooltipContent>
>(({ className, sideOffset = 8, collisionPadding = 12, ...props }, ref) => (
  <TooltipContent
    ref={ref}
    sideOffset={sideOffset}
    collisionPadding={collisionPadding}
    className={cn(
      // Premium styling with proper anchoring
      "z-50 overflow-hidden rounded-xl border bg-white shadow-xl drop-shadow-md",
      "px-3 py-2 text-sm text-popover-foreground backdrop-blur-sm",
      // Prevent overflow on mobile
      "max-w-[calc(100vw-2rem)]",
      // Enhanced animations
      "animate-in duration-200 ease-in-out fade-in-0 zoom-in-95",
      "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
      "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      "data-[state=closed]:animate-out data-[state=closed]:duration-150",
      "data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      className
    )}
    {...props}
  />
))
TooltipContentComponent.displayName = "TooltipContent"

export { Tooltip, TooltipTrigger, TooltipContentComponent as TooltipContent, TooltipProvider }
