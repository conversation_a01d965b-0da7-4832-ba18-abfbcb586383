"use client"

import Link from "next/link"
import { User } from "next-auth"
import { useAuth } from "@/lib/auth-context"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserAvatar } from "@/components/user-avatar"
import { cn } from "@/lib/utils"

interface UserAccountNavProps extends React.HTMLAttributes<HTMLDivElement> {
  user: Pick<User, "name" | "image" | "email">
}

export function UserAccountNav({ user }: UserAccountNavProps) {
  // Get the logout function from auth context
  const { logout } = useAuth();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button 
          className={cn(
            "rounded-full transition-all duration-200",
            "p-1 hover:bg-accent active:scale-95",
            "touch-target focus:outline-none focus:ring-2 focus:ring-ring"
          )}
          aria-label="User account menu"
        >
          <UserAvatar
            user={{ name: user.name || null, image: user.image || null }}
            className={cn(
              // Mobile-first avatar sizing
              "size-9 md:size-8"
            )}
          />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="end"
        sideOffset={8}
        collisionPadding={12}
        className={cn(
          // Mobile-first dropdown sizing
          "w-64 md:w-56"
        )}
        onCloseAutoFocus={(e) => {
          // Prevent focus from being trapped in the dropdown
          e.preventDefault();
        }}
      >
        <div className={cn(
          "flex items-center justify-start gap-3",
          // Mobile-first padding
          "p-3 md:p-2"
        )}>
          <div className="flex flex-col space-y-1 leading-none">
            {user.name && <p className={cn(
              "font-medium",
              // Mobile-first text sizing
              "text-base md:text-sm"
            )}>{user.name}</p>}
            {user.email && (
              <p className={cn(
                "truncate text-muted-foreground",
                // Mobile-first text sizing and width
                "w-[180px] text-sm md:w-[200px] md:text-xs"
              )}>
                {user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          asChild
          onSelect={(e) => {
            // Prevent default selection behavior that might cause focus issues
            e.preventDefault();
          }}
        >
          <Link href="/dashboard">Dashboard</Link>
        </DropdownMenuItem>
        <DropdownMenuItem 
          asChild
          onSelect={(e) => {
            // Prevent default selection behavior that might cause focus issues
            e.preventDefault();
          }}
        >
          <Link href="/dashboard/billing">Billing</Link>
        </DropdownMenuItem>
        <DropdownMenuItem 
          asChild
          onSelect={(e) => {
            // Prevent default selection behavior that might cause focus issues
            e.preventDefault();
          }}
        >
          <Link href="/settings/profile">Settings</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onSelect={(event) => {
            event.preventDefault()
            console.log('Logout triggered from user account nav');
            // Use our custom logout function instead of NextAuth's signOut
            logout();
          }}
        >
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
