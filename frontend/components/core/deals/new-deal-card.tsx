"use client"

import { motion, easeOut, easeIn, easeInOut } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NewDealCardProps {
  onClick: () => void;
  index?: number;
}

const linear = (t: number) => t;

export function NewDealCard({ onClick, index = 0 }: NewDealCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: easeOut,
        delay: index * 0.05
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group h-full"
    >
      <Card 
        className="relative h-full cursor-pointer overflow-hidden rounded-xl border-2 border-dashed border-gray-200 bg-gray-50/50 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:border-gray-300 hover:bg-white hover:shadow-lg"
        onClick={onClick}
        data-tour="new-deal-card"
      >

        
        <CardContent className="flex h-full min-h-[200px] flex-col items-center justify-center gap-y-2 p-6 text-center">
          {/* Large Plus Icon */}
          <div className="flex size-16 items-center justify-center rounded-full bg-gray-100 transition-colors duration-300 group-hover:bg-blue-50">
            <Plus className="relative top-px inline-block size-7 leading-none text-gray-400 transition-colors duration-300 group-hover:text-blue-500" />
          </div>
          
          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-700 transition-colors duration-300 group-hover:text-gray-900">
            Add New Deal
          </h3>
          
          {/* Description */}
          <p className="text-sm text-gray-500 transition-colors duration-300 group-hover:text-gray-600">
            Create a new investment opportunity
          </p>
        </CardContent>
      </Card>
    </motion.div>
  );
}
