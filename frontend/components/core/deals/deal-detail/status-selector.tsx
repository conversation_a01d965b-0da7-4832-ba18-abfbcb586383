"use client"

import React, { useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Deal, DealStatus } from '@/lib/types/deal'
import { DealAPI } from '@/lib/api/deal-api'

interface StatusSelectorProps {
  deal: Deal
  onDealUpdate: (updatedDeal: Deal) => void
  className?: string
}

// Status configuration with colors and labels
const STATUS_CONFIG = {
  [DealStatus.NEW]: {
    label: 'New',
    dotColor: 'bg-blue-500'
  },
  [DealStatus.TRIAGE]: {
    label: 'Triage',
    dotColor: 'bg-yellow-400'
  },
  [DealStatus.REVIEWED]: {
    label: 'Reviewed',
    dotColor: 'bg-green-500'
  },
  [DealStatus.EXCLUDED]: {
    label: 'Excluded',
    dotColor: 'bg-gray-400'
  },
  [DealStatus.REJECTED]: {
    label: 'Rejected',
    dotColor: 'bg-red-500'
  },
  [DealStatus.APPROVED]: {
    label: 'Approved',
    dotColor: 'bg-emerald-500'
  },
  [DealStatus.NEGOTIATING]: {
    label: 'Negotiating',
    dotColor: 'bg-indigo-500'
  },
  [DealStatus.CLOSED]: {
    label: 'Closed',
    dotColor: 'bg-zinc-500'
  },
}

export function StatusSelector({ deal, onDealUpdate, className }: StatusSelectorProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [note, setNote] = useState('')
  const [showNoteInput, setShowNoteInput] = useState(false)
  const [pendingStatus, setPendingStatus] = useState<DealStatus | null>(null)
  const { toast } = useToast()

  const currentStatus = deal.status
  const currentConfig = STATUS_CONFIG[currentStatus]

  const handleStatusSelect = (status: DealStatus) => {
    if (status === currentStatus) {
      setOpen(false)
      return
    }

    setPendingStatus(status)
    setShowNoteInput(true)
    setNote('')
  }

  const handleStatusUpdate = async () => {
    if (!pendingStatus || loading) return

    setLoading(true)
    try {
      const updatedDeal = await DealAPI.updateDealStatus(
        deal.id, 
        pendingStatus, 
        note.trim() || undefined
      )
      
      onDealUpdate(updatedDeal)
      setOpen(false)
      setShowNoteInput(false)
      setPendingStatus(null)
      setNote('')
      
      const statusLabel = STATUS_CONFIG[pendingStatus].label
      toast({
        title: "Status Updated",
        description: `Deal status changed to ${statusLabel}`,
      })
    } catch (error) {
      console.error('Failed to update status:', error)
      toast({
        title: "Error",
        description: "Failed to update deal status",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setShowNoteInput(false)
    setPendingStatus(null)
    setNote('')
    setOpen(false)
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <button 
            className="inline-flex items-center gap-2 rounded-full border px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-50 shadow-sm transition-colors"
            disabled={loading}
          >
            <span className={cn("h-2 w-2 rounded-full", currentConfig.dotColor)} />
                {currentConfig.label}
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </button>
        </PopoverTrigger>
        
        <PopoverContent className="w-40 p-1 rounded-lg shadow-xl border bg-white" align="start">
          {!showNoteInput ? (
            <>
                {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                <button
                    key={status}
                  className="flex items-center gap-2 w-full rounded-md px-3 py-2 text-sm hover:bg-gray-100 transition-colors"
                  onClick={() => handleStatusSelect(status as DealStatus)}
                >
                  <span className={cn("h-2 w-2 rounded-full", config.dotColor)} />
                      {config.label}
                </button>
                ))}
            </>
          ) : (
            <div className="space-y-4 p-3">
              <div className="flex items-center gap-2">
                <span className={cn("h-2 w-2 rounded-full", STATUS_CONFIG[pendingStatus!].dotColor)} />
                <span className="font-medium text-sm">
                  Change to {STATUS_CONFIG[pendingStatus!].label}
                </span>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status-note" className="text-xs">
                  Add a note (optional)
                </Label>
                <Textarea
                  id="status-note"
                  placeholder="Explain the status change..."
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  className="min-h-[60px] text-xs"
                  maxLength={1000}
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={handleStatusUpdate}
                  disabled={loading}
                  size="sm"
                  className="flex-1 text-xs"
                >
                  {loading ? "Updating..." : "Update"}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                  size="sm"
                  className="text-xs"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}
