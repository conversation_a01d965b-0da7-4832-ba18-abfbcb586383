"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  X,
  Calendar,
  HardDrive
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { useToast } from "@/components/ui/use-toast"
import { usePitchUploader } from "@/hooks/use-pitch-uploader"

interface PitchDeckUploadProps {
  deal: DealDetailData
  onPitchUploaded?: () => void
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const cleanFilename = (filename: string) => {
  // Remove duplicate words and clean up common patterns
  let cleaned = filename
  
  // Remove duplicate "deck" from pitch_deck_deck.pdf → pitch_deck.pdf
  cleaned = cleaned.replace(/pitch_deck_deck\./i, 'pitch_deck.')
  
  // Remove duplicate "pitch" from pitch_pitch.pdf → pitch.pdf
  cleaned = cleaned.replace(/pitch_pitch\./i, 'pitch.')
  
  // Remove "auto-generated" or similar system suffixes
  cleaned = cleaned.replace(/_auto_generated/i, '')
  cleaned = cleaned.replace(/_system/i, '')
  
  // Clean up multiple underscores
  cleaned = cleaned.replace(/_+/g, '_')
  
  // Remove trailing underscores before file extension
  cleaned = cleaned.replace(/_+\./g, '.')
  
  return cleaned
}

export function PitchDeckUpload({ deal, onPitchUploaded }: PitchDeckUploadProps) {
  const [showUpload, setShowUpload] = useState(false)
  const [busy, setBusy] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()
  
  const {
    uploadState,
    uploadPitch,
    resetUpload,
    isUploading
  } = usePitchUploader(deal.id)

  // Check if deal has a pitch deck
  const hasPitchDeck = deal.pitch_deck_url || 
    (deal.enriched_data?.processing_metadata?.pitch_type) ||
    (deal.documents?.some(doc => doc.source === 'pitch_deck'))

  // Get pitch deck document if it exists
  const pitchDeckDocument = deal.documents?.find(doc => doc.source === 'pitch_deck')

  // Handle successful upload
  useEffect(() => {
    if (uploadState.status === 'completed') {
      toast({
        title: "Pitch deck uploaded successfully",
        description: "Processing has been queued. Your pitch deck will be analyzed and enriched within 2-3 minutes.",
      })
      
      setTimeout(() => {
        resetUpload()
        setShowUpload(false)
        onPitchUploaded?.()
      }, 4000)
    }
  }, [uploadState.status, resetUpload, onPitchUploaded, toast])

  // Handle upload errors
  useEffect(() => {
    if (uploadState.status === 'error' && uploadState.error) {
      toast({
        title: "Upload failed",
        description: uploadState.error,
        variant: "destructive"
      })
    }
  }, [uploadState.status, uploadState.error, toast])

  const uploadInProgress = busy || isUploading || uploadState.status === 'preparing' || uploadState.status === 'uploading' || uploadState.status === 'processing'

  const handleUploadClick = () => {
    if (!uploadInProgress) fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (uploadInProgress) return
    
    const files = event.target.files
    if (!files || files.length === 0) return
    
    const file = files[0]
    
    setBusy(true)
    try {
      await uploadPitch(file)
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setBusy(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handlePreview = () => {
    if (deal.pitch_deck_url) {
      window.open(deal.pitch_deck_url, '_blank', 'noopener,noreferrer')
    } else if (pitchDeckDocument?.preview_url) {
      window.open(pitchDeckDocument.preview_url, '_blank', 'noopener,noreferrer')
    }
  }

  const handleDownload = () => {
    if (deal.pitch_deck_url) {
      window.open(deal.pitch_deck_url, '_blank', 'noopener,noreferrer')
    } else if (pitchDeckDocument?.download_url) {
      window.open(pitchDeckDocument.download_url, '_blank', 'noopener,noreferrer')
    }
  }

  const handleReplace = () => {
    setShowUpload(true)
    resetUpload()
  }

  const handleCancelUpload = () => {
    setShowUpload(false)
    resetUpload()
  }

  // If uploading, show upload progress
  if (isUploading || uploadState.status === 'completed') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="border border-blue-200 bg-blue-50">
          <CardContent className="px-3 py-3">
            <div className="flex items-center gap-3">
              {/* Status Icon */}
              <div className="flex size-8 items-center justify-center rounded-lg border border-blue-300 bg-blue-100 text-blue-600">
                {uploadState.status === 'completed' ? (
                  <CheckCircle className="size-5" />
                ) : (
                  <RefreshCw className={cn("size-5", isUploading && "animate-spin")} />
                )}
              </div>

              {/* Upload Info */}
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="text-sm font-medium text-blue-900">
                    {uploadState.status === 'completed' 
                      ? 'Pitch deck uploaded successfully!' 
                      : 'Uploading pitch deck...'
                    }
                  </h4>
                  <Badge className="rounded-full bg-blue-100 text-blue-700 border-blue-200 px-2 py-0.5 text-xs">
                    Pitch Deck
                  </Badge>
                </div>
                
                <p className="text-xs text-blue-700">
                  {uploadState.status === 'preparing' && 'Preparing upload...'}
                  {uploadState.status === 'uploading' && 'Uploading to server...'}
                  {uploadState.status === 'processing' && 'Processing pitch deck...'}
                  {uploadState.status === 'completed' && 'Processing queued - analysis will complete in 2-3 minutes'}
                </p>

                {/* Progress Bar */}
                {isUploading && (
                  <div className="mt-2">
                    <Progress value={uploadState.progress} className="h-1.5 bg-blue-200" />
                    <p className="mt-1 text-xs text-blue-600">
                      {Math.round(uploadState.progress)}% complete
                    </p>
                  </div>
                )}
              </div>

              {/* Close Button */}
              {uploadState.status === 'completed' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancelUpload}
                  className="size-8 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                >
                  <X className="size-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // If showing upload interface
  if (showUpload) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="border-2 border-dashed border-blue-300 bg-blue-50/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-sm font-semibold text-gray-900">Upload Pitch Deck</h3>
                <p className="text-xs text-gray-600">
                  Main deck used for parsing, enrichment, and scoring
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelUpload}
                className="size-8 p-0 text-gray-500 hover:text-gray-700"
              >
                <X className="size-4" />
              </Button>
            </div>
            
            <div
              className="relative border-2 border-dashed border-blue-300 rounded-lg p-6 text-center bg-white/80 hover:bg-blue-50 transition-colors cursor-pointer"
              onDragOver={e => { e.preventDefault(); e.stopPropagation(); }}
              onDrop={e => {
                e.preventDefault();
                e.stopPropagation();
                if (uploadInProgress) return;
                const files = e.dataTransfer.files;
                if (files && files.length > 0) {
                  handleFileSelect({ target: { files } } as any)
                }
              }}
              style={{ opacity: uploadInProgress ? 0.6 : 1, pointerEvents: uploadInProgress ? 'none' : 'auto' }}
            >
              <div className="flex justify-center mb-3">
                <div className="flex size-12 items-center justify-center rounded-full bg-blue-100">
                  <Upload className="size-6 text-blue-600" />
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Drag and drop a PDF file here, or click to browse
              </p>
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (!uploadInProgress) handleUploadClick()
                }} 
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
                disabled={uploadInProgress}
              >
                <Upload className="size-4 mr-2" />
                Choose PDF File
              </Button>
              <p className="text-xs text-gray-500 mt-3">
                Maximum file size: 20MB • PDF files only
              </p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              accept=".pdf"
              disabled={uploadInProgress}
            />
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // If pitch deck exists, show compact card like in the image
  if (hasPitchDeck) {
    const filename = pitchDeckDocument?.filename || 'pitch_deck.pdf'
    const cleanedFilename = cleanFilename(filename)
    const fileSize = pitchDeckDocument?.file_size || 0
    const uploadDate = pitchDeckDocument?.created_at || new Date().toISOString()
    const processingMetadata = deal.enriched_data?.processing_metadata

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="border border-gray-200 bg-white transition-all hover:bg-gray-50 hover:shadow-sm">
          <CardContent className="px-3 py-3">
            <div className="flex items-center justify-between">
              {/* Left Block: File Icon + Info */}
              <div className="flex items-center gap-3 min-w-0 flex-1">
                {/* File Icon */}
                <div className="flex size-8 items-center justify-center rounded-lg border border-red-200 bg-red-50 text-red-600">
                  <FileText className="size-5" />
                </div>

                {/* File Info */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="truncate text-sm font-medium text-gray-900">
                      {cleanedFilename}
                    </h4>
                    <Badge className="rounded-full bg-blue-100 text-blue-700 border-blue-200 px-2 py-0.5 text-xs">
                      Pitch Deck
                    </Badge>
                    {processingMetadata?.pitch_type && (
                      <Badge variant="outline" className="rounded-full text-xs px-2 py-0.5 bg-gray-50 text-gray-600 border-gray-200">
                        {processingMetadata.pitch_type}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <HardDrive className="size-3" />
                      <span>{formatFileSize(fileSize)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="size-3" />
                      <span>{formatDate(uploadDate)}</span>
                    </div>
                  </div>
                  
                  {/* Processing Status */}
                  {processingMetadata && (
                    <div className="flex items-center gap-1 mt-1 text-xs text-green-600">
                      <CheckCircle className="size-3" />
                      <span>
                        Processed • {processingMetadata.founders_extracted || 0} Founders extracted
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Actions: Icon-only buttons */}
              <div className="flex items-center gap-2 ml-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePreview}
                        className="size-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                      >
                        <Eye className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>Preview</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDownload}
                        className="size-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                      >
                        <Download className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>Download</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleReplace}
                        className="size-8 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                      >
                        <Upload className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>Replace</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // Default state - no pitch deck uploaded
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="border-2 border-dashed border-blue-300 bg-blue-50/30 transition-colors hover:border-blue-400 hover:bg-blue-50/50">
        <CardContent className="p-6">
          <div className="space-y-4 text-center">
            <div className="flex justify-center">
              <div className="flex size-16 items-center justify-center rounded-full bg-blue-100">
                <FileText className="size-8 text-blue-600" />
              </div>
            </div>
            <div>
              <h3 className="mb-2 text-sm font-semibold text-gray-900">Upload Pitch Deck</h3>
              <p className="mb-4 text-xs text-gray-600">
                Main deck used for parsing, enrichment, and scoring
              </p>
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (!uploadInProgress) handleUploadClick()
                }} 
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
                disabled={uploadInProgress}
              >
                <Upload className="size-4 mr-2" />
                Choose PDF File
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Maximum file size: 20MB • PDF files only
            </p>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept=".pdf"
        disabled={uploadInProgress}
      />
    </motion.div>
  )
} 