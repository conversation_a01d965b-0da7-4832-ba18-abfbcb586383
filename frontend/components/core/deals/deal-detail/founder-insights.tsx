"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check<PERSON>ircle, AlertTriangle, Copy, Download, Users, TrendingUp, Shield, Info } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { FounderSignals } from "@/lib/api/founder-api"
import { motion } from "framer-motion"
import clsx from "clsx"

interface FounderInsightsProps {
  signals: FounderSignals | null
  founderName: string
  className?: string
}

export function FounderInsights({ signals, founderName, className }: FounderInsightsProps) {
  const { toast } = useToast()

  const handleCopyInsights = async () => {
    if (!signals) return
    
    const insightsText = `
${founderName} - Founder Analysis

Score: ${signals.score}/100

Strengths:
${signals.strengths.items.map(item => `• ${item}`).join('\n')}

Risks:
${signals.risks.items.map(item => `• ${item}`).join('\n')}

Tags: ${signals.tags.join(', ')}

Skill Profile:
• Tech: ${signals.skillProfile.tech}/10
• Product: ${signals.skillProfile.product}/10
• Business: ${signals.skillProfile.business}/10
• Operations: ${signals.skillProfile.operations}/10
• Fundraising: ${signals.skillProfile.fundraising}/10
    `.trim()

    try {
      await navigator.clipboard.writeText(insightsText)
      toast({
        title: "Copied to clipboard",
        description: "Founder insights have been copied to your clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDownloadPDF = () => {
    // Placeholder for PDF generation
    toast({
      title: "PDF Download",
      description: "PDF generation will be available soon.",
    })
  }

  if (!signals) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className={clsx(
          "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
          className
        )}>
          <CardHeader className="border-b border-slate-100 pb-4">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <Users className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  AI Insights
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  AI-generated analysis
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl border border-slate-200 bg-slate-50">
                <Users className="size-8 text-slate-400" />
              </div>
              <p className="text-sm text-slate-500">No AI insights available</p>
              <p className="mt-1 text-xs text-slate-400">Enrich founder data to generate insights</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  const getScoreLevel = (score: number) => {
    if (score >= 80) return "High"
    if (score >= 60) return "Medium"
    if (score >= 40) return "Low"
    return "Limited"
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#059669'
    if (score >= 60) return '#d97706'
    if (score >= 40) return '#dc2626'
    return '#6b7280'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <Card className={clsx(
        "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
        className
      )}>
        <CardHeader className="border-b border-slate-100 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <Users className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  AI-Generated Insights
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Machine learning analysis
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="border-slate-200 text-xs text-slate-600">
                AI Generated
              </Badge>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyInsights}
                className="size-8 p-0 hover:bg-slate-100"
              >
                <Copy className="size-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownloadPDF}
                className="size-8 p-0 hover:bg-slate-100"
              >
                <Download className="size-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6 p-6">
          {/* Clean Score Display */}
          <div className="flex items-center justify-between rounded-2xl border border-slate-200 bg-slate-50 p-4">
            <div className="flex items-center gap-4">
              <div className="relative">
                <svg className="size-16 -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-slate-200"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="transition-all duration-1000 ease-out"
                    stroke={getScoreColor(signals.score)}
                    strokeWidth="3"
                    strokeDasharray={`${signals.score}, 100`}
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-semibold text-slate-900">
                    {signals.score}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-slate-900">Overall Score</h3>
                <p className="text-sm text-slate-600">AI assessment rating</p>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-slate-900">
                Confidence: {getScoreLevel(signals.score)}
              </div>
              <div className="text-xs text-slate-500">
                {signals.score}/100
              </div>
            </div>
          </div>

          {/* AI Summary */}
          {/* <div className="rounded-2xl border border-blue-200/50 bg-blue-50/50 p-4"> */}
            {/* <div className="flex items-start gap-2"> */}
              {/* <Info className="mt-0.5 size-4 shrink-0 text-blue-600" /> */}
              {/* <div className="text-sm leading-relaxed text-slate-700">
                {founderName} combines technical depth with operational scale-up experience, but has limited exposure to fundraising.
              </div> */}
            {/* </div> */}
          {/* </div> */}

          {/* Soft Tags */}
          {Array.isArray(signals.tags) && signals.tags.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <h4 className="mb-3 flex items-center gap-2 text-sm font-medium text-slate-900">
                <TrendingUp className="size-4 text-slate-600" />
                Profile Tags
              </h4>
              <div className="flex flex-wrap gap-2">
                {signals.tags.map((tag, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.4 + index * 0.05 }}
                  >
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer rounded-full border-slate-200 bg-white px-3 py-1 text-xs text-slate-600 transition-colors hover:bg-slate-50"
                    >
                      {tag}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Clean Strengths */}
          {Array.isArray(signals.strengths?.items) && signals.strengths.items.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="space-y-3"
            >
              <h4 className="flex items-center gap-2 text-sm font-medium text-slate-900">
                <Shield className="size-4 text-slate-600" />
                Key Strengths
              </h4>
              <div className="space-y-2">
                {signals.strengths.items.map((strength, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    className="flex items-start gap-3 rounded-2xl border border-slate-200 bg-slate-50 p-3"
                  >
                    <CheckCircle className="mt-0.5 size-4 shrink-0 text-slate-600" />
                    <span className="text-sm leading-relaxed text-slate-700">{strength}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Clean Risks */}
          {Array.isArray(signals.risks?.items) && signals.risks.items.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="space-y-3"
            >
              <h4 className="flex items-center gap-2 text-sm font-medium text-slate-900">
                <AlertTriangle className="size-4 text-slate-600" />
                Areas for Growth
              </h4>
              <div className="space-y-2">
                {signals.risks.items.map((risk, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="flex items-start gap-3 rounded-2xl border border-slate-200 bg-slate-50 p-3"
                  >
                    <AlertTriangle className="mt-0.5 size-4 shrink-0 text-slate-600" />
                    <span className="text-sm leading-relaxed text-slate-700">{risk}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* AI Attribution */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="border-t border-slate-200 pt-4"
          >
            <p className="flex items-center gap-2 text-xs text-slate-500">
              <Users className="size-3" />
              Generated by Orbit AI from founder history and experience data
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default FounderInsights
