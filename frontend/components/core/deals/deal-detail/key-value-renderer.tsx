"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ExternalLink, Calendar, DollarSign, Users, MapPin, Building2, Link, User, Globe, Settings } from "lucide-react"
import { cn } from "@/lib/utils"
import { TechStackRenderer } from "./tech-stack-renderer"

interface KeyValueRendererProps {
  data: Record<string, any>
  className?: string
  skipEmpty?: boolean
  highlightFields?: string[]
}

export function KeyValueRenderer({ 
  data, 
  className = "",
  skipEmpty = true,
  highlightFields = []
}: KeyValueRendererProps) {
  
  // Helper function to format field labels
  const formatLabel = (key: string): string => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  // Helper function to check if value should be skipped
  const shouldSkipValue = (value: any): boolean => {
    if (!skipEmpty) return false
    
    if (value === null || value === undefined || value === '') return true
    if (Array.isArray(value) && value.length === 0) return true
    if (typeof value === 'string' && value.trim() === '') return true
    if (typeof value === 'string' && value === '[]') return true
    if (typeof value === 'object' && Object.keys(value).length === 0) return true
    
    return false
  }

  // Helper function to check if key should be skipped (e.g., id fields)
  const shouldSkipKey = (key: string): boolean => {
    const lowerKey = key.toLowerCase()
    return lowerKey.startsWith('id') || lowerKey.endsWith('id') || 
           lowerKey.startsWith('uid') || lowerKey.endsWith('uid') ||
           lowerKey.startsWith('hash') || lowerKey.endsWith('hash')
  }



  // Helper function to get icon for a field
  const getFieldIcon = (key: string) => {
    if (key.includes('funding') || key.includes('valuation') || key.includes('revenue')) {
      return <DollarSign className="h-4 w-4" />
    }
    if (key.includes('employee') || key.includes('headcount')) {
      return <Users className="h-4 w-4" />
    }
    if (key.includes('date') || key.includes('created') || key.includes('updated')) {
      return <Calendar className="h-4 w-4" />
    }
    if (key.includes('location') || key.includes('country') || key.includes('city') || key.includes('geo')) {
      return <MapPin className="h-4 w-4" />
    }
    if (key.includes('company') || key.includes('business') || key.includes('industry') || key.includes('sector')) {
      return <Building2 className="h-4 w-4" />
    }
    if (key.includes('url') || key.includes('website') || key.includes('link')) {
      return <Link className="h-4 w-4" />
    }
    if (key.includes('user') || key.includes('person') || key.includes('name')) {
      return <User className="h-4 w-4" />
    }
    if (key.includes('domain')) {
      return <Globe className="h-4 w-4" />
    }
    if (key.includes('description') || key.includes('about')) {
      return <Building2 className="h-4 w-4" />
    }
    if (key.includes('model') || key.includes('type')) {
      return <Building2 className="h-4 w-4" />
    }
    if (key.includes('stage') || key.includes('level')) {
      return <Users className="h-4 w-4" />
    }
    if (key.includes('year') || key.includes('founded')) {
      return <Calendar className="h-4 w-4" />
    }
    // Default icon for any field that doesn't match above patterns
    return <Building2 className="h-4 w-4" />
  }

  // Helper function to render complex values
  const renderValue = (value: any, key: string) => {
    // Handle null/undefined
    if (value === null || value === undefined) {
      return <span className="text-gray-400 italic">Not available</span>
    }

    // Handle arrays
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-400 italic">None</span>
      }
      
      // Handle array of objects (like employees, locations, etc.)
      if (value.length > 0 && typeof value[0] === 'object') {
        return (
          <div className="space-y-2">
            {value.slice(0, 3).map((item, index) => (
              <div key={index} className="text-sm">
                {renderComplexObject(item, key)}
              </div>
            ))}
            {value.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{value.length - 3} more
              </Badge>
            )}
          </div>
        )
      }
      
      // Handle simple arrays
      return (
        <div className="flex flex-wrap gap-1">
          {value.map((item, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {String(item)}
            </Badge>
          ))}
        </div>
      )
    }

    // Handle numbers
    if (typeof value === 'number') {
      // Format currency fields
      if (key.includes('funding') || key.includes('valuation') || key.includes('revenue')) {
        return (
          <span className="font-semibold text-green-600">
            {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(value)}
          </span>
        )
      }
      
      // Format employee count
      if (key.includes('employee_count')) {
        return (
          <span className="font-medium">
            {new Intl.NumberFormat('en-US').format(value)}
          </span>
        )
      }
      
      return <span className="font-medium">{value.toLocaleString()}</span>
    }

    // Handle dates
    if (typeof value === 'string' && (key.includes('date') || key.includes('created') || key.includes('updated'))) {
      try {
        const date = new Date(value)
        if (!isNaN(date.getTime())) {
          return (
            <span className="text-sm">
              {date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </span>
          )
        }
      } catch (e) {
        // If date parsing fails, return as string
      }
    }

    // Handle URLs
    if (typeof value === 'string' && (value.startsWith('http://') || value.startsWith('https://'))) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <a
                href={value}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm break-all hover:underline"
              >
                {value}
                <ExternalLink className="h-3 w-3 flex-shrink-0" />
              </a>
            </TooltipTrigger>
            <TooltipContent>
              <p>Open link in new tab</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    // Handle objects
    if (typeof value === 'object' && value !== null) {
      return renderComplexObject(value, key)
    }

    // Handle strings - NO TRUNCATION, let text flow naturally
    if (typeof value === 'string') {
      return (
        <span className="text-sm whitespace-pre-wrap break-words">
          {value}
        </span>
      )
    }

    return <span className="text-sm">{String(value)}</span>
  }

  // Helper function to render complex objects
  const renderComplexObject = (obj: any, parentKey: string) => {
    if (!obj || typeof obj !== 'object') {
      return <span className="text-sm">{String(obj)}</span>
    }

    // Handle specific object types
    if (obj.title && obj.subtitle) {
      return (
        <div className="text-sm">
          <div className="font-medium">{obj.title}</div>
          <div className="text-gray-600">{obj.subtitle}</div>
        </div>
      )
    }

    if (obj.name && obj.link) {
      return (
        <div className="text-sm">
          <a 
            href={obj.link} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 hover:underline"
          >
            {obj.name}
          </a>
        </div>
      )
    }

    if (obj.img && obj.title) {
      return (
        <div className="flex items-center gap-2 text-sm">
          <img 
            src={obj.img} 
            alt={obj.title} 
            className="w-6 h-6 rounded-full object-cover"
            onError={(e) => {
              e.currentTarget.style.display = 'none'
            }}
          />
          <span>{obj.title}</span>
        </div>
      )
    }

    // Handle generic objects by showing key-value pairs
    const entries = Object.entries(obj).slice(0, 3)
    if (entries.length > 0) {
      return (
        <div className="space-y-1">
          {entries.map(([k, v]) => (
            <div key={k} className="text-xs">
              <span className="text-gray-600">{formatLabel(k)}:</span>{' '}
              <span>{renderValue(v, k)}</span>
            </div>
          ))}
          {Object.keys(obj).length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{Object.keys(obj).length - 3} more fields
            </Badge>
          )}
        </div>
      )
    }

    return <span className="text-sm text-gray-500">Complex data</span>
  }

  // Check if this is tech stack data
  const isTechStackData = (data: Record<string, any>): boolean => {
    const techStackKeys = [
      'builtwith_tech', 'technologies', 'tech_stack', 'stack', 
      'builtwith', 'tech', 'technologies_used', 'tech_used'
    ]
    
    // Check if any key matches tech stack patterns
    const hasTechKey = Object.keys(data).some(key => 
      techStackKeys.some(techKey => key.toLowerCase().includes(techKey))
    )
    
    // Check if the data structure looks like tech stack (array of strings/objects)
    const hasTechArray = Object.values(data).some(value => 
      Array.isArray(value) && value.length > 0 && 
      (typeof value[0] === 'string' || 
       (typeof value[0] === 'object' && value[0] !== null && 'name' in value[0]))
    )
    
    return hasTechKey || hasTechArray
  }

  // Check if this is tech stack data and extract it
  const techStackKey = Object.keys(data).find(key => 
    ['builtwith_tech', 'technologies', 'tech_stack', 'stack', 'builtwith', 'tech'].some(techKey => 
      key.toLowerCase().includes(techKey)
    )
  )
  
  const techData = techStackKey ? data[techStackKey] : null
  const hasTechData = techData && Array.isArray(techData) && techData.length > 0
  
  // Create data without tech stack for regular rendering
  const dataWithoutTech = { ...data }
  if (techStackKey) {
    delete dataWithoutTech[techStackKey]
  }

  // Filter and sort data (excluding tech stack data)
  const filteredData = Object.entries(dataWithoutTech)
    .filter(([key, value]) => !shouldSkipKey(key) && !shouldSkipValue(value))
    .sort(([a], [b]) => {
      // Sort highlighted fields first
      const aHighlighted = highlightFields.includes(a)
      const bHighlighted = highlightFields.includes(b)
      
      if (aHighlighted && !bHighlighted) return -1
      if (!aHighlighted && bHighlighted) return 1
      
      // Then sort alphabetically
      return a.localeCompare(b)
    })

  // Group data by category for better organization
  const groupedData = {
    "Company Info": filteredData.filter(([key]) => 
      ['name', 'domain', 'website', 'description', 'industry', 'sector', 'business_model', 'company_type'].includes(key)
    ),
    "Financial": filteredData.filter(([key]) => 
      ['revenue', 'valuation', 'funding_total', 'funding_status', 'funding_rounds', 'last_funding_amount', 'last_funding_date'].includes(key)
    ),
    "Team & Growth": filteredData.filter(([key]) => 
      ['employee_count', 'employee_count_range', 'founded_year', 'traction_level', 'stage'].includes(key)
    ),
    "Location": filteredData.filter(([key]) => 
      ['hq_location', 'incorporation_country', 'geo_tags'].includes(key)
    ),
    "Contact & Social": filteredData.filter(([key]) => 
      ['email', 'phone', 'linkedin_url', 'twitter_url', 'facebook_url'].includes(key)
    ),
    "Technology Stack": hasTechData ? [['tech_stack', techData]] : [],
    "Other": filteredData.filter(([key]) => 
      !['name', 'domain', 'website', 'description', 'industry', 'sector', 'business_model', 'company_type',
        'revenue', 'valuation', 'funding_total', 'funding_status', 'funding_rounds', 'last_funding_amount', 'last_funding_date',
        'employee_count', 'employee_count_range', 'founded_year', 'traction_level', 'stage',
        'hq_location', 'incorporation_country', 'geo_tags',
        'email', 'phone', 'linkedin_url', 'twitter_url', 'facebook_url'].includes(key)
    )
  }

  // Remove empty categories
  const nonEmptyGroups = Object.entries(groupedData).filter(([_, items]) => items.length > 0)

  if (nonEmptyGroups.length === 0 && !hasTechData) {
    return (
      <Card className={cn("border-0 bg-white/80 shadow-xl backdrop-blur-md", className)}>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <p>No data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

    return (
    <div className={cn("space-y-6", className)}>
      {/* Data Sections */}
      {nonEmptyGroups.map(([category, items]) => (
        <Card key={category} className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
          <CardContent className="p-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                {category === "Company Info" && <Building2 className="h-5 w-5 text-gray-600" />}
                {category === "Financial" && <DollarSign className="h-5 w-5 text-gray-600" />}
                {category === "Team & Growth" && <Users className="h-5 w-5 text-gray-600" />}
                {category === "Location" && <MapPin className="h-5 w-5 text-gray-600" />}
                {category === "Contact & Social" && <Link className="h-5 w-5 text-gray-600" />}
                {category === "Technology Stack" && <Settings className="h-5 w-5 text-gray-600" />}
                {category === "Other" && <Globe className="h-5 w-5 text-gray-600" />}
                {category}
              </h3>
              
              {category === "Technology Stack" ? (
                <TechStackRenderer data={techData} />
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {items.map(([key, value]) => {
                    const isHighlighted = highlightFields.includes(key)
                    const icon = getFieldIcon(key)

                    return (
                      <div 
                        key={key} 
                        className={cn(
                          "flex items-start gap-3 p-4 rounded-xl transition-all duration-200",
                          "bg-gray-50/50 hover:bg-gray-100/50 border border-transparent hover:border-gray-200/50"
                        )}
                      >
                        {icon && (
                          <div className="flex-shrink-0 p-2 rounded-lg bg-gray-100 text-gray-600">
                            {icon}
                          </div>
                        )}
                        
                        <div className="min-w-0 flex-1 space-y-1">
                          <div className="text-sm font-medium text-gray-700">
                            {formatLabel(key)}
                          </div>
                          <div className="text-sm text-gray-900">
                            {renderValue(value, key)}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 