"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Target,
  TrendingUp,
  TrendingDown,
  ChevronRight,
  BarChart3,
  Calendar,
  Shield,
  Copy,
  Check,
  ExternalLink
} from "lucide-react"
import { cn } from "@/lib/utils"
import { mobileRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Deal } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { ExclusionDisplay } from "./exclusion-display"
import { FormSubmissionRequest } from "./form-submission-request"

interface ScoreTabProps {
  deal: DealDetailData | Deal
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50'
  if (score >= 40) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

const getMatchStatusBadge = (score: number) => {
  if (score >= 80) return { text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200' }
  if (score >= 60) return { text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200' }
  if (score >= 40) return { text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
  return { text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200' }
}

// Reusable Org Thesis Match Component
const OrgThesisMatchSection = ({ orgThesisMatching, hasFormScoring }: { orgThesisMatching: any; hasFormScoring?: boolean }) => {
  if (!orgThesisMatching) return null;

  return (
    <Card className="border-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
              <Target className="size-5 text-blue-600" />
              Investment Thesis Match
            </CardTitle>
            {hasFormScoring && (
              <p className="text-xs text-muted-foreground mt-1">
                Initial assessment • Available immediately
              </p>
            )}
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-400 hover:text-gray-600">
                  <Shield className="size-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs max-w-xs">
                Investment Thesis Match is based on organization-level preferences and available deal data (pre-form submission).
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Badge className={cn(
              "text-lg font-bold px-4 py-2",
              orgThesisMatching.score >= 70 
                ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white" 
                : "bg-gray-100 text-gray-700 border border-gray-300"
            )}>
              {Math.round(orgThesisMatching.score)}%
            </Badge>
            <Badge variant="outline" className="text-sm bg-white/50">
              {orgThesisMatching.assessment}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Investment Rationale</p>
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-6">
              {orgThesisMatching.investment_rationale}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export function ScoreTab({ deal }: ScoreTabProps) {
  const [animatedScore, setAnimatedScore] = useState(0)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [copySuccess, setCopySuccess] = useState(false)

  // Extract scoring data from the correct structure
  const dealScoring = (deal as any).scoring
  const thesisScoring = dealScoring?.thesis
  const orgThesisMatching = (deal as any).org_thesis_matching
  const exclusionResult = (deal as any).exclusion_filter_result
  const isExcluded = exclusionResult?.excluded || false

  // Calculate key metrics from the actual backend data structure
  const thesisMatchPercent = thesisScoring?.score?.normalized_percent ? Math.round(thesisScoring.score.normalized_percent) : null
  const coreScore = thesisScoring?.score?.core || 0
  const bonusTotal = thesisScoring?.score?.bonus || 0
  const penaltyTotal = thesisScoring?.score?.penalty || 0
  const maxPossibleScore = thesisScoring?.score?.max_possible || 100
  const lastScored = thesisScoring?.last_scored_at
  const thesisName = thesisScoring?.thesis_name || 'Investment Thesis'

  // Check if we should show form submission request
  const hasSubmissions = (deal.submission_ids && deal.submission_ids.length > 0) || false
  const inviteStatus = deal.invite_status || null
  const shouldShowFormRequest = !hasSubmissions && inviteStatus !== 'sent'

  // Animate score on mount
  useEffect(() => {
    if (thesisMatchPercent !== null) {
      const timer = setTimeout(() => {
        setAnimatedScore(thesisMatchPercent)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [thesisMatchPercent])

  const handleViewFullAnalysis = () => {
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  const handleFormRequestSuccess = () => {
    // Trigger a refresh of the component to update the UI
    setRefreshTrigger(prev => prev + 1)
  }

  const handleCopyLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000) // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  // Show exclusion first if deal is excluded
  if (isExcluded) {
    return (
      <div className="space-y-6">
        <ExclusionDisplay exclusionResult={exclusionResult} />
        <OrgThesisMatchSection orgThesisMatching={orgThesisMatching} hasFormScoring={!!thesisScoring} />
        {thesisScoring && (
          <Card className="border-0 bg-gray-50/50 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg text-gray-600">
                <BarChart3 className="size-5" />
                Scoring Data (Hidden due to exclusion)
              </CardTitle>
              <p className="text-sm text-gray-500">
                This deal was scored but results are hidden due to exclusion filter.
              </p>
            </CardHeader>
          </Card>
        )}
      </div>
    )
  }

  // Show form submission request if no submissions and invite not sent
  if (shouldShowFormRequest) {
    return (
      <div className="space-y-6" data-tour="request-founder-submission">
        <OrgThesisMatchSection orgThesisMatching={orgThesisMatching} hasFormScoring={false} />
        <FormSubmissionRequest 
          deal={deal} 
          onSuccess={handleFormRequestSuccess}
        />
      </div>
    )
  }

  // Show empty state if no scoring data but form request was sent
  if (!thesisScoring || thesisMatchPercent === null) {
    // Get magic token URL from deal notes if available
    const magicTokenUrl = (deal as any).notes?.deal_collection_form?.magic_token

    return (
      <div className="space-y-6">
        <OrgThesisMatchSection orgThesisMatching={orgThesisMatching} hasFormScoring={false} />
        <div className={cn(mobileRetreat.empty.container, "py-16")}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-center"
          >
            <div className={mobileRetreat.empty.icon}>
              <Target className="mx-auto mb-6 size-16 text-gray-400 md:size-20" />
            </div>
            <h3 className={mobileRetreat.empty.title}>No Scoring Data Available</h3>
            <p className={mobileRetreat.empty.description}>
              {inviteStatus === 'sent' 
                ? "Form request has been sent. Scoring will appear here once the founder completes the submission."
                : "This deal hasn't been scored yet. Scoring will appear here once the thesis setup is complete."
              }
            </p>
            
            {/* Show magic token URL if form was sent */}
            {inviteStatus === 'sent' && magicTokenUrl && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm font-medium text-blue-900 mb-3">
                  Form Access Link (in case founder misses email):
                </p>
                <div className="flex items-center gap-3">
                  <a
                    href={magicTokenUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 flex items-center gap-2 p-3 bg-white border border-blue-200 rounded-lg hover:border-blue-300 hover:bg-blue-50/50 transition-colors group"
                  >
                    <ExternalLink className="size-4 text-blue-600 shrink-0" />
                    <span className="text-sm text-blue-800 break-all group-hover:text-blue-900">
                      {magicTokenUrl}
                    </span>
                  </a>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleCopyLink(magicTokenUrl)}
                    className={cn(
                      "shrink-0 transition-all duration-200",
                      copySuccess 
                        ? "bg-green-50 border-green-200 text-green-700 hover:bg-green-50" 
                        : "hover:bg-blue-50 hover:border-blue-300"
                    )}
                  >
                    <motion.div
                      initial={false}
                      animate={copySuccess ? { scale: [1, 1.2, 1] } : { scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      {copySuccess ? (
                        <Check className="size-4" />
                      ) : (
                        <Copy className="size-4" />
                      )}
                    </motion.div>
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-8">
              <Button
                onClick={handleViewFullAnalysis}
                variant="outline"
                className="gap-2 rounded-xl px-6 py-3"
              >
                <BarChart3 className="size-5" />
                Check Analysis Status
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <Shield className="size-6 text-blue-600" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {orgThesisMatching ? 'Form-Based Scoring' : thesisName}
                </h2>
                {orgThesisMatching && (
                  <p className="text-sm text-muted-foreground">
                    Detailed analysis from founder submission
                  </p>
                )}
              </div>
            </div>
            {lastScored && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="size-4" />
                <span>Last scored {new Date(lastScored * 1000).toLocaleDateString()}</span>
              </div>
            )}
          </div>
          <Button
            onClick={handleViewFullAnalysis}
            size="sm"
            className="gap-2"
          >
            View Full Analysis
            <ChevronRight className="size-4" />
          </Button>
        </div>

        {/* Main Thesis Match Score Card */}
        <Card className={cn(
          "border-0 p-6 shadow-lg",
          getScoreBackground(thesisMatchPercent)
        )}>
          <CardContent className="p-0">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-4">
                    <motion.span
                      className="text-5xl font-bold text-gray-900"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8 }}
                    >
                      {animatedScore}%
                    </motion.span>
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        {orgThesisMatching ? 'Form-Based Match' : 'Thesis Match'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Based on {Object.keys(thesisScoring.question_scores || {}).length} criteria
                        {orgThesisMatching && ' from founder submission'}
                      </p>
                    </div>
                  </div>

                  <div className="w-80">
                    <Progress
                      value={animatedScore}
                      className="h-3"
                    />
                  </div>
                </div>
              </div>

              {/* Status Badges */}
              <div className="flex flex-col items-end gap-2">
                <Badge className={cn(
                  "px-4 py-2 text-sm font-bold",
                  getMatchStatusBadge(thesisMatchPercent).color
                )}>
                  {getMatchStatusBadge(thesisMatchPercent).text}
                </Badge>

                {(bonusTotal > 0 || penaltyTotal > 0) && (
                  <Badge variant="outline" className="text-xs">
                    {bonusTotal > 0 && `+${bonusTotal} bonus`}
                    {bonusTotal > 0 && penaltyTotal > 0 && ", "}
                    {penaltyTotal > 0 && `-${penaltyTotal} penalty`}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Org Thesis Match Section */}
      <OrgThesisMatchSection orgThesisMatching={orgThesisMatching} hasFormScoring={true} />

      {/* Score Summary Section */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="border-0 bg-white/80 shadow-sm backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-100 p-2 text-blue-600">
                <BarChart3 className="size-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Core Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {coreScore.toFixed(1)} / {maxPossibleScore}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {bonusTotal > 0 && (
          <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-green-100 p-2 text-green-600">
                  <TrendingUp className="size-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-600">Bonus Points</p>
                  <p className="text-2xl font-bold text-green-900">+{bonusTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {penaltyTotal > 0 && (
          <Card className="border-0 bg-red-50/80 shadow-sm backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-red-100 p-2 text-red-600">
                  <TrendingDown className="size-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-red-600">Penalty Points</p>
                  <p className="text-2xl font-bold text-red-900">-{penaltyTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Placeholder for upcoming AI Summary */}
      <Card className="border-0 bg-gradient-to-br from-purple-50 to-blue-50 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
            <Target className="size-5 text-purple-600" />
            AI Analysis Summary
          </CardTitle>
          <p className="text-sm text-gray-600">
            Coming soon: AI-powered highlights and insights about this deal's performance
          </p>
        </CardHeader>
      </Card>
    </div>
  )
}
