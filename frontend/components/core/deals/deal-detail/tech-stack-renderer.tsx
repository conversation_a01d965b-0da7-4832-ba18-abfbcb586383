"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>lt<PERSON>, Too<PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronDown, ChevronUp, Settings, Globe, Shield, BarChart3, Mail, Smartphone, Server } from "lucide-react"
import { cn } from "@/lib/utils"

interface TechItem {
  name: string
  category?: string
  usage?: number
  icon?: string
}

interface TechStackRendererProps {
  data: TechItem[] | string[]
  className?: string
}

// Tech categories with their icons and matching patterns
const TECH_CATEGORIES = {
  "Mobile Optimization": {
    icon: Smartphone,
    patterns: ["viewport", "mobile", "iphone", "android", "responsive", "clip", "meta"]
  },
  "Hosting & CDN": {
    icon: Server,
    patterns: ["aws", "gcp", "azure", "netlify", "vercel", "cloudflare", "cdn", "hosting"]
  },
  "Dev Tools": {
    icon: Settings,
    patterns: ["webpack", "next", "react", "vue", "angular", "docker", "kubernetes", "git", "npm", "yarn"]
  },
  "Analytics & Tracking": {
    icon: BarChart3,
    patterns: ["google analytics", "ga", "mixpanel", "segment", "hotjar", "analytics", "tracking"]
  },
  "Security": {
    icon: Shield,
    patterns: ["https", "ssl", "csp", "captcha", "security", "encryption", "firewall"]
  },
  "Marketing & CRM": {
    icon: Mail,
    patterns: ["hubspot", "mailchimp", "drift", "intercom", "salesforce", "marketing", "crm"]
  }
}

// Helper function to categorize tech by name
function categorizeTech(techName: string): string {
  const normalizedName = techName.toLowerCase()
  
  for (const [category, config] of Object.entries(TECH_CATEGORIES)) {
    if (config.patterns.some(pattern => normalizedName.includes(pattern))) {
      return category
    }
  }
  
  return "Other Technologies"
}

// Helper function to format usage numbers
function formatUsage(usage: number): string {
  if (usage >= 1000000) {
    return `${(usage / 1000000).toFixed(1)}M`
  } else if (usage >= 1000) {
    return `${(usage / 1000).toFixed(1)}K`
  }
  return usage.toString()
}

// Tech Pill Component
function TechPill({ tech, className }: { tech: TechItem; className?: string }) {
  const category = tech.category || categorizeTech(tech.name)
  const categoryConfig = TECH_CATEGORIES[category as keyof typeof TECH_CATEGORIES]
  const IconComponent = categoryConfig?.icon || Globe
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="secondary" 
            className={cn(
              "rounded-xl px-3 py-1 text-sm bg-white/20 backdrop-blur border border-gray-200/50",
              "hover:bg-white/30 transition-all duration-200 cursor-pointer",
              className
            )}
          >
            <IconComponent className="h-4 w-4 mr-2" />
            <span className="truncate max-w-[120px]">{tech.name}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-1">
            <p className="font-medium">{tech.name}</p>
            <p className="text-xs text-gray-500">Category: {category}</p>
            {tech.usage && (
              <p className="text-xs text-gray-500">
                Used by {formatUsage(tech.usage)} companies
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export function TechStackRenderer({ data, className }: TechStackRendererProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  
  // Normalize data to TechItem array
  const techItems: TechItem[] = Array.isArray(data) ? data.map(item => {
    if (typeof item === 'string') {
      return { name: item }
    }
    return item as TechItem
  }) : []
  
  if (!techItems.length) {
    return (
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardContent className="p-6">
          <div className="text-center space-y-2">
            <Settings className="mx-auto h-8 w-8 text-gray-400" />
            <p className="text-sm text-gray-500">No tech stack data found for this company.</p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  // Group tech items by category
  const groupedTechs: Record<string, TechItem[]> = {}
  techItems.forEach(tech => {
    const category = tech.category || categorizeTech(tech.name)
    if (!groupedTechs[category]) {
      groupedTechs[category] = []
    }
    groupedTechs[category].push(tech)
  })
  
  // Calculate insights
  const totalTechs = techItems.length
  const categories = Object.keys(groupedTechs)
  const insights: string[] = []
  
  if (totalTechs > 0) {
    insights.push(`This company uses ${totalTechs} technology${totalTechs > 1 ? 's' : ''}`)
  }
  
  const mobileTechs = groupedTechs["Mobile Optimization"]?.length || 0
  if (mobileTechs > 0) {
    insights.push(`${mobileTechs} mobile-friendly technology${mobileTechs > 1 ? 'ies' : ''}`)
  }
  
  const securityTechs = groupedTechs["Security"]?.length || 0
  if (securityTechs > 0) {
    insights.push(`${securityTechs} security technology${securityTechs > 1 ? 'ies' : ''}`)
  }
  
  const toggleSection = (category: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [category]: !prev[category]
    }))
  }
  
  return (
    <div className={cn("space-y-6", className)}>
      {/* Insights Header */}
      {insights.length > 0 && (
        <Card className="border-0 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg backdrop-blur-md">
          <CardContent className="p-4">
            <p className="text-sm text-blue-700 font-medium">
              {insights.join(', ')}.
            </p>
          </CardContent>
        </Card>
      )}
      
      {/* Tech Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Object.entries(groupedTechs).map(([category, techs]) => {
          const categoryConfig = TECH_CATEGORIES[category as keyof typeof TECH_CATEGORIES]
          const IconComponent = categoryConfig?.icon || Globe
          const isExpanded = expandedSections[category]
          const displayTechs = isExpanded ? techs : techs.slice(0, 5)
          const hasMore = techs.length > 5
          
          return (
            <Card key={category} className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg font-medium">
                  <IconComponent className="h-5 w-5 text-gray-600" />
                  {category}
                  <Badge variant="outline" className="ml-auto text-xs">
                    {techs.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {displayTechs.map((tech, index) => (
                    <TechPill key={`${tech.name}-${index}`} tech={tech} />
                  ))}
                </div>
                
                {hasMore && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSection(category)}
                    className="w-full text-xs text-gray-500 hover:text-gray-700"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp className="h-3 w-3 mr-1" />
                        Show less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-3 w-3 mr-1" />
                        +{techs.length - 5} more
                      </>
                    )}
                  </Button>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
} 