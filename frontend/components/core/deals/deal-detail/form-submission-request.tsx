"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Loader2, Mail, CheckCircle, Send, FileText, ChevronDown, Copy, Check, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"
import { FormAPI } from "@/lib/api/form-api"
import { Deal } from "@/lib/types/deal"
import { Form } from "@/lib/types/form"
import apiClient from "@/lib/api-client"

interface FormSubmissionRequestProps {
  deal: Deal
  onSuccess?: () => void
}

interface ShareDealFormRequest {
  email: string
}

export function FormSubmissionRequest({ deal, onSuccess }: FormSubmissionRequestProps) {
  const [invitedEmail, setInvitedEmail] = useState(deal.invited_email || "")
  const [selectedFormId, setSelectedFormId] = useState("")
  const [availableForms, setAvailableForms] = useState<Form[]>([])
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")
  const [formSelectionOpen, setFormSelectionOpen] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)

  // Load available forms on mount
  useEffect(() => {
    loadAvailableForms()
  }, [])

  const loadAvailableForms = async () => {
    try {
      setLoading(true)
      const forms = await FormAPI.listForms()
      setAvailableForms(forms)
    } catch (err) {
      console.error("Error loading forms:", err)
      setError("Failed to load available forms")
    } finally {
      setLoading(false)
    }
  }

  const handleSendFormRequest = async () => {
    if (!invitedEmail || !selectedFormId) {
      setError("Please provide both email and select a form")
      return
    }

    if (!invitedEmail.includes("@")) {
      setError("Please enter a valid email address")
      return
    }

    try {
      setLoading(true)
      setError("")

      // Call the sharing endpoint
      const response = await apiClient.post(
        `/sharing/deals/${deal.id}/forms/${selectedFormId}/share`,
        {
          email: invitedEmail
        } as ShareDealFormRequest
      )

      console.log("Form request sent successfully:", response.data)
      setSuccess(true)
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (err: any) {
      console.error("Error sending form request:", err)
      setError(err.response?.data?.detail || "Failed to send form request")
    } finally {
      setLoading(false)
    }
  }

  const handleCopyLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000) // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const handleEmailChange = (email: string) => {
    setInvitedEmail(email)
    setError("") // Clear error when user types
  }

  // If invite was already sent, show success state
  if (deal.invite_status === "sent") {
    return (
      <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-green-100 p-2 text-green-600">
              <CheckCircle className="size-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">Form Request Sent</p>
              <p className="text-sm text-green-700">
              Form request sent to {deal.invited_email} — waiting on startup submission.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If form request was just sent successfully
  if (success) {
    // Get magic token URL from deal notes if available  
    const magicTokenUrl = (deal as any).notes?.deal_collection_form?.magic_token

    return (
      <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="rounded-lg bg-green-100 p-2 text-green-600">
              <CheckCircle className="size-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">Form Request Sent</p>
              <p className="text-sm text-green-700">
                Form request sent to {invitedEmail}. Scoring will begin once the submission is received.
              </p>
            </div>
          </div>
          
          {/* Show magic token URL for manual sharing */}
          {magicTokenUrl && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs font-medium text-blue-900 mb-2">
                Form Access Link (backup):
              </p>
              <div className="flex items-center gap-2">
                <a
                  href={magicTokenUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center gap-2 p-2 bg-white border border-blue-200 rounded text-xs hover:border-blue-300 hover:bg-blue-50/50 transition-colors group"
                >
                  <ExternalLink className="size-3 text-blue-600 shrink-0" />
                  <span className="text-blue-800 break-all group-hover:text-blue-900">
                    {magicTokenUrl}
                  </span>
                </a>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleCopyLink(magicTokenUrl)}
                  className={cn(
                    "shrink-0 transition-all duration-200",
                    copySuccess 
                      ? "bg-green-50 border-green-200 text-green-700 hover:bg-green-50" 
                      : "hover:bg-blue-50 hover:border-blue-300"
                  )}
                >
                  <motion.div
                    initial={false}
                    animate={copySuccess ? { scale: [1, 1.2, 1] } : { scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    {copySuccess ? (
                      <Check className="size-3" />
                    ) : (
                      <Copy className="size-3" />
                    )}
                  </motion.div>
                </Button>
              </div>
              <p className="text-xs text-blue-600 mt-2">
                Share this link directly if the founder doesn't receive the email
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
          <FileText className="size-5 text-blue-600" />
          Request Founder Submission
        </CardTitle>
        <p className="text-sm text-gray-600">
          Send a form request to the founder to begin scoring this deal.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Email Input */}
        <div className="space-y-2">
          <Label htmlFor="invited-email" className="text-sm font-medium text-gray-700">
            Founder Email
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              id="invited-email"
              type="email"
              placeholder="<EMAIL>"
              value={invitedEmail}
              onChange={(e) => handleEmailChange(e.target.value)}
              className="pl-10"
              disabled={loading}
            />
          </div>
          {deal.invited_email && (
            <p className="text-xs text-gray-500">
              Previously invited: {deal.invited_email}
            </p>
          )}
        </div>

        {/* Form Selection */}
        <div className="space-y-2">
          <Label htmlFor="form-select" className="text-sm font-medium text-gray-700">
            Select Form
          </Label>
          <Popover open={formSelectionOpen} onOpenChange={setFormSelectionOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={formSelectionOpen}
                className="w-full justify-between"
                disabled={loading}
              >
                {selectedFormId
                  ? availableForms.find((form) => (form.id || form._id) === selectedFormId)?.name || "Select form..."
                  : loading 
                    ? "Loading forms..." 
                    : "Choose a form to send"
                }
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-white border border-gray-200 shadow-lg">
              <div className="max-h-60 overflow-auto">
                {availableForms.map((form) => (
                  <div
                    key={form.id || form._id}
                    className="flex items-center gap-2 p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => {
                      setSelectedFormId(form.id || form._id || "")
                      setFormSelectionOpen(false)
                    }}
                  >
                    <span className="flex-1">{form.name}</span>
                    {form.status === "active" && (
                      <Badge variant="secondary" className="text-xs">
                        Active
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>
          {availableForms.length === 0 && !loading && (
            <p className="text-xs text-gray-500">
              No forms available. Please create a form first.
            </p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="rounded-lg border border-red-200 bg-red-50 p-3"
          >
            <p className="text-sm text-red-600">{error}</p>
          </motion.div>
        )}

        {/* Send Button */}
        <Button
          onClick={handleSendFormRequest}
          disabled={!invitedEmail || !selectedFormId || loading}
          className="w-full gap-2"
        >
          {loading ? (
            <>
              <Loader2 className="size-4 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="size-4" />
              Send Form Request
            </>
          )}
        </Button>

        {/* Help Text */}
        <div className="text-center text-xs text-gray-500">
          <p>
            The founder will receive a magic link email to access and complete the form.
          </p>
          {deal.company_name && (
            <p className="mt-1">
              You'll receive a structured form to share more about {deal.company_name} with investors reviewing TractionX.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 