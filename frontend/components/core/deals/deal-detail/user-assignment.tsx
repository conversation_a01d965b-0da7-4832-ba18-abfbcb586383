"use client"

import React, { useState, useEffect } from 'react'
import { User as UserIcon, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { useToast } from '@/components/ui/use-toast'
import { Deal, User } from '@/lib/types/deal'
import { DealAPI } from '@/lib/api/deal-api'

interface UserAssignmentProps {
  deal: Deal
  onDealUpdate: (updatedDeal: Deal) => void
  className?: string
}

export function UserAssignment({ deal, onDealUpdate, className }: UserAssignmentProps) {
  const [open, setOpen] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [assignedUser, setAssignedUser] = useState<User | null>(null)
  const { toast } = useToast()

  // Load organization users
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const orgUsers = await DealAPI.getOrgUsers()
        setUsers(orgUsers)
        
        // Find assigned user if deal has one
        if (deal.assigned_user_ids && deal.assigned_user_ids.length > 0) {
          const assigned = orgUsers.find(user => 
            deal.assigned_user_ids!.includes(user.id)
          )
          setAssignedUser(assigned || null)
        } else {
          setAssignedUser(null)
        }
      } catch (error) {
        console.error('Failed to load users:', error)
        toast({
          title: "Error",
          description: "Failed to load team members",
          variant: "destructive",
        })
      }
    }

    loadUsers()
  }, [deal.assigned_user_ids, toast])

  const handleAssignUser = async (user: User) => {
    if (loading) return
    
    // Check if already assigned to same user (no-op)
    if (assignedUser?.id === user.id) {
      setOpen(false)
      return
    }

    setLoading(true)
    try {
      const updatedDeal = await DealAPI.assignUserToDeal(deal.id, user.id)
      setAssignedUser(user)
      onDealUpdate(updatedDeal)
      setOpen(false)
      
      toast({
        title: "Assigned",
        description: `Deal assigned to ${user.name}`,
      })
    } catch (error) {
      console.error('Failed to assign user:', error)
      toast({
        title: "Error",
        description: "Failed to assign user to deal",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-blue-500 text-white',
      'bg-green-500 text-white', 
      'bg-purple-500 text-white',
      'bg-orange-500 text-white',
      'bg-pink-500 text-white',
      'bg-indigo-500 text-white',
    ]
    
    const hash = name.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    
    return colors[Math.abs(hash) % colors.length]
  }

  return (
    <div className={cn("flex items-center", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <button className="flex items-center gap-2 w-full rounded-xl border px-3 py-2 text-sm shadow-sm hover:bg-gray-50 transition-colors min-w-[160px]">
            {assignedUser ? (
              <>
                <Avatar className="h-6 w-6">
                  <AvatarFallback className={cn("text-xs font-medium", getAvatarColor(assignedUser.name))}>
                    {getInitials(assignedUser.name)}
                        </AvatarFallback>
                      </Avatar>
                <span className="text-gray-700 font-medium truncate">{assignedUser.name}</span>
                </>
              ) : (
                <>
                <UserIcon className="h-4 w-4 text-gray-400" />
                <span className="text-gray-700 font-medium">Assign to…</span>
                </>
              )}
            <ChevronDown className="ml-auto h-4 w-4 text-gray-400" />
          </button>
        </PopoverTrigger>
        
        <PopoverContent className="w-64 rounded-xl border bg-white shadow-lg p-1" align="start">
          <Command>
            <CommandInput placeholder="Search team members..." />
            <CommandEmpty>No team members found.</CommandEmpty>
            <CommandGroup>
              {users.map((user) => (
                <CommandItem
                  key={user.id}
                  value={user.name}
                  onSelect={() => handleAssignUser(user)}
                  className="flex items-center gap-3 px-3 py-2 hover:bg-gray-100 rounded-md cursor-pointer"
                >
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className={cn("text-xs font-medium", getAvatarColor(user.name))}>
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm text-gray-900 truncate">{user.name}</div>
                    <div className="text-xs text-gray-500 truncate">{user.email}</div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
