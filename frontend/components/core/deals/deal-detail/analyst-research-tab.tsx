// "use client"

// import { Card, CardContent } from "@/components/ui/card"
// import { Skeleton } from "@/components/ui/skeleton"
// import { AlertCircle } from "lucide-react"
// import { DealDetailData } from "@/lib/types/deal-detail"
// import { EmptyPlaceholder } from "@/components/empty-placeholder"
// import { BackgroundPattern } from "@/components/ui/background-pattern"
// import { useAuth } from "@/lib/auth-context"

// interface AnalystResearchTabProps {
//   deal: DealDetailData
// }

// export function AnalystResearchTab({ deal }: AnalystResearchTabProps) {
//   const { isAuthenticated } = useAuth()

//   // Show authentication required message
//   if (!isAuthenticated) {
//     return (
//       <div className="relative min-h-screen">
//         <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
//         <div className="relative flex items-center justify-center min-h-[400px]">
//           <EmptyPlaceholder>
//             <EmptyPlaceholder.Icon name="user" />
//             <EmptyPlaceholder.Title>Authentication Required</EmptyPlaceholder.Title>
//             <EmptyPlaceholder.Description>
//               Please log in to view the analyst research data for this deal.
//             </EmptyPlaceholder.Description>
//           </EmptyPlaceholder>
//         </div>
//       </div>
//     )
//   }

//   // Loading skeleton (optional, can be removed if not needed)
//   // return (
//   //   <div className="relative min-h-screen">
//   //     <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
//   //     <div className="relative space-y-8">
//   //       <div className="flex items-center justify-between">
//   //         <div>
//   //           <Skeleton className="mb-2 h-8 w-48" />
//   //           <Skeleton className="h-4 w-96" />
//   //         </div>
//   //         <div className="flex gap-3">
//   //           <Skeleton className="h-9 w-20" />
//   //           <Skeleton className="h-9 w-24" />
//   //         </div>
//   //       </div>
//   //       <div className="space-y-6">
//   //         {Array.from({ length: 4 }).map((_, i) => (
//   //           <Card key={i} className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
//   //             <CardContent className="p-8 space-y-6">
//   //               <Skeleton className="h-6 w-32" />
//   //               <Skeleton className="h-20 w-full" />
//   //             </CardContent>
//   //           </Card>
//   //         ))}
//   //       </div>
//   //     </div>
//   //   </div>
//   // )

//   // Placeholder for when no research data is available
//   return (
//     <EmptyPlaceholder>
//       <EmptyPlaceholder.Icon name="page" />
//       <EmptyPlaceholder.Title>No Research Data Available</EmptyPlaceholder.Title>
//       <EmptyPlaceholder.Description>
//         Research data for this deal has not been generated yet.
//         This typically happens after the pitch deck has been processed and analyzed.
//       </EmptyPlaceholder.Description>
//     </EmptyPlaceholder>
//   )
// }


"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, FileText, Building2, LogIn } from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { BackgroundPattern } from "@/components/ui/background-pattern"
import { useExecutiveSummary } from "@/lib/hooks/use-executive-summary"
import { useAuth } from "@/lib/auth-context"
import { ExecutiveSummarySection } from "./executive-summary-section"
import { CompanyEnrichmentTabs } from "./company-enrichment-tabs"
import { AIInsightCard } from "./ai-insight-card"
import { CompetitorSection } from "./competitor-section"
import { useAnalystResearch } from '@/lib/hooks/use-analyst-research';

interface AnalystResearchTabProps {
  deal: DealDetailData
}

export function AnalystResearchTab({ deal }: AnalystResearchTabProps) {
  const { isAuthenticated } = useAuth()
  const analystResearch = useAnalystResearch({ dealId: deal.id })
  
  // Check if components are properly imported
  if (!AIInsightCard || !CompetitorSection) {
    console.error('Component import error: AIInsightCard or CompetitorSection is undefined')
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Component Error</h3>
            <p className="mt-1 text-sm text-gray-600">
              There was an error loading the research components. Please refresh the page.
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  // Use hooks for data management
  const {
    data: executiveSummaryData,
    loading: executiveLoading,
    error: executiveError,
    refreshing: executiveRefreshing,
    hasData: hasExecutiveData,
    refresh: handleExecutiveRefresh,
    reload: handleExecutiveReload
  } = useExecutiveSummary({ dealId: deal.id })

  // Combined loading state
  const isLoading = executiveLoading
  const hasAnyData = hasExecutiveData
  
  // Check for authentication errors
  const isAuthError = executiveError?.includes('Authentication required')

  // Show authentication required message
  if (!isAuthenticated || isAuthError) {
    return (
      <div className="relative min-h-screen">
        <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
        <div className="relative flex items-center justify-center min-h-[400px]">
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="user" />
            <EmptyPlaceholder.Title>Authentication Required</EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              Please log in to view the analyst research data for this deal.
            </EmptyPlaceholder.Description>
            {/* Trigger Enrichment Button (disabled if not authenticated) */}
            <button
              className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
              onClick={analystResearch.triggerEnrichment}
              disabled={!isAuthenticated}
            >
              Trigger Company Enrichment
            </button>
          </EmptyPlaceholder>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="relative min-h-screen">
        <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
        <div className="relative space-y-8">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="mb-2 h-8 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex gap-3">
              <Skeleton className="h-9 w-20" />
              <Skeleton className="h-9 w-24" />
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="space-y-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
                <CardContent className="p-8 space-y-6">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Show error if executive summary fails
  if (executiveError) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Research Data Unavailable</h3>
            <p className="mt-1 text-sm text-gray-600">
              {executiveError}
            </p>
          </div>
          <div className="flex gap-2 justify-center">
            <button 
              onClick={handleExecutiveReload}
              className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90"
            >
              Retry Executive Summary
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Show empty state if no data available
  if (!hasAnyData) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="page" />
        <EmptyPlaceholder.Title>No Research Data Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Research data for this deal has not been generated yet. 
          This typically happens after the pitch deck has been processed and analyzed.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  // Add the button to the main UI as well (for authenticated users)
  // Place at the top of the main content
  const triggerButton = (
    <button
      className="mb-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
      onClick={analystResearch.triggerEnrichment}
      disabled={!isAuthenticated}
    >
      Trigger Company Enrichment
    </button>
  );

  return (
    <div className="relative min-h-screen">
      {/* Subtle Background Pattern */}
      <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />

      {isAuthenticated && triggerButton}

      <div className="relative space-y-8">
        {/* Executive Summary Section */}
        {hasExecutiveData && executiveSummaryData && (
          <ExecutiveSummarySection
            executive_summary={executiveSummaryData?.executive_summary || []}
            metadata={executiveSummaryData?.metadata}
            onRefresh={handleExecutiveRefresh}
            refreshing={executiveRefreshing}
          />
        )}

        {/* Company Enrichment Tabs Section */}
        <CompanyEnrichmentTabs dealId={deal.id} />

        {/* Coming Soon Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AIInsightCard />
          <CompetitorSection />
        </div>
      </div>
    </div>
  )
}


