"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ChevronLeft,
  Share2,
  MessageSquare,
  MoreHorizontal,
  Flag,
  StickyNote,
  Star,
  ExternalLink,
  Copy,
  Download,
  Eye,
  Mail,
  Check
} from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Deal } from "@/lib/types/deal"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import { UserAssignment } from "./user-assignment"
import { StatusSelector } from "./status-selector"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tooltip<PERSON>rigger } from "@/components/ui/tooltip"
import { DealAPI } from "@/lib/api/deal-api"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { TourBadge } from "@/components/core/tour/enhanced-tour-provider"

interface DealHeaderProps {
  deal: DealDetailData
  onDealUpdate?: (updatedDeal: Deal) => void
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60 before:bg-blue-500'
    case 'active':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60 before:bg-emerald-500'
    case 'triage':
      return 'bg-amber-50/80 text-amber-700 border-amber-200/60 before:bg-amber-500'
    case 'completed':
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
    case 'flagged':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    case 'hard_pass':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-50/80 text-purple-700 border-purple-200/60'
    case 'seed':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60'
    case 'series a':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60'
    case 'series b':
      return 'bg-orange-50/80 text-orange-700 border-orange-200/60'
    case 'series c':
      return 'bg-red-50/80 text-red-700 border-red-200/60'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const formatTimeAgo = (timestamp: string | number) => {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export function DealHeader({ deal, onDealUpdate }: DealHeaderProps) {
  // Use initial favourited state from deal.favourites (if available)
  const [isFavourited, setIsFavourited] = useState(
    deal.favourites?.length ? true : false
  );
  const [favouriteLoading, setFavouriteLoading] = useState(false);
  const [pocEmail, setPocEmail] = useState<string | null>(null);
  const [pocLoading, setPocLoading] = useState(false);
  const [copiedEmail, setCopiedEmail] = useState(false);
  const router = useRouter();

  // Fetch POC email
  useEffect(() => {
    const fetchPocEmail = async () => {
      setPocLoading(true);
      try {
        const pocData = await DealDetailAPI.getDealPoc(deal.id);
        setPocEmail(pocData.email);
      } catch (error) {
        console.warn('Failed to fetch POC email:', error);
        setPocEmail(null);
      } finally {
        setPocLoading(false);
      }
    };

    fetchPocEmail();
  }, [deal.id]);

  const handleStarToggle = async () => {
    setFavouriteLoading(true);
    try {
      let updatedDeal;
      if (isFavourited) {
        updatedDeal = await DealAPI.unfavouriteDeal(deal.id);
        setIsFavourited(false);
        toast({ title: 'Removed from favourites', description: `${deal.company_name} removed from your favourites.` });
      } else {
        updatedDeal = await DealAPI.favouriteDeal(deal.id);
        setIsFavourited(true);
        toast({ title: 'Added to favourites', description: `${deal.company_name} added to your favourites.` });
      }
      // Optionally update local state or refetch deal
    } catch (e: any) {
      if (e?.response?.status === 401 || e?.response?.status === 403) {
        toast({ title: 'Not signed in', description: 'Sign in to favourite deals.' });
      } else {
        toast({ title: 'Error', description: 'Could not update favourite.' });
      }
    } finally {
      setFavouriteLoading(false);
    }
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href)
    toast({
      title: "Link copied",
      description: "Deal link has been copied to your clipboard.",
    })
  }

  const handleCreateMemo = () => {
    toast({
      title: "AI Memo Generation",
      description: "Coming soon!",
    })
    // TODO: Implement deal memo creation
  }

  const handleViewSubmission = () => {
    router.push(`/deals/${deal.id}/submissions/preview`)
  }

  const handleCopyEmail = async () => {
    if (!pocEmail) return;
    
    try {
      await navigator.clipboard.writeText(pocEmail);
      setCopiedEmail(true);
      toast({
        title: "Email copied",
        description: "POC email has been copied to your clipboard.",
      });
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedEmail(false), 2000);
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Could not copy email to clipboard.",
        variant: "destructive",
      });
    }
  };

  const humanize = (key: string) => {
    return key
      .split('_')
      .map(part =>
        part.length <= 2
          ? part.toUpperCase()                                   // e.g. 'ai' → 'AI'
          : part[0].toUpperCase() + part.slice(1).toLowerCase()  // e.g. 'pre' → 'Pre'
      )
      .join('-');                                              // e.g. ['Pre','Seed'] → 'Pre-Seed'
  }

  const overallScore = deal.score_breakdown?.overall_score || 0
  const foundersCount = deal.founders?.length || 0
  const documentsCount = deal.documents?.length || 0
  const signalsCount = deal.external_signals?.length || 0
  const lastUpdated = formatTimeAgo(deal.updated_at)
  const lastUpdatedExact = new Date(
    typeof deal.updated_at === 'string' ? deal.updated_at : deal.updated_at * 1000
  ).toLocaleString()

  // Helper for tags (sector/tags)
  const tags: string[] = Array.isArray(deal.sector)
    ? deal.sector
    : deal.sector
    ? [deal.sector]
    : []

  // --- REVOLUTIONARY LAYOUT ---
  return (
    <div className="flex w-full flex-col gap-6 px-4 md:px-12" data-tour="deal-header">
      
      {/* GRID BLOCK: Name, tags, status, owner, actions */}
      <div className="grid grid-cols-1 items-start gap-8 md:grid-cols-[1fr_auto]">
        
        {/* LEFT SECTION */}
        <div className="space-y-3">
          {/* Name + Website */}
          <div className="flex items-center gap-3">
            {/* Company Logo */}
            {deal.logo_url ? (
              <div className="size-12 rounded-lg overflow-hidden shadow-sm ring-1 ring-gray-200">
                <img 
                  src={deal.logo_url} 
                  alt={`${deal.company_name || 'Company'} logo`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Hide logo if it fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
            ) : null}
            
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold tracking-tight text-foreground">
                {deal.company_name || 'Unnamed Company'}
              </h1>
              <TourBadge tourType="deal-detail" />
              {deal.company_website && (
                <a
                  href={deal.company_website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-muted-foreground underline transition-colors hover:text-foreground"
                >
                  {deal.company_website.replace(/^https?:\/\//, '')} ↗
                </a>
              )}
            </div>
          </div>

          {/* Tags - Dedicated Line */}
          <div className="flex flex-wrap items-center gap-2">
            {tags.map((tag, i) => (
              <Badge
                key={tag + i}
                variant="outline"
                className="flex h-7 items-center rounded-md border-muted bg-transparent px-2 py-0.5 text-xs font-medium text-muted-foreground"
              >
                {humanize(tag)}
              </Badge>
            ))}
          </div>

          {/* Metadata Row */}
          <div className="text-sm text-muted-foreground">
            {foundersCount > 0 && <span>{foundersCount} founder{foundersCount !== 1 ? 's' : ''}</span>}
            {foundersCount > 0 && (documentsCount > 0 || signalsCount > 0) && <span> • </span>}
            {documentsCount > 0 && <span>{documentsCount} document{documentsCount !== 1 ? 's' : ''}</span>}
            {documentsCount > 0 && signalsCount > 0 && <span> • </span>}
            {signalsCount > 0 && <span>{signalsCount} signal{signalsCount !== 1 ? 's' : ''}</span>}
            {(foundersCount > 0 || documentsCount > 0 || signalsCount > 0) && <span> • </span>}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="cursor-help underline">Updated {lastUpdated}</span>
                </TooltipTrigger>
                <TooltipContent>
                  <span>{lastUpdatedExact}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Actions Grouped - Compact */}
          <div className="flex flex-wrap gap-2 pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleStarToggle}
              className={cn(
                "transition-colors",
                isFavourited ? "border-amber-400 bg-amber-400 text-white hover:bg-amber-500" : "",
                favouriteLoading && "pointer-events-none opacity-60"
              )}
              aria-label={isFavourited ? "Unstar" : "Star"}
              disabled={favouriteLoading}
              data-tour="favorite-deal"
            >
              <Star className={cn("size-4", isFavourited && "fill-current")} />
              <span className="ml-1">Favorite</span>
            </Button>
            <Button
              onClick={handleCreateMemo}
              className="gap-2 relative"
              size="sm"
              variant="secondary"
              aria-label="AI Memo"
              data-tour="ai-memo"
            >
              <StickyNote className="size-4" />
              <span>AI Memo</span>
              <Badge variant="outline" className="absolute -top-1 -right-1 h-5 rounded-full border-gray-300 px-2 text-xs text-gray-400">
                Soon
              </Badge>
            </Button>
            <Button
              onClick={handleViewSubmission}
              variant="secondary"
              className="gap-2"
              size="sm"
              aria-label="View Submission"
              data-tour="view-submission"
            >
              <Eye className="size-4" />
              <span>View Submission</span>
            </Button>
            <Button
              variant="ghost"
              onClick={handleShare}
              className="gap-2 relative"
              size="sm"
              aria-label="Share"
              data-tour="share-deal"
            >
              <Share2 className="size-4" />
              <span>Share</span>
              <Badge variant="outline" className="absolute -top-1 -right-1 h-5 rounded-full border-gray-300 px-2 text-xs text-gray-400">
                Soon
              </Badge>
            </Button>
          </div>
        </div>

        {/* RIGHT SECTION */}
        <div className="flex flex-col items-start gap-4 pt-1 text-sm text-muted-foreground md:items-end">
          <div>
            {/* <div className="text-xs uppercase tracking-wide font-medium text-neutral-600 mb-1">Status</div> */}
            {onDealUpdate ? (
              <div data-tour="deal-status">
                <StatusSelector deal={deal as Deal} onDealUpdate={onDealUpdate} />
              </div>
            ) : (
              <div data-tour="deal-status">
                <Badge className={cn("flex h-7 items-center rounded-md bg-yellow-400 px-2 py-0.5 text-xs font-medium text-yellow-900")}>
                  {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
                </Badge>
              </div>
            )}
          </div>
          <div>
            {/* <div className="text-xs uppercase tracking-wide font-medium text-neutral-600 mb-1">Owner</div> */}
            {onDealUpdate && (
              <div data-tour="deal-owner">
                <UserAssignment deal={deal as Deal} onDealUpdate={onDealUpdate} />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* DESCRIPTION — STRETCHED FULL WIDTH */}
      <p className="w-full max-w-none border-t pt-6 text-base leading-relaxed text-neutral-800">
        {deal.short_description || <span className="italic text-muted-foreground">No description available</span>}
      </p>

      {/* POC EMAIL SECTION */}
      {pocEmail && (
        <div className="border-t pt-4">
          <div className="flex items-center gap-3">
            <Mail className="size-4 text-muted-foreground" />
            <div className="flex-1">
              <div className="text-sm font-medium text-foreground">Point of Contact</div>
              <div className="text-sm text-muted-foreground">{pocEmail}</div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyEmail}
              className="h-8 w-8 p-0"
              aria-label="Copy email"
            >
              {copiedEmail ? (
                <Check className="size-4 text-green-600" />
              ) : (
                <Copy className="size-4 text-muted-foreground" />
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
