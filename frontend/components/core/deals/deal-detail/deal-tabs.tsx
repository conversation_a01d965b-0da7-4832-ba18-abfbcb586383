"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { AnalystResearchTab } from "./analyst-research-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import { NotesTab } from "./notes-tab"
import {
  Clock,
  Target,
  Users,
  Radio,
  Search,
  FileText,
  BarChart3,
  Star,
  MessageSquare
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
  onDealUpdate?: (deal: DealDetailData) => void
}

const tabs = [
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'research',
    label: 'Analyst Research',
    icon: Search,
    component: AnalystResearchTab
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  },
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  },
  {
    id: 'notes',
    label: 'Notes',
    icon: MessageSquare,
    component: NotesTab
  }
]

export function DealTabs({ deal, activeTab, onTabChange, onDealUpdate }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'research':
        // Count available research components
        const research = deal.analyst_research
        if (!research) return 0
        let count = 0
        if (research.competitors?.competitors?.length) count++
        if (research.market?.market_trends?.length) count++
        if (research.news?.news_signals?.length) count++
        if (research.summary?.executive_summary) count++
        return count
      case 'documents':
        return deal.documents?.length || 0
      case 'notes':
        // Notes count will be handled by the NotesTab component
        return null
      default:
        return null
    }
  }

  return (
    <div className="flex size-full flex-col" data-tour="deal-tabs">
      <Tabs value={activeTab} onValueChange={onTabChange} className="flex size-full flex-col">
        {/* Full-width tab navigation with perfect mobile scrolling */}
        <div className="w-full border-b border-gray-200/60 bg-white">
          <div className="scrollbar-hide w-full overflow-x-auto">
            <TabsList className="flex h-auto w-full min-w-max gap-1 rounded-none border-0 bg-transparent p-2">
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const count = getTabCount(tab.id)

                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    disabled={tab.disabled}
                    data-tour={`${tab.id}-tab`}
                    className={cn(
                      // Premium mobile-first tab design
                      "flex min-w-[100px] shrink-0 items-center justify-center gap-2 px-4 py-3",
                      "rounded-xl transition-all duration-300 ease-out",
                      "text-gray-600 hover:bg-gray-100/80 hover:text-gray-900",
                      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
                      "disabled:cursor-not-allowed disabled:opacity-50",
                      // Active state with glassmorphism
                      "data-[state=active]:bg-white data-[state=active]:text-blue-600",
                      "data-[state=active]:border data-[state=active]:border-blue-200/50 data-[state=active]:shadow-sm",
                      // Mobile optimizations - proper touch targets
                      "min-h-touch-lg touch-manipulation",
                      // Ensure better mobile scrolling
                      "scroll-ml-4 first:scroll-ml-0"
                    )}
                  >
                    <Icon className="size-5" />
                    <span className="whitespace-nowrap text-xs font-medium">{tab.label}</span>

                    {count !== null && count > 0 && (
                      <Badge
                        variant="secondary"
                        className="h-5 min-w-[20px] rounded-full bg-gray-100 px-1.5 text-xs text-gray-700"
                      >
                        {count}
                      </Badge>
                    )}

                    {tab.disabled && (
                      <Badge
                        variant="outline"
                        className="h-5 rounded-full border-gray-300 px-2 text-xs text-gray-400"
                      >
                        Soon
                      </Badge>
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>
        </div>

        {/* Full-screen tab content */}
        <div className="w-full flex-1">
          {tabs.map((tab) => {
            const Component = tab.component

            return (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="mt-0 size-full focus-visible:outline-none data-[state=active]:flex data-[state=active]:flex-col"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full flex-1"
                >
                  <div className="size-full p-2">
                    {(() => {
                      switch (tab.id) {
                        case 'founders':
                          return <FoundersTab deal={deal} onDealUpdate={onDealUpdate} />
                        case 'score':
                          return <ScoreTab deal={deal} />
                        case 'timeline':
                          return <TimelineTab deal={deal} />
                        case 'documents':
                          return <DocumentsTab deal={deal} />
                        case 'research':
                          return <AnalystResearchTab deal={deal} />
                        case 'benchmarks':
                          return <BenchmarksTab deal={deal} />
                        case 'notes':
                          return <NotesTab deal={deal} onDealUpdate={onDealUpdate} />
                        default:
                          return <div>Unknown tab</div>
                      }
                    })()}
                  </div>
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </div>
  )
}
