"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, RefreshCw, Building2, TrendingUp, Briefcase, Target } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useCallback } from "react"
import { useCompanyEnrichedV2 } from "@/lib/hooks/use-company-enriched-v2"
import { KeyValueRenderer } from "./key-value-renderer"
import { cn } from "@/lib/utils"

interface CompanyEnrichmentTabsProps {
  dealId: string
  className?: string
}

// Source display names and icons
const SOURCE_CONFIG: Record<string, { 
  name: string; 
  icon: any; 
  logo?: string;
}> = {
  pitchbook: { name: "PitchB<PERSON>", icon: TrendingUp, logo: "/pitchbook.svg" },
  crunchbase: { name: "Crunchbase", icon: Building2, logo: "/crunchbase.png" },
  linkedin: { name: "LinkedIn", icon: Briefcase, logo: "/linkedin.png" },
  apollo: { name: "Apollo", icon: Target, logo: "/apollo.svg" }
}

// Fields to highlight in the base company tab
const HIGHLIGHTED_FIELDS = [
  'valuation',
  'funding_total', 
  'stage',
  'geo_tags',
  'sector',
  'business_model',
  'employee_count',
  'revenue',
  'founded_year'
]

// Helper function to check if data has meaningful content
function hasMeaningfulData(data: any): boolean {
  if (!data || typeof data !== 'object') return false
  
  const entries = Object.entries(data)
  return entries.some(([key, value]) => {
    if (value === null || value === undefined || value === '') return false
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === 'object') {
      // For complex objects, check if they have meaningful nested data
      return hasMeaningfulData(value)
    }
    return true
  })
}

export function CompanyEnrichmentTabs({ dealId, className }: CompanyEnrichmentTabsProps) {
  const [activeTab, setActiveTab] = useState("company")
  
  const {
    data,
    loading,
    error,
    refreshing,
    hasData,
    availableSources,
    sourceData,
    sourceLoading,
    sourceErrors,
    refresh,
    reload,
    fetchSourceData
  } = useCompanyEnrichedV2({ dealId })

  // Handle tab change and lazy load source data
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    
    // If it's a source tab and we haven't loaded the data yet, fetch it
    if (value !== "company" && !sourceData[value] && !sourceLoading[value]) {
      fetchSourceData(value)
    }
  }

  // Handle manual source data loading
  const handleSourceTabClick = useCallback(async (source: string) => {
    if (sourceLoading[source] || sourceData[source]) return
    await fetchSourceData(source)
  }, [sourceLoading, sourceData, fetchSourceData])

  // Show loading state
  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-9 w-24" />
        </div>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className={cn("flex min-h-[400px] items-center justify-center", className)}>
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Company Data Unavailable</h3>
            <p className="mt-1 text-sm text-gray-600">{error}</p>
          </div>
          <Button onClick={reload} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  // Show empty state if no data
  if (!hasData) {
    return (
      <div className={cn("flex min-h-[400px] items-center justify-center", className)}>
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-gray-400" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">No Company Data</h3>
            <p className="mt-1 text-sm text-gray-600">
              No company enrichment data is available for this deal.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Get company data
  const companyData = data?.company || data || {}

  // Build tabs configuration - only include tabs with meaningful data
  const tabs = [
    {
      value: "company",
      label: "Company Overview",
      icon: Building2,
      hasData: hasMeaningfulData(companyData)
    }
  ]

  // Add all source tabs - they will fetch data when clicked
  availableSources.forEach(source => {
    tabs.push({
      value: source,
      label: SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]?.name || source,
      icon: SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]?.icon || Building2,
      hasData: false // Will be updated when data is fetched
    })
  })

  // If no meaningful data in any tab, show empty state
  if (tabs.length === 1 && !tabs[0].hasData) {
    return (
      <div className={cn("flex min-h-[400px] items-center justify-center", className)}>
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-gray-400" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">No Enrichment Data</h3>
            <p className="mt-1 text-sm text-gray-600">
              No third-party enrichment sources available for this company.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Company Insights</h2>
          <p className="text-sm text-gray-600">
            Enriched company data from multiple sources
          </p>
        </div>
        <Button 
          onClick={refresh} 
          disabled={refreshing}
          variant="outline"
          size="sm"
          className="bg-white/80 backdrop-blur-md border-gray-200/50 hover:bg-white/90"
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Premium Glassmorphic Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-auto-fit bg-white/80 backdrop-blur-md border border-gray-200/50 shadow-lg rounded-xl p-1 gap-1">
          {tabs.map((tab) => {
            const sourceConfig = SOURCE_CONFIG[tab.value as keyof typeof SOURCE_CONFIG]
            const hasLogo = sourceConfig?.logo
            
            return (
              <TabsTrigger 
                key={tab.value} 
                value={tab.value}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200",
                  "data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-md",
                  "data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-white/50",
                  "font-medium text-sm"
                )}
              >
                {hasLogo ? (
                  <img 
                    src={sourceConfig.logo} 
                    alt={`${sourceConfig.name} logo`}
                    className={cn(
                      "object-contain",
                      tab.value === "pitchbook" ? "h-5 w-5" : "h-4 w-4"
                    )}
                  />
                ) : (
                  <tab.icon className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            )
          })}
        </TabsList>

        {/* Company Overview Tab */}
        <TabsContent value="company" className="mt-6">
          <KeyValueRenderer
            data={companyData}
            highlightFields={HIGHLIGHTED_FIELDS}
            className="w-full"
          />
        </TabsContent>

        {/* Source-specific tabs */}
        {availableSources.map((source) => {
          const sourceInfo = sourceData[source]
          const hasSourceData = sourceInfo && hasMeaningfulData(sourceInfo.data)
          
          return (
            <TabsContent key={source} value={source} className="mt-6">
              {sourceLoading[source] ? (
                <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardContent>
                </Card>
              ) : sourceErrors[source] ? (
                <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
                  <CardContent className="p-6">
                    <div className="text-center space-y-4">
                      <div className="flex justify-center">
                        <div className="flex size-16 items-center justify-center rounded-full bg-gray-100">
                          {(() => {
                            const sourceConfig = SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]
                            const hasLogo = sourceConfig?.logo
                            
                            if (hasLogo) {
                              return (
                                <img 
                                  src={sourceConfig.logo} 
                                  alt={`${sourceConfig.name} logo`}
                                  className={cn(
                                    "object-contain opacity-50",
                                    source === "pitchbook" ? "size-10" : "size-8"
                                  )}
                                />
                              )
                            } else {
                              const IconComponent = sourceConfig?.icon || Building2
                              return <IconComponent className="size-8 text-gray-400" />
                            }
                          })()}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold text-gray-700">{SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]?.name || source} Data</h3>
                        <p className="text-sm text-gray-500 max-w-md mx-auto">
                          Not available at the moment. Please try again later.
                        </p>
                        <Button 
                          onClick={() => handleSourceTabClick(source)}
                          disabled={sourceLoading[source]}
                          className="mt-4"
                          variant="outline"
                        >
                          {sourceLoading[source] ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Try Again
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : sourceInfo ? (
                <KeyValueRenderer
                  data={sourceInfo.data || {}}
                  className="w-full"
                />
              ) : (
                <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
                  <CardContent className="p-6">
                    <div className="text-center space-y-4">
                      <div className="flex justify-center">
                        <div className="flex size-16 items-center justify-center rounded-full bg-gray-100">
                          {(() => {
                            const sourceConfig = SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]
                            const hasLogo = sourceConfig?.logo
                            
                            if (hasLogo) {
                              return (
                                <img 
                                  src={sourceConfig.logo} 
                                  alt={`${sourceConfig.name} logo`}
                                  className={cn(
                                    "object-contain",
                                    source === "pitchbook" ? "size-10" : "size-8"
                                  )}
                                />
                              )
                            } else {
                              const IconComponent = sourceConfig?.icon || Building2
                              return <IconComponent className="size-8 text-gray-400" />
                            }
                          })()}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold text-gray-700">{SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]?.name || source} Data</h3>
                        <p className="text-sm text-gray-500 max-w-md mx-auto">
                          {source === 'pitchbook' && "Click to load comprehensive financial data, funding rounds, and company metrics from PitchBook."}
                          {source === 'crunchbase' && "Click to load detailed company information, funding history, and market insights from Crunchbase."}
                          {source === 'linkedin' && "Click to load company updates, employee information, and professional network data from LinkedIn."}
                          {source === 'apollo' && "Click to load B2B contact data, company information, and sales intelligence from Apollo."}
                        </p>
                        <Button 
                          onClick={() => handleSourceTabClick(source)}
                          disabled={sourceLoading[source]}
                          className="mt-4"
                          variant="outline"
                        >
                          {sourceLoading[source] ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Load {SOURCE_CONFIG[source as keyof typeof SOURCE_CONFIG]?.name || source} Data
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          )
        })}
      </Tabs>
    </div>
  )
} 