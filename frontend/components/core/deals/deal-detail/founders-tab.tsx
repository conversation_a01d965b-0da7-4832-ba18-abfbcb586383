"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Users, 
  UserPlus, 
  TrendingUp, 
  AlertCircle, 
  ExternalLink,
  Copy,
  Download,
  Star,
  Building2,
  GraduationCap,
  Briefcase,
  Award,
  Lightbulb
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { FounderAPI, EnrichedFounder, FounderExperience, FounderEducation as FounderEducationType, FounderSkill } from "@/lib/api/founder-api"
import { FounderRadarChart } from "./founder-radar-chart"
import { FounderInsights } from "./founder-insights"
import { FounderTimeline } from "./founder-timeline"
import { FounderSkills } from "./founder-skills"
import { FounderEducation } from "./founder-education"
import FoundersTabSimple from "./founders-tab-simple"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

// Adapter functions to convert API types to component types
const adaptExperience = (exp: FounderExperience) => ({
  id: exp.id,
  title: exp.title,
  company: exp.companyName,
  location: exp.location,
  startDate: exp.startDate,
  endDate: exp.endDate,
  description: exp.industry,
  type: 'startup' as const, // Default to startup, could be enhanced with logic
  companySize: exp.companySize,
  industry: exp.industry
})

const adaptSkill = (skill: FounderSkill) => ({
  name: skill.skill,
  level: 'intermediate' as const, // Default level, could be enhanced with logic
  years: undefined,
  category: 'tech' // Default category, could be enhanced with logic
})

const adaptEducation = (edu: FounderEducationType) => ({
  id: edu.id,
  degree: edu.degrees.join(', '),
  major: edu.majors.join(', '),
  institution: edu.schoolName,
  location: edu.location,
  startDate: edu.startDate || '',
  endDate: edu.endDate,
  gpa: undefined,
  honors: [],
  type: 'bachelor' as const // Default type, could be enhanced with logic
})

// Utility function to capitalize names properly
const capitalizeName = (name: string) => {
  if (!name) return '';
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

// Utility to parse JSON fields if needed
function parseIfString<T>(value: T | string | null | undefined): T | undefined {
  if (!value) return undefined;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch {
      return undefined;
    }
  }
  return value as T;
}

export function FoundersTab({ deal, onDealUpdate }: FoundersTabProps) {
  const [enrichedFounders, setEnrichedFounders] = useState<EnrichedFounder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedFounder, setSelectedFounder] = useState<EnrichedFounder | null>(null)
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false)
  const { toast } = useToast()

  // Fetch enriched founder data
  useEffect(() => {
    const fetchFounders = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log('Fetching enriched founders for deal:', deal.id)
        const response = await FounderAPI.getDealFounders(deal.id)
        setEnrichedFounders(response.founders)
        
        // Set first founder as selected by default
        if (response.founders.length > 0) {
          setSelectedFounder(response.founders[0])
        }
      } catch (err: any) {
        console.error('Error fetching founders:', err)
        setError('Failed to load founder data. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (deal.id) {
      fetchFounders()
    }
  }, [deal.id])

  // Handle founder selection
  const handleFounderSelect = (founder: EnrichedFounder) => {
    setSelectedFounder(founder)
  }

  // Generate team summary for all founders
  const generateTeamSummary = async () => {
    try {
      setIsGeneratingSummary(true);
      
      // Safely extract domain from company_website
      let domain = "unknown";
      if (deal.company_website) {
        try {
          // Add protocol if missing
          const websiteUrl = deal.company_website.startsWith('http') 
            ? deal.company_website 
            : `https://${deal.company_website}`;
          domain = new URL(websiteUrl).hostname;
        } catch (urlError) {
          console.warn('Invalid company website URL:', deal.company_website);
          // Try to extract domain manually if URL parsing fails
          const website = deal.company_website.replace(/^https?:\/\//, '');
          domain = website.split('/')[0] || "unknown";
        }
      }
      
      // Ensure domain is never undefined
      if (!domain || domain === "unknown") {
        domain = deal.company_name?.toLowerCase().replace(/\s+/g, '') || "unknown";
      }
      
      console.log('Deal data:', {
        company_website: deal.company_website,
        company_name: deal.company_name,
        org_id: deal.org_id
      });
      console.log('Extracted domain:', domain);
      console.log('Founders to enrich:', deal.founders);
      
      // Trigger enrichment for ALL founders from deal.founders (basic data)
      const enrichmentPromises = (deal.founders || []).map(async (founder, index) => {
        const payload = {
          company_id: domain,
          org_id: deal.org_id || localStorage.getItem('orgId') || "",
          company_name: deal.company_name || "Unknown Company",
          domain: domain,
          form_data: {
            founder_name: founder.name || "",
            founder_linkedin: founder.linkedin || ""
          },
          pipeline_types: ["founder"],
          priority: "high" as const
        };

        console.log(`Triggering enrichment for founder ${index + 1}:`, payload);
        return await FounderAPI.triggerEnrichment(payload);
      });
      
      // Wait for all enrichment requests to complete
      const responses = await Promise.all(enrichmentPromises);
      
      // Check if all requests were successful
      const allSuccessful = responses.every(response => response.success);
      
      if (allSuccessful) {
        toast({
          title: "Founder Enrichment Started",
          description: `AI analysis is now processing for ${deal.founders?.length || 0} founder${(deal.founders?.length || 0) !== 1 ? 's' : ''}. This may take a few minutes.`,
        });
        
        // Optionally refresh the page or refetch data after a delay
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        const failedCount = responses.filter(response => !response.success).length;
        throw new Error(`${failedCount} out of ${deal.founders?.length || 0} enrichment requests failed`);
      }
    } catch (error: any) {
      console.error('Error triggering founder enrichment:', error);
      toast({
        title: "Enrichment Failed",
        description: error.message || "Failed to start founder enrichment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-8">
        {/* Loading header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="flex items-center gap-3">
            <Skeleton className="h-12 w-12 rounded-2xl" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </motion.div>

        {/* Loading content */}
        <div className="grid gap-6 lg:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1 }}
            >
              <Skeleton className="h-64 w-full rounded-2xl" />
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="space-y-6"
      >
        <Alert variant="destructive" className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        
        <Card className="border-slate-200 bg-white/90 backdrop-blur-sm">
          <CardContent className="p-8 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-slate-100">
              <Users className="h-8 w-8 text-slate-400" />
            </div>
            <h3 className="mb-2 text-lg font-semibold text-slate-900">
              No Founder Data Available
            </h3>
            <p className="mb-6 text-sm text-slate-600">
              Add founder information to see team analysis and insights.
            </p>
            <Button className="rounded-xl">
              <UserPlus className="mr-2 h-4 w-4" />
              Add Founders
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // No founders state
  if (enrichedFounders.length === 0) {
    // Fallback to basic/simple version if no enriched founders
    return <FoundersTabSimple deal={deal} onDealUpdate={onDealUpdate} />
  }

  return (
    <div className="space-y-8">
      {/* Team Members Selector & Actions - always at the top */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex items-center gap-2">
            <h2 className="text-sm font-medium text-gray-900">Team Members</h2>
            <div className="flex flex-wrap gap-2">
              {enrichedFounders.map((founder, index) => (
                <Button
                  key={founder.founder.id}
                  variant={selectedFounder?.founder.id === founder.founder.id ? "default" : "outline"}
                  size="sm"
                  className={cn(
                    "rounded-full px-4 py-2 text-sm font-medium transition-all duration-200",
                    selectedFounder?.founder.id === founder.founder.id
                      ? "bg-slate-900 text-white"
                      : "border-slate-200 bg-white text-slate-700 hover:bg-slate-50"
                  )}
                  onClick={() => handleFounderSelect(founder)}
                >
                  <Users className="mr-1.5 h-3 w-3" />
                  {capitalizeName(founder.founder.fullName)}
                </Button>
              ))}
            </div>
          </div>
          <Button
            onClick={generateTeamSummary}
            disabled={isGeneratingSummary}
            className="gap-1.5 rounded-full px-4 py-2 text-sm font-medium bg-slate-900 text-white hover:bg-slate-800"
            size="sm"
          >
            <Lightbulb className="size-3" />
            {isGeneratingSummary ? 'Generating...' : 'Generate Summary'}
          </Button>
        </div>
      </motion.div>

      {/* Premium Founder Identity Card & Enrichment Data - below team selector */}
      {selectedFounder && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200 mt-4">
            <CardContent className="p-6">
              <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
                {/* Founder Avatar & Basic Info */}
                <div className="flex flex-col items-center gap-4 lg:items-start">
                  <div className="relative">
                    <div className="h-20 w-20 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200">
                      <div className="flex h-full w-full items-center justify-center">
                        <Users className="h-8 w-8 text-slate-400" />
                      </div>
                    </div>
                    {/* AI Score Badge */}
                    {selectedFounder?.signals?.score && (
                      <div className="absolute -bottom-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-blue-600 text-xs font-bold text-white shadow-sm">
                        {Math.round(selectedFounder.signals.score)}
                      </div>
                    )}
                  </div>
                  <div className="text-center lg:text-left">
                    <h2 className="text-xl font-bold text-slate-900">
                      {capitalizeName(selectedFounder?.founder.fullName || "Unknown Founder")}
                    </h2>
                    <p className="text-sm text-slate-600">
                      {capitalizeName(selectedFounder?.founder.currentJobTitle || "Founder")}
                      {selectedFounder?.founder.currentJobCompany && (
                        <span> at {capitalizeName(selectedFounder.founder.currentJobCompany)}</span>
                      )}
                    </p>
                    {/* Serial Founder Badge */}
                    {selectedFounder?.founder.serialFounder && (
                      <Badge variant="secondary" className="mt-2 rounded-full bg-blue-50 text-blue-700">
                        <Star className="mr-1 h-3 w-3" />
                        Serial Founder
                      </Badge>
                    )}
                  </div>
                </div>
                {/* Founder Stats & Links */}
                <div className="flex flex-1 flex-col gap-4 lg:ml-8">
                  <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
                    <div className="rounded-xl border border-slate-200 bg-slate-50/50 p-3 text-center">
                      <div className="text-lg font-bold text-slate-900">
                        {selectedFounder?.experiences?.length || 0}
                      </div>
                      <div className="text-xs text-slate-600">Experience</div>
                    </div>
                    <div className="rounded-xl border border-slate-200 bg-slate-50/50 p-3 text-center">
                      <div className="text-lg font-bold text-slate-900">
                        {selectedFounder?.skills?.length || 0}
                      </div>
                      <div className="text-xs text-slate-600">Skills</div>
                    </div>
                    <div className="rounded-xl border border-slate-200 bg-slate-50/50 p-3 text-center">
                      <div className="text-lg font-bold text-slate-900">
                        {selectedFounder?.education?.length || 0}
                      </div>
                      <div className="text-xs text-slate-600">Education</div>
                    </div>
                    <div className="rounded-xl border border-slate-200 bg-slate-50/50 p-3 text-center">
                      <div className="text-lg font-bold text-slate-900">
                        {selectedFounder?.signals?.score ? Math.round(selectedFounder.signals.score) : "N/A"}
                      </div>
                      <div className="text-xs text-slate-600">AI Score</div>
                    </div>
                  </div>
                  {/* Social Links */}
                  <div className="flex flex-wrap gap-2">
                    {selectedFounder?.founder.linkedinUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-xl border-slate-200 bg-white text-slate-700 hover:bg-slate-50"
                        onClick={() => window.open(selectedFounder.founder.linkedinUrl, '_blank')}
                      >
                        <ExternalLink className="mr-2 h-3 w-3" />
                        LinkedIn
                      </Button>
                    )}
                    {selectedFounder?.founder.twitterUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-xl border-slate-200 bg-white text-slate-700 hover:bg-slate-50"
                        onClick={() => window.open(selectedFounder.founder.twitterUrl, '_blank')}
                      >
                        <ExternalLink className="mr-2 h-3 w-3" />
                        Twitter
                      </Button>
                    )}
                    {selectedFounder?.founder.githubUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-xl border-slate-200 bg-white text-slate-700 hover:bg-slate-50"
                        onClick={() => window.open(selectedFounder.founder.githubUrl, '_blank')}
                      >
                        <ExternalLink className="mr-2 h-3 w-3" />
                        GitHub
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Premium Analysis Grid */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Skill Profile */}
        {(() => {
          if (!selectedFounder || !selectedFounder.signals) return null;
          const parsedSkillProfile = parseIfString(selectedFounder.signals.skillProfile);
          if (!parsedSkillProfile) return null;
          const parsedSignals = {
            ...selectedFounder.signals,
            skillProfile: parsedSkillProfile,
            strengths: parseIfString(selectedFounder.signals.strengths) || { items: [] },
            risks: parseIfString(selectedFounder.signals.risks) || { items: [] },
          };
          return (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <FounderRadarChart
                skillProfile={parsedSignals.skillProfile}
                founderName={capitalizeName(selectedFounder.founder.fullName)}
              />
            </motion.div>
          );
        })()}

        {/* AI Insights */}
        {(() => {
          if (!selectedFounder || !selectedFounder.signals) return null;
          const parsedSkillProfile = parseIfString(selectedFounder.signals.skillProfile);
          if (!parsedSkillProfile) return null;
          const parsedSignals = {
            ...selectedFounder.signals,
            skillProfile: parsedSkillProfile,
            strengths: parseIfString(selectedFounder.signals.strengths) || { items: [] },
            risks: parseIfString(selectedFounder.signals.risks) || { items: [] },
          };
          return (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <FounderInsights
                signals={parsedSignals}
                founderName={capitalizeName(selectedFounder.founder.fullName)}
              />
            </motion.div>
          );
        })()}

        {/* Experience Timeline */}
        {selectedFounder?.experiences && selectedFounder.experiences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <FounderTimeline experiences={selectedFounder.experiences.map(exp => {
              if (typeof exp === 'string') {
                return { id: '', title: exp, company: '', location: '', startDate: '', endDate: '', description: '', type: 'startup', companySize: '', industry: '' }
              } else if (exp && typeof exp === 'object') {
                return {
                  id: exp.id || '',
                  title: exp.title || '',
                  company: exp.companyName || '',
                  location: exp.location || '',
                  startDate: exp.startDate || '',
                  endDate: exp.endDate || '',
                  description: exp.industry || '',
                  type: 'startup',
                  companySize: exp.companySize || '',
                  industry: exp.industry || ''
                }
              } else {
                return { id: '', title: '', company: '', location: '', startDate: '', endDate: '', description: '', type: 'startup', companySize: '', industry: '' }
              }
            })} />
          </motion.div>
        )}

        {/* Skills */}
        {selectedFounder?.skills && selectedFounder.skills.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <FounderSkills skills={selectedFounder.skills.map(adaptSkill)} />
          </motion.div>
        )}

        {/* Education */}
        {selectedFounder?.education && selectedFounder.education.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <FounderEducation education={selectedFounder.education.map(edu => {
              if (typeof edu === 'string') {
                return { id: '', degree: edu, major: '', institution: '', location: '', startDate: '', endDate: '', type: 'bachelor' }
              } else if (edu && typeof edu === 'object') {
                return {
                  id: edu.id || '',
                  degree: (edu.degrees ? edu.degrees.join(', ') : ''),
                  major: (edu.majors ? edu.majors.join(', ') : ''),
                  institution: edu.schoolName || '',
                  location: edu.location || '',
                  startDate: edu.startDate || '',
                  endDate: edu.endDate || '',
                  type: 'bachelor'
                }
              } else {
                return { id: '', degree: '', major: '', institution: '', location: '', startDate: '', endDate: '', type: 'bachelor' }
              }
            })} />
          </motion.div>
        )}
      </div>
    </div>
  )
}
