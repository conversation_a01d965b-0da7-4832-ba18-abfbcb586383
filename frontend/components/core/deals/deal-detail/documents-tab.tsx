"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  Trash2,
  File,
  Image,
  FileSpreadsheet,
  Monitor,
  Calendar,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, DealDocument } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { useToast } from "@/components/ui/use-toast"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { PitchDeckUpload } from "./pitch-deck-upload"

interface DocumentsTabProps {
  deal: DealDetailData
}

const getFileIcon = (type: DealDocument['document_type']) => {
  switch (type) {
    case 'pdf':
      return FileText
    case 'doc':
    case 'docx':
      return FileText
    case 'xls':
    case 'xlsx':
      return FileSpreadsheet
    case 'ppt':
    case 'pptx':
      return Monitor
    case 'image':
      return Image
    default:
      return File
  }
}

const getFileColor = (type: DealDocument['document_type']) => {
  switch (type) {
    case 'pdf':
      return 'bg-red-50 text-red-600 border-red-100'
    case 'doc':
    case 'docx':
      return 'bg-blue-50 text-blue-600 border-blue-100'
    case 'xls':
    case 'xlsx':
      return 'bg-green-50 text-green-600 border-green-100'
    case 'ppt':
    case 'pptx':
      return 'bg-orange-50 text-orange-600 border-orange-100'
    case 'image':
      return 'bg-purple-50 text-purple-600 border-purple-100'
    default:
      return 'bg-gray-50 text-gray-600 border-gray-100'
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const cleanFilename = (filename: string) => {
  // Remove duplicate words and clean up common patterns
  let cleaned = filename
  
  // Remove duplicate "deck" from pitch_deck_deck.pdf → pitch_deck.pdf
  cleaned = cleaned.replace(/pitch_deck_deck\./i, 'pitch_deck.')
  
  // Remove duplicate "pitch" from pitch_pitch.pdf → pitch.pdf
  cleaned = cleaned.replace(/pitch_pitch\./i, 'pitch.')
  
  // Remove "auto-generated" or similar system suffixes
  cleaned = cleaned.replace(/_auto_generated/i, '')
  cleaned = cleaned.replace(/_system/i, '')
  
  // Clean up multiple underscores
  cleaned = cleaned.replace(/_+/g, '_')
  
  // Remove trailing underscores before file extension
  cleaned = cleaned.replace(/_+\./g, '.')
  
  // Capitalize first letter and make it more readable
  if (cleaned.startsWith('pitch_deck')) {
    cleaned = cleaned.replace('pitch_deck', 'Pitch Deck')
  } else if (cleaned.startsWith('pitch')) {
    cleaned = cleaned.replace('pitch', 'Pitch')
  }
  
  // Replace underscores with spaces for better readability (except before file extension)
  const parts = cleaned.split('.')
  if (parts.length > 1) {
    const namepart = parts.slice(0, -1).join('.').replace(/_/g, ' ')
    const extension = parts[parts.length - 1]
    cleaned = `${namepart}.${extension}`
  } else {
    cleaned = cleaned.replace(/_/g, ' ')
  }
  
  return cleaned
}

export function DocumentsTab({ deal }: DocumentsTabProps) {
  const [documents, setDocuments] = useState(deal.documents || [])
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Fetch fresh documents when component mounts
  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const freshDocuments = await DealDetailAPI.getDocuments(deal.id)
      setDocuments(freshDocuments)
      console.log('Documents fetched:', freshDocuments)
    } catch (error) {
      console.error('Error fetching documents:', error)
      toast({
        title: "Failed to load documents",
        description: "There was an error loading the documents. Please refresh the page.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Load documents on mount
  useEffect(() => {
    fetchDocuments()
  }, [])

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Basic validation
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      toast({
        title: "File too large",
        description: "Please select a file smaller than 50MB.",
        variant: "destructive"
      })
      return
    }

    // Check file type
    const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.csv', '.jpg', '.jpeg', '.png', '.gif', '.bmp']
    const fileExt = '.' + file.name.split('.').pop()?.toLowerCase()
    
    if (!allowedTypes.includes(fileExt)) {
      toast({
        title: "File type not supported",
        description: `Please select a supported file type: ${allowedTypes.join(', ')}`,
        variant: "destructive"
      })
      return
    }

    try {
      setUploading(true)
      const uploadedDoc = await DealDetailAPI.uploadDocument(deal.id, file)
      
      // Refresh documents list to get the latest data
      await fetchDocuments()
      
      toast({
        title: "File uploaded",
        description: `${cleanFilename(file.name)} has been uploaded successfully.`
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handlePreview = (document: DealDocument) => {
    if (document.preview_url) {
      window.open(document.preview_url, '_blank', 'noopener,noreferrer')
    } else {
      // Fallback to download
      handleDownload(document)
    }
  }

  const handleDownload = async (document: DealDocument) => {
    try {
      const downloadUrl = await DealDetailAPI.getDocumentDownloadUrl(deal.id, document.id)
      window.open(downloadUrl, '_blank', 'noopener,noreferrer')
    } catch (error) {
      console.error('Download error:', error)
      toast({
        title: "Download failed",
        description: "There was an error downloading the file. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleDelete = async (document: DealDocument) => {
    try {
      await DealDetailAPI.deleteDocument(deal.id, document.id)
      
      // Refresh documents list to get the latest data
      await fetchDocuments()
      
      toast({
        title: "Document deleted",
        description: `${cleanFilename(document.filename)} has been deleted.`
      })
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: "Delete failed",
        description: "There was an error deleting the document. Please try again.",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Card className="border-2 border-dashed border-gray-200">
          <CardContent className="p-6">
            <div className="space-y-3 text-center">
              <div className="flex justify-center">
                <div className="flex size-12 animate-pulse items-center justify-center rounded-full bg-gray-100">
                  <Upload className="size-5 text-gray-400" />
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium">Loading documents...</h3>
                <p className="text-xs text-gray-500">
                  Please wait while we fetch the latest documents
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (documents.length === 0) {
    return (
      <div className="space-y-4">
        {/* Pitch Deck Upload Section */}
        <PitchDeckUpload 
          deal={deal} 
          onPitchUploaded={fetchDocuments}
        />
        
        <Separator />
        
        {/* General Document Upload Area */}
        <Card className="border-2 border-dashed border-gray-200 transition-colors hover:border-gray-300">
          <CardContent className="p-6">
            <div className="space-y-3 text-center">
              <div className="flex justify-center">
                <div className="flex size-12 items-center justify-center rounded-full bg-gray-100">
                  <Upload className="size-5 text-gray-400" />
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium">Upload documents</h3>
                <p className="mb-3 text-xs text-gray-500">
                  Drag and drop files here, or click to browse
                </p>
                <Button 
                  onClick={handleUploadClick} 
                  disabled={uploading}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  {uploading ? 'Uploading...' : 'Choose Files'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
        />

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="page" />
          <EmptyPlaceholder.Title>No documents yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Upload documents to share with your team and track deal progress.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Pitch Deck Upload Section */}
      <PitchDeckUpload 
        deal={deal} 
        onPitchUploaded={fetchDocuments}
      />
      
      <Separator />
      
      {/* General Document Upload Area */}
      <Card className="border-2 border-dashed border-gray-200 transition-colors hover:border-gray-300">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium">Upload new document</h3>
              <p className="text-xs text-gray-500">
                PDF, Word, Excel, PowerPoint, and images supported
              </p>
            </div>
            <Button 
              onClick={handleUploadClick} 
              disabled={uploading} 
              variant="outline"
              size="sm"
              className="gap-2 text-xs"
            >
              <Upload className="size-3" />
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
      />

      {/* Documents List */}
      <div className="space-y-3">
        {documents
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .map((document, index) => {
            const Icon = getFileIcon(document.document_type)
            
            return (
              <motion.div
                key={document.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.03 }}
              >
                <Card className="border border-gray-200 bg-white transition-all hover:bg-gray-50 hover:shadow-sm min-h-[80px]">
                  <CardContent className="px-3 py-2">
                    <div className="flex items-center justify-between">
                      {/* Left Block: File Icon + Info */}
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                      {/* File Icon */}
                      <div className={cn(
                          "flex size-6 items-center justify-center rounded border",
                        getFileColor(document.document_type)
                      )}>
                          <Icon className="size-3.5" />
                      </div>

                      {/* File Info */}
                      <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="truncate text-sm font-medium text-gray-900">
                              {cleanFilename(document.filename)}
                          </h4>
                          {/* Source Badge */}
                          <Badge 
                            variant={document.source === 'startup_submission' ? 'secondary' : 'outline'}
                              className="text-xs rounded-full px-2 py-0.5 bg-gray-100 text-gray-700 border-gray-200"
                          >
                              {document.source === 'startup_submission' ? 'Submission' : 'Upload'}
                            </Badge>
                            {/* File Type Badge */}
                            <Badge variant="outline" className="text-xs uppercase rounded-full px-2 py-0.5 bg-gray-50 text-gray-600 border-gray-200">
                              {document.document_type}
                          </Badge>
                        </div>
                          
                          <div className="flex items-center gap-3 text-xs text-gray-500">
                          <span>{formatFileSize(document.file_size)}</span>
                          <div className="flex items-center gap-1">
                            <Calendar className="size-3" />
                            {formatDate(document.created_at)}
                          </div>
                          {document.uploaded_by_name && (
                            <div className="flex items-center gap-1">
                              <User className="size-3" />
                              {document.uploaded_by_name}
                            </div>
                          )}
                          {document.download_count > 0 && (
                            <div className="flex items-center gap-1">
                              <Download className="size-3" />
                                {document.download_count}
                            </div>
                          )}
                        </div>
                          
                        {/* Tags */}
                        {document.tags && document.tags.length > 0 && (
                            <div className="mt-1 flex flex-wrap gap-1">
                              {document.tags.slice(0, 3).map((tag, tagIndex) => (
                                <Badge key={tagIndex} variant="outline" className="text-xs rounded-full px-2 py-0.5 bg-gray-100 text-gray-600 border-gray-200">
                                {tag}
                              </Badge>
                            ))}
                              {document.tags.length > 3 && (
                                <Badge variant="outline" className="text-xs rounded-full px-2 py-0.5 bg-gray-100 text-gray-600 border-gray-200">
                                  +{document.tags.length - 3}
                                </Badge>
                              )}
                          </div>
                        )}
                        </div>
                      </div>

                      {/* Right Actions: Icon-only buttons */}
                      <div className="flex items-center gap-2 ml-3">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(document)}
                                className="size-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                        >
                          <Eye className="size-4" />
                        </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <span>Preview</span>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(document)}
                                className="size-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                        >
                          <Download className="size-4" />
                        </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <span>Download</span>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(document)}
                                className="size-8 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="size-4" />
                          </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <span>Delete</span>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
      </div>

      {/* Summary */}
      <Card className="border border-gray-200 bg-gray-50">
        <CardContent className="px-3 py-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">
              {documents.length} document{documents.length !== 1 ? 's' : ''} total
            </span>
            <span className="text-gray-600">
              {formatFileSize(documents.reduce((acc, doc) => acc + doc.file_size, 0))} total size
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
