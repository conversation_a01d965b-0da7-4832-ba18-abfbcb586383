"use client"

import Link from 'next/link';
import { motion, easeOut } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Deal, DealStatus, User } from '@/lib/types/deal';
import { getDealId } from '@/lib/utils/deal-id';
import { DealAPI } from '@/lib/api/deal-api';
import {
  MoreHorizontal,
  ExternalLink,
  Flag,
  Trash2,
  Target,
  TrendingUp,
  TrendingDown,
  Calendar,
  Building2,
  Tag,
  FileText,
  User as UserIcon,
  Globe,
  Clock,
  Users,
  Shield
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { useToast } from '@/components/ui/use-toast';
import { useOrgMembersCache } from '@/lib/hooks/use-org-members-cache';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
  onDelete?: (dealId: string) => Promise<boolean>;
}

// Backend-aligned utility functions
const getStatusColor = (status: DealStatus) => {
  switch (status) {
    case DealStatus.NEW:
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case DealStatus.TRIAGE:
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case DealStatus.REVIEWED:
      return 'bg-purple-50 text-purple-700 border-purple-200';
    case DealStatus.APPROVED:
      return 'bg-green-50 text-green-700 border-green-200';
    case DealStatus.NEGOTIATING:
      return 'bg-orange-50 text-orange-700 border-orange-200';
    case DealStatus.CLOSED:
      return 'bg-gray-50 text-gray-700 border-gray-200';
    case DealStatus.EXCLUDED:
    case DealStatus.REJECTED:
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

// Enhanced scoring utilities for thesis match display
const getThesisMatchColor = (percent: number) => {
  if (percent >= 80) return 'border-emerald-200 bg-emerald-50 text-emerald-700';
  if (percent >= 60) return 'border-amber-200 bg-amber-50 text-amber-700';
  if (percent >= 40) return 'border-orange-200 bg-orange-50 text-orange-700';
  return 'border-red-200 bg-red-50 text-red-700';
};

// Org thesis match colors - gradient style with score-based colors
const getOrgThesisGradient = (percent: number) => {
  if (percent >= 85) return 'bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 shadow-sm';
  if (percent >= 70) return 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-sm';
  if (percent >= 55) return 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 shadow-sm';
  if (percent >= 40) return 'bg-gradient-to-r from-orange-500 to-red-500 text-white border-0 shadow-sm';
  return 'bg-gradient-to-r from-red-500 to-red-600 text-white border-0 shadow-sm';
};

// Assessment badge colors based on score
const getAssessmentBadgeColor = (percent: number) => {
  if (percent >= 85) return 'border-green-200 bg-green-50 text-green-700';
  if (percent >= 70) return 'border-blue-200 bg-blue-50 text-blue-700';
  if (percent >= 55) return 'border-yellow-200 bg-yellow-50 text-yellow-700';
  if (percent >= 40) return 'border-orange-200 bg-orange-50 text-orange-700';
  return 'border-red-200 bg-red-50 text-red-700';
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500 text-white',
    'bg-green-500 text-white',
    'bg-purple-500 text-white',
    'bg-orange-500 text-white',
    'bg-pink-500 text-white',
    'bg-indigo-500 text-white',
    'bg-teal-500 text-white',
    'bg-red-500 text-white',
  ];

  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

const formatSector = (sector: string | string[] | undefined): string[] => {
  if (!sector) return [];
  if (Array.isArray(sector)) return sector;
  return [sector];
};

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) return 'Today';
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
  return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
};

// Tag Row Component for consistent tag display
const TagRow = ({ tags }: { tags: string[] }) => {
  if (tags.length === 0) return null;
  
  return (
    <div className="mt-1.5 flex flex-wrap gap-1">
      {tags.map((tag, idx) => (
        <Badge
          key={idx}
          variant="secondary"
          className="rounded-full border-gray-200 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-700"
        >
          {tag.toUpperCase()}
        </Badge>
      ))}
    </div>
  );
};

// Assignment Display Component
const AssignmentDisplay = ({ assignedUserIds }: { assignedUserIds?: string[] }) => {
  const { members: orgUsers, isLoading: isLoadingUsers } = useOrgMembersCache();
  const [assignedUsers, setAssignedUsers] = useState<User[]>([]);

  useEffect(() => {
    if (!assignedUserIds || assignedUserIds.length === 0) {
      setAssignedUsers([]);
      return;
    }

    if (orgUsers.length > 0) {
      const assigned = orgUsers.filter(user => 
        assignedUserIds.includes(user.id)
      );
      setAssignedUsers(assigned);
    }
  }, [assignedUserIds, orgUsers]);

  const loading = isLoadingUsers;

  if (loading) {
    return <span className="text-muted-foreground">Loading...</span>;
  }

  if (assignedUsers.length === 0) {
    return <span className="text-muted-foreground">Unassigned</span>;
  }

  if (assignedUsers.length === 1) {
    const user = assignedUsers[0];
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1.5">
              <Avatar className="size-3">
                <AvatarFallback className={cn(
                  "text-xs font-medium",
                  getAvatarColor(user.name)
                )}>
                  {user.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate text-muted-foreground">
                {user.name}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <p className="font-medium">Assigned to</p>
              <p>{user.name}</p>
              <p className="text-muted-foreground">{user.email}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Multiple users assigned
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-1.5">
            <div className="flex -space-x-1">
              {assignedUsers.slice(0, 2).map((user) => (
                <Avatar key={user.id} className="size-3 border border-white">
                  <AvatarFallback className={cn(
                    "text-xs font-medium",
                    getAvatarColor(user.name)
                  )}>
                    {user.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ))}
              {assignedUsers.length > 2 && (
                <div className="flex size-3 items-center justify-center rounded-full border border-white bg-muted text-xs">
                  +{assignedUsers.length - 2}
                </div>
              )}
            </div>
            <span className="text-muted-foreground">
              {assignedUsers.length} assigned
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1 text-xs">
            <p className="font-medium">Assigned to</p>
            {assignedUsers.map((user) => (
              <p key={user.id}>{user.name}</p>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Metadata Grid Component for consistent layout
const MetadataGrid = ({ 
  website, 
  founderCount, 
  createdAt, 
  sourceType,
  assignedUserIds
}: { 
  website?: string; 
  founderCount: number; 
  createdAt: number; 
  sourceType?: string;
  assignedUserIds?: string[];
}) => {
  return (
    <div className="mt-2.5 grid grid-cols-2 gap-x-3 gap-y-1 text-xs text-muted-foreground">
      <div className="flex items-center gap-1.5">
        <Globe className="size-3" />
        <span>{website ? "Website" : "No site"}</span>
      </div>
      <div className="flex items-center gap-1.5">
        <UserIcon className="size-3" />
        <span>{founderCount} founder{founderCount !== 1 ? 's' : ''}</span>
      </div>
      <div className="flex items-center gap-1.5">
        <Clock className="size-3" />
        <span>Created {formatDate(createdAt)}</span>
      </div>
      <div className="flex items-center gap-1.5">
        <FileText className="size-3" />
        <span>{sourceType || "Manual entry"}</span>
      </div>
      <div className="flex items-center gap-1.5">
        <Users className="size-3" />
        <AssignmentDisplay assignedUserIds={assignedUserIds} />
      </div>
    </div>
  );
};

// Score Display Component
const ScoreDisplay = ({ 
  isExcluded, 
  exclusionReason, 
  thesisMatchPercent,
  thesisAssessment,
  thesisDescription,
  coreScore, 
  bonusScore, 
  penaltyScore, 
  founderScore, 
  marketScore,
  thesisScoring
}: {
  isExcluded: boolean;
  exclusionReason?: string;
  thesisMatchPercent: number | null;
  thesisAssessment?: string;
  thesisDescription?: string;
  coreScore: number;
  bonusScore: number;
  penaltyScore: number;
  founderScore?: number;
  marketScore?: number;
  thesisScoring?: any;
}) => {
  const netBonusPenalty = bonusScore - penaltyScore;

  if (isExcluded) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge className="flex items-center gap-1.5 rounded-full border border-red-200 bg-red-50 px-3 py-1 text-sm font-semibold text-red-700">
              <Flag className="size-3.5" />
              Excluded
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="max-w-xs text-xs">
              <p className="font-medium">Excluded by filter</p>
              {exclusionReason && <p className="mt-1">{exclusionReason}</p>}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (typeof thesisMatchPercent !== 'number') return null;

  return (
    <div className="mt-2.5 flex items-center gap-1.5 flex-wrap">
      {/* Org Thesis Match Badge */}
      {thesisDescription && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className={cn(
                "flex items-center gap-1.5 rounded-full px-3 py-1 text-sm font-semibold",
                getOrgThesisGradient(thesisMatchPercent)
              )}>
                <Shield className="size-3.5" />
                {thesisMatchPercent}% Thesis Match
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1 text-xs max-w-xs">
                <p className="font-medium">Org Thesis Match</p>
                <p className="text-muted-foreground mb-2">{thesisDescription}</p>
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Based on organization preferences and deal metadata (available immediately)
                  </p>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Form-Based Scoring Badge */}
      {(coreScore > 0 || thesisScoring) && !thesisDescription && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className={cn(
                "flex items-center gap-1.5 rounded-full px-3 py-1 text-sm font-semibold",
                getThesisMatchColor(thesisMatchPercent)
              )}>
                <Target className="size-3.5" />
                {thesisMatchPercent}% Form Score
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1 text-xs max-w-xs">
                <p className="font-medium">Form-Based Scoring</p>
                <p>Core Score: {coreScore}</p>
                {bonusScore > 0 && <p className="text-green-600">Bonus: +{bonusScore}</p>}
                {penaltyScore > 0 && <p className="text-red-600">Penalty: -{penaltyScore}</p>}
                {founderScore && <p>Founder Score: {Math.round(founderScore)}%</p>}
                {marketScore && <p>Market Score: {Math.round(marketScore)}%</p>}
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Based on founder form submission
                  </p>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Form Score Badge (when BOTH exist) */}
      {thesisDescription && (coreScore > 0 || thesisScoring) && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge className={cn(
                "flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium",
                getThesisMatchColor(Math.round(thesisScoring?.normalized_percent || 0))
              )}>
                <Target className="size-3" />
                {Math.round(thesisScoring?.normalized_percent || 0)}% Form
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1 text-xs max-w-xs">
                <p className="font-medium">Form-Based Scoring</p>
                <p>Core Score: {coreScore}</p>
                {bonusScore > 0 && <p className="text-green-600">Bonus: +{bonusScore}</p>}
                {penaltyScore > 0 && <p className="text-red-600">Penalty: -{penaltyScore}</p>}
                {founderScore && <p>Founder Score: {Math.round(founderScore)}%</p>}
                {marketScore && <p>Market Score: {Math.round(marketScore)}%</p>}
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Based on founder form submission
                  </p>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Assessment Badge from org_thesis_matching */}
      {thesisAssessment && thesisMatchPercent && (
        <Badge className={cn(
          "rounded-full border px-2.5 py-0.5 text-xs font-medium",
          getAssessmentBadgeColor(thesisMatchPercent)
        )}>
          {thesisAssessment}
        </Badge>
      )}

      {netBonusPenalty !== 0 && (
        <Badge className={cn(
          "flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-sm font-medium",
          "border-current/20 border",
          netBonusPenalty > 0
            ? "border-blue-200 bg-blue-50 text-blue-700"
            : "border-red-200 bg-red-50 text-red-700"
        )}>
          {netBonusPenalty > 0 ? (
            <TrendingUp className="size-3.5" />
          ) : (
            <TrendingDown className="size-3.5" />
          )}
          {netBonusPenalty > 0 ? `+${netBonusPenalty}` : netBonusPenalty}
        </Badge>
      )}
    </div>
  );
};

export function DealCard({ deal, index, onClick, onDelete }: DealCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const { toast } = useToast();
  
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: easeOut,
        delay: index * 0.05
      }
    }
  };

  // Get normalized deal ID - critical for navigation
  const dealId = getDealId(deal);

  // Extract backend data with proper fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = formatSector(deal.sector);
  const stage = deal.stage;
  const website = deal.company_website;
  const status = deal.status;
  const tags = deal.tags || [];
  const founders = deal.founders || [];

  // Enhanced scoring data extraction - prioritize org_thesis_matching
  const orgThesisMatching = deal.org_thesis_matching;
  const thesisScoring = deal.scoring?.thesis?.score;
  const exclusionResult = deal.exclusion_filter_result;
  
  // Use org_thesis_matching.score as primary source, fallback to legacy scoring
  const thesisMatchPercent = orgThesisMatching?.score 
    ? Math.round(orgThesisMatching.score)
    : (thesisScoring?.normalized_percent ? Math.round(thesisScoring.normalized_percent) : null);
  
  const thesisAssessment = orgThesisMatching?.assessment;
  const thesisDescription = orgThesisMatching?.description;
  
  const coreScore = thesisScoring?.core || 0;
  const bonusScore = thesisScoring?.bonus || 0;
  const penaltyScore = thesisScoring?.penalty || 0;
  const isExcluded = exclusionResult?.excluded || false;
  const exclusionReason = exclusionResult?.reason;

  // Market and founder scores
  const founderScore = deal.scoring?.founders?.normalized_score;
  const marketScore = deal.scoring?.market?.normalized_score;

  // Prevent navigation if no valid ID
  if (!dealId) {
    console.error('Deal card missing valid ID:', deal);
    return null;
  }

  const handleDelete = async () => {
    if (!onDelete || !dealId) return;

    try {
      setDeleting(true);
      const success = await onDelete(dealId);
      
      if (success) {
        setDeleteDialogOpen(false);
        setDropdownOpen(false); // Ensure dropdown is closed after successful deletion
      }
    } catch (error) {
      console.error('Error deleting deal:', error);
    } finally {
      setDeleting(false);
    }
  };

  // Generate company initials for avatar
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  // Debug logo URL
  console.log(`🔍 Deal card for ${companyName}:`, {
    hasLogoUrl: !!deal.logo_url,
    logoUrl: deal.logo_url,
    dealId: deal._id
  });

  // Prepare tags for display
  const displayTags = [
    ...(stage ? [stage.replace('_', ' ').toUpperCase()] : []),
    ...sectors,
    ...tags.slice(0, 2) // Limit to 2 additional tags
  ];

  // Determine source type
  const sourceType = (deal.submission_ids || []).length > 0 ? "Form Submission" : "Manual entry";

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group w-full"
    >
      <Card className="relative h-full cursor-pointer rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md">
        <Link href={`/deals/${dealId}`} className="block h-full">
          {/* Card Header with Avatar and Status */}
          <CardHeader className="flex flex-row items-center justify-between p-3 pb-2">
            <div className="flex items-center gap-2.5">
              <div className={cn(
                "size-9 rounded-full bg-gradient-to-br from-blue-500 to-purple-600",
                "flex items-center justify-center shadow-sm ring-2 ring-white overflow-hidden relative"
              )}>
                {deal.logo_url ? (
                  <img 
                    src={deal.logo_url} 
                    alt={`${companyName} logo`}
                    className="w-full h-full object-cover absolute inset-0"
                    onLoad={() => {
                      console.log(`✅ Logo loaded successfully for ${companyName}:`, deal.logo_url);
                    }}
                    onError={(e) => {
                      console.error(`❌ Logo failed to load for ${companyName}:`, deal.logo_url);
                      // Hide logo and show initials if it fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                ) : null}
                <span className={cn(
                  "text-xs font-bold text-white",
                  deal.logo_url ? "opacity-0" : "opacity-100"
                )}>
                  {initials}
                </span>
              </div>
              <div className="min-w-0 flex-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <h3 className="truncate text-base font-semibold text-gray-900 transition-colors group-hover:text-blue-600">
                        {companyName}
                      </h3>
                    </TooltipTrigger>
                    {companyName.length > 20 && (
                      <TooltipContent>
                        <span>{companyName}</span>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge className={cn(
                "rounded-full border px-2.5 py-0.5 text-xs font-medium",
                getStatusColor(status)
              )}>
                {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
              </Badge>

              {/* Menu Dropdown */}
              <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="size-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    aria-label="Deal actions menu"
                  >
                    <MoreHorizontal className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  className="w-[160px]"
                  onCloseAutoFocus={(e) => {
                    // Prevent focus from being trapped in the dropdown
                    e.preventDefault();
                  }}
                >
                  <DropdownMenuItem
                    className="text-red-600 focus:text-red-600 hover:text-red-600"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setDropdownOpen(false); // Close dropdown first
                      setDeleteDialogOpen(true);
                    }}
                    onSelect={(e) => {
                      // Prevent default selection behavior that might cause focus issues
                      e.preventDefault();
                    }}
                  >
                    <Trash2 className="mr-2 size-4" />
                    Delete Deal
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>

          {/* Card Content */}
          <CardContent className="p-3 pt-0">
            {/* Tags Row */}
            <TagRow tags={displayTags} />

            {/* Metadata Grid */}
            <MetadataGrid
              website={website}
              founderCount={founders.length}
              createdAt={deal.created_at}
              sourceType={sourceType}
              assignedUserIds={deal.assigned_user_ids}
            />

            {/* Score Display */}
            <ScoreDisplay
              isExcluded={isExcluded}
              exclusionReason={exclusionReason}
              thesisMatchPercent={thesisMatchPercent}
              thesisAssessment={thesisAssessment}
              thesisDescription={thesisDescription}
              coreScore={coreScore}
              bonusScore={bonusScore}
              penaltyScore={penaltyScore}
              founderScore={founderScore}
              marketScore={marketScore}
              thesisScoring={thesisScoring}
            />

            {/* Website Link */}
            {website && (
              <div className="mt-2.5">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 text-xs font-medium text-muted-foreground hover:text-primary"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.open(website.startsWith('http') ? website : `https://${website}`, '_blank');
                  }}
                >
                  <ExternalLink className="mr-1.5 size-3.5" />
                  Visit Website
                </Button>
              </div>
            )}
          </CardContent>
        </Link>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Deal"
        description={`Are you sure you want to delete the deal "${companyName}"? This action cannot be undone.`}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setDropdownOpen(false); // Ensure dropdown is closed
        }}
        onConfirm={handleDelete}
        loading={deleting}
      />
    </motion.div>
  );
}
