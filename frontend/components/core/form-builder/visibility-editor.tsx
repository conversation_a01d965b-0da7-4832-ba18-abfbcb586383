"use client"

import React, { useState } from 'react';
import { Plus, Trash2, Eye, AlertTriangle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { FormWithDetails, Question, VisibilityCondition, ConditionClause } from '@/lib/types/form';

interface VisibilityEditorProps {
  question: Question;
  form: FormWithDetails;
  onUpdate: (visibilityCondition: VisibilityCondition | null) => void;
}

// All operators - we'll filter these based on question type
const ALL_OPERATORS = [
  { value: '==', label: 'Equals', symbol: '=', description: 'Value must be exactly equal', numeric: false },
  { value: '!=', label: 'Not equal', symbol: '≠', description: 'Value must not be equal', numeric: false },
  { value: '>', label: 'Greater than', symbol: '>', description: 'Numeric value must be greater', numeric: true },
  { value: '<', label: 'Less than', symbol: '<', description: 'Numeric value must be less', numeric: true },
  { value: '>=', label: 'Greater than or Equal', symbol: '≥', description: 'Numeric value must be greater or equal', numeric: true },
  { value: '<=', label: 'Less than or Equal', symbol: '≤', description: 'Numeric value must be less or equal', numeric: true },
];

const LOGIC_OPERATORS = [
  { value: 'and', label: 'Satisfies all conditions' },
  { value: 'or', label: 'Satisfies any one condition' },
];

export function VisibilityEditor({ question, form, onUpdate }: VisibilityEditorProps) {
  const [isEnabled, setIsEnabled] = useState(!!question.visibility_condition);
  const [conditions, setConditions] = useState<ConditionClause[]>(
    question.visibility_condition?.conditions as ConditionClause[] || []
  );
  const [logicOperator, setLogicOperator] = useState(
    question.visibility_condition?.operator || 'and'
  );

  // Get all questions that appear before this question in the form
  // Only include Boolean and MCQ question types for conditional visibility
  const getPreviousQuestions = (): Question[] => {
    const allQuestions: Question[] = [];
    const currentQuestionId = question._id || question.id;
    const allowedTypes = ['boolean', 'single_select', 'multi_select', 'number'];

    for (const section of form.sections.sort((a, b) => a.order - b.order)) {
      for (const q of section.questions.sort((a, b) => a.order - b.order)) {
        if ((q._id || q.id) === currentQuestionId) {
          return allQuestions; // Stop when we reach the current question
        }
        
        // Only include allowed question types
        if (allowedTypes.includes(q.type)) {
          allQuestions.push(q);
        }
      }
    }

    return allQuestions;
  };

  const previousQuestions = getPreviousQuestions();

  // Get available operators for a specific question type
  const getAvailableOperators = (questionType: string) => {
    const numericTypes = ['number', 'range'];
    const isNumeric = numericTypes.includes(questionType);
    
    return ALL_OPERATORS.filter(op => !op.numeric || isNumeric);
  };

  // Get options for a specific question
  const getQuestionOptions = (targetQuestion: Question) => {
    switch (targetQuestion.type) {
      case 'boolean':
        return [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' }
        ];
      case 'single_select':
      case 'multi_select':
        return targetQuestion.options || [];
      default:
        return [];
    }
  };

  // Check if question supports option selection
  const supportsOptions = (questionType: string) => {
    return ['boolean', 'single_select', 'multi_select'].includes(questionType);
  };

  const handleToggleVisibility = (enabled: boolean) => {
    setIsEnabled(enabled);
    if (!enabled) {
      onUpdate(null);
      setConditions([]);
    } else {
      // Create a default condition
      const defaultCondition: ConditionClause = {
        question_id: '',
        value: ''
      };
      setConditions([defaultCondition]);
    }
  };

  const handleAddCondition = () => {
    const newCondition: ConditionClause = {
      question_id: '',
      value: '',
      operator: '=='  // Default operator
    };
    const updatedConditions = [...conditions, newCondition];
    setConditions(updatedConditions);
    updateVisibilityCondition(updatedConditions, logicOperator);
  };

  const handleRemoveCondition = (index: number) => {
    const updatedConditions = conditions.filter((_, i) => i !== index);
    setConditions(updatedConditions);
    updateVisibilityCondition(updatedConditions, logicOperator);
  };

  const handleUpdateCondition = (index: number, field: keyof ConditionClause, value: any) => {
    const updatedConditions = conditions.map((condition, i) => {
      if (i === index) {
        const newCondition = { ...condition, [field]: value };
        
        // Reset value when question changes
        if (field === 'question_id') {
          newCondition.value = '';
          // Reset operator to == when question changes
          newCondition.operator = '==';
        }
        
        return newCondition;
      }
      return condition;
    });
    setConditions(updatedConditions);
    updateVisibilityCondition(updatedConditions, logicOperator);
  };

  const handleLogicOperatorChange = (operator: string) => {
    setLogicOperator(operator as typeof logicOperator);
    updateVisibilityCondition(conditions, operator);
  };

  const updateVisibilityCondition = (conditionsList: ConditionClause[], operator: string) => {
    if (conditionsList.length === 0) {
      onUpdate(null);
      return;
    }

    // Ensure root operator is always 'and' or 'or'.
    let rootOperator: 'and' | 'or' | 'not' = 'and';
    if (operator === 'or') rootOperator = 'or';
    // (If you ever support 'not', add logic here)
    const visibilityCondition: VisibilityCondition = {
      operator: rootOperator,
      conditions: conditionsList
    };
    onUpdate(visibilityCondition);
  };

  const getQuestionById = (questionId: string): Question | undefined => {
    return previousQuestions.find(q => (q._id || q.id) === questionId);
  };

  const renderConditionSummary = () => {
    if (!isEnabled || conditions.length === 0) return null;

    const validConditions = conditions.filter(c => c.question_id && c.value);
    if (validConditions.length === 0) return null;

    return (
      <Card className="border-primary/20 bg-muted/50">
        <CardContent className="p-4">
          <div className="mb-2 flex items-center gap-2">
            <Eye className="size-4 text-primary" />
            <span className="text-sm font-medium">This question will be shown when:</span>
          </div>
          <div className="space-y-1 text-sm">
            {validConditions.map((condition, index) => {
              const targetQuestion = getQuestionById(condition.question_id);
              const operator = ALL_OPERATORS.find(op => op.value === (condition.operator || '=='));
              return (
                <div key={index} className="flex items-center gap-2">
                  {index > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {logicOperator.toUpperCase()}
                    </Badge>
                  )}
                  <span className="text-muted-foreground">
                    "{targetQuestion?.label || 'Unknown question'}" {operator?.label || 'equals'} "{condition.value}"
                  </span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderValueInput = (condition: ConditionClause, index: number) => {
    const targetQuestion = condition.question_id ? getQuestionById(condition.question_id) : null;
    
    if (!targetQuestion) {
      return (
        <div className="w-[120px] shrink-0">
          <Input
            placeholder="Select question first"
            disabled
            className="w-full text-sm text-muted-foreground"
          />
        </div>
      );
    }

    const questionOptions = getQuestionOptions(targetQuestion);
    const hasOptions = supportsOptions(targetQuestion.type);

    if (hasOptions && questionOptions.length > 0) {
      return (
        <div className="w-[120px] shrink-0">
          <Select
            value={condition.value}
            onValueChange={(value) => handleUpdateCondition(index, 'value', value)}
          >
            <SelectTrigger className="w-full text-sm">
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {questionOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <span className="text-sm">{option.label}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }

    // For numeric or other types without predefined options
    return (
      <div className="w-[120px] shrink-0">
        <Input
          placeholder="Enter value"
          type={targetQuestion.type === 'number' ? 'number' : 'text'}
          value={condition.value}
          onChange={(e) => handleUpdateCondition(index, 'value', e.target.value)}
          className="w-full text-sm"
        />
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Warning when no suitable questions available */}
      {previousQuestions.length === 0 && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="size-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            <strong>No suitable questions available</strong>
            <br />
            Conditional visibility works with Boolean (Yes/No), Multiple Choice (single/multi-select), and Number questions. 
            Add these question types before this question to enable conditional visibility.
          </AlertDescription>
        </Alert>
      )}

      {/* Toggle Visibility */}
      <Card className="rounded-lg border-primary/30 bg-white/80 shadow-sm" data-tour="question-visibility">
        <CardContent className="flex items-center justify-between p-3">
          <span className="text-sm font-medium">
            Visibility Conditions: {isEnabled ? 'Enabled' : 'Disabled'}
          </span>
          <Button
            variant={isEnabled ? "default" : "outline"}
            size="sm"
            onClick={() => handleToggleVisibility(!isEnabled)}
            disabled={previousQuestions.length === 0}
          >
            {isEnabled ? 'Disable' : 'Enable'}
          </Button>
        </CardContent>
      </Card>

      {/* Logic Operator (only show when multiple conditions) */}
      {isEnabled && conditions.length > 1 && (
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-3">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-blue-900">
                Condition Logic:
              </span>
              <Select value={logicOperator} onValueChange={handleLogicOperatorChange}>
                <SelectTrigger className="w-auto min-w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LOGIC_OPERATORS.map((op) => (
                    <SelectItem key={op.value} value={op.value}>
                      {op.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conditions */}
      {isEnabled && (
        <Card className="border-primary/20 bg-muted/40">
          <CardContent className="space-y-3 p-4">
            {conditions.length === 0 && (
              <div className="text-sm text-muted-foreground">No conditions set.</div>
            )}

            {conditions.map((condition, index) => {
              const targetQuestion = getQuestionById(condition.question_id);
              const availableOperators = targetQuestion ? getAvailableOperators(targetQuestion.type) : ALL_OPERATORS;
              const operator = availableOperators.find(
                (op) => op.value === (condition.operator || '==')
              );

              return (
                <div key={index} className="space-y-2">
                  {/* Condition Index Badge */}
                  {conditions.length > 1 && (
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Condition {index + 1}
                      </Badge>
                      {index > 0 && (
                        <Badge variant="outline" className="text-xs text-blue-600">
                          {logicOperator.toUpperCase()}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Condition Fields */}
                  <div className="flex flex-wrap items-center gap-2 rounded-lg border border-muted/30 bg-white p-3">
                    {/* 1) Question Dropdown */}
                    <div className="min-w-[180px] flex-1">
                      <Select
                        value={condition.question_id}
                        onValueChange={(value) =>
                          handleUpdateCondition(index, 'question_id', value)
                        }
                      >
                        <SelectTrigger className="w-full text-sm">
                          <SelectValue placeholder="Select question" />
                        </SelectTrigger>
                        <SelectContent>
                          {previousQuestions.map((q) => (
                            <SelectItem key={q._id || q.id} value={q._id || q.id!}>
                              <div className="flex flex-col items-start">
                                <span className="text-sm font-medium">{q.label}</span>
                                {/* <span className="text-xs text-muted-foreground capitalize">
                                  {q.type.replace('_', ' ')}
                                </span> */}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 2) Operator Dropdown */}
                    <div className="w-[60px] shrink-0">
                      <Select
                        value={condition.operator || '=='}
                        onValueChange={(value) =>
                          handleUpdateCondition(index, 'operator', value)
                        }
                        disabled={!condition.question_id}
                      >
                        <SelectTrigger className="w-full text-sm">
                          <SelectValue>
                            {operator ? (
                              <span className="font-mono text-base font-semibold">
                                {operator.symbol}
                              </span>
                            ) : (
                              '='
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {availableOperators.map((op) => (
                            <SelectItem key={op.value} value={op.value}>
                              <div className="flex items-center gap-2">
                                <span className="font-mono text-sm">{op.symbol}</span>
                                <span className="text-sm">{op.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 3) Value Input/Select */}
                    {renderValueInput(condition, index)}

                    {/* 4) Remove Button */}
                    <button
                      type="button"
                      onClick={() => handleRemoveCondition(index)}
                      className="flex items-center justify-center rounded p-1.5 text-red-500 hover:bg-red-50"
                      title="Remove condition"
                    >
                      <Trash2 className="size-4" />
                    </button>
                  </div>
                </div>
              );
            })}

            {/* Add Condition Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddCondition}
              className="w-full sm:w-auto"
            >
              <Plus className="mr-2 size-4" />
              Add Condition
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Condition Summary */}
      {renderConditionSummary()}
    </div>
  );
}