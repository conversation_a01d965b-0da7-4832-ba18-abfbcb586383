"use client"

import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import {
  GripVertical,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  ChevronDown,
  ChevronRight,
  Eye,
  <PERSON>h,
  <PERSON>,
  AlignLeft,
  Circle,
  CheckSquare,
  ToggleLeft,
  Upload,
  Calendar,
  Minus
} from 'lucide-react';

import { FormWithDetails, Question, QuestionType, QUESTION_TYPES, isCoreFieldQuestion, getCoreFieldDisplayName } from '@/lib/types/form';
import { EditQuestionDialog } from './edit-question-dialog';
import { cn } from '@/lib/utils';

interface QuestionCardProps {
  question: Question;
  form: FormWithDetails;
  isSelected?: boolean;
  onUpdate: (updates: any) => void | Promise<void>;
  onDelete: () => void;
  onDuplicate: () => void;
  onSelect?: () => void;
}

// Icon mapping for question types
const QUESTION_TYPE_ICONS = {
  [QuestionType.SHORT_TEXT]: Type,
  [QuestionType.LONG_TEXT]: AlignLeft,
  [QuestionType.NUMBER]: Hash,
  [QuestionType.RANGE]: Minus,
  [QuestionType.SINGLE_SELECT]: Circle,
  [QuestionType.MULTI_SELECT]: CheckSquare,
  [QuestionType.BOOLEAN]: ToggleLeft,
  [QuestionType.FILE]: Upload,
  [QuestionType.DATE]: Calendar,
};

export function QuestionCard({
  question,
  form,
  isSelected = false,
  onUpdate,
  onDelete,
  onDuplicate,
  onSelect
}: QuestionCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `question-${question._id || question.id}`,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const TypeIcon = QUESTION_TYPE_ICONS[question.type] || Type;
  const typeInfo = QUESTION_TYPES.find(t => t.type === question.type);

  return (
    <>
      <Card
        ref={setNodeRef}
        style={style}
        className={cn(
          "group w-full cursor-pointer rounded-xl border transition-all duration-300",
          "touch-manipulation active:scale-[0.99]", // Mobile touch optimization
          // Mobile-first spacing
          "ml-2 min-h-14 md:ml-4 md:min-h-16",
          // Drag states
          isDragging ? 'rotate-1 scale-105 opacity-50 shadow-xl' : 'shadow-sm hover:shadow-md',
          // Expanded state
          isExpanded ? 'ring-1 ring-primary/20' : '',
          // Selected state
          isSelected
            ? 'border-primary/30 bg-primary/10 shadow-lg ring-2 ring-primary'
            : 'border-border hover:border-primary/30'
        )}
        onClick={(e) => {
          e.stopPropagation();
          onSelect?.();
        }}
        data-tour="question-card"
      >
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between gap-3">
            <div className="flex min-w-0 flex-1 items-start gap-3">
              {/* Drag Handle */}
              <div
                {...attributes}
                {...listeners}
                className="mt-1 flex size-10 shrink-0 cursor-grab touch-manipulation items-center justify-center opacity-60 transition-colors hover:text-primary active:cursor-grabbing group-hover:opacity-100"
                onClick={(e) => e.stopPropagation()}
              >
                <GripVertical className="size-5" />
              </div>

              {/* Question Info */}
              <div className="min-w-0 flex-1">
                <div className="mb-3 flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-2">
                    <TypeIcon className="size-4 text-primary" />
                    <Badge variant="outline" className="text-xs font-medium">
                      {typeInfo?.label || question.type}
                    </Badge>
                  </div>
                  {question.required && (
                    <Badge variant="destructive" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {isCoreFieldQuestion(question) && (
                    <Badge variant="default" className="bg-blue-600 text-xs hover:bg-blue-700">
                      Core Field
                    </Badge>
                  )}
                  {question.visibility_condition && (
                    <Badge variant="secondary" className="text-xs">
                      <Eye className="mr-1 size-3" />
                      Conditional
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Just a visual indicator - clicking the question card will select it
                    }}
                    className="h-6 px-2 text-xs text-muted-foreground hover:text-primary"
                  >
                    <Eye className="mr-1 size-3" />
                    Set Visibility
                  </Button>
                </div>

                <h4 className="mb-2 line-clamp-2 text-lg font-semibold leading-relaxed transition-colors group-hover:text-primary">
                  {question.label}
                </h4>

                {question.help_text && (
                  <p className="mb-3 line-clamp-2 text-xs leading-relaxed text-muted-foreground sm:text-sm">
                    {question.help_text}
                  </p>
                )}

                <div className="flex flex-wrap items-center gap-3 text-xs sm:gap-4">
                  <div className="flex items-center gap-1">
                    <span className="text-muted-foreground">Position</span>
                    <span className="font-semibold text-foreground">{question.order + 1}</span>
                  </div>
                  {question.options && (
                    <div className="flex items-center gap-1">
                      <span className="font-semibold text-foreground">{question.options.length}</span>
                      <span className="text-muted-foreground">option{question.options.length !== 1 ? 's' : ''}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex shrink-0 items-center gap-2 opacity-60 transition-opacity group-hover:opacity-100">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
                className="size-10 touch-manipulation p-0 text-muted-foreground hover:text-primary"
              >
                {isExpanded ? (
                  <ChevronDown className="size-5" />
                ) : (
                  <ChevronRight className="size-5" />
                )}
              </Button>

              <div className="relative">
                <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                    className="size-10 touch-manipulation p-0 text-muted-foreground hover:text-primary"
                  >
                    <MoreHorizontal className="size-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="bottom"
                  align="end"
                  sideOffset={8}
                  collisionPadding={12}
                  className="w-48"
                >
                  <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
                    <Edit className="mr-2 size-4" />
                    Edit Question
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onDuplicate}>
                    <Copy className="mr-2 size-4" />
                    Duplicate Question
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {isCoreFieldQuestion(question) ? (
                    <DropdownMenuItem
                      disabled
                      className="cursor-not-allowed text-muted-foreground"
                      title="Core dashboard field. Can't delete."
                    >
                      <Trash2 className="mr-2 size-4" />
                      Delete Question
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem
                      onClick={() => setShowDeleteDialog(true)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 size-4" />
                      Delete Question
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              </div>
            </div>
          </div>
        </CardHeader>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <CardContent className="border-t pt-0">
                <div className="mt-3 space-y-3">
                  {/* Question Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-muted-foreground">Type:</span>
                      <p className="mt-1">{typeInfo?.label || question.type}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">Required:</span>
                      <p className="mt-1">{question.required ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  {/* Options */}
                  {question.options && question.options.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Options:</span>
                      <div className="mt-2 space-y-1">
                        {question.options.map((option, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <div className="size-2 rounded-full bg-muted" />
                            <span>{option.label}</span>
                            <span className="text-muted-foreground">({option.value})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Validation Rules */}
                  {question.validation && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Validation:</span>
                      <div className="mt-2 space-y-1 text-sm">
                        {question.validation.min !== undefined && (
                          <div>Min: {question.validation.min}</div>
                        )}
                        {question.validation.max !== undefined && (
                          <div>Max: {question.validation.max}</div>
                        )}
                        {question.validation.regex && (
                          <div>Pattern: {question.validation.regex}</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Visibility Condition */}
                  {question.visibility_condition && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Visibility:</span>
                      <div className="mt-2 rounded bg-muted/50 p-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Eye className="size-3" />
                          <span>Show if conditions are met</span>
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground">
                          {question.visibility_condition.conditions.length} condition{question.visibility_condition.conditions.length !== 1 ? 's' : ''} with {question.visibility_condition.operator} logic
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Dialogs */}
      <EditQuestionDialog
        question={question}
        form={form}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={async (data) => {
          const result = onUpdate(data);
          if (result instanceof Promise) {
            await result;
          }
        }}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Question</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{question.label}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete();
                setShowDeleteDialog(false);
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Question
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
