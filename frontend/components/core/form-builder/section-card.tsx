"use client"

import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import {
  GripVertical,
  MoreHorizontal,
  Plus,
  Edit,
  Copy,
  Trash2,
  ChevronDown,
  ChevronRight,
  Repeat,
  HelpCircle
} from 'lucide-react';

import { FormWithDetails, Section, Question } from '@/lib/types/form';
import { QuestionCard } from './question-card';
import { AddQuestionDialog } from './add-question-dialog';
import { EditSectionDialog } from './edit-section-dialog';
import { FormBuilderAPI } from '@/lib/api/form-builder-api';
import FormAPI from '@/lib/api/form-api';
import { toast } from '@/components/ui/use-toast';

interface SectionCardProps {
  section: Section;
  form: FormWithDetails;
  isActive: boolean;
  isSelected?: boolean;
  selectedQuestionId?: string;
  onUpdate: (updates: Partial<Section>) => Promise<void>;
  onDelete: () => void;
  onDuplicate: () => void;
  onToggleActive: () => void;
  onSelect: () => void;
  onSelectQuestion?: (questionId: string) => void;
  onRefreshForm?: () => Promise<void>;
}

export function SectionCard({
  section,
  form,
  isActive,
  isSelected = false,
  selectedQuestionId,
  onUpdate,
  onDelete,
  onDuplicate,
  onToggleActive,
  onSelect,
  onSelectQuestion,
  onRefreshForm
}: SectionCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showAddQuestion, setShowAddQuestion] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `section-${section._id || section.id}`,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Handle question operations
  const handleAddQuestion = async (questionData: any) => {
    try {
      const newOrder = Math.max(...section.questions.map(q => q.order), 0) + 1;
      await FormBuilderAPI.createQuestion(section._id || section.id!, {
        ...questionData,
        order: newOrder
      });

      // Refresh the form data to get the updated state
      if (onRefreshForm) {
        await onRefreshForm();
      }

      toast({
        title: 'Success',
        description: 'Question added successfully'
      });
    } catch (err) {
      console.error('Error adding question:', err);
      toast({
        title: 'Error',
        description: 'Failed to add question',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateQuestion = async (questionId: string, updates: any) => {
    try {
      // For order updates, delegate to parent component which handles drag-and-drop reordering
      // Individual order updates should not be used for drag-and-drop operations
      if (updates.order !== undefined) {
        console.warn('Individual order updates are deprecated for drag-and-drop. Use reorder APIs instead.');
        return;
      }

      // For non-order updates, use the regular update API
      await FormAPI.updateQuestion(questionId, updates);

      // Update local state
      const updatedQuestions = section.questions.map(q =>
        (q._id || q.id) === questionId ? { ...q, ...updates } : q
      );

      await onUpdate({ questions: updatedQuestions });

      toast({
        title: 'Success',
        description: 'Question updated successfully'
      });
    } catch (err) {
      console.error('Error updating question:', err);
      toast({
        title: 'Error',
        description: 'Failed to update question',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    try {
      await FormBuilderAPI.deleteQuestion(questionId);

      // Update local state
      const updatedQuestions = section.questions.filter(q => (q._id || q.id) !== questionId);
      onUpdate({ questions: updatedQuestions });

      toast({
        title: 'Success',
        description: 'Question deleted successfully'
      });
    } catch (err) {
      console.error('Error deleting question:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete question',
        variant: 'destructive'
      });
    }
  };

  const handleDuplicateQuestion = async (questionId: string) => {
    try {
      await FormBuilderAPI.duplicateQuestion(
        section._id || section.id!,
        questionId
      );

      // Refresh the form data to get the updated state
      if (onRefreshForm) {
        await onRefreshForm();
      }

      toast({
        title: 'Success',
        description: 'Question duplicated successfully'
      });
    } catch (err) {
      console.error('Error duplicating question:', err);
      toast({
        title: 'Error',
        description: 'Failed to duplicate question',
        variant: 'destructive'
      });
    }
  };

  return (
    <>
      <Card
        ref={setNodeRef}
        style={style}
        className={`
          group cursor-pointer rounded-xl border transition-all duration-300
          ${isDragging ? 'rotate-2 scale-105 opacity-50 shadow-xl' : 'shadow-sm hover:shadow-md'}
          ${isActive ? 'bg-primary/5 shadow-md ring-2 ring-primary/20' : ''}
          ${isSelected ? 'border-primary/30 bg-primary/10 shadow-lg ring-2 ring-primary' : 'border-border hover:border-primary/30'}
          min-h-16 w-full
        `}
        onClick={onSelect}
        data-tour="section-card"
      >
        <CardHeader className="px-6 py-4">
          <div className="flex items-start justify-between gap-4">
            <div className="flex min-w-0 flex-1 items-start gap-4">
              {/* Drag Handle */}
              <div
                {...attributes}
                {...listeners}
                className="mt-1 flex size-10 shrink-0 cursor-grab touch-manipulation items-center justify-center opacity-60 transition-colors hover:text-primary active:cursor-grabbing group-hover:opacity-100"
                onClick={(e) => e.stopPropagation()}
              >
                <GripVertical className="size-5" />
              </div>

              {/* Section Info */}
              <div className="min-w-0 flex-1">
                <div className="mb-3 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
                  <CardTitle className="truncate text-lg font-semibold transition-colors group-hover:text-primary sm:text-xl">
                    {section.title}
                  </CardTitle>
                  {section.repeatable && (
                    <Badge variant="secondary" className="w-fit text-xs font-medium">
                      <Repeat className="mr-1 size-3" />
                      Repeatable
                    </Badge>
                  )}
                </div>
                {section.description && (
                  <p className="mb-4 line-clamp-2 text-sm leading-relaxed text-muted-foreground">
                    {section.description}
                  </p>
                )}
                <div className="flex flex-wrap items-center gap-4 text-xs sm:gap-6">
                  <div className="flex items-center gap-1">
                    <span className="font-semibold text-foreground">{section.questions.length}</span>
                    <span className="text-muted-foreground">question{section.questions.length !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-muted-foreground">Position</span>
                    <span className="font-semibold text-foreground">{section.order + 1}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex shrink-0 items-center gap-2 opacity-60 transition-opacity group-hover:opacity-100">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleActive();
                }}
                className="size-10 touch-manipulation p-0 text-muted-foreground hover:text-primary"
                data-tour="expand-section"
              >
                {isActive ? (
                  <ChevronDown className="size-5" />
                ) : (
                  <ChevronRight className="size-5" />
                )}
              </Button>

              <div className="relative">
                <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                    className="size-10 touch-manipulation p-0 text-muted-foreground hover:text-primary"
                  >
                    <MoreHorizontal className="size-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="bottom"
                  align="end"
                  sideOffset={8}
                  collisionPadding={12}
                  className="w-48"
                >
                  <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
                    <Edit className="mr-2 size-4" />
                    Edit Section
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onDuplicate}>
                    <Copy className="mr-2 size-4" />
                    Duplicate Section
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-destructive focus:text-destructive hover:text-destructive"
                  >
                    <Trash2 className="mr-2 size-4" />
                    Delete Section
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              </div>
            </div>
          </div>
        </CardHeader>

        {/* Collapsible Content */}
        <AnimatePresence>
          {isActive && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <CardContent className="pt-0">
                <Separator className="mb-8" />

                {/* Questions */}
                <div className="space-y-4">
                  {section.questions.length > 0 ? (
                    <SortableContext
                      items={section.questions.map(q => `question-${q._id || q.id}`)}
                      strategy={verticalListSortingStrategy}
                    >
                      {section.questions
                        .sort((a, b) => a.order - b.order)
                        .map((question) => (
                          <QuestionCard
                            key={question._id || question.id}
                            question={question}
                            form={form}
                            isSelected={selectedQuestionId === (question._id || question.id)}
                            onUpdate={(updates) => handleUpdateQuestion(question._id || question.id!, updates)}
                            onDelete={() => handleDeleteQuestion(question._id || question.id!)}
                            onDuplicate={() => handleDuplicateQuestion(question._id || question.id!)}
                            onSelect={() => {
                              onSelectQuestion?.(question._id || question.id!);
                            }}
                          />
                        ))}
                    </SortableContext>
                  ) : (
                    <div className="py-12 text-center text-muted-foreground">
                      <HelpCircle className="mx-auto mb-4 size-10 opacity-50" />
                      <p className="text-sm">No questions in this section yet</p>
                      <p className="mt-1 text-xs">Add your first question to get started</p>
                    </div>
                  )}

                  {/* Add Question Button */}
                  <div className="pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddQuestion(true)}
                      className="h-12 min-h-10 w-full min-w-10 touch-manipulation rounded-lg border-2 border-dashed transition-colors hover:border-primary/50 hover:bg-primary/5"
                      size="lg"
                      data-tour="add-question-button"
                    >
                      <Plus className="size-5" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Dialogs */}
      <EditSectionDialog
        section={section}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={async (data) => {
          try {
            await onUpdate(data);
            toast({
              title: 'Success',
              description: 'Section updated successfully'
            });
          } catch (err) {
            console.error('Error updating section:', err);
            toast({
              title: 'Error',
              description: 'Failed to update section',
              variant: 'destructive'
            });
            throw err; // Re-throw to let EditSectionDialog handle the error
          }
        }}
      />

      <AddQuestionDialog
        open={showAddQuestion}
        onOpenChange={setShowAddQuestion}
        onAdd={handleAddQuestion}
        form={form}
        sectionId={section._id || section.id!}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Section</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{section.title}"? This will also delete all questions in this section. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete();
                setShowDeleteDialog(false);
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Section
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
