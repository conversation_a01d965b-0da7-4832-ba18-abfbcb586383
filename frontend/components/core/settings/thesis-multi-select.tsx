"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronDown, X, Plus, Check, type Icon as LucideIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

interface ThesisMultiSelectProps {
  label: string
  description?: string
  icon?: LucideIcon
  options: string[]
  values: string[]
  onChange: (values: string[]) => void
  placeholder?: string
  maxSelections?: number
}

export function ThesisMultiSelect({
  label,
  description,
  icon: Icon,
  options,
  values,
  onChange,
  placeholder = "Select options...",
  maxSelections = 5
}: ThesisMultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [otherValue, setOtherValue] = useState("")
  const [showOtherInput, setShowOtherInput] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const otherInputRef = useRef<HTMLInputElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setShowOtherInput(false)
        setOtherValue("")
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Focus other input when it becomes visible
  useEffect(() => {
    if (showOtherInput && otherInputRef.current) {
      otherInputRef.current.focus()
    }
  }, [showOtherInput])

  const handleOptionToggle = (option: string) => {
    if (option === "Other") {
      setShowOtherInput(true)
      return
    }

    const isSelected = values.includes(option)
    let newValues: string[]

    if (isSelected) {
      newValues = values.filter(v => v !== option)
    } else {
      if (values.length >= maxSelections) {
        return // Don't add if at max selections
      }
      newValues = [...values, option]
    }

    onChange(newValues)
  }

  const handleOtherSubmit = () => {
    const trimmedValue = otherValue.trim()
    if (!trimmedValue) return

    const otherEntry = `Other: ${trimmedValue}`
    
    // Check if this exact "Other" entry already exists
    if (values.includes(otherEntry)) {
      setOtherValue("")
      setShowOtherInput(false)
      return
    }

    // Check if we're at max selections
    if (values.length >= maxSelections) {
      return
    }

    const newValues = [...values, otherEntry]
    onChange(newValues)
    setOtherValue("")
    setShowOtherInput(false)
  }

  const handleOtherKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      handleOtherSubmit()
    } else if (e.key === "Escape") {
      setShowOtherInput(false)
      setOtherValue("")
    }
  }

  const removeValue = (valueToRemove: string) => {
    const newValues = values.filter(v => v !== valueToRemove)
    onChange(newValues)
  }

  const availableOptions = options.filter(option => {
    if (option === "Other") return true
    return !values.includes(option)
  })

  const isAtMaxSelections = values.length >= maxSelections

  return (
    <div className="space-y-3">
      {/* Label and Description */}
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          {Icon && <Icon className="size-4 text-gray-500" />}
          <Label className="text-sm font-medium text-gray-900">{label}</Label>
          <span className="text-xs text-gray-400">
            ({values.length}/{maxSelections})
          </span>
        </div>
        {description && (
          <p className="text-xs text-gray-600">{description}</p>
        )}
      </div>

      {/* Selected Values */}
      <AnimatePresence>
        {values.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="flex flex-wrap gap-2"
          >
            {values.map((value, index) => (
              <motion.div
                key={value}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ delay: index * 0.05 }}
              >
                <Badge
                  variant="secondary"
                  className={cn(
                    "flex items-center gap-1.5 px-2.5 py-1 text-sm",
                    "bg-blue-50 text-blue-700 hover:bg-blue-100",
                    "transition-colors duration-200"
                  )}
                >
                  <span className="truncate max-w-32">{value}</span>
                  <button
                    onClick={() => removeValue(value)}
                    className="ml-1 rounded-full p-0.5 hover:bg-blue-200 transition-colors"
                  >
                    <X className="size-3" />
                  </button>
                </Badge>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Dropdown */}
      <div className="relative" ref={dropdownRef}>
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          disabled={isAtMaxSelections}
          className={cn(
            "w-full justify-between text-left font-normal",
            "touch-target h-12 px-4",
            isOpen && "ring-2 ring-blue-500 ring-offset-2",
            isAtMaxSelections && "opacity-50 cursor-not-allowed"
          )}
        >
          <span className={cn(
            values.length === 0 ? "text-gray-500" : "text-gray-900"
          )}>
            {values.length === 0 ? placeholder : `${values.length} selected`}
          </span>
          <ChevronDown className={cn(
            "size-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )} />
        </Button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={cn(
                "absolute top-full z-50 mt-2 w-full",
                "rounded-xl border bg-white shadow-lg",
                "max-h-64 overflow-y-auto"
              )}
            >
              <div className="p-2 space-y-1">
                {availableOptions.map((option) => {
                  const isSelected = values.includes(option)
                  
                  return (
                    <motion.button
                      key={option}
                      type="button"
                      onClick={() => handleOptionToggle(option)}
                      disabled={!isSelected && isAtMaxSelections}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2.5 text-left",
                        "rounded-lg transition-colors duration-150",
                        "hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",
                        isSelected && "bg-blue-50 text-blue-700"
                      )}
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.99 }}
                    >
                      <div className={cn(
                        "size-4 rounded border-2 flex items-center justify-center",
                        isSelected 
                          ? "bg-blue-600 border-blue-600" 
                          : "border-gray-300"
                      )}>
                        {isSelected && <Check className="size-3 text-white" />}
                      </div>
                      <span className="flex-1 text-sm font-medium">{option}</span>
                      {option === "Other" && <Plus className="size-4 text-gray-400" />}
                    </motion.button>
                  )
                })}

                {/* Other Input Field */}
                <AnimatePresence>
                  {showOtherInput && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="px-3 py-2 border-t"
                    >
                      <div className="flex gap-2">
                        <Input
                          ref={otherInputRef}
                          value={otherValue}
                          onChange={(e) => setOtherValue(e.target.value)}
                          onKeyDown={handleOtherKeyDown}
                          placeholder="Enter your custom value"
                          className="flex-1 text-sm"
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={handleOtherSubmit}
                          disabled={!otherValue.trim()}
                          className="px-3"
                        >
                          Add
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Max selections warning */}
      {isAtMaxSelections && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-xs text-amber-600 flex items-center gap-1"
        >
          <span>Maximum {maxSelections} selections reached</span>
        </motion.p>
      )}
    </div>
  )
}
