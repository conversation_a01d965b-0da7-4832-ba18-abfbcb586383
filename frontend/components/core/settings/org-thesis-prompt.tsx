"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Target, X, <PERSON>R<PERSON>, Settings } from "lucide-react"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useOrgThesisCheck } from "@/lib/hooks/use-org-thesis-check"

interface OrgThesisPromptProps {
  variant?: "banner" | "popover"
  className?: string
}

export function OrgThesisPrompt({ variant = "banner", className }: OrgThesisPromptProps) {
  const router = useRouter()
  const { showPrompt, dismissPrompt } = useOrgThesisCheck()
  const [isVisible, setIsVisible] = useState(true)

  const handleGoToSettings = () => {
    router.push("/settings/org-thesis")
    dismissPrompt()
  }

  const handleDismiss = () => {
    setIsVisible(false)
    setTimeout(dismissPrompt, 300) // Wait for animation
  }

  if (!showPrompt || !isVisible) {
    return null
  }

  if (variant === "banner") {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={`relative w-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg ${className}`}
        >
          <div className="px-4 py-3 sm:px-6 sm:py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* <div className="flex size-8 items-center justify-center rounded-lg bg-blue-100">
                  <Target className="size-4 text-blue-600" />
                </div> */}
                <div>
                  <h4 className="text-base font-medium text-gray-900">
                    Complete Your Investment Thesis
                  </h4>
                  <p className="text-xs text-gray-600">
                    Set up your organization's investment preferences to get better deal matching
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleGoToSettings}
                  className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1.5 h-auto"
                >
                  <Settings className="size-3 mr-1" />
                  Setup Now
                  <ArrowRight className="size-3 ml-1" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismiss}
                  className="text-gray-400 hover:text-gray-600 p-1 h-auto"
                >
                  <X className="size-4" />
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    )
  }

  // Popover variant (floating card)
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className={`fixed bottom-4 right-4 z-50 w-80 ${className}`}
      >
        <Card className="border border-blue-200 bg-white shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <div className="flex size-8 items-center justify-center rounded-lg bg-blue-100">
                  <Target className="size-4 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-sm">Setup Investment Thesis</CardTitle>
                  <Badge variant="outline" className="mt-1 text-xs">
                    2 min setup
                  </Badge>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="p-1 h-auto text-gray-400 hover:text-gray-600"
              >
                <X className="size-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <CardDescription className="text-xs text-gray-600 mb-4">
              Configure your organization's investment preferences (geography, sectors, stages) to improve deal matching and scoring.
            </CardDescription>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleGoToSettings}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs h-8"
              >
                <Settings className="size-3 mr-1" />
                Go to Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDismiss}
                className="text-xs h-8 px-3"
              >
                Later
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
} 