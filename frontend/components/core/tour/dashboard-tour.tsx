"use client"

import { useTour } from "./enhanced-tour-provider"
import { Star } from "lucide-react"
import { UnifiedTourButton } from "./unified-tour-button"

export function DashboardTour() {
  const { startTour, hasCompletedTour } = useTour()

  // Removed auto-start tour logic - tours should only start when user clicks "Take a Tour"
  // The onboarding logic remains untouched and handles the main onboarding flow

  return (
    <UnifiedTourButton
      tourType="dashboard"
      icon={Star}
      label="Take Tour"
      iconColor="text-purple-500"
      gradientFrom="from-purple-500"
      gradientTo="to-blue-500"
    />
  )
} 