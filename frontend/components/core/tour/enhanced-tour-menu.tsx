"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  Play, 
  CheckCircle, 
  Clock, 
  HelpCircle, 
  ChevronDown,
  BookOpen,
  Target,
  FileText,
  FolderOpen,
  Settings,
  BarChart3,
  Zap,
  ArrowRight,
  Pause,
  RotateCcw
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useEnhancedTour, useAutoTour } from "./enhanced-tour-provider"
import { tourConfigurations, TourType } from "./tour-steps"
import { cn } from "@/lib/utils"

const tourIcons: Record<TourType, any> = {
  onboarding: Play,
  dashboard: BarChart3,
  deals: FolderOpen,
  forms: FileText,
  theses: Target,
  workflow: BookOpen,
  settings: Settings,
  "form-builder": FileText,
  "thesis-builder": Target,
  "deal-detail": FolderOpen
}

interface EnhancedTourMenuProps {
  variant?: 'button' | 'icon' | 'minimal'
  className?: string
}

export function EnhancedTourMenu({ variant = 'button', className }: EnhancedTourMenuProps) {
  const { startTour, hasCompletedTour, engineProgress, isEngineRunning } = useEnhancedTour()
  const { startOnboardingFlow, stopOnboardingFlow, triggerOnboardingFlow, shouldShowFirstTimeTour } = useAutoTour()
  const [isOpen, setIsOpen] = useState(false)

  const handleStartTour = (tourType: TourType) => {
    startTour(tourType)
    setIsOpen(false)
  }

  const handleStartOnboarding = () => {
    triggerOnboardingFlow()
    setIsOpen(false)
  }

  const handleStopOnboarding = () => {
    stopOnboardingFlow()
    setIsOpen(false)
  }

  const availableTours = Object.entries(tourConfigurations).map(([key, config]) => ({
    type: key as TourType,
    ...config,
    completed: hasCompletedTour(key as TourType)
  }))

  // Create simplified tour structure
  const mainOnboardingTour = {
    tourId: "onboarding",
    name: "Complete Onboarding", 
    route: "/dashboard",
    completed: engineProgress.percentage === 100
  }

  const productTours = ['dashboard', 'deals', 'theses', 'forms'].map(tourId => {
    const config = tourConfigurations[tourId as TourType]
    return {
      tourId,
      ...config,
      completed: hasCompletedTour(tourId as TourType)
    }
  })

  // Combine main onboarding + product tours for the onboardingTours array
  const onboardingTours = [mainOnboardingTour, ...productTours]

  // Other tours not in the main flow
  const otherTours = availableTours.filter(
    tour => !['dashboard', 'deals', 'theses', 'forms', 'onboarding'].includes(tour.type)
  )

  const completedCount = availableTours.filter(tour => tour.completed).length
  const totalCount = availableTours.length

  if (variant === 'icon') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("relative", className)}
          >
            <HelpCircle className="size-4" />
            {engineProgress.percentage < 100 && (
              <span className="absolute -top-1 -right-1 size-2 bg-cyan-500 rounded-full animate-pulse" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <EnhancedTourMenuContent 
          onboardingTours={onboardingTours}
          otherTours={otherTours}
          onStartTour={handleStartTour}
          onStartOnboarding={handleStartOnboarding}
          onStopOnboarding={handleStopOnboarding}
          engineProgress={engineProgress}
          isEngineRunning={isEngineRunning}
          shouldAutoTrigger={shouldShowFirstTimeTour()}
        />
      </DropdownMenu>
    )
  }

  if (variant === 'minimal') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("text-xs text-gray-500 hover:text-gray-700", className)}
          >
            Help & Tours
            <ChevronDown className="size-3 ml-1" />
            {engineProgress.percentage < 100 && (
              <span className="ml-2 size-2 bg-cyan-500 rounded-full animate-pulse" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <EnhancedTourMenuContent 
          onboardingTours={onboardingTours}
          otherTours={otherTours}
          onStartTour={handleStartTour}
          onStartOnboarding={handleStartOnboarding}
          onStopOnboarding={handleStopOnboarding}
          engineProgress={engineProgress}
          isEngineRunning={isEngineRunning}
          shouldAutoTrigger={shouldShowFirstTimeTour()}
        />
      </DropdownMenu>
    )
  }

  // Default button variant
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={cn("", className)}>
          <HelpCircle className="size-4 mr-2" />
          Tours & Help
          {engineProgress.percentage < 100 && (
            <Badge variant="secondary" className="ml-2 size-2 p-0 bg-cyan-500" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <EnhancedTourMenuContent 
        onboardingTours={onboardingTours}
        otherTours={otherTours}
        onStartTour={handleStartTour}
        onStartOnboarding={handleStartOnboarding}
        onStopOnboarding={handleStopOnboarding}
        engineProgress={engineProgress}
        isEngineRunning={isEngineRunning}
        shouldAutoTrigger={shouldShowFirstTimeTour()}
      />
    </DropdownMenu>
  )
}

interface EnhancedTourMenuContentProps {
  onboardingTours: any[]
  otherTours: any[]
  onStartTour: (tourType: TourType) => void
  onStartOnboarding: () => void
  onStopOnboarding: () => void
  engineProgress: { completed: number; total: number; percentage: number }
  isEngineRunning: boolean
  shouldAutoTrigger: boolean
}

function EnhancedTourMenuContent({ 
  onboardingTours, 
  otherTours, 
  onStartTour, 
  onStartOnboarding, 
  onStopOnboarding,
  engineProgress,
  isEngineRunning,
  shouldAutoTrigger
}: EnhancedTourMenuContentProps) {
  return (
    <DropdownMenuContent align="end" className="w-80 p-0">
      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-gray-900">Product Tours</h3>
          <Badge variant="secondary" className="text-xs">
            {engineProgress.completed}/{engineProgress.total} completed
          </Badge>
        </div>
        
        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress value={engineProgress.percentage} className="h-2" />
          <p className="text-xs text-gray-600">
            {engineProgress.percentage === 100 
              ? "All onboarding tours completed! 🎉" 
              : `${engineProgress.percentage}% complete`
            }
          </p>
        </div>
      </div>

      {/* Onboarding Flow Section */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <DropdownMenuLabel className="p-0 font-medium text-sm">
            Onboarding Flow
          </DropdownMenuLabel>
          
        </div>

        {/* Onboarding Tours List */}
        <div className="space-y-2">
          {onboardingTours.map((tour, index) => {
            const Icon = tourIcons[tour.tourId]
            const isNext = !tour.completed && index === onboardingTours.findIndex(t => !t.completed)
            
            return (
              <motion.div
                key={tour.tourId}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => onStartTour(tour.tourId)}
                className={cn(
                  "flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all",
                  "hover:bg-gray-50 border",
                  tour.completed ? "bg-green-50 border-green-200" : "border-gray-200",
                  isNext && isEngineRunning ? "ring-2 ring-purple-500 bg-purple-50" : ""
                )}
              >
                <div className={cn(
                  "flex size-8 items-center justify-center rounded-lg flex-shrink-0",
                  tour.completed 
                    ? "bg-green-100 text-green-600" 
                    : isNext && isEngineRunning
                    ? "bg-purple-100 text-purple-600"
                    : "bg-gray-100 text-gray-600"
                )}>
                  {tour.completed ? (
                    <CheckCircle className="size-4" />
                  ) : isNext && isEngineRunning ? (
                    <motion.div animate={{ rotate: 360 }} transition={{ duration: 2, repeat: Infinity, ease: "linear" }}>
                      <Zap className="size-4" />
                    </motion.div>
                  ) : (
                    <Icon className="size-4" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm text-gray-900 truncate">
                      {tour.name}
                    </h4>
                    {isNext && isEngineRunning && (
                      <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700 border-purple-300">
                        Next
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 line-clamp-1">
                    {tour.description}
                  </p>
                </div>

                <div className="flex items-center">
                  {tour.completed ? (
                    <RotateCcw className="size-3 text-gray-400" />
                  ) : (
                    <ArrowRight className="size-3 text-gray-400" />
                  )}
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Other Tours Section */}
      {otherTours.length > 0 && (
        <div className="p-4">
          <DropdownMenuLabel className="px-0 pb-3 font-medium text-sm">
            Additional Tours
          </DropdownMenuLabel>
          
          <div className="space-y-2">
            {otherTours.map((tour) => {
              const Icon = tourIcons[tour.type]
              
              return (
                <DropdownMenuItem
                  key={tour.type}
                  onClick={() => onStartTour(tour.type)}
                  className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50 rounded-lg"
                >
                  <div className={cn(
                    "flex size-7 items-center justify-center rounded-lg flex-shrink-0",
                    tour.completed 
                      ? "bg-green-100 text-green-600" 
                      : "bg-gray-100 text-gray-600"
                  )}>
                    {/* {tour.completed ? (
                      <CheckCircle className="size-3" />
                    ) : (
                      <Icon className="size-3" />
                    )} */}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 truncate">
                      {tour.name}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Clock className="size-3" />
                        {tour.steps.length} steps
                      </div>
                    </div>
                  </div>
                </DropdownMenuItem>
              )
            })}
          </div>
        </div>
      )}
    </DropdownMenuContent>
  )
}

// Enhanced floating tour button for first-time users
export function EnhancedFloatingTourButton({ className }: { className?: string }) {
  const { triggerOnboardingFlow, shouldShowFirstTimeTour } = useAutoTour()
  const [isVisible, setIsVisible] = useState(true)

  // Show for first-time users who should see the tour
  if (!shouldShowFirstTimeTour() || !isVisible) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 20 }}
      className={cn(
        "fixed bottom-6 right-6 z-50",
        className
      )}
    >
      <Button
        onClick={triggerOnboardingFlow}
        className={cn(
          "bg-gradient-to-r from-purple-500 to-blue-500 text-white",
          "hover:from-purple-600 hover:to-blue-600",
          "shadow-lg hover:shadow-xl transition-all duration-300",
          "flex items-center gap-2 px-6 py-3 rounded-full",
          "animate-pulse"
        )}
      >
        <Play className="size-4" />
        Take the Tour
        <ArrowRight className="size-4" />
      </Button>
      
      {/* Dismiss button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsVisible(false)}
        className="absolute -top-2 -right-2 size-6 p-0 bg-white shadow-sm rounded-full text-gray-400 hover:text-gray-600"
      >
        ×
      </Button>
    </motion.div>
  )
} 