"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, <PERSON>R<PERSON>, ArrowLeft, Play, SkipForward } from "lucide-react"
import { createPortal } from "react-dom"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface TourStep {
  id: string
  title: string
  content: string
  target: string // CSS selector for the element to highlight
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  action?: 'click' | 'hover' | 'none'
  waitForElement?: boolean
  onBeforeStep?: () => void
  onAfterStep?: () => void
}

interface ProductTourProps {
  steps: TourStep[]
  isActive: boolean
  onComplete: () => void
  onSkip: () => void
  onClose: () => void
  autoStart?: boolean
  initialStepIndex?: number
  onStepIndexChange?: (index: number) => void
}

interface SpotlightProps {
  target: Element
  children: React.ReactNode
}

// Spotlight component that creates a precise cutout effect
function Spotlight({ target, children }: SpotlightProps) {
  const [position, setPosition] = useState({ x: 0, y: 0, width: 0, height: 0 })

  useEffect(() => {
    const updatePosition = () => {
      const rect = target.getBoundingClientRect()
      setPosition({
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height
      })
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    return () => {
      window.removeEventListener('resize', updatePosition)
      window.removeEventListener('scroll', updatePosition)
    }
  }, [target])

  const spotlightPadding = 12
  const spotlightX = position.x - spotlightPadding
  const spotlightY = position.y - spotlightPadding
  const spotlightWidth = position.width + (spotlightPadding * 2)
  const spotlightHeight = position.height + (spotlightPadding * 2)

  return createPortal(
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="fixed inset-0 z-[9999] pointer-events-none"
    >
      {/* Premium glassmorphic overlay with rounded cutout */}
      <div
        className="absolute inset-0 bg-white/10 transition-all duration-300"
        style={{
          clipPath: `path('M 0 0 L 0 100% L 100% 100% L 100% 0 Z M ${spotlightX + 16} ${spotlightY + 16} A 16 16 0 0 1 ${spotlightX + 16} ${spotlightY} L ${spotlightX + spotlightWidth - 16} ${spotlightY} A 16 16 0 0 1 ${spotlightX + spotlightWidth} ${spotlightY + 16} L ${spotlightX + spotlightWidth} ${spotlightY + spotlightHeight - 16} A 16 16 0 0 1 ${spotlightX + spotlightWidth - 16} ${spotlightY + spotlightHeight} L ${spotlightX + 16} ${spotlightY + spotlightHeight} A 16 16 0 0 1 ${spotlightX} ${spotlightY + spotlightHeight - 16} L ${spotlightX} ${spotlightY + 16} A 16 16 0 0 1 ${spotlightX + 16} ${spotlightY} Z')`,
          isolation: 'isolate'
        }}
      />
      
      {/* Subtle gradient overlay for depth */}
      <div
        className="absolute inset-0 bg-gradient-to-br from-purple-500/2 to-blue-500/2 transition-all duration-300"
        style={{
          clipPath: `path('M 0 0 L 0 100% L 100% 100% L 100% 0 Z M ${spotlightX + 16} ${spotlightY + 16} A 16 16 0 0 1 ${spotlightX + 16} ${spotlightY} L ${spotlightX + spotlightWidth - 16} ${spotlightY} A 16 16 0 0 1 ${spotlightX + spotlightWidth} ${spotlightY + 16} L ${spotlightX + spotlightWidth} ${spotlightY + spotlightHeight - 16} A 16 16 0 0 1 ${spotlightX + spotlightWidth - 16} ${spotlightY + spotlightHeight} L ${spotlightX + 16} ${spotlightY + spotlightHeight} A 16 16 0 0 1 ${spotlightX} ${spotlightY + spotlightHeight - 16} L ${spotlightX} ${spotlightY + 16} A 16 16 0 0 1 ${spotlightX + 16} ${spotlightY} Z')`
        }}
      />

      {/* Premium highlighted border with glow effect */}
      <motion.div
        initial={{ opacity: 0, scale: 0.98 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className="absolute z-[10000]"
        style={{
          left: spotlightX,
          top: spotlightY,
          width: spotlightWidth,
          height: spotlightHeight,
        }}
      >
        {/* Glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-sm" />
        
        {/* Border with premium styling */}
        <div className="absolute inset-0 rounded-2xl border-2 border-white/80 shadow-2xl shadow-purple-500/25" />
        
        {/* Inner highlight */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent" />
        
        {/* Subtle animated pulse */}
        <motion.div
          animate={{
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute inset-0 rounded-2xl"
          style={{
            outline: '1px solid rgb(147 51 234 / 0.4)',
            outlineOffset: '2px'
          }}
        />
      </motion.div>

      {children}
    </motion.div>,
    document.body
  )
}

// Tooltip component for tour content
interface TooltipProps {
  step: TourStep
  currentStep: number
  totalSteps: number
  onNext: () => void
  onPrev: () => void
  onSkip: () => void
  onClose: () => void
  target: Element
}

function TourTooltip({
  step,
  currentStep,
  totalSteps,
  onNext,
  onPrev,
  onSkip,
  onClose,
  target
}: TooltipProps) {
  const [position, setPosition] = useState({ x: 0, y: 0, placement: 'bottom' as 'top' | 'bottom' })
  const [arrowPosition, setArrowPosition] = useState({ x: 0, show: false })
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updatePosition = () => {
      const targetRect = target.getBoundingClientRect()
      const tooltipRect = tooltipRef.current?.getBoundingClientRect()

      if (!tooltipRect) return

      const spotlightPadding = 8
      const tooltipWidth = 320 // Fixed width for consistency
      const tooltipHeight = tooltipRect.height
      const gap = 16

      let x = 0
      let y = 0
      let placement: 'top' | 'bottom' = 'bottom'
      let arrowX = 0
      let showArrow = false

      // Calculate spotlight bounds
      const spotlightTop = targetRect.top - spotlightPadding
      const spotlightBottom = targetRect.bottom + spotlightPadding
      const spotlightLeft = targetRect.left - spotlightPadding
      const spotlightRight = targetRect.right + spotlightPadding
      const spotlightCenterX = spotlightLeft + (spotlightRight - spotlightLeft) / 2

      // Determine placement based on available space and element position
      const spaceAbove = spotlightTop - gap
      const spaceBelow = window.innerHeight - spotlightBottom - gap
      const isElementNearTop = spotlightTop < window.innerHeight * 0.3 // Element is in top 30% of screen
      const isElementNearBottom = spotlightBottom > window.innerHeight * 0.7 // Element is in bottom 30% of screen

      if (step.placement === 'center') {
        // Center placement
        x = window.innerWidth / 2 - tooltipWidth / 2
        y = window.innerHeight / 2 - tooltipHeight / 2
        showArrow = false
      } else {
        // Smart placement based on element position and available space
        if (isElementNearTop && spaceAbove >= tooltipHeight * 0.8) {
          // Element is near top and there's enough space above - place above
          placement = 'top'
          y = Math.max(16, spotlightTop - gap - tooltipHeight)
        } else if (isElementNearBottom && spaceBelow >= tooltipHeight * 0.8) {
          // Element is near bottom and there's enough space below - place below
          placement = 'bottom'
          y = Math.min(window.innerHeight - tooltipHeight - 16, spotlightBottom + gap)
        } else if (spaceBelow >= tooltipHeight || spaceBelow > spaceAbove) {
          // Default: place below if more space available
          placement = 'bottom'
          y = spotlightBottom + gap
        } else {
          // Place above if more space available
          placement = 'top'
          y = spotlightTop - gap - tooltipHeight
        }

        // Center horizontally relative to spotlight
        x = spotlightCenterX - tooltipWidth / 2
        
        // SURGICAL FIX: Only for nav-to-theses step - position to the right to avoid blocking
        if (step.target === "[data-tour='nav-theses']") {
          x = spotlightRight + gap
          showArrow = false
        }

        // Keep within viewport horizontally
        const minX = 16
        const maxX = window.innerWidth - tooltipWidth - 16
        x = Math.max(minX, Math.min(x, maxX))

        // Calculate arrow position
        arrowX = spotlightCenterX - x
        showArrow = arrowX >= 16 && arrowX <= tooltipWidth - 16 // Only show if arrow would be within tooltip bounds
      }

      // Keep within viewport vertically with extra padding for top placement
      const minY = position.placement === 'top' ? 24 : 16 // Extra padding when tooltip is above
      y = Math.max(minY, Math.min(y, window.innerHeight - tooltipHeight - 16))

      setPosition({ x, y, placement })
      setArrowPosition({ x: arrowX, show: showArrow })
    }

    // Initial positioning
    const timer = setTimeout(updatePosition, 50)

    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    return () => {
      clearTimeout(timer)
      window.removeEventListener('resize', updatePosition)
      window.removeEventListener('scroll', updatePosition)
    }
  }, [step.placement, target])

  return (
    <motion.div
      ref={tooltipRef}
      initial={{ opacity: 0, scale: 0.95, y: position.placement === 'top' ? 10 : -10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: position.placement === 'top' ? 10 : -10 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={cn(
        "fixed z-[10000] pointer-events-auto",
        "bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20",
        "w-[320px] max-w-[calc(100vw-32px)] relative overflow-hidden",
        "focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2",
        "[&_*]:!bg-transparent"
      )}
      style={{
        left: position.x,
        top: position.y
      }}
    >
      {/* Premium gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/40 via-white/20 to-blue-100/40" />
      
      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-15 bg-gradient-to-r from-purple-500 to-blue-500" />
      
      {/* Additional glassmorphic layer */}
      <div className="absolute inset-0 bg-white/10 backdrop-blur-sm" />
      
      {/* Content container */}
      <div className="relative z-10 p-6" style={{ backgroundColor: 'transparent !important' }}>
      
      {/* Premium arrow */}
      {arrowPosition.show && (
        <div
          className={cn(
            "absolute w-4 h-4 bg-white/95 backdrop-blur-xl border border-white/30 rotate-45 shadow-lg",
            position.placement === 'bottom' ? "-top-2 border-b-0 border-r-0" : "-bottom-2 border-t-0 border-l-0"
          )}
          style={{
            left: arrowPosition.x - 8
          }}
        />
      )}
      
              {/* Content */}
        <div className="space-y-5" style={{ backgroundColor: 'transparent !important' }}>
        {/* Header */}
        <div className="flex items-start justify-between bg-transparent">
          <Badge variant="secondary" className="text-xs font-medium bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0 shadow-sm">
            {currentStep} of {totalSteps}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-200"
          >
            <X className="size-4" />
          </Button>
        </div>

        {/* Main Content */}
        <div className="space-y-3" style={{ backgroundColor: 'transparent !important' }}>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 leading-tight" style={{ backgroundColor: 'transparent !important' }}>{step.title}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed" style={{ backgroundColor: 'transparent !important' }}>{step.content}</p>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-2 bg-transparent">
          <div className="flex items-center">
            {currentStep > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onPrev}
                className="flex items-center gap-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="size-3.5" />
                Back
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onSkip}
              className="text-sm text-gray-500 hover:text-gray-700 hover:bg-white/20 backdrop-blur-sm underline underline-offset-2 px-3 py-1.5 rounded-lg transition-all duration-200"
            >
              Skip Tour
            </Button>

            <Button
              onClick={onNext}
              size="sm"
              className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white text-sm font-medium px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
            >
              {currentStep === totalSteps ? 'Finish' : 'Next'}
              <ArrowRight className="size-3.5 ml-1.5" />
            </Button>
          </div>
        </div>

        {/* Progress dots */}
        <div className="flex items-center justify-center gap-1.5 pt-4 border-t border-white/20">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={cn(
                "h-1.5 rounded-full transition-all duration-300",
                i + 1 === currentStep
                  ? "bg-gradient-to-r from-purple-500 to-blue-500 w-8 shadow-sm"
                  : i + 1 < currentStep
                    ? "bg-purple-400/60 w-1.5"
                    : "bg-gray-300/50 w-1.5"
              )}
            />
          ))}
        </div>
      </div>
      </div>
    </motion.div>
  )
}

export function ProductTour({
  steps,
  isActive,
  onComplete,
  onSkip,
  onClose,
  autoStart = false,
  initialStepIndex = 0,
  onStepIndexChange
}: ProductTourProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStepIndex)
  const [targetElement, setTargetElement] = useState<Element | null>(null)
  const [isReady, setIsReady] = useState(false)

  const currentStep = steps[currentStepIndex]

  console.log('🔍 ProductTour: Render state', {
    isActive,
    stepsLength: steps.length,
    currentStepIndex,
    currentStep: currentStep?.id,
    targetElement: !!targetElement,
    isReady,
    pathname: window.location.pathname
  })

  // Keyboard navigation
  useEffect(() => {
    if (!isActive) return

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          event.preventDefault()
          onClose()
          break
        case 'ArrowRight':
        case 'Enter':
        case ' ':
          event.preventDefault()
          handleNext()
          break
        case 'ArrowLeft':
          event.preventDefault()
          handlePrev()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isActive, currentStepIndex, steps.length])

  // Find target element for current step
  useEffect(() => {
    if (!isActive || !currentStep) return

    const findTarget = () => {
      console.log('ProductTour: Finding target for step:', currentStep.id, 'target:', currentStep.target)
      const element = document.querySelector(currentStep.target)
      console.log('ProductTour: Element found:', !!element)
      if (element) {
        setTargetElement(element)

        // Smooth scroll with better positioning
        const elementRect = element.getBoundingClientRect()
        const isInViewport = (
          elementRect.top >= 0 &&
          elementRect.left >= 0 &&
          elementRect.bottom <= window.innerHeight &&
          elementRect.right <= window.innerWidth
        )

        if (!isInViewport) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          })
        }

        // Small delay to ensure scrolling completes before showing tour
        setTimeout(() => {
          setIsReady(true)
          currentStep.onBeforeStep?.()
        }, isInViewport ? 100 : 800)

      } else if (currentStep.waitForElement) {
        // Wait for element to appear with timeout
        let attempts = 0
        const maxAttempts = 50 // 5 seconds max wait

        const checkForElement = () => {
          const element = document.querySelector(currentStep.target)
          if (element) {
            setTargetElement(element)

            element.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center'
            })

            setTimeout(() => {
              setIsReady(true)
              currentStep.onBeforeStep?.()
            }, 800)
          } else if (attempts < maxAttempts) {
            attempts++
            setTimeout(checkForElement, 100)
          } else {
            console.warn(`Tour: Element not found after ${maxAttempts} attempts:`, currentStep.target)
          }
        }

        checkForElement()
      }
    }

    setIsReady(false)
    const timer = setTimeout(findTarget, 100)
    return () => clearTimeout(timer)
  }, [currentStep, isActive, currentStepIndex])

  const handleNext = useCallback(() => {
    currentStep?.onAfterStep?.()
    
    if (currentStepIndex < steps.length - 1) {
      const newIndex = currentStepIndex + 1
      setCurrentStepIndex(newIndex)
      onStepIndexChange?.(newIndex)
      setIsReady(false)
    } else {
      onComplete()
    }
  }, [currentStepIndex, steps.length, onComplete, currentStep, onStepIndexChange])

  const handlePrev = useCallback(() => {
    if (currentStepIndex > 0) {
      const newIndex = currentStepIndex - 1
      setCurrentStepIndex(newIndex)
      onStepIndexChange?.(newIndex)
      setIsReady(false)
    }
  }, [currentStepIndex, onStepIndexChange])

  const handleSkip = useCallback(() => {
    onSkip()
  }, [onSkip])

  const handleClose = useCallback(() => {
    onClose()
  }, [onClose])

  // Auto-start tour
  useEffect(() => {
    if (autoStart && isActive && steps.length > 0) {
      setCurrentStepIndex(initialStepIndex)
      onStepIndexChange?.(initialStepIndex)
    }
  }, [autoStart, isActive, steps.length, initialStepIndex, onStepIndexChange])



  if (!isActive || !currentStep || !targetElement || !isReady) {
    console.log('ProductTour: Not rendering because:', {
      isActive,
      hasCurrentStep: !!currentStep,
      hasTargetElement: !!targetElement,
      isReady
    })
    return null
  }

  console.log('ProductTour: Rendering tour tooltip')

  return (
    <AnimatePresence>
      <Spotlight target={targetElement}>
        <TourTooltip
          step={currentStep}
          currentStep={currentStepIndex + 1}
          totalSteps={steps.length}
          onNext={handleNext}
          onPrev={handlePrev}
          onSkip={handleSkip}
          onClose={handleClose}
          target={targetElement}
        />
      </Spotlight>
    </AnimatePresence>
  )
}
