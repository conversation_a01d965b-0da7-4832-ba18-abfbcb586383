import { TourStep } from "./product-tour"

// UNIFIED ONBOARDING TOUR - Complete tour across all pages
export const unifiedOnboardingTourSteps: TourStep[] = [
  // === DASHBOARD SECTION ===
  {
    id: "welcome",
    title: "Welcome to TractionX",
    content: "Let's take a complete tour of your investment decision support system. This will help you understand how everything works together across all features.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "quick-stats",
    title: "Quick Statistics",
    content: "Get an instant overview of your deal pipeline, thesis matches, and recent activity. These metrics update in real-time.",
    target: "[data-tour='quick-stats']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deals-section",
    title: "Deals Overview",
    content: "This is where all your investment opportunities live. You can view, filter, and analyze deals based on your investment thesis.",
    target: "[data-tour='deals-section']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "recent-activity",
    title: "Quick Actions",
    content: "Take immediate action on deals - add deals, add forms, change status, or share with team members.",
    target: "[data-tour='recent-activity']",
    placement: "left",
    waitForElement: true
  },
  
  // === NAVIGATION TO DEALS ===
  {
    id: "nav-to-deals",
    title: "Navigate to Deals",
    content: "Now let's explore the deals section in detail. Click on 'Deals' in the sidebar to continue the tour.",
    target: "[data-tour='nav-deals']",
    placement: "left",
    waitForElement: true
  },
  
  // === DEALS SECTION ===
  {
    id: "deals-header",
    title: "Deal Management Hub",
    content: "This is your central command for managing investment opportunities. Filter, sort, and analyze deals with AI-powered insights.",
    target: "[data-tour='deals-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-filters",
    title: "Smart Filters",
    content: "Filter deals by stage, sector, thesis match score, or any custom criteria. Save your favorite filter combinations.",
    target: "[data-tour='deal-filters']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-card",
    title: "Deal Cards",
    content: "Each deal shows key metrics, thesis match percentage, and AI-generated insights. Click to dive deeper into analysis. If you don't have deals yet, you can create your first one here.",
    target: "[data-tour='deal-card'], [data-tour='new-deal-card']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "deal-actions",
    title: "Drop a Deck",
    content: "Have a deck you want to drop? Click here to upload it and start evaluating it against your thesis.",
    target: "[data-tour='deal-actions'], [data-tour='new-deal-card']",
    placement: "left",
    waitForElement: false
  },
  {
    id: "bulk-import",
    title: "Bulk Import Deals",
    content: "Already have deals in a spreadsheet? Use bulk import to quickly upload multiple deals at once and get them analyzed instantly.",
    target: "[data-tour='bulk-import']",
    placement: "bottom",
    waitForElement: true
  },
  
  // === NAVIGATION TO THESES ===
  {
    id: "nav-to-theses",
    title: "Navigate to Theses",
    content: "Great! Now let's explore your investment theses. Click on 'Theses' in the sidebar to continue.",
    target: "[data-tour='nav-theses']",
    placement: "bottom",
    waitForElement: true
  },
  
  // === THESES SECTION ===
  {
    id: "theses-header",
    title: "Investment Thesis Hub",
    content: "Define and manage your investment theses here. These are the criteria that deals will be scored against.",
    target: "[data-tour='theses-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "thesis-card",
    title: "Thesis Cards",
    content: "Each thesis card shows your investment criteria, scoring weights, and performance metrics. You can edit, activate, or create new theses.",
    target: "[data-tour='thesis-card']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "thesis-actions",
    title: "Thesis Management",
    content: "Create new investment theses, edit existing ones, or adjust scoring parameters to fine-tune your deal evaluation process.",
    target: "[data-tour='thesis-actions']",
    placement: "left",
    waitForElement: true
  },
  
  // === NAVIGATION TO FORMS ===
  {
    id: "nav-to-forms",
    title: "Navigate to Forms",
    content: "Finally, let's check out the forms section where you can create deal collection forms. Click on 'Forms' in the sidebar.",
    target: "[data-tour='nav-forms']",
    placement: "bottom",
    waitForElement: true
  },
  
  // === FORMS SECTION ===
  {
    id: "forms-header",
    title: "Deal Collection Forms",
    content: "Create custom forms to collect deal information from entrepreneurs, partners, or other sources. These feed directly into your deal pipeline.",
    target: "[data-tour='forms-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "form-creation",
    title: "Create New Form",
    content: "Click here to create a new form. You can customize questions, add conditional logic, and set up automated scoring.",
    target: "[data-tour='form-creation']",
    placement: "left",
    waitForElement: true
  },
  
  // === COMPLETION ===
  {
    id: "tour-complete",
    title: "Tour Complete! 🎉",
    content: "You've successfully completed the TractionX onboarding tour! You now understand all the key features: Dashboard insights, Deal management, Investment theses, and Deal collection forms. Start exploring and building your investment pipeline!",
    target: "[data-tour='forms-header']",
    placement: "center",
    waitForElement: true
  }
]

// Original individual page tours for manual replay
// Dashboard Tour - Main overview of the platform
export const dashboardTourSteps: TourStep[] = [
  {
    id: "welcome",
    title: "Welcome to TractionX",
    content: "Let's take a quick tour of your investment decision support system. This will help you understand how everything works together.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "quick-stats",
    title: "Quick Statistics",
    content: "Get an instant overview of your deal pipeline, thesis matches, and recent activity. These metrics update in real-time.",
    target: "[data-tour='quick-stats']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deals-section",
    title: "Deals Overview",
    content: "This is where all your investment opportunities live. You can view, filter, and analyze deals based on your investment thesis.",
    target: "[data-tour='deals-section']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "recent-activity",
    title: "Quick Actions",
    content: "Take immediate action on deals - add deals, add forms, change status, or share with team members.",
    target: "[data-tour='recent-activity']",
    placement: "left",
    waitForElement: true
  }
]

// Deals Tour - Deep dive into deal management
export const dealsTourSteps: TourStep[] = [
  {
    id: "deals-header",
    title: "Deal Management Hub",
    content: "This is your central command for managing investment opportunities. Filter, sort, and analyze deals with AI-powered insights.",
    target: "[data-tour='deals-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-filters",
    title: "Smart Filters",
    content: "Filter deals by stage, sector, thesis match score, or any custom criteria. Save your favorite filter combinations.",
    target: "[data-tour='deal-filters']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-card",
    title: "Deal Cards",
    content: "Each deal shows key metrics, thesis match percentage, and AI-generated insights. Click to dive deeper into analysis. If you don't have deals yet, you can create your first one here.",
    target: "[data-tour='deal-card'], [data-tour='new-deal-card']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "deal-actions",
    title: "Drop a Deck",
    content: "Have a deck you want to drop? Click here to upload it and start evaluating it against your thesis.",
    target: "[data-tour='deal-actions'], [data-tour='new-deal-card']",
    placement: "left",
    waitForElement: false
  },
  {
    id: "bulk-import",
    title: "Bulk Import Deals",
    content: "Already have deals in a spreadsheet? Use bulk import to quickly upload multiple deals at once and get them analyzed instantly.",
    target: "[data-tour='bulk-import']",
    placement: "bottom",
    waitForElement: true
  }
]

// Forms Tour - Understanding form creation and management
export const formsTourSteps: TourStep[] = [
  {
    id: "forms-overview",
    title: "Form Builder",
    content: "Create custom forms to collect structured data from startups. Standardize your deal intake process with intelligent form builders.",
    target: "[data-tour='forms-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "form-creation",
    title: "Create New Form",
    content: "Click here to create a new form. You can customize questions, add conditional logic, and set up automated scoring.",
    target: "[data-tour='form-creation']",
    placement: "left",
    waitForElement: true
  },
  {
    id: "form-management",
    title: "Form Management",
    content: "View, edit, and manage all your forms. Track submissions, analyze responses, and optimize your forms for better results. Click on any form to edit it and access advanced features like templates, sections, and sharing options.",
    target: "[data-tour='form-management']",
    placement: "top",
    waitForElement: true
  }
]

// Theses Tour - Investment thesis and scoring
export const thesesTourSteps: TourStep[] = [
  {
    id: "theses-overview",
    title: "Investment Theses",
    content: "Define your investment criteria and scoring rules. Let AI evaluate deals against your thesis automatically with detailed analysis.",
    target: "[data-tour='theses-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "create-thesis",
    title: "Create Thesis",
    content: "Build a new investment thesis with specific criteria, scoring rules, and matching logic. Connect it to forms for automatic evaluation.",
    target: "[data-tour='create-thesis-btn']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "thesis-card",
    title: "Thesis Cards",
    content: "Each thesis card shows your investment criteria, status, and performance metrics. Click on any thesis to edit it and configure detailed scoring rules, matching criteria, and bonus conditions.",
    target: "[data-tour='thesis-card']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "thesis-actions",
    title: "Thesis Management",
    content: "Use the actions menu to edit, delete, or manage your theses. Click 'Edit Thesis' to access the full thesis builder where you can configure detailed scoring rules and matching criteria.",
    target: "[data-tour='thesis-actions']",
    placement: "left",
    waitForElement: true
  }
]

// Settings Tour - Organization and user management
export const settingsTourSteps: TourStep[] = [
  {
    id: "settings-overview",
    title: "Organization Settings",
    content: "Manage your organization profile, team members, integrations, and platform preferences from this central hub.",
    target: "[data-tour='settings-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "org-profile",
    title: "Organization Profile",
    content: "Update your firm's information, logo, and public profile. This information appears on shared forms and public pages.",
    target: "[data-tour='org-profile']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "team-members",
    title: "Team Management",
    content: "Invite team members, assign roles, and manage permissions. Control who can access different parts of the platform.",
    target: "[data-tour='team-members']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "org-thesis-settings",
    title: "Organization Thesis",
    content: "Configure your organization-wide investment preferences. These serve as defaults for new theses and deal filtering.",
    target: "[data-tour='org-thesis']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "integrations",
    title: "Integrations",
    content: "Connect TractionX with your existing tools like CRM, email, calendar, and data providers for seamless workflow.",
    target: "[data-tour='integrations']",
    placement: "left",
    waitForElement: true
  }
]

// Complete workflow tour - End-to-end process
export const workflowTourSteps: TourStep[] = [
  {
    id: "workflow-intro",
    title: "Complete Workflow",
    content: "Let's walk through the complete TractionX workflow: from creating forms to evaluating deals with your investment thesis.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "step1-forms",
    title: "Step 1: Create Forms",
    content: "Start by creating forms to collect structured data from startups. This standardizes your deal intake process.",
    target: "[data-tour='nav-forms']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      // Could navigate to forms page
      console.log("Navigating to forms section")
    }
  },
  {
    id: "step2-theses",
    title: "Step 2: Define Theses",
    content: "Create investment theses that define your criteria and scoring rules. Connect them to your forms for automatic evaluation.",
    target: "[data-tour='nav-theses']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "step3-deals",
    title: "Step 3: Evaluate Deals",
    content: "As startups submit forms, deals are automatically created and scored against your theses. Review and take action on opportunities.",
    target: "[data-tour='nav-deals']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "workflow-complete",
    title: "Workflow Complete",
    content: "That's the complete TractionX workflow! Forms collect data, theses provide scoring, and deals give you actionable insights.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  }
]

// Form Builder Tour - Learn how to create and configure forms
export const formBuilderTourSteps: TourStep[] = [
  {
    id: "form-builder-header",
    title: "Form Builder Overview",
    content: "Welcome to the Form Builder! This is where you'll create and customize your investment forms. Let's explore the key features that will help you build powerful forms for collecting startup information.",
    target: "[data-tour='form-builder-header']",
    placement: "center"
  },
  {
    id: "add-section-button",
    title: "Adding Sections",
    content: "Sections help organize your form into logical groups. Click the '+' button to add a new section. Each section can contain multiple questions and can be made repeatable for collecting multiple instances of the same information.",
    target: "[data-tour='add-section-button']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "share-form-button",
    title: "Sharing Your Form",
    content: "Click the Share button to create public links for your form. You can generate unique URLs that allow external users to submit applications. Share these links with startups, partners, or post them on your website.",
    target: "[data-tour='share-form-button']",
    placement: "bottom"
  },
  {
    id: "preview-form-button",
    title: "Previewing Your Form",
    content: "Use the Preview button to see exactly how your form will appear to users. This helps you test the user experience, check question flow, and ensure everything works as expected before sharing.",
    target: "[data-tour='preview-form-button']",
    placement: "bottom"
  },
  {
    id: "form-settings-button",
    title: "Form Settings & Configuration",
    content: "Click the Settings button to configure your form. Here you can set the form name, description, active status, exclusion filters, and sharing options. This is where you'll control how your form behaves.",
    target: "[data-tour='form-settings-button']",
    placement: "bottom"
  },
  {
    id: "exclusion-filters",
    title: "Exclusion Filters",
    content: "Exclusion filters help you automatically filter out submissions that don't meet your criteria. You can set conditions based on company stage, sector, funding amount, and other factors to ensure you only review relevant submissions.",
    target: "[data-tour='exclusion-filters']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Ensuring form detail panel is open...');
      // Ensure the form is selected to show the detail panel
      const formHeader = document.querySelector('[data-tour="form-builder-header"]') as HTMLElement;
      if (formHeader) {
        console.log('Tour: Clicking form header to open detail panel');
        formHeader.click();
      } else {
        console.log('Tour: Form header not found');
      }
    }
  },
  {
    id: "ensure-section-visible",
    title: "Section Management",
    content: "Let's look at how to manage sections. First, make sure you have at least one section in your form. If you don't see any sections, click the 'Add Section' button we just covered.",
    target: "[data-tour='section-card']",
    placement: "center",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Ensuring section is visible...');
      // If no sections exist, create one by clicking add section button
      const sectionCards = document.querySelectorAll('[data-tour="section-card"]');
      console.log('Tour: Found', sectionCards.length, 'section cards');
      
      if (sectionCards.length === 0) {
        console.log('Tour: No sections found, creating one...');
        const addSectionButton = document.querySelector('[data-tour="add-section-button"]') as HTMLElement;
        if (addSectionButton) {
          console.log('Tour: Clicking add section button');
          addSectionButton.click();
          // Wait a bit for the section to be created
          setTimeout(() => {
            const newSectionCards = document.querySelectorAll('[data-tour="section-card"]');
            console.log('Tour: After creation, found', newSectionCards.length, 'section cards');
            if (newSectionCards.length > 0) {
              const firstSection = newSectionCards[0] as HTMLElement;
              const expandButton = firstSection.querySelector('[data-tour="expand-section"]') as HTMLElement;
              if (expandButton) {
                console.log('Tour: Expanding first section');
                expandButton.click();
              }
            }
          }, 1000);
        } else {
          console.log('Tour: Add section button not found');
        }
      } else {
        // Expand the first section
        console.log('Tour: Expanding existing section');
        const firstSection = sectionCards[0] as HTMLElement;
        const expandButton = firstSection.querySelector('[data-tour="expand-section"]') as HTMLElement;
        if (expandButton) {
          expandButton.click();
        } else {
          console.log('Tour: Expand button not found in section');
        }
      }
    }
  },
  {
    id: "add-question-button",
    title: "Adding Questions",
    content: "Within each section, you can add questions by clicking the '+' button. Choose from various question types like text, multiple choice, file upload, and more. Questions can be reordered by dragging and dropping.",
    target: "[data-tour='add-question-button']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "question-card",
    title: "Question Configuration",
    content: "Now let's configure a question! Click on any question card to open its configuration panel. Here you can change the question type, make it required, and set up visibility conditions.",
    target: "[data-tour='question-card']",
    placement: "center",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Waiting for user to click on a question...');
      // Add a visual indicator to encourage clicking
      const questionCards = document.querySelectorAll('[data-tour="question-card"]');
      questionCards.forEach(card => {
        (card as HTMLElement).style.boxShadow = '0 0 0 2px #8b5cf6, 0 4px 6px -1px rgba(0, 0, 0, 0.1)';
        (card as HTMLElement).style.transform = 'scale(1.02)';
      });
    },
    onAfterStep: () => {
      // Remove visual indicators
      const questionCards = document.querySelectorAll('[data-tour="question-card"]');
      questionCards.forEach(card => {
        (card as HTMLElement).style.boxShadow = '';
        (card as HTMLElement).style.transform = '';
      });
    }
  },
  {
    id: "question-type-config",
    title: "Question Types",
    content: "You can change the question type here. Choose from text, multiple choice, file upload, date picker, and more. Each type has specific configuration options that will appear below.",
    target: "[data-tour='question-type-select']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining question type configuration...');
      // Ensure the question configuration panel is visible
      const questionConfig = document.querySelector('[data-tour="question-type-select"]');
      if (questionConfig) {
        questionConfig.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  },
  {
    id: "question-required",
    title: "Required Questions",
    content: "Toggle this switch to make the question required. Required questions must be answered before the form can be submitted. This helps ensure you collect all necessary information.",
    target: "[data-tour='question-required']",
    placement: "left",
    waitForElement: true
  },
  {
    id: "visibility-conditions",
    title: "Visibility Conditions",
    content: "Set visibility conditions to show or hide sections and questions based on previous answers. This creates dynamic, intelligent forms that adapt to user responses, making the submission process more efficient.",
    target: "[data-tour='visibility-conditions']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Ensuring form detail panel is open for visibility conditions...');
      // Ensure the form is selected to show the detail panel
      const formHeader = document.querySelector('[data-tour="form-builder-header"]') as HTMLElement;
      if (formHeader) {
        console.log('Tour: Clicking form header to open detail panel');
        formHeader.click();
      } else {
        console.log('Tour: Form header not found');
      }
    }
  }
]

// Thesis Builder Tour - Learn how to create and configure investment theses
export const thesisBuilderTourSteps: TourStep[] = [
  {
    id: "thesis-builder-header",
    title: "Thesis Builder Overview",
    content: "Welcome to the Thesis Builder! This is where you'll create and configure your investment theses. Theses define your scoring criteria and help automatically evaluate startup submissions against your investment criteria.",
    target: "[data-tour='theses-header']",
    placement: "center"
  },
  {
    id: "thesis-basic-info",
    title: "Basic Information",
    content: "Start by providing basic information about your thesis. Give it a clear name and description that explains your investment focus and criteria. This helps team members understand your investment strategy.",
    target: "[data-tour='thesis-basic-info']",
    placement: "top"
  },
  {
    id: "form-selector",
    title: "Form Selection",
    content: "Select the form that this thesis will evaluate. The thesis will automatically score submissions from this form based on your configured rules. Make sure to choose the right form for your investment focus.",
    target: "[data-tour='form-selector']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "matching-rules-tab",
    title: "Matching Rules",
    content: "Matching rules define which submissions qualify for evaluation. These act as filters to ensure only relevant startups are scored. Click here to configure your matching criteria.",
    target: "[data-tour='matching-rules-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Matching Rules tab...');
      const matchingTab = document.querySelector('[data-tour="matching-rules-tab"]') as HTMLElement;
      if (matchingTab) {
        matchingTab.click();
      }
    }
  },
  {
    id: "add-matching-rule",
    title: "Adding Matching Rules",
    content: "Click the '+' button to add a new matching rule. Each rule defines specific criteria that submissions must meet to be considered for evaluation. You can create multiple rules for different investment stages or sectors.",
    target: "[data-tour='add-matching-rule']",
    placement: "top",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Add matching rule...');
      // No scrolling needed - let the tour system handle it
    }
  },
  {
    id: "matching-rule-config",
    title: "Configuring Matching Rules",
    content: "Configure your matching rule by setting conditions. Choose questions from your form and define what values qualify. For example, you might require companies to be in 'Series A' stage or have 'SaaS' as their sector.",
    target: "[data-tour='matching-rule-config']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining matching rule configuration...');
      const matchingTab = document.querySelector('[data-tour="matching-rules-tab"]') as HTMLElement;
      if (matchingTab) {
        matchingTab.click();
        
        // Expand and edit the first matching rule
        setTimeout(() => {
          const matchingRuleCards = document.querySelectorAll('[data-tour="matching-rule-card"]');
          if (matchingRuleCards.length > 0) {
            const firstRule = matchingRuleCards[0] as HTMLElement;
            const expandButton = firstRule.querySelector('[data-tour="expand-matching-rule"]') as HTMLElement;
            if (expandButton) {
              expandButton.click();
              setTimeout(() => {
                const editButton = firstRule.querySelector('[data-tour="edit-matching-rule"]') as HTMLElement;
                if (editButton) {
                  editButton.click();
                }
              }, 500);
            }
          }
        }, 300);
      }
    }
  },
  {
    id: "scoring-rules-tab",
    title: "Scoring Rules",
    content: "Scoring rules determine how submissions are evaluated and ranked. Each question can be assigned a weight and scoring criteria. Toggle questions on/off and configure how they contribute to the overall score.",
    target: "[data-tour='scoring-rules-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Scoring Rules tab...');
      const scoringTab = document.querySelector('[data-tour="scoring-rules-tab"]') as HTMLElement;
      if (scoringTab) {
        scoringTab.click();
      }
    }
  },
  {
    id: "scoring-toggle",
    title: "Enabling Scoring",
    content: "Toggle this switch to enable scoring for this question. Only enabled questions contribute to the overall thesis score. You can adjust the weight to control how much each question affects the final score.",
    target: "[data-tour='scoring-toggle']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Scoring toggle...');
      const scoringTab = document.querySelector('[data-tour="scoring-rules-tab"]') as HTMLElement;
      if (scoringTab) {
        scoringTab.click();
      }
    }
  },
  {
    id: "scoring-weight",
    title: "Scoring Weight",
    content: "Set the weight for this question. Higher weights mean this question has more impact on the overall score. For example, a weight of 10 means this question is twice as important as a question with weight 5.",
    target: "[data-tour='scoring-weight']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Scoring weight...');
      const scoringTab = document.querySelector('[data-tour="scoring-rules-tab"]') as HTMLElement;
      if (scoringTab) {
        scoringTab.click();
      }
    }
  },
  {
    id: "edit-scoring-rule",
    title: "Advanced Scoring Rules",
    content: "Click the edit button to configure advanced scoring rules. Here you can set up complex scoring logic, define score ranges, and create conditional scoring based on multiple criteria.",
    target: "[data-tour='edit-scoring-rule']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Edit scoring rule...');
      const scoringTab = document.querySelector('[data-tour="scoring-rules-tab"]') as HTMLElement;
      if (scoringTab) {
        scoringTab.click();
      }
    }
  },
  {
    id: "scoring-rule-modal",
    title: "Scoring Rule Configuration",
    content: "In this modal, you can define how the question is scored. Set up score ranges, define what constitutes excellent, good, or poor performance, and configure the scoring logic that will be applied to submissions.",
    target: "[data-tour='scoring-rule-modal']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "bonus-rules-tab",
    title: "Bonus Rules",
    content: "Bonus rules allow you to award extra points or apply penalties based on specific criteria. These can reward exceptional performance or penalize red flags. Click here to configure bonus and penalty rules.",
    target: "[data-tour='bonus-rules-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Bonus Rules tab...');
      const bonusTab = document.querySelector('[data-tour="bonus-rules-tab"]') as HTMLElement;
      if (bonusTab) {
        bonusTab.click();
      }
    }
  },
  {
    id: "add-bonus-rule",
    title: "Adding Bonus Rules",
    content: "Click the '+' button to add a new bonus rule. Bonus rules can award extra points for exceptional criteria or apply penalties for red flags. For example, award bonus points for companies with strong unit economics.",
    target: "[data-tour='add-bonus-rule']",
    placement: "top",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Add bonus rule...');
      const bonusTab = document.querySelector('[data-tour="bonus-rules-tab"]') as HTMLElement;
      if (bonusTab) {
        bonusTab.click();
      }
    }
  },
  {
    id: "bonus-rule-config",
    title: "Configuring Bonus Rules",
    content: "Configure your bonus rule by setting the bonus amount (positive for rewards, negative for penalties), defining the conditions that trigger the bonus, and specifying the criteria that must be met.",
    target: "[data-tour='bonus-rule-config']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining bonus rule configuration...');
      // First scroll the bonus rules tab into view
      const bonusTab = document.querySelector('[data-tour="bonus-rules-tab"]') as HTMLElement;
      console.log('Tour: Found bonus tab:', bonusTab);
      if (bonusTab) {
        console.log('Tour: Scrolling bonus tab into view...');
        bonusTab.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // Then click the tab after scrolling
        setTimeout(() => {
          console.log('Tour: Clicking bonus tab...');
          bonusTab.click();
          console.log('Tour: Clicked bonus tab');
        }, 500);
      } else {
        console.log('Tour: Could not find bonus tab element');
      }
    }
  },
  {
    id: "bonus-amount",
    title: "Bonus Amount",
    content: "Set the bonus amount here. Positive values award extra points (e.g., +10 for exceptional performance), while negative values apply penalties (e.g., -5 for red flags). The amount is added to the final score.",
    target: "[data-tour='bonus-amount']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Bonus amount...');
      const bonusTab = document.querySelector('[data-tour="bonus-rules-tab"]') as HTMLElement;
      if (bonusTab) {
        bonusTab.click();
      }
    }
  },
  {
    id: "bonus-conditions",
    title: "Bonus Conditions",
    content: "Define the conditions that trigger this bonus. Set up criteria based on form questions, such as requiring specific answers or value ranges. Only submissions meeting these conditions will receive the bonus.",
    target: "[data-tour='bonus-conditions']",
    placement: "left",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Bonus conditions...');
      const bonusTab = document.querySelector('[data-tour="bonus-rules-tab"]') as HTMLElement;
      if (bonusTab) {
        bonusTab.click();
      }
    }
  },
  {
    id: "thesis-status",
    title: "Thesis Status",
    content: "Set your thesis status to Active when you're ready to start evaluating submissions. Active theses will automatically score new submissions, while Draft theses allow you to configure without affecting evaluations.",
    target: "[data-tour='thesis-status']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "save-thesis",
    title: "Saving Your Thesis",
    content: "Click Save Thesis to save your configuration. Make sure to save regularly as you build your thesis. Once saved and active, your thesis will automatically evaluate new submissions against your criteria.",
    target: "[data-tour='save-thesis']",
    placement: "right",
    waitForElement: true
  }
]

// Deal Detail Tour - Learn how to navigate and manage deal details
export const dealDetailTourSteps: TourStep[] = [
  {
    id: "deal-header",
    title: "Deal Overview",
    content: "Welcome to the Deal Detail page! This is where you'll review and manage individual startup deals. The header shows the company name, website, sector tags, and key metadata like founder count and last update time.",
    target: "[data-tour='deal-header']",
    placement: "center"
  },
  {
    id: "deal-status",
    title: "Deal Status",
    content: "The status shows the current stage of this deal in your pipeline. You can change it using the status selector to track deals through your evaluation process - from New to Active, Triage, Completed, or Flagged.",
    target: "[data-tour='deal-status']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "deal-owner",
    title: "Assign Deal Owner",
    content: "Assign this deal to a team member who will be responsible for evaluating it. This helps with accountability and ensures deals don't get overlooked. Only assigned owners can make status changes.",
    target: "[data-tour='deal-owner']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "favorite-deal",
    title: "Favorite a Deal",
    content: "Click the Favorite button to bookmark this deal for quick access. Favorited deals are highlighted and can be filtered in your deals list. This is useful for deals you want to track closely.",
    target: "[data-tour='favorite-deal']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "ai-memo",
    title: "AI Memo",
    content: "Generate an AI-powered investment memo for this deal. The AI will analyze all available data and create a comprehensive memo with key insights, risks, and recommendations to help with your decision-making.",
    target: "[data-tour='ai-memo']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "view-submission",
    title: "View Submission",
    content: "View the original submission from the founder. This shows exactly what the founder submitted through your form, including all their answers and uploaded documents. This is only available after the founder has submitted their application.",
    target: "[data-tour='view-submission']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "share-deal",
    title: "Share Deal",
    content: "Share this deal with team members or external stakeholders. You can generate shareable links or export deal information for presentations and discussions.",
    target: "[data-tour='share-deal']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-tabs",
    title: "Deal Information Tabs",
    content: "These tabs organize all the deal information into logical sections. Each tab contains different aspects of the deal analysis - from scoring and founders to documents and research. Let's explore each tab.",
    target: "[data-tour='deal-tabs']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "score-tab",
    title: "Score Tab",
    content: "The Score tab shows the AI-generated evaluation of this deal. It displays the overall score, breakdown by category (team, market, traction), and detailed reasoning for each score component. This is your primary evaluation tool. Click on this tab to see the scoring details.",
    target: "[data-tour='score-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Score tab...');
      // Click the score tab to show its content
      const scoreTab = document.querySelector('[data-tour="score-tab"]') as HTMLElement;
      if (scoreTab) {
        scoreTab.click();
      }
    }
  },
  {
    id: "founders-tab",
    title: "Founders Tab",
    content: "The Founders tab contains detailed information about the founding team. Review their backgrounds, experience, and track records. Strong founders are often the most important factor in early-stage investments. Click to explore the team details.",
    target: "[data-tour='founders-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Founders tab...');
      // Click the founders tab to show its content
      const foundersTab = document.querySelector('[data-tour="founders-tab"]') as HTMLElement;
      if (foundersTab) {
        foundersTab.click();
      }
    }
  },
  {
    id: "documents-tab",
    title: "Documents Tab",
    content: "The Documents tab shows all files uploaded by the founders, including pitch decks, financials, and other supporting materials. Review these documents to understand the business model and financial projections. Click to view the documents.",
    target: "[data-tour='documents-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Documents tab...');
      // Click the documents tab to show its content
      const documentsTab = document.querySelector('[data-tour="documents-tab"]') as HTMLElement;
      if (documentsTab) {
        documentsTab.click();
      }
    }
  },
  {
    id: "research-tab",
    title: "Analyst Research Tab",
    content: "The Analyst Research tab contains AI-generated market analysis, competitive landscape, and industry insights. This helps you understand the market opportunity and competitive positioning. Click to explore the research.",
    target: "[data-tour='research-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Research tab...');
      // Click the research tab to show its content
      const researchTab = document.querySelector('[data-tour="research-tab"]') as HTMLElement;
      if (researchTab) {
        researchTab.click();
      }
    }
  },
  {
    id: "timeline-tab",
    title: "Timeline Tab",
    content: "The Timeline tab shows the complete history of this deal, including status changes, assignments, notes, and other activities. This helps you track the deal's progress through your pipeline. Click to view the timeline.",
    target: "[data-tour='timeline-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Timeline tab...');
      // Click the timeline tab to show its content
      const timelineTab = document.querySelector('[data-tour="timeline-tab"]') as HTMLElement;
      if (timelineTab) {
        timelineTab.click();
      }
    }
  },
  {
    id: "notes-tab",
    title: "Notes Tab",
    content: "The Notes tab contains all team discussions and notes about this deal. Add your thoughts, questions, and insights here to collaborate with your team and maintain a record of your evaluation process. Click to view and add notes.",
    target: "[data-tour='notes-tab']",
    placement: "bottom",
    waitForElement: true,
    onBeforeStep: () => {
      console.log('Tour: Explaining Notes tab...');
      // Click the notes tab to show its content
      const notesTab = document.querySelector('[data-tour="notes-tab"]') as HTMLElement;
      if (notesTab) {
        notesTab.click();
      }
    }
  }
]

// Export all tour configurations
export const tourConfigurations = {
  // Unified onboarding tour
  onboarding: {
    name: "Complete Onboarding",
    steps: unifiedOnboardingTourSteps
  },
  // Individual page tours
  dashboard: {
    name: "Dashboard Tour",
    steps: dashboardTourSteps
  },
  deals: {
    name: "Deal Management",
    steps: dealsTourSteps
  },
  workflow: {
    name: "Workflow Management",
    steps: workflowTourSteps
  },
  forms: {
    name: "Form Builder",
    steps: formsTourSteps
  },
  theses: {
    name: "Investment Theses",
    steps: thesesTourSteps
  },
  settings: {
    name: "Organization Settings",
    steps: settingsTourSteps
  },
  "form-builder": {
    name: "Form Builder",
    steps: formBuilderTourSteps
  },
  "thesis-builder": {
    name: "Thesis Builder",
    steps: thesisBuilderTourSteps
  },
  "deal-detail": {
    name: "Deal Detail",
    steps: dealDetailTourSteps
  }
} as const

export type TourType = keyof typeof tourConfigurations
