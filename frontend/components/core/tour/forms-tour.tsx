"use client"

import { useTour } from "./enhanced-tour-provider"
import { FileText } from "lucide-react"
import { UnifiedTourButton } from "./unified-tour-button"

export function FormsTour() {
  const { startTour, hasCompletedTour } = useTour()

  // Removed auto-start tour logic - tours should only start when user clicks "Take a Tour"
  // The onboarding logic remains untouched and handles the main onboarding flow

  return (
    <UnifiedTourButton
      tourType="forms"
      icon={FileText}
      label="Forms Tour"
      iconColor="text-emerald-500"
      gradientFrom="from-emerald-500"
      gradientTo="to-blue-500"
    />
  )
} 