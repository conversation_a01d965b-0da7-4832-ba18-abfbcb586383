"use client"

import { useTour } from "./enhanced-tour-provider"
import { Target } from "lucide-react"
import { UnifiedTourButton } from "./unified-tour-button"

export function ThesesTour() {
  const { startTour, hasCompletedTour } = useTour()

  // Removed auto-start tour logic - tours should only start when user clicks "Take a Tour"
  // The onboarding logic remains untouched and handles the main onboarding flow

  return (
    <UnifiedTourButton
      tourType="theses"
      icon={Target}
      label="Theses Tour"
      iconColor="text-orange-500"
      gradientFrom="from-orange-500"
      gradientTo="to-purple-500"
    />
  )
} 