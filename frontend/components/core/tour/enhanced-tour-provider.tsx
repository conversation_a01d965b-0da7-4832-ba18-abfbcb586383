"use client"

import { createContext, useContext, useState, useC<PERSON>back, useEffect, ReactNode } from "react"
import { ProductTour } from "./product-tour"
import { tourConfigurations, TourType } from "./tour-steps"
import { TourAPI } from "@/lib/api/tour-api"

interface TourContextType {
  activeTour: TourType | null
  isActive: boolean
  startTour: (tourType: TourType) => void
  stopTour: () => void
  completeTour: (tourType: TourType) => void
  skipTour: (tourType: TourType) => void
  hasCompletedTour: (tourType: TourType) => boolean
  shouldShowTour: (tourType: TourType) => boolean
  // Enhanced features for tour engine
  isEngineRunning: boolean
  engineProgress: { completed: number; total: number; percentage: number; productTours?: { completed: number; total: number } }
  backendProgress: any
  startOnboardingFlow: () => void
  stopOnboardingFlow: () => void
}

const TourContext = createContext<TourContextType | undefined>(undefined)

interface TourProviderProps {
  children: ReactNode
}

export function EnhancedTourProvider({ children }: TourProviderProps) {
  const [activeTour, setActiveTour] = useState<TourType | null>(null)
  const [completedTours, setCompletedTours] = useState<Set<string>>(new Set())
  const [isEngineRunning, setIsEngineRunning] = useState(false)
  const [backendProgress, setBackendProgress] = useState<any>(null)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  
  // Save step index to localStorage when it changes
  const updateStepIndex = useCallback((index: number) => {
    setCurrentStepIndex(index)
    localStorage.setItem('tractionx-tour-step-index', index.toString())
    console.log('💾 Saved tour step index:', index)
  }, [])

  // Load completed tours, step index, and active tour from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('tractionx-completed-tours')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setCompletedTours(new Set(parsed))
      } catch (error) {
        console.error('Failed to parse completed tours:', error)
      }
    }
    
    // Load step index
    const savedStepIndex = localStorage.getItem('tractionx-tour-step-index')
    if (savedStepIndex) {
      try {
        const stepIndex = parseInt(savedStepIndex, 10)
        if (!isNaN(stepIndex)) {
          setCurrentStepIndex(stepIndex)
          console.log('🔄 Loaded tour step index:', stepIndex)
        }
      } catch (error) {
        console.error('Failed to parse tour step index:', error)
      }
    }
    
    // Load active tour and engine state
    const savedActiveTour = localStorage.getItem('tractionx-active-tour')
    const savedEngineRunning = localStorage.getItem('tractionx-engine-running')
    if (savedActiveTour) {
      console.log('🔄 Restoring active tour:', savedActiveTour)
      setActiveTour(savedActiveTour as TourType)
      setIsEngineRunning(savedEngineRunning === 'true')
    }
  }, [])

  // Load backend progress on mount
  useEffect(() => {
    const loadBackendProgress = async () => {
      try {
        const userInfo = await TourAPI.getUserInfo()
        const progress = userInfo.tour_progress || { completed: [], last_seen_prompt: null, completed_at: null }
        setBackendProgress(progress)
        
        if (progress.completed) {
          const backendCompleted = new Set(progress.completed)
          setCompletedTours(prev => {
            const merged = new Set([...Array.from(prev), ...Array.from(backendCompleted)])
            localStorage.setItem('tractionx-completed-tours', JSON.stringify(Array.from(merged)))
            return merged
          })
        }
      } catch (error) {
        console.error('Failed to load backend progress:', error)
      }
    }
    
    loadBackendProgress()
  }, [])

  // Save completed tours to localStorage
  const saveCompletedTours = useCallback((tours: Set<string>) => {
    localStorage.setItem('tractionx-completed-tours', JSON.stringify(Array.from(tours)))
  }, [])

  const startTour = useCallback((tourType: TourType) => {
    // Validate tour configuration exists
    if (!tourConfigurations[tourType]) {
      console.error(`❌ Tour configuration not found for: ${tourType}`)
      return
    }
    
    console.log(`🎯 Starting tour: ${tourType}`)
    
    // Reset step index to 0 for any new tour
    setCurrentStepIndex(0)
    localStorage.setItem('tractionx-tour-step-index', '0')
    
    setActiveTour(tourType)
    localStorage.setItem('tractionx-active-tour', tourType)
    
    // Track tour start
    try {
      console.log("Tour: Started", {
        tourType,
        tourName: tourConfigurations[tourType]?.name || 'Unknown Tour',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour start:", error)
    }
  }, [])

  const stopTour = useCallback(() => {
    if (activeTour) {
      console.log(`🛑 Stopping tour: ${activeTour}`)
      
      // Track tour stop
      try {
        console.log("Tour: Stopped", {
          tourType: activeTour,
          tourName: tourConfigurations[activeTour]?.name || 'Unknown Tour',
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error("Failed to track tour stop:", error)
      }
    }
    setActiveTour(null)
    setIsEngineRunning(false)
    
    // Clear step index when tour is stopped
    setCurrentStepIndex(0)
    localStorage.removeItem('tractionx-tour-step-index')
    localStorage.removeItem('tractionx-active-tour')
    localStorage.removeItem('tractionx-engine-running')
  }, [activeTour])

  const completeTour = useCallback(async (tourType: TourType) => {
    console.log(`✅ Completing tour: ${tourType}`)
    
    // Update local state first
    const newCompleted = new Set(completedTours)
    newCompleted.add(tourType)
    setCompletedTours(newCompleted)
    saveCompletedTours(newCompleted)
    setActiveTour(null)
    setIsEngineRunning(false)
    
    // Clear step index and active tour when completed
    setCurrentStepIndex(0)
    localStorage.removeItem('tractionx-tour-step-index')
    localStorage.removeItem('tractionx-active-tour')
    localStorage.removeItem('tractionx-engine-running')

    // Track tour completion locally
    try {
      console.log("Tour: Completed", {
        tourType,
        tourName: tourConfigurations[tourType]?.name || 'Unknown Tour',
        totalSteps: tourConfigurations[tourType]?.steps.length || 0,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour completion:", error)
    }

    // Sync with backend
    try {
      await TourAPI.completeTour(tourType)
      // Update local backend progress
      setBackendProgress(prev => prev ? {
        ...prev,
        completed: [...(prev.completed || []), tourType]
      } : null)
    } catch (error) {
      console.error("Failed to sync tour completion with backend:", error)
    }
  }, [completedTours, saveCompletedTours])

  const skipTour = useCallback(async (tourType: TourType) => {
    console.log(`⏭️ Skipping tour: ${tourType}`)
    
    // Mark as completed so it doesn't auto-trigger again
    const newCompleted = new Set(completedTours)
    newCompleted.add(tourType)
    setCompletedTours(newCompleted)
    saveCompletedTours(newCompleted)
    setActiveTour(null)
    setIsEngineRunning(false)
    
    // Clear step index and active tour when skipped
    setCurrentStepIndex(0)
    localStorage.removeItem('tractionx-tour-step-index')
    localStorage.removeItem('tractionx-active-tour')
    localStorage.removeItem('tractionx-engine-running')

    // Track tour skip
    try {
      console.log("Tour: Skipped", {
        tourType,
        tourName: tourConfigurations[tourType]?.name || 'Unknown Tour',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour skip:", error)
    }

    // Sync with backend
    try {
      await TourAPI.completeTour(tourType)
      // Update local backend progress
      setBackendProgress(prev => prev ? {
        ...prev,
        completed: [...(prev.completed || []), tourType]
      } : null)
    } catch (error) {
      console.error("Failed to sync tour skip with backend:", error)
    }
  }, [completedTours, saveCompletedTours])

  // Handle tour step completion for navigation
  const handleStepComplete = useCallback((stepId: string) => {
    console.log(`🔄 Step completed: ${stepId}`)
    // Handle navigation steps
    if (stepId === 'nav-to-deals') {
      window.location.href = '/deals'
    } else if (stepId === 'nav-to-theses') {
      window.location.href = '/theses'
    } else if (stepId === 'nav-to-forms') {
      window.location.href = '/forms'
    }
  }, [])

  const hasCompletedTour = useCallback((tourType: TourType) => {
    // Check both local and backend state
    const localCompleted = completedTours.has(tourType)
    const backendCompleted = backendProgress?.completed?.includes(tourType) || false
    return localCompleted || backendCompleted
  }, [completedTours, backendProgress])

  const shouldShowTour = useCallback((tourType: TourType) => {
    // Don't show if another tour is active
    if (activeTour && activeTour !== tourType) return false

    // Always allow tours to be shown (even if completed before)
    return true
  }, [activeTour])

  // Enhanced tour engine functions
  const startOnboardingFlow = useCallback(async () => {
    console.log('🚀 Starting onboarding flow from tour provider')
    try {
      setIsEngineRunning(true)
      // Reset step index when starting fresh
      updateStepIndex(0)
      localStorage.setItem('tractionx-engine-running', 'true')
      startTour('onboarding')
    } catch (error) {
      console.error('Failed to start onboarding flow:', error)
    }
  }, [updateStepIndex])

  const stopOnboardingFlow = useCallback(() => {
    console.log('🛑 Stopping onboarding flow from tour provider')
    setIsEngineRunning(false)
    // Clear step index when stopping
    setCurrentStepIndex(0)
    localStorage.removeItem('tractionx-tour-step-index')
    localStorage.removeItem('tractionx-engine-running')
    stopTour()
  }, [])

  // Calculate engine progress
  const engineProgress = {
    completed: backendProgress?.completed?.length || 0,
    total: 5, // onboarding + 4 product tours
    percentage: Math.round(((backendProgress?.completed?.length || 0) / 5) * 100),
    productTours: {
      completed: (backendProgress?.completed || []).filter((tour: string) => 
        ['dashboard', 'deals', 'theses', 'forms'].includes(tour)
      ).length,
      total: 4
    }
  }

  const isActive = activeTour !== null

  const value: TourContextType = {
    activeTour,
    isActive,
    startTour,
    stopTour,
    completeTour,
    skipTour,
    hasCompletedTour,
    shouldShowTour,
    // Enhanced features
    isEngineRunning,
    engineProgress,
    backendProgress,
    startOnboardingFlow,
    stopOnboardingFlow,
  }

  return (
    <TourContext.Provider value={value}>
      {children}
      
      {/* Render active tour */}
      {activeTour && tourConfigurations[activeTour] && (
        <ProductTour
          steps={tourConfigurations[activeTour].steps as any}
          isActive={true}
          onComplete={() => completeTour(activeTour)}
          onSkip={() => skipTour(activeTour)}
          onClose={() => stopTour()}
          autoStart={true}
          initialStepIndex={currentStepIndex}
          onStepIndexChange={updateStepIndex}
        />
      )}
    </TourContext.Provider>
  )
}

export function useEnhancedTour() {
  const context = useContext(TourContext)
  if (context === undefined) {
    throw new Error('useEnhancedTour must be used within an EnhancedTourProvider')
  }
  return context
}

// Export both for backward compatibility
export { useEnhancedTour as useTour }

// Hook for auto-triggering tours based on conditions
export function useAutoTour() {
  const { startTour, shouldShowTour, hasCompletedTour, startOnboardingFlow, isEngineRunning, engineProgress } = useEnhancedTour()

  const triggerDashboardTour = useCallback(() => {
    if (shouldShowTour('dashboard')) {
      startTour('dashboard')
    }
  }, [shouldShowTour, startTour])

  const triggerWorkflowTour = useCallback(() => {
    if (shouldShowTour('workflow')) {
      startTour('workflow')
    }
  }, [shouldShowTour, startTour])

  const triggerTourForPage = useCallback((page: TourType) => {
    if (shouldShowTour(page)) {
      startTour(page)
    }
  }, [shouldShowTour, startTour])

  // Check if user should see first-time tour (enhanced for existing users)
  const shouldShowFirstTimeTour = useCallback(() => {
    return engineProgress.percentage < 100
  }, [engineProgress.percentage])

  // Force start any tour (ignores completion status)
  const forceStartTour = useCallback((tourType: TourType) => {
    startTour(tourType)
  }, [startTour])

  // Start the coordinated onboarding flow
  const triggerOnboardingFlow = useCallback(() => {
    console.log('🚀 Triggering onboarding flow')
    startOnboardingFlow()
  }, [startOnboardingFlow])

  return {
    triggerDashboardTour,
    triggerWorkflowTour,
    triggerTourForPage,
    shouldShowFirstTimeTour,
    forceStartTour,
    triggerOnboardingFlow,
    // Tour engine controls
    startOnboardingFlow,
    stopOnboardingFlow: () => {}, // Simplified for now
    isEngineRunning,
    engineProgress
  }
}

// Component for manual tour triggers
interface TourTriggerProps {
  tourType: TourType
  children: React.ReactNode
  className?: string
}

export function TourTrigger({ tourType, children, className }: TourTriggerProps) {
  const { startTour } = useEnhancedTour()

  const handleClick = () => {
    console.log('TourTrigger: Clicked for tour type:', tourType)
    startTour(tourType)
  }

  return (
    <div onClick={handleClick} className={className}>
      {children}
    </div>
  )
}

// Tour badge component with enhanced features
interface TourBadgeProps {
  tourType: TourType
  className?: string
}

export function TourBadge({ tourType, className }: TourBadgeProps) {
  const { hasCompletedTour, startTour } = useEnhancedTour()

  const handleClick = () => {
    console.log('TourBadge: Clicked for tour type:', tourType)
    try {
      startTour(tourType)
      console.log('TourBadge: startTour called successfully')
    } catch (error) {
      console.error('TourBadge: Error starting tour:', error)
    }
  }

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors ${className}`}
    >
      <span className={`size-2 rounded-full ${hasCompletedTour(tourType) ? 'bg-green-500' : 'bg-purple-500 animate-pulse'}`} />
      {hasCompletedTour(tourType) ? 'Take a Tour' : 'Take a Tour'}
    </button>
  )
} 