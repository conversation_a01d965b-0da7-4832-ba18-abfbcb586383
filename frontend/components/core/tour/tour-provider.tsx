"use client"

import { createContext, useContext, useState, useCallback, useEffect } from "react"
import { ProductTour } from "./product-tour"
import { tourConfigurations, TourType } from "./tour-steps"

interface TourContextType {
  activeTour: TourType | null
  isActive: boolean
  startTour: (tourType: TourType) => void
  stopTour: () => void
  completeTour: (tourType: TourType) => void
  skipTour: (tourType: TourType) => void
  hasCompletedTour: (tourType: TourType) => boolean
  shouldShowTour: (tourType: TourType) => boolean
}

const TourContext = createContext<TourContextType | undefined>(undefined)

interface TourProviderProps {
  children: React.ReactNode
}

export function TourProvider({ children }: TourProviderProps) {
  const [activeTour, setActiveTour] = useState<TourType | null>(null)
  const [completedTours, setCompletedTours] = useState<Set<string>>(new Set())

  // Load completed tours from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('tractionx-completed-tours')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setCompletedTours(new Set(parsed))
      } catch (error) {
        console.error('Failed to parse completed tours:', error)
      }
    }
  }, [])

  // Save completed tours to localStorage
  const saveCompletedTours = useCallback((tours: Set<string>) => {
    localStorage.setItem('tractionx-completed-tours', JSON.stringify(Array.from(tours)))
  }, [])

  const startTour = useCallback((tourType: TourType) => {
    console.log(`Starting tour: ${tourType}`)
    setActiveTour(tourType)
    
    // Track tour start
    try {
      console.log("Tour: Started", {
        tourType,
        tourName: tourConfigurations[tourType].name,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour start:", error)
    }
  }, [])

  const stopTour = useCallback(() => {
    if (activeTour) {
      console.log(`Stopping tour: ${activeTour}`)
      
      // Track tour stop
      try {
        console.log("Tour: Stopped", {
          tourType: activeTour,
          tourName: tourConfigurations[activeTour].name,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error("Failed to track tour stop:", error)
      }
    }
    setActiveTour(null)
  }, [activeTour])

  const completeTour = useCallback((tourType: TourType) => {
    console.log(`Completing tour: ${tourType}`)
    
    const newCompleted = new Set(completedTours)
    newCompleted.add(tourType)
    setCompletedTours(newCompleted)
    saveCompletedTours(newCompleted)
    setActiveTour(null)

    // Track tour completion
    try {
      console.log("Tour: Completed", {
        tourType,
        tourName: tourConfigurations[tourType].name,
        totalSteps: tourConfigurations[tourType].steps.length,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour completion:", error)
    }
  }, [completedTours, saveCompletedTours])

  const skipTour = useCallback((tourType: TourType) => {
    console.log(`Skipping tour: ${tourType}`)
    
    const newCompleted = new Set(completedTours)
    newCompleted.add(tourType) // Mark as completed so it doesn't auto-trigger again
    setCompletedTours(newCompleted)
    saveCompletedTours(newCompleted)
    setActiveTour(null)

    // Track tour skip
    try {
      console.log("Tour: Skipped", {
        tourType,
        tourName: tourConfigurations[tourType].name,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track tour skip:", error)
    }
  }, [completedTours, saveCompletedTours])

  const hasCompletedTour = useCallback((tourType: TourType) => {
    return completedTours.has(tourType)
  }, [completedTours])

  const shouldShowTour = useCallback((tourType: TourType) => {
    // Don't show if another tour is active
    if (activeTour && activeTour !== tourType) return false

    // Always allow tours to be shown (even if completed before)
    return true
  }, [activeTour])

  const isActive = activeTour !== null

  const value: TourContextType = {
    activeTour,
    isActive,
    startTour,
    stopTour,
    completeTour,
    skipTour,
    hasCompletedTour,
    shouldShowTour
  }

  return (
    <TourContext.Provider value={value}>
      {children}
      
      {/* Render active tour */}
      {activeTour && (
        <ProductTour
          steps={tourConfigurations[activeTour].steps}
          isActive={true}
          onComplete={() => completeTour(activeTour)}
          onSkip={() => skipTour(activeTour)}
          onClose={() => stopTour()}
          autoStart={true}
        />
      )}
    </TourContext.Provider>
  )
}

export function useTour() {
  const context = useContext(TourContext)
  if (context === undefined) {
    throw new Error('useTour must be used within a TourProvider')
  }
  return context
}

// Hook for auto-triggering tours based on conditions
export function useAutoTour() {
  const { startTour, shouldShowTour, hasCompletedTour } = useTour()

  const triggerDashboardTour = useCallback(() => {
    if (shouldShowTour('dashboard')) {
      startTour('dashboard')
    }
  }, [shouldShowTour, startTour])

  const triggerWorkflowTour = useCallback(() => {
    if (shouldShowTour('workflow')) {
      startTour('workflow')
    }
  }, [shouldShowTour, startTour])

  const triggerTourForPage = useCallback((page: TourType) => {
    if (shouldShowTour(page)) {
      startTour(page)
    }
  }, [shouldShowTour, startTour])

  // Check if user should see first-time tour (only for auto-triggering)
  const shouldShowFirstTimeTour = useCallback(() => {
    return !hasCompletedTour('dashboard') && !hasCompletedTour('workflow')
  }, [hasCompletedTour])

  // Force start any tour (ignores completion status)
  const forceStartTour = useCallback((tourType: TourType) => {
    startTour(tourType)
  }, [startTour])

  return {
    triggerDashboardTour,
    triggerWorkflowTour,
    triggerTourForPage,
    shouldShowFirstTimeTour,
    forceStartTour
  }
}

// Component for manual tour triggers
interface TourTriggerProps {
  tourType: TourType
  children: React.ReactNode
  className?: string
}

export function TourTrigger({ tourType, children, className }: TourTriggerProps) {
  const { startTour } = useTour()

  const handleClick = () => {
    startTour(tourType)
  }

  return (
    <div onClick={handleClick} className={className}>
      {children}
    </div>
  )
}

// Component for showing tour availability
interface TourBadgeProps {
  tourType: TourType
  className?: string
}

export function TourBadge({ tourType, className }: TourBadgeProps) {
  const { hasCompletedTour, startTour } = useTour()

  const handleClick = () => {
    console.log('TourBadge: Clicked for tour type:', tourType)
    try {
      startTour(tourType)
      console.log('TourBadge: startTour called successfully')
    } catch (error) {
      console.error('TourBadge: Error starting tour:', error)
    }
  }

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors ${className}`}
    >
      <span className={`size-2 rounded-full ${hasCompletedTour(tourType) ? 'bg-green-500' : 'bg-purple-500 animate-pulse'}`} />
      {hasCompletedTour(tourType) ? 'Take a Tour' : 'Take a Tour'}
    </button>
  )
}
