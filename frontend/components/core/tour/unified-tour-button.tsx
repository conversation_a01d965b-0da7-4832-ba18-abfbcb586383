"use client"

import { useTour, TourTrigger } from "./enhanced-tour-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { type Icon as LucideIcon } from "lucide-react"

interface UnifiedTourButtonProps {
  tourType: "dashboard" | "forms" | "deals" | "theses" | "settings" | "form-builder" | "thesis-builder" | "deal-detail"
  icon: LucideIcon
  label: string
  iconColor: string
  gradientFrom: string
  gradientTo: string
  className?: string
}

export function UnifiedTourButton({
  tourType,
  icon: Icon,
  label,
  iconColor,
  gradientFrom,
  gradientTo,
  className
}: UnifiedTourButtonProps) {
  const { hasCompletedTour } = useTour()

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* "Replay tour" badge - always visible */}
      <TourTrigger tourType={tourType}>
        <Badge 
          variant="secondary" 
          className={cn(
            "h-6 px-2 text-xs font-medium border-0 cursor-pointer",
            "bg-purple-100 text-purple-700 hover:bg-purple-200",
            "transition-all duration-200 rounded-full",
            "flex items-center gap-1.5"
          )}
        >
          {/* Green dot indicator */}
          <div className="size-1.5 rounded-full bg-green-500" />
          Replay tour
        </Badge>
      </TourTrigger>

      {/* "New" badge for uncompleted tours */}
      {!hasCompletedTour(tourType) && (
        <Badge 
          variant="secondary" 
          className={cn(
            "h-5 px-1.5 text-xs font-medium border-0",
            "bg-gradient-to-r text-white shadow-sm",
            gradientFrom, gradientTo,
            "animate-pulse"
          )}
        >
          New
        </Badge>
      )}
    </div>
  )
} 