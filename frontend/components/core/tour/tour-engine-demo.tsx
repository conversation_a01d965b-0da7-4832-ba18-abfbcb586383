"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  Play, 
  Pause,
  RotateCcw,
  CheckCircle,
  Clock,
  Zap,
  ArrowRight,
  Settings,
  Info
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAutoTour } from "./enhanced-tour-provider"
import { useTourEngine } from "@/lib/hooks/use-tour-engine"

export function TourEngineDemo() {
  const { 
    triggerOnboardingFlow, 
    shouldShowFirstTimeTour, 
    isEngineRunning,
    engineProgress 
  } = useAutoTour()
  
  const tourEngine = useTourEngine()
  const [isExpanded, setIsExpanded] = useState(false)

  const handleStartFlow = () => {
    triggerOnboardingFlow()
  }

  const handleStopFlow = () => {
    tourEngine.stopTourEngine()
  }

  const handleRefresh = () => {
    tourEngine.refresh()
  }

  const stats = tourEngine.getTourStats()
  const currentStatus = tourEngine.getCurrentTourStatus()

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="size-5 text-purple-500" />
            Tour Engine Demo
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Settings className="size-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Engine Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <div className={`size-2 rounded-full ${
              isEngineRunning 
                ? 'bg-green-500 animate-pulse' 
                : tourEngine.shouldAutoTrigger 
                ? 'bg-yellow-500' 
                : 'bg-gray-400'
            }`} />
            <span className="text-sm font-medium">
              {isEngineRunning 
                ? 'Engine Running' 
                : tourEngine.shouldAutoTrigger 
                ? 'Ready to Start' 
                : 'Standby'
              }
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {tourEngine.isLoading && (
              <div className="size-4 animate-spin rounded-full border-2 border-purple-500 border-t-transparent" />
            )}
            <Badge variant={isEngineRunning ? "default" : "secondary"}>
              {stats.completedCount}/{stats.totalCount} completed
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Onboarding Progress</span>
            <span className="text-sm font-medium">{stats.progressPercentage}%</span>
          </div>
          <Progress value={stats.progressPercentage} className="h-2" />
        </div>

        {/* Current Status Info */}
        {currentStatus.status !== 'completed' && (
          <Alert>
            <Info className="size-4" />
            <AlertDescription>
              Status: <strong>{currentStatus.tourName}</strong> - {currentStatus.description}
            </AlertDescription>
          </Alert>
        )}

        {/* Control Buttons */}
        <div className="flex gap-2">
          {!isEngineRunning ? (
            <Button 
              onClick={handleStartFlow}
              disabled={!tourEngine.shouldAutoTrigger || stats.isComplete}
              className="flex-1"
            >
              <Play className="size-4 mr-2" />
              Start Onboarding Flow
            </Button>
          ) : (
            <Button 
              onClick={handleStopFlow}
              variant="outline"
              className="flex-1"
            >
              <Pause className="size-4 mr-2" />
              Stop Flow
            </Button>
          )}
          
          <Button 
            onClick={handleRefresh}
            variant="outline"
            size="icon"
          >
            <RotateCcw className="size-4" />
          </Button>
        </div>

        {/* Expanded Debug Info */}
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-3 border-t pt-4"
          >
            <h4 className="font-medium text-sm">Debug Information</h4>
            
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div>
                <strong>Should Auto Trigger:</strong>
                <br />
                <code>{tourEngine.shouldAutoTrigger ? 'true' : 'false'}</code>
              </div>
              <div>
                <strong>Is First Login:</strong>
                <br />
                <code>{tourEngine.userInfo?.is_first_login ? 'true' : 'false'}</code>
              </div>
              <div>
                <strong>Prompt Seen:</strong>
                <br />
                <code>{tourEngine.hasPromptBeenSeen ? 'true' : 'false'}</code>
              </div>
              <div>
                <strong>Current Index:</strong>
                <br />
                <code>{tourEngine.currentTourIndex}</code>
              </div>
            </div>

            {/* Tour Flow Status */}
            <div>
              <strong className="text-sm">Tour Flow Status:</strong>
              <div className="mt-2 space-y-2">
                {tourEngine.PRODUCT_TOURS.map((flow, index) => {
                  const completed = tourEngine.isTourCompleted(flow.tourId)
                  const isCurrent = index === tourEngine.currentTourIndex
                  
                  return (
                    <div 
                      key={flow.tourId}
                      className={`flex items-center gap-2 p-2 rounded text-xs ${
                        isCurrent && isEngineRunning 
                          ? 'bg-purple-100 border border-purple-300' 
                          : 'bg-gray-50'
                      }`}
                    >
                      {completed ? (
                        <CheckCircle className="size-3 text-green-500" />
                      ) : isCurrent && isEngineRunning ? (
                        <div className="size-3 animate-spin rounded-full border border-purple-500 border-t-transparent" />
                      ) : (
                        <Clock className="size-3 text-gray-400" />
                      )}
                      <span className="flex-1">{flow.tourId}</span>
                      <code className="text-gray-500">{flow.route}</code>
                      {isCurrent && isEngineRunning && (
                        <ArrowRight className="size-3 text-purple-500" />
                      )}
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Error Display */}
            {tourEngine.error && (
              <Alert variant="destructive">
                <AlertDescription>
                  <strong>Error:</strong> {tourEngine.error}
                </AlertDescription>
              </Alert>
            )}
          </motion.div>
        )}

        {/* Completion Message */}
        {stats.isComplete && (
          <Alert>
            <CheckCircle className="size-4 text-green-500" />
            <AlertDescription>
              🎉 All onboarding tours completed! Users can still access individual tours for replay.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
} 