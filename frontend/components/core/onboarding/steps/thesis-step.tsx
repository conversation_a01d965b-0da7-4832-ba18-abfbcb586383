"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Target, ArrowRight, Globe, Building, TrendingUp, Users } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThesisMultiSelect } from "@/components/core/settings/thesis-multi-select"
import { SettingsAPI, ThesisConfig } from "@/lib/api/settings-api"
import { cn } from "@/lib/utils"

interface ThesisStepProps {
  user: any
  stepData: any
  onNext: (data?: any) => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  currentStep: number
  totalSteps: number
}

// Default options for each field (same as settings page)
const GEOGRAPHY_OPTIONS = [
  "Southeast Asia", "US", "Europe", "India", "LATAM", "China", 
  "Middle East", "Africa", "Australia", "Other"
]

const SECTOR_OPTIONS = [
  "AI", "Climate", "Fintech", "Healthcare", "EdTech", "E-commerce",
  "SaaS", "DeepTech", "Consumer", "Enterprise", "Biotech", "Mobility", "Other"
]

const STAGE_OPTIONS = [
  "Pre-seed", "Seed", "Series A", "Series B", "Series C", 
  "Growth", "Late Stage", "Other"
]

const BUSINESS_MODEL_OPTIONS = [
  "B2B", "B2C", "B2B2C", "Marketplace", "Platform", 
  "SaaS", "Hardware", "Services", "Other"
]

export function ThesisStep({ 
  stepData, 
  onNext, 
  isLoading, 
  setIsLoading 
}: ThesisStepProps) {
  const [formData, setFormData] = useState<ThesisConfig>({
    geography: [],
    sector: [],
    stage: [],
    business_model: []
  })

  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    // Check if there are any selections
    const hasSelections = Object.values(formData).some(arr => arr.length > 0)
    setHasChanges(hasSelections)
  }, [formData])

  const handleFieldChange = (field: keyof ThesisConfig, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: values
    }))
  }

  const handleSaveAndContinue = async () => {
    try {
      setIsLoading(true)
      
      // Save thesis configuration to backend
      await SettingsAPI.updateThesisConfig(formData)
      
      // Clear any prompt dismissal since thesis is now configured
      localStorage.removeItem('org-thesis-prompt-dismissed')
      
      toast.success("Investment thesis saved!")
      
      // Pass data to next step
      onNext({ thesis: formData })
    } catch (error: any) {
      console.error("Failed to save thesis configuration:", error)
      toast.error(error.message || "Failed to save thesis configuration")
    } finally {
      setIsLoading(false)
    }
  }

  const fieldIcons = {
    geography: Globe,
    sector: Building,
    stage: TrendingUp,
    business_model: Users
  }

  const fieldLabels = {
    geography: "Geography",
    sector: "Sector",
    stage: "Stage",
    business_model: "Business Model"
  }

  const fieldDescriptions = {
    geography: "Which regions do you focus on?",
    sector: "What industries interest you most?",
    stage: "What investment stages do you target?",
    business_model: "Which business models do you prefer?"
  }

  return (
    <div className="flex-1 flex flex-col p-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="flex items-center justify-center mb-4">
          <div className="flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg">
            <Target className="size-8 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Set Your Investment Thesis
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Help us understand your investment preferences so we can personalize your deal flow and recommendations.
        </p>
      </motion.div>

      {/* Form Fields */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex-1 space-y-8 max-w-3xl mx-auto w-full"
      >
        {/* Geography Field */}
        <div className="space-y-3">
          <ThesisMultiSelect
            label={fieldLabels.geography}
            description={fieldDescriptions.geography}
            icon={fieldIcons.geography}
            options={GEOGRAPHY_OPTIONS}
            values={formData.geography}
            onChange={(values) => handleFieldChange('geography', values)}
            placeholder="Select geographic regions..."
          />
        </div>

        {/* Sector Field */}
        <div className="space-y-3">
          <ThesisMultiSelect
            label={fieldLabels.sector}
            description={fieldDescriptions.sector}
            icon={fieldIcons.sector}
            options={SECTOR_OPTIONS}
            values={formData.sector}
            onChange={(values) => handleFieldChange('sector', values)}
            placeholder="Select sectors and industries..."
          />
        </div>

        {/* Stage Field */}
        <div className="space-y-3">
          <ThesisMultiSelect
            label={fieldLabels.stage}
            description={fieldDescriptions.stage}
            icon={fieldIcons.stage}
            options={STAGE_OPTIONS}
            values={formData.stage}
            onChange={(values) => handleFieldChange('stage', values)}
            placeholder="Select investment stages..."
          />
        </div>

        {/* Business Model Field */}
        <div className="space-y-3">
          <ThesisMultiSelect
            label={fieldLabels.business_model}
            description={fieldDescriptions.business_model}
            icon={fieldIcons.business_model}
            options={BUSINESS_MODEL_OPTIONS}
            values={formData.business_model}
            onChange={(values) => handleFieldChange('business_model', values)}
            placeholder="Select business models..."
          />
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex items-center justify-center pt-8"
      >
        <Button
          onClick={handleSaveAndContinue}
          disabled={!hasChanges || isLoading}
          size="lg"
          className={cn(
            "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
            "hover:from-purple-600 hover:to-purple-700",
            "px-8 py-3 text-lg font-semibold",
            "shadow-lg hover:shadow-xl transition-all duration-300",
            "flex items-center gap-2",
            "disabled:opacity-50 disabled:cursor-not-allowed"
          )}
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
              Saving...
            </>
          ) : (
            <>
              Save and Continue
              <ArrowRight className="size-5" />
            </>
          )}
        </Button>
      </motion.div>

      {/* Helper Text */}
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="text-center text-sm text-gray-500 mt-4"
      >
        You can always update these preferences later in Settings
      </motion.p>
    </div>
  )
}
