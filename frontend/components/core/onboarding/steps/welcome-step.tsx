"use client"

import { motion } from "framer-motion"
import { ArrowR<PERSON>, Target, Users } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface WelcomeStepProps {
  user: any
  stepData: any
  onNext: (data?: any) => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  currentStep: number
  totalSteps: number
}

export function WelcomeStep({ user, stepData, onNext }: WelcomeStepProps) {
  const handleGetStarted = () => {
    onNext()
  }

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="space-y-8 max-w-2xl"
      >
        {/* Logo/Brand Section */}
        <div className="space-y-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5, ease: "easeOut" }}
            className="mx-auto flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg"
          >
            {/* <Sparkles className="size-10 text-white" /> */}
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="text-4xl font-bold tracking-tight text-gray-900"
          >
            Welcome to TractionX
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="text-xl text-gray-600 leading-relaxed"
          >
            Your decision support system is live.
          </motion.p>
        </div>

        {/* User Context */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="bg-gray-50 rounded-xl p-6 border border-gray-100"
        >
          <p className="text-gray-700">
            Hello <span className="font-semibold text-gray-900">{user?.name}</span>,
            let's set up your investment workspace in just a few steps.
          </p>
        </motion.div>

        {/* Value Proposition */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left"
        >
          <div className="space-y-3">
            <div className="flex size-12 items-center justify-center rounded-xl bg-blue-50">
              <Target className="size-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Smart Scoring</h3>
              <p className="text-sm text-gray-600">AI-powered deal evaluation based on your thesis</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex size-12 items-center justify-center rounded-xl bg-green-50">
              <Users className="size-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Team Collaboration</h3>
              <p className="text-sm text-gray-600">Seamless workflow for your investment team</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex size-12 items-center justify-center rounded-xl bg-purple-50">
              {/* <Sparkles className="size-6 text-purple-600" /> */}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Premium Experience</h3>
              <p className="text-sm text-gray-600">Enterprise-grade tools for modern investors</p>
            </div>
          </div>
        </motion.div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
          className="pt-4"
        >
          <Button
            onClick={handleGetStarted}
            size="lg"
            className={cn(
              "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
              "hover:from-purple-600 hover:to-purple-700",
              "px-8 py-3 text-lg font-semibold",
              "shadow-lg hover:shadow-xl transition-all duration-300",
              "flex items-center gap-2"
            )}
          >
            Let's Get Started
            <ArrowRight className="size-5" />
          </Button>
        </motion.div>

        {/* Subtle Footer */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="text-sm text-gray-500 pt-4"
        >
          This will take less than 2 minutes to complete
        </motion.p>
      </motion.div>
    </div>
  )
}
