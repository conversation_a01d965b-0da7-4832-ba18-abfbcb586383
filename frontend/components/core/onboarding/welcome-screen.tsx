"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Star, Users, ArrowRight, Mail, Plus } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import OnboardingAPI from "@/lib/api/onboarding-api"

interface WelcomeScreenProps {
  userData: any
  organizationData: any
  onComplete: (data: any) => void
}

export function WelcomeScreen({ userData, organizationData, onComplete }: WelcomeScreenProps) {
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [emails, setEmails] = useState<string[]>([''])
  const [message, setMessage] = useState('')
  const [isInviting, setIsInviting] = useState(false)

  const handleAddEmail = () => {
    setEmails([...emails, ''])
  }

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...emails]
    newEmails[index] = value
    setEmails(newEmails)
  }

  const handleRemoveEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index)
      setEmails(newEmails)
    }
  }

  const handleSendInvites = async () => {
    try {
      setIsInviting(true)
      
      const validEmails = emails.filter(email => email.trim() && email.includes('@'))
      
      if (validEmails.length === 0) {
        toast.error("Please enter at least one valid email address")
        return
      }

      const response = await OnboardingAPI.sendPeerInvites({
        emails: validEmails,
        message: message.trim() || undefined,
      })

      toast.success(`Invitations sent to ${response.total_sent} people!`)
      
      // Continue to dashboard
      onComplete(userData)
    } catch (error: any) {
      console.error("Send invites error:", error)
      toast.error(error.message || "Failed to send invitations")
    } finally {
      setIsInviting(false)
    }
  }

  const handleSkipInvites = () => {
    onComplete(userData)
  }

  return (
    <div className="space-y-8 text-center">
      {/* Success Animation */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", duration: 0.6 }}
        className="relative"
      >
        <div className="mx-auto flex size-24 items-center justify-center rounded-full bg-gradient-to-br from-green-400 to-green-600">
          <Star className="size-12 text-white" />
        </div>
        
        {/* Confetti Effect */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="pointer-events-none absolute inset-0"
        >
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, rotate: 0 }}
              animate={{ 
                scale: [0, 1, 0],
                rotate: [0, 180, 360],
                x: [0, (i % 2 ? 1 : -1) * (50 + i * 10)],
                y: [0, -30 - i * 5, 20]
              }}
              transition={{ 
                duration: 2,
                delay: 0.5 + i * 0.1,
                ease: "easeOut"
              }}
              className="absolute left-1/2 top-1/2 size-2 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500"
            />
          ))}
        </motion.div>
      </motion.div>

      {/* Welcome Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <h2 className="text-3xl font-bold text-foreground">
        Welcome to TractionX!
        </h2>
        <p className="mx-auto max-w-md text-lg text-muted-foreground">
          Your organization has been set up successfully. You're ready to start building the future of investing.
        </p>
      </motion.div>

      {/* Invite Team Section */}
      {!showInviteForm ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          <Separator />
          
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Users className="size-5" />
              <span className="font-medium">Invite Your Team</span>
            </div>
            <p className="mx-auto max-w-sm text-sm text-muted-foreground">
              Get your colleagues on board and start collaborating immediately.
            </p>
          </div>

          <div className="flex flex-col justify-center gap-3 sm:flex-row">
            <Button
              onClick={() => setShowInviteForm(true)}
              className="flex h-12 items-center gap-2 rounded-xl px-6 font-semibold"
            >
              <Mail className="size-4" />
              Invite Team Members
            </Button>
            <Button
              variant="outline"
              onClick={handleSkipInvites}
              className="flex h-12 items-center gap-2 rounded-xl px-6"
            >
              Skip for Now
              <ArrowRight className="size-4" />
            </Button>
          </div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 text-left"
        >
          <Separator />
          
          <div className="space-y-4">
            <h3 className="text-center text-lg font-semibold">Invite Your Team</h3>
            
            {/* Email Inputs */}
            <div className="space-y-3">
              <Label>Email Addresses</Label>
              {emails.map((email, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => handleEmailChange(index, e.target.value)}
                    className="h-10 rounded-lg"
                  />
                  {emails.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveEmail(index)}
                      className="h-10 px-3"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddEmail}
                className="flex items-center gap-2"
              >
                <Plus className="size-4" />
                Add Another Email
              </Button>
            </div>

            {/* Optional Message */}
            <div className="space-y-2">
              <Label>Personal Message (Optional)</Label>
              <Textarea
                placeholder="Join our team on TractionX! We're excited to have you on board."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="rounded-lg"
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={handleSendInvites}
                disabled={isInviting}
                className="h-12 flex-1 rounded-xl font-semibold"
              >
                {isInviting ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="mr-2 size-4 rounded-full border-2 border-white border-t-transparent"
                  />
                ) : (
                  <Mail className="mr-2 size-4" />
                )}
                {isInviting ? "Sending..." : "Send Invites"}
              </Button>
              <Button
                variant="outline"
                onClick={handleSkipInvites}
                className="h-12 rounded-xl px-6"
              >
                Skip
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
