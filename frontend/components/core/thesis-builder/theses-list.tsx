"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Plus, Target, Edit, MoreHorizontal, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmptyPlaceholder } from '@/components/empty-placeholder';
import { DashboardHeader } from '@/components/header';
import { useToast } from '@/components/ui/use-toast';
import { ThesisAPI } from '@/lib/api/thesis-api';
import { InvestmentThesis, ThesisStatus } from '@/lib/types/thesis';
import { useAuth } from '@/lib/auth-context';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { FormAPI } from '@/lib/api/form-api';

export function ThesesList() {
  const { isAuthenticated, loading } = useAuth();
  const { toast: toastHook } = useToast();
  const router = useRouter();
  
  // State management
  const [allTheses, setAllTheses] = useState<InvestmentThesis[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [thesisToDelete, setThesisToDelete] = useState<InvestmentThesis | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  const isFetchingRef = useRef(false);

  const handleCreateThesis = async () => {
    console.log('🚀 Creating thesis with placeholder data');
    try {
      // First, get available forms to use as default
      const forms = await FormAPI.listForms();
      const defaultForm = forms.find(f => f.is_active) || forms[0];
      
      if (!defaultForm) {
        toastHook({
          title: "No forms available",
          description: "Please create a form first before creating a thesis.",
          variant: "destructive",
        });
        return;
      }

      // Create thesis with placeholder data
      const placeholderThesis = await ThesisAPI.createThesis({
        name: "New Investment Thesis",
        description: "Describe your investment thesis, target criteria, and evaluation approach...",
        form_id: defaultForm._id || defaultForm.id || '',
        status: ThesisStatus.DRAFT,
        is_active: true,
      });

      console.log('✅ Thesis created:', placeholderThesis);
      
      // Navigate to the thesis builder with the new thesis
      router.push(`/theses/${placeholderThesis._id || placeholderThesis.id}`);
      
      toastHook({
        title: "Thesis created",
        description: "Your new thesis has been created. You can now start adding matching and scoring rules.",
      });
      
    } catch (error) {
      console.error('❌ Failed to create thesis:', error);
      toastHook({
        title: "Error creating thesis",
        description: "Failed to create thesis. Please try again.",
        variant: "destructive",
      });
    }
  };

  // ✅ Removed unnecessary useCallback - not passing to child components

  // Initialize and fetch theses once authenticated
  useEffect(() => {
    if (isAuthenticated && !isInitialized) {
      console.log('🚀 Initializing theses page');
      (async () => {
        try {
          isFetchingRef.current = true;
          console.log("🔄 Fetching theses from API");
          setError(null);

          const thesesList = await ThesisAPI.listTheses();
          console.log("✅ Theses fetched:", thesesList?.length || 0, 'theses');

          if (Array.isArray(thesesList)) {
            setAllTheses(thesesList);
          } else {
            console.warn("⚠️ API returned non-array response:", thesesList);
            setAllTheses([]);
          }
        } catch (error) {
          console.error("❌ Error fetching theses:", error);
          setAllTheses([]);
          setError('Failed to load theses. Please try again.');
          toastHook({
            title: "Error",
            description: "Failed to load theses. Please try again.",
            variant: "destructive",
          });
        } finally {
          isFetchingRef.current = false;
        }
      })();
      setIsInitialized(true);
    }
  }, [isAuthenticated, isInitialized]); // ✅ Clean, safe - no fetchTheses dependency

  const handleDeleteThesis = async (thesisId: string): Promise<boolean> => {
    if (isDeleting) {
      console.log('⚠️ Delete operation already in progress');
      return false;
    }

    try {
      setIsDeleting(true);
      console.log('🗑️ Deleting thesis:', thesisId);
      
      await ThesisAPI.deleteThesis(thesisId);
      console.log('✅ Thesis deleted successfully');
      
      // ✅ Direct refetch without state tracking
      try {
        isFetchingRef.current = true;
        const thesesList = await ThesisAPI.listTheses();
        console.log("✅ Theses refetched:", thesesList?.length || 0, 'theses');
        
        if (Array.isArray(thesesList)) {
          setAllTheses(thesesList);
        } else {
          setAllTheses([]);
        }
      } catch (fetchError) {
        console.error("❌ Error refetching theses:", fetchError);
        setError('Failed to refresh theses list.');
      } finally {
        isFetchingRef.current = false;
      }
      
      toastHook({
        title: "Thesis deleted",
        description: "Thesis has been deleted successfully.",
      });
      
      return true
    } catch (error: any) {
      console.error('❌ Error deleting thesis:', error);
      toastHook({
        title: "Error",
        description: "Failed to delete thesis. Please try again.",
        variant: "destructive",
      });
      return false
    } finally {
      setIsDeleting(false);
      setOpenDropdownId(null); // Ensure dropdown is closed after successful deletion
    }
  };

  // ✅ Simple refresh function without useCallback
  const handleRefresh = () => {
    if (isFetchingRef.current) return;
    
    (async () => {
      try {
        isFetchingRef.current = true;
        const thesesList = await ThesisAPI.listTheses();
        if (Array.isArray(thesesList)) {
          setAllTheses(thesesList);
        }
      } catch (error) {
        console.error("❌ Error refreshing theses:", error);
      } finally {
        isFetchingRef.current = false;
      }
    })();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 text-xs font-medium shadow-sm border border-blue-200">
            Active
          </span>
        );
      case 'draft':
      case 'archived':
      default:
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-500 text-xs font-medium border border-gray-200">
            {status === 'draft' ? 'Draft' : status === 'archived' ? 'Archived' : status}
          </span>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
        <div className="size-8 animate-spin rounded-full border-b-2 border-blue-600" aria-hidden="true"></div>
        <span className="ml-2">Checking authentication...</span>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
        <div className="text-center">
          <p className="text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" role="main" aria-label="Investment theses management page">
      <DashboardHeader
        data-tour="theses-header"
        heading="Investment Theses"
        text="Create and manage your investment theses."
        tourType="theses"
      >
        <div className="flex items-center gap-3">
          <Button data-tour="create-thesis-btn" onClick={handleCreateThesis}>
            <Plus className="size-4" />
          </Button>
        </div>
      </DashboardHeader>

      <div>
        {!isInitialized ? (
          // Loading skeleton
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3" role="status" aria-live="polite" aria-label="Loading theses">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                  <div className="h-3 w-1/2 rounded bg-muted"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 rounded bg-muted"></div>
                    <div className="h-3 w-2/3 rounded bg-muted"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <div className="flex items-center justify-center p-8" role="alert" aria-live="assertive">
            <div className="text-center">
              <p className="text-red-600">{error}</p>
              <Button onClick={handleRefresh} className="mt-4">
                Try Again
              </Button>
            </div>
          </div>
        ) : !allTheses || allTheses.length === 0 ? (
          <div role="status" aria-live="polite">
            <EmptyPlaceholder>
              <EmptyPlaceholder.Icon name="page" />
              <EmptyPlaceholder.Title>No theses created</EmptyPlaceholder.Title>
              <EmptyPlaceholder.Description>
                You don&apos;t have any investment theses yet. Create a thesis to define your investment strategy.
              </EmptyPlaceholder.Description>
              <Button variant="outline" onClick={handleCreateThesis}>
                <Plus className="size-4" />
              </Button>
            </EmptyPlaceholder>
          </div>
        ) : (
          <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3" role="grid" aria-label="Theses grid">
            {allTheses.map((thesis) => (
              <Card 
                key={thesis._id || thesis.id} 
                className="flex flex-col hover:shadow-md transition-shadow duration-200"
                data-tour={allTheses.indexOf(thesis) === 0 ? "thesis-card" : undefined}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="min-w-0 flex-1 space-y-2">
                      <CardTitle className="text-xl">{thesis.name}</CardTitle>
                      <div className="flex items-center gap-3">
                        {getStatusBadge(thesis.status)}
                        <span className="text-sm text-muted-foreground">
                          Updated {thesis.updated_at ? new Date(thesis.updated_at * 1000).toLocaleDateString() : 'N/A'}
                        </span>
                      </div>
                    </div>
                    <div className="relative">
                      <DropdownMenu 
                        open={openDropdownId === (thesis._id || thesis.id)} 
                        onOpenChange={(open) => {
                          const thesisId = thesis._id || thesis.id;
                          if (open && thesisId) {
                            setOpenDropdownId(thesisId);
                          } else {
                            setOpenDropdownId(null);
                          }
                        }}
                      >
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="shrink-0"
                            data-tour={allTheses.indexOf(thesis) === 0 ? "thesis-actions" : undefined}
                            aria-label="Thesis actions menu"
                          >
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          side="bottom"
                          align="end"
                          sideOffset={4}
                          collisionPadding={12}
                          className="w-48"
                          onCloseAutoFocus={(e) => {
                            // Prevent focus from being trapped in the dropdown
                            e.preventDefault();
                          }}
                        >
                          <DropdownMenuItem 
                            asChild
                            onSelect={(e) => {
                              // Prevent default selection behavior that might cause focus issues
                              e.preventDefault();
                            }}
                          >
                            <Link href={`/theses/${thesis._id || thesis.id}`}>
                              <Edit className="mr-2 size-4 text-muted-foreground" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setOpenDropdownId(null); // Close dropdown first
                              setThesisToDelete(thesis);
                              setDeleteDialogOpen(true);
                            }}
                            className="flex w-full text-destructive focus:text-destructive hover:text-destructive"
                            onSelect={(e) => {
                              // Prevent default selection behavior that might cause focus issues
                              e.preventDefault();
                            }}
                          >
                            <Trash2 className="mr-2 size-4 text-destructive" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="flex grow flex-col space-y-4">
                  <CardDescription className="line-clamp-2">
                    {thesis.description}
                  </CardDescription>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Rules:</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          <Target className="mr-1 size-3" />
                          {thesis.match_rules?.length || 0} match
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {thesis.scoring_rules?.length || 0} scoring
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Status:</span>
                      <span className={`font-medium ${thesis.status === 'active' ? 'text-gray-900' : thesis.status === 'archived' ? 'text-gray-500' : 'text-gray-500'}`}>
                        {thesis.status === 'active' ? 'Active' : thesis.status === 'archived' ? 'Archived' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                  <div className="mt-auto pt-2">
                    <Link href={`/theses/${thesis._id || thesis.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        <Edit className="size-4" />
                        Edit Thesis
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Thesis"
        description={`Are you sure you want to delete the thesis "${thesisToDelete?.name || ''}"? This action cannot be undone.`}
        onCancel={() => { 
          setDeleteDialogOpen(false); 
          setThesisToDelete(null); 
          setOpenDropdownId(null); // Ensure dropdown is closed when modal closes
        }}
        onConfirm={async () => {
          if (!thesisToDelete) return;
          const thesisId = String(thesisToDelete._id || thesisToDelete.id);
          if (!thesisId || thesisId === 'undefined') {
            toastHook({
              title: 'Error',
              description: 'Invalid thesis ID.',
              variant: 'destructive'
            });
            return;
          }
          
          const success = await handleDeleteThesis(thesisId);
          if (success) {
            setDeleteDialogOpen(false);
            setThesisToDelete(null);
            setOpenDropdownId(null); // Ensure dropdown is closed when modal closes
          }
        }}
        loading={deleting}
      />
    </div>
  );
}
