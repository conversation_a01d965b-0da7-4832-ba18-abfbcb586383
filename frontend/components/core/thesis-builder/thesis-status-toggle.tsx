import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';
import { ThesisStatus } from '@/lib/types/thesis';

interface ThesisStatusToggleProps {
  currentStatus: ThesisStatus;
  onStatusChange: (status: ThesisStatus) => void;
  disabled?: boolean;
}

const statusConfig = {
  [ThesisStatus.DRAFT]: {
    label: 'Draft',
    description: 'Thesis is in draft mode and not yet active',
    variant: 'secondary' as const,
  },
  [ThesisStatus.ACTIVE]: {
    label: 'Active',
    description: 'Thesis is active and being used for scoring',
    variant: 'active' as const,
  },
  [ThesisStatus.ARCHIVED]: {
    label: 'Archived',
    description: 'Thesis is archived and no longer in use',
    variant: 'destructive' as const,
  },
};

export function ThesisStatusToggle({ currentStatus, onStatusChange, disabled = false }: ThesisStatusToggleProps) {
  const config = statusConfig[currentStatus];

  const renderStatusBadge = (status: ThesisStatus) => {
    const statusInfo = statusConfig[status];
    
    if (status === ThesisStatus.ACTIVE) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-blue-50 text-blue-700 text-xs font-medium border border-blue-200">
          {statusInfo.label}
        </span>
      );
    }
    
    if (status === ThesisStatus.DRAFT) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-gray-50 text-gray-600 text-xs font-medium border border-gray-200">
          {statusInfo.label}
        </span>
      );
    }
    
    if (status === ThesisStatus.ARCHIVED) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-gray-100 text-gray-500 text-xs font-medium border border-gray-200">
          {statusInfo.label}
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-gray-50 text-gray-600 text-xs font-medium border border-gray-200">
        {statusInfo.label}
      </span>
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button variant="outline" size="sm" className="flex items-center gap-2 h-8">
          {renderStatusBadge(currentStatus)}
          <ChevronDown className="size-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="end"
        sideOffset={8}
        collisionPadding={12}
      >
        {Object.entries(statusConfig).map(([status, { label, description }]) => (
          <DropdownMenuItem
            key={status}
            onClick={() => onStatusChange(status as ThesisStatus)}
            disabled={status === currentStatus || disabled}
            className="flex flex-col items-start gap-1"
          >
            <span className="font-medium">{label}</span>
            <span className="text-xs text-muted-foreground">{description}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 