"use client"

import React from "react"
import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/lib/auth-context"
import { Icons } from "@/components/icons"
import { UnifiedTourButton } from "@/components/core/tour/unified-tour-button"
import { Target, FileText, BarChart3, Settings } from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardHeaderProps {
  heading: string
  text?: string
  children?: React.ReactNode
  showOrgName?: boolean
  "data-tour"?: string
  tourType?: "dashboard" | "forms" | "deals" | "theses" | "settings"
  icon?: React.ComponentType<{ className?: string }>
  iconClassName?: string
  className?: string
}

export function DashboardHeader({
  heading,
  text,
  children,
  showOrgName = false,
  "data-tour": dataTour,
  tourType,
  icon,
  iconClassName,
  className,
}: DashboardHeaderProps) {
  const { userOrganizations, orgId } = useAuth()

  // Find current organization
  const currentOrg = userOrganizations.find(org => org.id === orgId) || userOrganizations[0]
  const orgName = currentOrg?.name || "Organization"

  // Tour badge configuration
  const getTourConfig = (type: string) => {
    switch (type) {
      case "dashboard":
        return { icon: BarChart3, label: "Dashboard Tour", iconColor: "text-purple-500", gradientFrom: "from-purple-500", gradientTo: "to-blue-500" }
      case "deals":
        return { icon: Target, label: "Deals Tour", iconColor: "text-blue-500", gradientFrom: "from-blue-500", gradientTo: "to-purple-500" }
      case "forms":
        return { icon: FileText, label: "Forms Tour", iconColor: "text-emerald-500", gradientFrom: "from-emerald-500", gradientTo: "to-blue-500" }
      case "theses":
        return { icon: Target, label: "Theses Tour", iconColor: "text-orange-500", gradientFrom: "from-orange-500", gradientTo: "to-purple-500" }
      case "settings":
        return { icon: Settings, label: "Settings Tour", iconColor: "text-blue-500", gradientFrom: "from-blue-500", gradientTo: "to-purple-500" }
      default:
        return null
    }
  }

  const tourConfig = tourType ? getTourConfig(tourType) : null

  return (
    <div className={cn("border-b border-gray-100 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60", className)}>
      <div className="px-6 py-8 sm:px-8 sm:py-10 lg:px-2">
        <div className="flex items-center justify-between" data-tour={dataTour}>
          <div className="space-y-3">
            {showOrgName && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{orgName}</span>
                <span>•</span>
                <span>{heading}</span>
              </div>
            )}
            <div className="flex items-center gap-3">
              {/* {icon && (
                <div className={cn("flex size-12 items-center justify-center rounded-xl bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] shadow-lg", iconClassName)}>
                  {React.createElement(icon, { className: "size-6 text-white" })}
                </div>
              )} */}
              <h1 className="text-4xl font-bold tracking-tight text-gray-900">{heading}</h1>
              {tourConfig && (
                <UnifiedTourButton
                  tourType={tourType as any}
                  icon={tourConfig.icon}
                  label={tourConfig.label}
                  iconColor={tourConfig.iconColor}
                  gradientFrom={tourConfig.gradientFrom}
                  gradientTo={tourConfig.gradientTo}
                />
              )}
            </div>
            {text && <p className="text-xl text-gray-600">{text}</p>}
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
