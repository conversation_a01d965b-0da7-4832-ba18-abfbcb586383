/**
 * Tour API Client
 *
 * Handles communication with the backend tour progress tracking system.
 * Supports fetching tour progress, marking tours complete, and tracking prompt display.
 */

import apiClient from '@/lib/api-client';

export interface TourProgressResponse {
  completed: string[];
  last_seen_prompt: number | null;
  completed_at: number | null;
}

export interface CompleteTourRequest {
  tour_id: string;
}

export interface TourCompletionResponse {
  success: boolean;
  message?: string;
}

export interface UserMeResponse {
  user_id: string;
  email: string;
  name: string;
  org_id: string | null;
  role_id: string | null;
  status: string;
  tour_progress: TourProgressResponse | null;
  should_prompt_tour: boolean;
  is_first_login: boolean;
}

// Cache for preventing duplicate API calls
let userInfoCache: { data: UserMeResponse | null; timestamp: number } = {
  data: null,
  timestamp: 0
}
let userInfoPromise: Promise<UserMeResponse> | null = null

const CACHE_DURATION = 30000 // 30 seconds

/**
 * Tour API service for interacting with the backend
 */
export const TourAPI = {
  /**
   * Get current user's tour progress
   */
  async getTourProgress(): Promise<TourProgressResponse> {
    console.log('Fetching user tour progress');
    
    try {
      const response = await apiClient.get('/users/tours');
      console.log('Tour progress fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching tour progress:', error);
      throw error;
    }
  },

  /**
   * Mark a tour as completed
   */
  async completeTour(tourId: string): Promise<TourCompletionResponse> {
    console.log(`Marking tour as completed: ${tourId}`);
    
    try {
      const response = await apiClient.post('/users/tours/complete', {
        tour_id: tourId
      });
      console.log('Tour marked as completed:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error completing tour:', error);
      throw error;
    }
  },

  /**
   * Mark that user has seen the tour prompt
   */
  async markPromptSeen(): Promise<TourCompletionResponse> {
    console.log('Marking tour prompt as seen');
    
    try {
      const response = await apiClient.post('/users/tours/prompt_seen');
      console.log('Tour prompt marked as seen:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error marking prompt as seen:', error);
      throw error;
    }
  },

  /**
   * Get enhanced user info including tour progress (with caching)
   */
  async getUserInfo(): Promise<UserMeResponse> {
    const now = Date.now()
    
    // Return cached data if still valid
    if (userInfoCache.data && (now - userInfoCache.timestamp) < CACHE_DURATION) {
      console.log('📦 Using cached user info')
      return userInfoCache.data
    }
    
    // Return existing promise if one is already in flight
    if (userInfoPromise) {
      console.log('⏳ Waiting for existing user info request')
      return userInfoPromise
    }
    
    console.log('🌐 Fetching fresh user info with tour progress');
    
    userInfoPromise = (async () => {
      try {
        const response = await apiClient.get('/users/me');
        console.log('Enhanced user info fetched:', response.data);
        
        // Update cache
        userInfoCache = {
          data: response.data,
          timestamp: now
        }
        
        return response.data;
      } catch (error) {
        console.error('Error fetching user info:', error);
        throw error;
      } finally {
        // Clear the promise so new requests can be made
        userInfoPromise = null
      }
    })()
    
    return userInfoPromise
  },

  /**
   * Clear the user info cache (call after tour progress updates)
   */
  clearUserInfoCache() {
    console.log('🗑️ Clearing user info cache')
    userInfoCache = { data: null, timestamp: 0 }
    userInfoPromise = null
  }
}; 