import { useState, useEffect, useCallback } from 'react'
import { DealDetailAPI } from '@/lib/api/deal-detail-api'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/auth-context'

interface CompanyEnrichedV2Data {
  // Base company fields (always present)
  company: {
    name?: string
    domain?: string
    website?: string
    description?: string
    industry?: string
    sub_industry?: string
    business_model?: string
    company_type?: string
    incorporation_country?: string
    hq_location?: string
    revenue?: number
    employee_count?: number
    employee_count_range?: string
    founded_year?: number
    traction_level?: string
    funding_status?: string
    funding_total?: number
    funding_rounds?: any[]
    last_funding_date?: string
    last_funding_amount?: number
    valuation?: number
    deal_type?: string
    round_size?: string
    stage?: string
    sector?: string
    geo_tags?: string[]
    linkedin_url?: string
    twitter_url?: string
    facebook_url?: string
    email?: string
    phone?: string
    source?: string
    confidence_score?: number
    enrichment_date?: string
    created_at?: string
    updated_at?: string
  }
  // Available enrichment sources
  sources: {
    pitchbook?: boolean
    crunchbase?: boolean
    linkedin?: boolean
    apollo?: boolean
  }
  // Optional company fields from RDS
  company_fields?: Record<string, any>
}

interface SourceEnrichmentData {
  source: string
  data: Record<string, any>
}

interface UseCompanyEnrichedV2Return {
  data: CompanyEnrichedV2Data | null
  loading: boolean
  error: string | null
  refreshing: boolean
  hasData: boolean
  availableSources: string[]
  sourceData: Record<string, SourceEnrichmentData | null>
  sourceLoading: Record<string, boolean>
  sourceErrors: Record<string, string | null>
  refresh: () => Promise<void>
  reload: () => Promise<void>
  fetchSourceData: (source: string) => Promise<void>
}

export function useCompanyEnrichedV2({ dealId }: { dealId: string }): UseCompanyEnrichedV2Return {
  const [data, setData] = useState<CompanyEnrichedV2Data | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [sourceData, setSourceData] = useState<Record<string, SourceEnrichmentData | null>>({})
  const [sourceLoading, setSourceLoading] = useState<Record<string, boolean>>({})
  const [sourceErrors, setSourceErrors] = useState<Record<string, string | null>>({})
  
  const { toast } = useToast()
  const { isAuthenticated } = useAuth()

  const fetchCompanyEnrichedV2 = useCallback(async () => {
    // Don't fetch if not authenticated
    if (!isAuthenticated) {
      setError('Authentication required. Please log in to view company data.')
      setLoading(false)
      return
    }

    try {
      setError(null)
      console.log(`🔍 Fetching company enriched data v2 for deal ${dealId}`)
      const companyData = await DealDetailAPI.getCompanyEnrichedV2(dealId)
      console.log('✅ Company enriched data v2 received:', companyData)
      
      // Backend returns flat company object directly
      if (companyData && typeof companyData === 'object') {
        // Check if this looks like company data (has fields like name, domain, etc.)
        const hasCompanyFields = companyData.name || companyData.domain || companyData.website
        if (hasCompanyFields) {
          // Convert flat structure to expected structure for frontend
          const sources = {}
          for (const source of ["pitchbook", "crunchbase", "linkedin", "apollo"]) {
            const sourceField = `${source}_data`
            sources[source] = Boolean(companyData[sourceField])
          }
          
          setData({
            company: companyData,
            sources: sources
          })
        } else {
          setData(companyData)
        }
      } else {
        setData(companyData)
      }
    } catch (err: any) {
      // Handle different types of errors gracefully
      let errorMessage = 'Failed to fetch company enriched data'
      
      if (err?.response?.status === 401) {
        errorMessage = 'Authentication required. Please log in to view company data.'
      } else if (err?.response?.status === 403) {
        errorMessage = 'Access denied. You do not have permission to view this company data.'
      } else if (err?.response?.status === 404) {
        errorMessage = 'Company data not found for this deal.'
      } else if (err?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.'
      } else if (err?.message) {
        errorMessage = err.message
      }
      
      console.error('❌ Error fetching company enriched data v2:', {
        error: err,
        message: errorMessage,
        status: err?.response?.status,
        dealId
      })
      
      setError(errorMessage)
      
      // Only show toast for non-authentication errors
      if (err?.response?.status !== 401) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    }
  }, [dealId, isAuthenticated, toast])

  const fetchSourceData = useCallback(async (source: string) => {
    // Don't fetch if not authenticated
    if (!isAuthenticated) {
      setSourceErrors(prev => ({ ...prev, [source]: 'Authentication required' }))
      return
    }

    // Don't fetch if already loaded
    if (sourceData[source] !== null && sourceData[source] !== undefined) {
      return
    }

    try {
      setSourceLoading(prev => ({ ...prev, [source]: true }))
      setSourceErrors(prev => ({ ...prev, [source]: null }))
      
      console.log(`🔍 Fetching ${source} enrichment data for deal ${dealId}`)
      const sourceResponse = await DealDetailAPI.getSourceEnrichment(dealId, source)
      console.log(`✅ ${source} enrichment data received:`, sourceResponse)
      
      setSourceData(prev => ({ 
        ...prev, 
        [source]: { source, data: sourceResponse.data || sourceResponse } 
      }))
    } catch (err: any) {
      let errorMessage = `Failed to fetch ${source} data`
      
      if (err?.response?.status === 404) {
        errorMessage = `No ${source} data available for this company.`
      } else if (err?.response?.status >= 500) {
        errorMessage = `Server error while fetching ${source} data.`
      } else if (err?.message) {
        errorMessage = err.message
      }
      
      console.error(`❌ Error fetching ${source} enrichment data:`, {
        error: err,
        message: errorMessage,
        status: err?.response?.status,
        dealId,
        source
      })
      
      setSourceErrors(prev => ({ ...prev, [source]: errorMessage }))
      
      // Show toast for source-specific errors
      toast({
        title: `${source.charAt(0).toUpperCase() + source.slice(1)} Error`,
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setSourceLoading(prev => ({ ...prev, [source]: false }))
    }
  }, [dealId, isAuthenticated, sourceData, toast])

  const load = useCallback(async () => {
    setLoading(true)
    await fetchCompanyEnrichedV2()
    setLoading(false)
  }, [fetchCompanyEnrichedV2])

  const refresh = useCallback(async () => {
    setRefreshing(true)
    await fetchCompanyEnrichedV2()
    setRefreshing(false)
    
    toast({
      title: "Company Data Refreshed",
      description: "The company enrichment data has been updated.",
    })
  }, [fetchCompanyEnrichedV2, toast])

  const reload = useCallback(async () => {
    setLoading(true)
    setError(null)
    await fetchCompanyEnrichedV2()
    setLoading(false)
  }, [fetchCompanyEnrichedV2])

  useEffect(() => {
    load()
  }, [load])

  // Always show all source tabs - let the API calls determine if data is available
  const availableSources = ["pitchbook", "crunchbase", "linkedin", "apollo"]

  // Check if data exists - handle both the expected structure and the flat structure
  const hasData = Boolean(data?.company || (data && typeof data === 'object' && Object.keys(data).length > 0))
  


  return {
    data,
    loading,
    error,
    refreshing,
    hasData,
    availableSources,
    sourceData,
    sourceLoading,
    sourceErrors,
    refresh,
    reload,
    fetchSourceData
  }
} 