"use client"

import { useState, useEffect, useCallback } from "react"
import { SettingsAPI, ThesisConfig, OrganizationInfo } from "@/lib/api/settings-api"

interface UseOrgThesisCheckReturn {
  isConfigured: boolean
  isLoading: boolean
  error: string | null
  showPrompt: boolean
  dismissPrompt: () => void
  refresh: () => void
}

export function useOrgThesisCheck(): UseOrgThesisCheckReturn {
  const [isConfigured, setIsConfigured] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showPrompt, setShowPrompt] = useState(false)

  const checkThesisConfig = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Check organization data for thesis_config field
      const orgData = await SettingsAPI.getOrganization()
      
      // If thesis_config is null or undefined, show prompt
      const hasThesisConfig = orgData.thesis_config !== null && orgData.thesis_config !== undefined
      
      let hasValues = false
      if (hasThesisConfig && orgData.thesis_config) {
        // Check if at least one field has values
        hasValues = orgData.thesis_config.geography.length > 0 || 
                   orgData.thesis_config.sector.length > 0 || 
                   orgData.thesis_config.stage.length > 0 || 
                   orgData.thesis_config.business_model.length > 0
      }
      
      setIsConfigured(hasValues)
      
      // Always show prompt if thesis_config is null or empty - clear any previous dismissal
      if (!hasValues) {
        // Clear dismissal when thesis is not configured - make it concrete!
        localStorage.removeItem('org-thesis-prompt-dismissed')
        setShowPrompt(true)
      } else {
        // Only respect dismissal when thesis is actually configured
        const dismissed = localStorage.getItem('org-thesis-prompt-dismissed')
        setShowPrompt(false)
      }
      
    } catch (err: any) {
      console.error('Error checking org thesis config:', err)
      // If org doesn't exist or other error, assume not configured
      setIsConfigured(false)
      // Always show prompt on error (likely means not configured)
      localStorage.removeItem('org-thesis-prompt-dismissed')
      setShowPrompt(true)
      setError(err.message || 'Failed to check thesis configuration')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const dismissPrompt = useCallback(() => {
    setShowPrompt(false)
    localStorage.setItem('org-thesis-prompt-dismissed', 'true')
  }, [])

  const refresh = useCallback(() => {
    checkThesisConfig()
  }, [checkThesisConfig])

  useEffect(() => {
    checkThesisConfig()
  }, [checkThesisConfig])

  return {
    isConfigured,
    isLoading,
    error,
    showPrompt,
    dismissPrompt,
    refresh
  }
} 