"use client"

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/lib/auth-context';
import { User } from '@/lib/types/deal';
import DealAPI from '@/lib/api/deal-api';

interface UseOrgMembersCacheReturn {
  members: User[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Global cache to prevent multiple API calls
let globalMembersCache: User[] = [];
let globalCacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function useOrgMembersCache(): UseOrgMembersCacheReturn {
  const { user } = useAuth();
  const [members, setMembers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Create a stable key for the user to prevent unnecessary re-renders
  const userKey = useMemo(() => {
    return user?.org_id && user?.id ? `${user.org_id}-${user.id}` : null;
  }, [user?.org_id, user?.id]);

  const fetchMembers = useCallback(async () => {
    if (!user?.org_id) {
      setError('No organization context');
      setIsLoading(false);
      return;
    }

    // Check if we have valid cached data
    const now = Date.now();
    if (globalMembersCache.length > 0 && (now - globalCacheTimestamp) < CACHE_DURATION) {
      console.log('🔍 Using cached org members');
      setMembers(globalMembersCache);
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      setIsLoading(true);
      
      console.log('🔍 Fetching org members from API');
      const users = await DealAPI.getOrgUsers();
      
      // Update global cache
      globalMembersCache = users;
      globalCacheTimestamp = now;
      
      setMembers(users);
    } catch (err) {
      console.error('Error fetching org members:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch members');
    } finally {
      setIsLoading(false);
    }
  }, [user?.org_id]);

  useEffect(() => {
    if (userKey) {
      fetchMembers();
    }
  }, [userKey, fetchMembers]);

  const refetch = useCallback(async () => {
    // Clear cache to force fresh fetch
    globalMembersCache = [];
    globalCacheTimestamp = 0;
    await fetchMembers();
  }, [fetchMembers]);

  return {
    members,
    isLoading,
    error,
    refetch,
  };
} 