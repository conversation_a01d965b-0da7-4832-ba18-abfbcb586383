/**
 * Global Tour Engine Hook
 * 
 * Central coordination system for the TractionX onboarding tour flow.
 * Handles automatic tour triggering, page navigation, and backend sync.
 */

"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { TourAPI, TourProgressResponse, UserMeResponse } from '@/lib/api/tour-api'

// Onboarding flow configuration - Use unified tour instead of separate page tours
const UNIFIED_ONBOARDING_FLOW = {
  tourId: "onboarding",
  navigationSteps: [
    "nav-to-deals",
    "nav-to-theses", 
    "nav-to-forms"
  ]
} as const

// Main onboarding tour (unified experience)
const MAIN_ONBOARDING_TOUR = "onboarding" as const

// Product tours (individual page features)
const PRODUCT_TOURS = [
  { tourId: "dashboard", route: "/dashboard" },
  { tourId: "deals", route: "/deals" },
  { tourId: "theses", route: "/theses" },
  { tourId: "forms", route: "/forms" },
] as const

export type TourId = typeof PRODUCT_TOURS[number]['tourId'] | typeof MAIN_ONBOARDING_TOUR

interface TourEngineState {
  // Backend tour progress
  backendProgress: TourProgressResponse | null
  // User info with tour flags
  userInfo: UserMeResponse | null
  // Engine state
  isEngineRunning: boolean
  currentTourIndex: number
  isLoading: boolean
  error: string | null
  // Flow control
  hasPromptBeenSeen: boolean
  shouldAutoTrigger: boolean
}

// Tour system launch timestamp for existing users
const TOUR_SYSTEM_LAUNCH_TIMESTAMP = 1737504000  // January 21, 2025

export function useTourEngine() {
  const router = useRouter()
  const pathname = usePathname()
  
  // Use a ref to track engine running status to avoid stale closures
  const isEngineRunningRef = useRef(false)

  const [state, setState] = useState<TourEngineState>({
    backendProgress: null,
    userInfo: null,
    isEngineRunning: false,
    currentTourIndex: 0,
    isLoading: true,
    error: null,
    hasPromptBeenSeen: false,
    shouldAutoTrigger: false,
  })

  // Sync ref with state
  useEffect(() => {
    isEngineRunningRef.current = state.isEngineRunning
  }, [state.isEngineRunning])

  // Initialize the tour engine by fetching user info and tour progress
  const initializeTourEngine = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Fetch user info with tour progress from backend
      const userInfo = await TourAPI.getUserInfo()
      const tourProgress = userInfo.tour_progress || {
        completed: [],
        last_seen_prompt: null,
        completed_at: null
      }

      // Enhanced logic focused on main onboarding tour
      const completedTours = tourProgress.completed
      const hasCompletedMainOnboarding = completedTours.includes(MAIN_ONBOARDING_TOUR)
      const hasSeenPrompt = !!tourProgress.last_seen_prompt

      // Should auto-trigger based on backend logic (mainly for main onboarding)
      const shouldAutoTrigger = userInfo.should_prompt_tour

      // Track completion of main onboarding vs product tours
      const completedProductTours = PRODUCT_TOURS.filter(tour => 
        completedTours.includes(tour.tourId)
      ).length

      setState(prev => ({
        ...prev,
        backendProgress: tourProgress,
        userInfo,
        shouldAutoTrigger,
        hasPromptBeenSeen: hasSeenPrompt,
        currentTourIndex: 0, // No longer needed with main onboarding approach
        isLoading: false
      }))

      console.log('🎯 Tour Engine Initialized:', {
        userInfo: userInfo.email,
        shouldAutoTrigger,
        completedTours,
        hasCompletedMainOnboarding,
        completedProductTours,
        hasSeenPrompt,
        isFirstLogin: userInfo.is_first_login,
        mainOnboardingTour: MAIN_ONBOARDING_TOUR
      })

    } catch (error) {
      console.error('❌ Tour Engine Initialization Failed:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to initialize tour engine',
        isLoading: false
      }))
    }
  }, [])

  // Initialize tour engine on mount (only once)
  useEffect(() => {
    if (!state.userInfo && !state.isLoading) {
      initializeTourEngine()
    }
  }, [initializeTourEngine])

  // Start the coordinated tour engine flow
  const startTourEngine = useCallback(async () => {
    if (isEngineRunningRef.current) {
      console.log('🚫 Tour engine already running')
      return
    }

    // Allow any user to start the flow manually
    try {
      console.log('🚀 Starting Unified Onboarding Tour')

      // Mark prompt as seen in backend if not already
      if (!state.hasPromptBeenSeen) {
        await TourAPI.markPromptSeen()
        setState(prev => ({ ...prev, hasPromptBeenSeen: true }))
        
        // Clear cache to ensure fresh data on next fetch
        TourAPI.clearUserInfoCache()
      }

      setState(prev => ({ ...prev, isEngineRunning: true }))
      isEngineRunningRef.current = true

      // Start the main onboarding tour directly
      triggerPageTour(MAIN_ONBOARDING_TOUR)

    } catch (error) {
      console.error('❌ Failed to start tour engine:', error)
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to start tour engine',
        isEngineRunning: false 
      }))
      isEngineRunningRef.current = false
    }
  }, [state.hasPromptBeenSeen])

  // Handle navigation within the unified tour
  const handleNavigationStep = useCallback((stepId: string) => {
    console.log(`🧭 Handling navigation step: ${stepId}`)
    
    switch (stepId) {
      case 'nav-to-deals':
        console.log('📍 Navigating to /deals')
        router.push('/deals')
        break
      case 'nav-to-theses':
        console.log('📍 Navigating to /theses')
        router.push('/theses')
        break
      case 'nav-to-forms':
        console.log('📍 Navigating to /forms')
        router.push('/forms')
        break
      default:
        console.log(`🤷 Unknown navigation step: ${stepId}`)
    }
  }, [router])

  // Trigger a specific tour (to be called by page components)
  const triggerPageTour = useCallback((tourId: TourId) => {
    console.log(`🎯 Triggering page tour: ${tourId}`)
    // This will be handled by the enhanced tour provider
    // We emit a custom event that the provider can listen to
    window.dispatchEvent(new CustomEvent('tourEngine:startTour', { 
      detail: { tourId } 
    }))
  }, [])

  // Handle tour completion (both local and backend sync)
  const handleTourComplete = useCallback(async (tourId: TourId) => {
    try {
      console.log(`✅ Tour completed: ${tourId}`)
      
            // Handle main onboarding tour completion
      if (tourId === MAIN_ONBOARDING_TOUR) {
        console.log('🎉 Main onboarding tour completed!')
        
        // Mark main onboarding as completed in backend
        await TourAPI.completeTour(MAIN_ONBOARDING_TOUR)
        
        // Clear cache and refresh engine state
        TourAPI.clearUserInfoCache()
        
        // Update local state to reflect completion
        setState(prev => ({
          ...prev,
          backendProgress: prev.backendProgress ? {
            ...prev.backendProgress,
            completed: [...(prev.backendProgress.completed || []), MAIN_ONBOARDING_TOUR],
            completed_at: Date.now()
          } : null,
          isEngineRunning: false,
          shouldAutoTrigger: false
        }))
        isEngineRunningRef.current = false
        
        console.log('🎊 Main onboarding tour completed!')
        return
      }

      // Handle individual product tour completion
      console.log(`🔍 Product tour completed: ${tourId}`)

      // Mark tour as completed in backend
      await TourAPI.completeTour(tourId)
      
      // Clear cache to ensure fresh data on next fetch
      TourAPI.clearUserInfoCache()

      // Update local state for product tours
      const completedTours = [...(state.backendProgress?.completed || []), tourId]
      
      setState(prev => ({
        ...prev,
        backendProgress: prev.backendProgress ? {
          ...prev.backendProgress,
          completed: completedTours
        } : null
      }))

      console.log(`✅ Product tour "${tourId}" marked as completed`)

    } catch (error) {
      console.error(`❌ Failed to complete tour ${tourId}:`, error)
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to complete tour' 
      }))
    }
  }, [state.backendProgress, state.currentTourIndex])

  // Handle tour step completion with navigation
  const handleTourStepComplete = useCallback((stepId: string) => {
    console.log(`🔄 Tour step completed: ${stepId}`)
    
    // Check if this is a navigation step
    if (UNIFIED_ONBOARDING_FLOW.navigationSteps.includes(stepId as any)) {
      handleNavigationStep(stepId)
    }
  }, [handleNavigationStep])

  // Stop the tour engine
  const stopTourEngine = useCallback(() => {
    console.log('🛑 Stopping tour engine')
    setState(prev => ({ ...prev, isEngineRunning: false }))
    isEngineRunningRef.current = false
  }, [])

  // Manual tour trigger (bypasses engine flow)
  const startManualTour = useCallback((tourId: TourId) => {
    console.log(`🎯 Starting manual tour: ${tourId}`)
    
    if (tourId === MAIN_ONBOARDING_TOUR) {
      // Main onboarding tour starts on current page
      triggerPageTour(tourId)
    } else {
      // Product tours need to navigate to their respective pages
      const tour = PRODUCT_TOURS.find(t => t.tourId === tourId)
      
      if (tour && pathname !== tour.route) {
        router.push(tour.route)
        // The tour will be triggered by the page component
      } else {
        triggerPageTour(tourId)
      }
    }
  }, [pathname, router, triggerPageTour])

  // Check if a specific tour is completed
  const isTourCompleted = useCallback((tourId: TourId): boolean => {
    return state.backendProgress?.completed.includes(tourId) || false
  }, [state.backendProgress])

  // Check if we should auto-trigger tour for the current page (disabled for main onboarding approach)
  const shouldAutoTriggerForPage = useCallback((pageRoute: string) => {
    // Main onboarding tour handles navigation internally, no auto-triggering needed
    return false
  }, [])

  // Get main onboarding tour progress stats
  const getTourStats = useCallback(() => {
    const completedTours = state.backendProgress?.completed || []
    
    // Check if main onboarding tour is completed
    const isMainOnboardingCompleted = completedTours.includes(MAIN_ONBOARDING_TOUR)
    
    // Count completed product tours for additional info
    const completedProductTours = PRODUCT_TOURS.filter(tour => 
      completedTours.includes(tour.tourId)
    ).length
    
    return {
      completedCount: isMainOnboardingCompleted ? 1 : 0,
      totalCount: 1, // Only one main onboarding tour
      progressPercentage: isMainOnboardingCompleted ? 100 : 0,
      isComplete: isMainOnboardingCompleted,
      // Additional info for display
      completedProductTours,
      totalProductTours: PRODUCT_TOURS.length
    }
  }, [state.backendProgress])

  // Get current tour status for unified approach
  const getCurrentTourStatus = useCallback(() => {
    const stats = getTourStats()
    
    if (stats.isComplete) {
      return {
        status: 'completed',
        tourName: 'Onboarding Complete',
        description: 'You have completed the full TractionX onboarding tour!'
      }
    }
    
          if (stats.completedProductTours > 0 && !stats.isComplete) {
        return {
          status: 'not_started',
          tourName: 'Onboarding Tour',
          description: `Take the guided onboarding tour (${stats.completedProductTours} product tours completed separately)`
        }
      }
    
    return {
      status: 'not_started',
      tourName: 'Onboarding Tour',
      description: 'Take a guided tour to discover TractionX features'
    }
  }, [getTourStats])

  return {
    // State (but use ref for isEngineRunning in critical paths)
    ...state,
    
    // Flow control
    startTourEngine,
    stopTourEngine,
    handleTourComplete,
    handleTourStepComplete,
    triggerPageTour,
    shouldAutoTriggerForPage: () => false, // Disabled for unified tour
    
    // Manual controls
    startManualTour,
    
    // Utilities
    isTourCompleted,
    getCurrentTourStatus,
    getTourStats,
    
    // Constants
    MAIN_ONBOARDING_TOUR,
    PRODUCT_TOURS,
    
    // Refresh
    refresh: initializeTourEngine,
    forceRefresh: initializeTourEngine
  }
} 