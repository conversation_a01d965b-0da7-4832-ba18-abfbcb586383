"use client"

/**
 * Organization Members Hook
 * 
 * Fetches and provides organization members for mention functionality
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/lib/auth-context';
import { OrgUser } from '@/lib/types/deal-notes';
import { useOrgMembersCache } from './use-org-members-cache';

interface UseOrgMembersReturn {
  members: OrgUser[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useOrgMembers(): UseOrgMembersReturn {
  const { members: cachedMembers, isLoading, error, refetch } = useOrgMembersCache();

  // Transform User[] to OrgUser[] for compatibility
  const members = useMemo(() => {
    return cachedMembers.map(user => ({
      _id: user.id,
      name: user.name || user.email,
      email: user.email,
      avatar_url: undefined // User type doesn't have avatar_url
    }));
  }, [cachedMembers]);

  return {
    members,
    isLoading,
    error,
    refetch,
  };
} 