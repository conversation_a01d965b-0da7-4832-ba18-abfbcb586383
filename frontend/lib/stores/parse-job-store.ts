import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { JobStatus, QueueAPI } from '@/lib/api/queue-api'

export type ParseJobState = 
  | { status: 'idle' }
  | { status: 'starting' }
  | { status: 'processing'; jobId: string; tempId: string; dealId?: string }
  | { status: 'completed'; jobId: string; tempId: string; dealId?: string; data: any }
  | { status: 'failed'; jobId: string; tempId: string; dealId?: string; error: string }

interface ParseJobStore {
  // State
  jobState: ParseJobState
  lastPollTime: number | null
  pollInterval: number
  maxPollTime: number
  
  // Actions
  startJob: (jobId: string, tempId: string, dealId?: string) => void
  updateJobStatus: (jobStatus: JobStatus) => void
  setJobFailed: (jobId: string, error: string) => void
  setJobCompleted: (jobId: string, data: any) => void
  resetJob: () => void
  setPollInterval: (interval: number) => void
  
  // Polling
  shouldPoll: () => boolean
  incrementPollTime: () => void
}

const POLL_INTERVAL_MS = 5000 // 5 seconds
const MAX_POLL_TIME_MS = 300000 // 5 minutes (extended for pitch parsing)
const BACKOFF_MULTIPLIER = 1.5 // Exponential backoff after 60s

export const useParseJobStore = create<ParseJobStore>()(
  persist(
    (set, get) => ({
      // Initial state
      jobState: { status: 'idle' },
      lastPollTime: null,
      pollInterval: POLL_INTERVAL_MS,
      maxPollTime: MAX_POLL_TIME_MS,

      // Actions
      startJob: (jobId: string, tempId: string, dealId?: string) => {
        set({
          jobState: { status: 'processing', jobId, tempId, dealId },
          lastPollTime: Date.now(),
          pollInterval: POLL_INTERVAL_MS
        })
      },

      updateJobStatus: (jobStatus: JobStatus) => {
        const { jobState } = get()
        
        if (jobState.status !== 'processing' || jobState.jobId !== jobStatus.id) {
          return // Not our job
        }

        switch (jobStatus.status) {
          case 'completed':
            set({
              jobState: { 
                status: 'completed', 
                jobId: jobStatus.id, 
                tempId: jobState.tempId,
                dealId: jobState.dealId,
                data: jobStatus.result 
              }
            })
            break
          case 'failed':
            set({
              jobState: { 
                status: 'failed', 
                jobId: jobStatus.id, 
                tempId: jobState.tempId,
                dealId: jobState.dealId,
                error: jobStatus.error || 'Job failed' 
              }
            })
            break
          case 'processing':
          case 'queued':
            // Continue polling, but implement exponential backoff after 60s
            const currentTime = Date.now()
            const timeSinceStart = currentTime - (get().lastPollTime || currentTime)
            
            if (timeSinceStart > 60000) { // After 60 seconds
              const newInterval = Math.min(
                get().pollInterval * BACKOFF_MULTIPLIER,
                30000 // Max 30 seconds
              )
              set({ pollInterval: newInterval })
            }
            break
        }
      },

      setJobFailed: (jobId: string, error: string) => {
        const currentState = get().jobState
        const tempId = currentState.status === 'processing' ? currentState.tempId : ''
        const dealId = currentState.status === 'processing' ? currentState.dealId : undefined
        set({
          jobState: { status: 'failed', jobId, tempId, dealId, error }
        })
      },

      setJobCompleted: (jobId: string, data: any) => {
        const currentState = get().jobState
        const tempId = currentState.status === 'processing' ? currentState.tempId : ''
        const dealId = currentState.status === 'processing' ? currentState.dealId : undefined
        set({
          jobState: { status: 'completed', jobId, tempId, dealId, data }
        })
      },

      resetJob: () => {
        set({
          jobState: { status: 'idle' },
          lastPollTime: null,
          pollInterval: POLL_INTERVAL_MS
        })
      },

      setPollInterval: (interval: number) => {
        set({ pollInterval: interval })
      },

      // Polling logic
      shouldPoll: () => {
        const { jobState, lastPollTime, maxPollTime } = get()
        
        if (jobState.status !== 'processing') {
          return false
        }

        if (!lastPollTime) {
          return true
        }

        const timeSinceStart = Date.now() - lastPollTime
        return timeSinceStart < maxPollTime
      },

      incrementPollTime: () => {
        set({ lastPollTime: Date.now() })
      }
    }),
    {
      name: 'parse-job-store',
      // Only persist the job state, not polling metadata
      partialize: (state) => ({
        jobState: state.jobState
      })
    }
  )
)

// Hook for polling job status
export const useJobPolling = () => {
  const { jobState, shouldPoll, incrementPollTime, updateJobStatus, pollInterval } = useParseJobStore()
  
  const pollJob = async () => {
    if (!shouldPoll() || jobState.status !== 'processing') {
      return
    }

    try {
      const token = localStorage.getItem('token')
      const orgId = localStorage.getItem('orgId')
      
      if (!token || !orgId) {
        throw new Error('Authentication required')
      }

      const jobStatus = await QueueAPI.getJobStatus(jobState.jobId, token, orgId)
      updateJobStatus(jobStatus)
      incrementPollTime()
      
    } catch (error) {
      console.error('Job polling error:', error)
      // Don't fail the job on polling errors, just log them
    }
  }

  return {
    jobState,
    pollJob,
    pollInterval,
    shouldPoll: shouldPoll()
  }
} 