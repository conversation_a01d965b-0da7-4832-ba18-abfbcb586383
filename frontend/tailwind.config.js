const { fontFamily } = require("tailwindcss/defaultTheme")

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./ui/**/*.{ts,tsx}",
    "./content/**/*.{md,mdx}",
  ],
  darkMode: false,
  theme: {
    // PRD-compliant breakpoint system
    screens: {
      'xs': '480px',   // Mobile small
      'sm': '640px',   // Mobile large
      'md': '1024px',  // Tablet
      'lg': '1440px',  // Desktop
      'xl': '1920px',  // Large desktop
    },
    container: {
      center: true,
      padding: {
        DEFAULT: "1rem",
        xs: "1rem",
        sm: "1.5rem",
        md: "2rem",
        lg: "2.5rem",
        xl: "3rem",
      },
      screens: {
        xs: "480px",
        sm: "640px",
        md: "1024px",
        lg: "1440px",
        xl: "1920px",
      },
    },
    extend: {
      colors: {
        tx: {
          primary: '#54FFBD', // Accent glow, button outlines, graph strokes
          info: '#00C2FF',    // Info alerts, data freshness badges
          warning: '#FF7D5C', // Filter badges, alert banners
          surface: '#F9FAFB', // Card & dashboard background
          muted: '#5F5F63',   // Secondary text, tooltips
          border: '#E0E2E7',  // Card outlines, chart gridlines
          fg: '#0F0F0F',      // Primary text
          gradient: 'linear-gradient(135deg, #54FFBD, #00C2FF)', // Radial fills for charts, backgrounds
          glass: 'rgba(255,255,255,0.6)', // Frosted surfaces (tooltip, hover)
          ring: '#27272A',    // Chart data ring or loading effects
        },
      },
      borderRadius: {
        lg: "26px", // +2px
        md: "18px", // +2px
        sm: "10px", // +2px
        xl: "26px", // +2px
        "2xl": "34px", // +2px
      },
      gridTemplateColumns: {
        '13': 'repeat(13, minmax(0, 1fr))'
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        display: ["Inter", "system-ui", "sans-serif"],
        heading: ["Inter", "system-ui", "sans-serif"],
        mono: ["IBM Plex Mono", "monospace"],
      },
      fontSize: {
        xs: ["0.65rem", { lineHeight: "0.9rem" }], // 0.8 * 0.8125rem
        sm: ["0.75rem", { lineHeight: "1.1rem" }], // 0.8 * 0.9375rem
        base: ["0.9rem", { lineHeight: "1.3rem" }], // 0.8 * 1.125rem
        lg: ["1rem", { lineHeight: "1.5rem" }], // 0.8 * 1.25rem
        xl: ["1.1rem", { lineHeight: "1.5rem" }], // 0.8 * 1.375rem
        "2xl": ["1.3rem", { lineHeight: "1.7rem" }], // 0.8 * 1.625rem
        "3xl": ["1.4rem", { lineHeight: "1.8rem" }], // 0.8 * 2rem
        "4xl": ["1.9rem", { lineHeight: "2.1rem" }], // 0.8 * 2.375rem
        "5xl": ["2.5rem", { lineHeight: "1" }], // 0.8 * 3.125rem
        "6xl": ["3.1rem", { lineHeight: "1" }], // 0.8 * 3.875rem
        "7xl": ["3.7rem", { lineHeight: "1" }], // 0.8 * 4.625rem
        "8xl": ["4.9rem", { lineHeight: "1" }], // 0.8 * 6.125rem
        "9xl": ["6.5rem", { lineHeight: "1" }], // 0.8 * 8.125rem
      },
      fontWeight: {
        thin: "100",
        extralight: "200",
        light: "300",
        normal: "400",
        medium: "500",
        semibold: "600",
        bold: "700",
        extrabold: "800",
        black: "900",
      },
      boxShadow: {
        // Institutional-grade shadows
        "institutional": "0 1px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.01)",
        "institutional-lg": "0 4px 6px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.01)",
        "institutional-xl": "0 10px 15px rgba(0, 0, 0, 0.02), 0 4px 6px rgba(0, 0, 0, 0.01)",
        // Glow effects
        "glow-mint": "0 0 20px rgba(63, 224, 197, 0.15)",
        "glow-lavender": "0 0 20px rgba(131, 137, 253, 0.15)",
        "glow-mint-lg": "0 0 30px rgba(63, 224, 197, 0.2)",
        "glow-lavender-lg": "0 0 30px rgba(131, 137, 253, 0.2)",
      },
      animation: {
        // Live data shimmer
        "shimmer": "shimmer 2s linear infinite",
        "radar-ping": "radar-ping 3s ease-in-out infinite",
        "live-pulse": "live-pulse 2s ease-in-out infinite",
        "glow-fade": "glow-fade 4s ease-in-out infinite",
        "terminal-glow": "terminal-glow 6s ease-in-out infinite",
      },
      keyframes: {
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        "radar-ping": {
          "0%": { transform: "scale(1)", opacity: "1" },
          "100%": { transform: "scale(2)", opacity: "0" },
        },
        "live-pulse": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.5" },
        },
        "glow-fade": {
          "0%, 100%": { boxShadow: "0 0 20px rgba(63, 224, 197, 0.1)" },
          "50%": { boxShadow: "0 0 30px rgba(63, 224, 197, 0.2)" },
        },
        "terminal-glow": {
          "0%, 100%": { boxShadow: "0 0 20px rgba(131, 137, 253, 0.1)" },
          "50%": { boxShadow: "0 0 40px rgba(131, 137, 253, 0.3)" },
        },
      },
      backdropBlur: {
        xs: "2px",
      },
      spacing: {
        '0': '0px',
        '1': '6px', // +2px
        '2': '10px', // +2px
        '3': '14px', // +2px
        '4': '18px', // +2px
        '5': '22px', // +2px
        '6': '26px', // +2px
        '7': '30px', // +2px
        '8': '34px', // +2px
        '9': '38px', // +2px
        '10': '42px', // +2px
        // ... add more as needed
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
}
