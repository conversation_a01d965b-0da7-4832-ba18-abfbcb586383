"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"

import { OnboardingWizard } from "@/components/core/onboarding/new-user-wizard"
import { useAuth } from "@/lib/auth-context"
import { SettingsAPI } from "@/lib/api/settings-api"
import { cn } from "@/lib/utils"

export default function OnboardingPage() {
  const router = useRouter()
  const { user, isAuthenticated, loading } = useAuth()

  useEffect(() => {
    // Wait for auth to load
    if (loading) return

    // Redirect to login if not authenticated
    if (!isAuthenticated || !user) {
      router.push('/login')
      return
    }

    // Since organization onboarding already handles everything,
    // redirect directly to dashboard
    console.log("User already completed organization onboarding, redirecting to dashboard")
    router.push('/dashboard')
  }, [loading, isAuthenticated, user, router])

  // Show loading while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center"
      >
        <div className="relative mb-6">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="border-3 mx-auto size-12 rounded-full border-purple-200 border-t-purple-600"
          />
        </div>
        <h3 className="text-lg font-semibold text-gray-900">Redirecting to Dashboard</h3>
        <p className="text-sm text-gray-600 mt-2">Your workspace is ready...</p>
      </motion.div>
    </div>
  )
}
