"use client"

import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { useDebouncedCallback } from 'use-debounce'
import { useSearchParams } from 'next/navigation'
import { Shell } from '@/components/shell'
import { DealsHeader } from '@/components/core/deals/deals-header'
import { DealsGrid } from '@/components/core/deals/deals-grid'
import { NewDealModal } from '@/components/core/deals/new-deal-modal'
import { ExcelUploadModal } from '@/components/core/deals/excel-upload-modal'
import { DealAPI } from '@/lib/api/deal-api'
import { Deal, DealStatus, DealDashboardFilters } from '@/lib/types/deal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { PitchDeckParserStatus } from '@/components/core/deals/pitch-deck-parser-status'
import { useParseJobStore } from '@/lib/stores/parse-job-store'
import { TourBadge } from '@/components/core/tour/enhanced-tour-provider'
import { useTourEngine } from '@/lib/hooks/use-tour-engine'

export default function DealsPage() {
  const { isAuthenticated, loading, user } = useAuth()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // State management
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([])
  const [error, setError] = useState<string | null>(null)
  const [showNewDealModal, setShowNewDealModal] = useState(false)
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [userPreferences, setUserPreferences] = useState<DealDashboardFilters | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isSearching, setIsSearching] = useState(false)
  const isFetchingRef = useRef(false)

  const parseJobState = useParseJobStore(state => state.jobState)
  const tourEngine = useTourEngine()

  // Auto-trigger tour disabled for unified onboarding
  // The unified onboarding tour handles all page navigation automatically

  // Parse filters from URL with preferences fallback
  const currentFilters = useMemo(() => {
    // Start with user preferences as defaults, or fallback to hardcoded defaults
    const defaultFilters: DealDashboardFilters = userPreferences || {
      status: ['new', 'triage', 'reviewed', 'excluded', 'rejected', 'approved', 'negotiating', 'closed'],
      assigned_to_me: false,
      sort_by: 'updated_at',
      sort_dir: 'desc'
    }

    const filters: DealDashboardFilters = { ...defaultFilters }

    // Parse URL parameters (these take priority over preferences)
    const status = searchParams?.get('status')
    if (status) {
      filters.status = status.split(',')
    }
    const assignedToMe = searchParams?.get('assigned_to_me')
    if (assignedToMe !== null) {
      filters.assigned_to_me = assignedToMe === 'true'
    }
    const createdAtStart = searchParams?.get('created_at_start')
    if (createdAtStart) {
      filters.created_at_start = createdAtStart
    }
    const createdAtEnd = searchParams?.get('created_at_end')
    if (createdAtEnd) {
      filters.created_at_end = createdAtEnd
    }
    const sortBy = searchParams?.get('sort_by')
    if (sortBy) {
      filters.sort_by = sortBy
    }
    const sortDir = searchParams?.get('sort_dir')
    if (sortDir) {
      filters.sort_dir = sortDir
    }
    const favouritesOnly = searchParams?.get('favourites_only')
    if (favouritesOnly !== null) {
      filters.favourites_only = favouritesOnly === 'true'
    }

    return filters
  }, [searchParams, userPreferences])

  // Use current filters for API calls (search is handled separately via debouncing)
  const apiFilters = useMemo(() => {
    return { ...currentFilters }
  }, [currentFilters])

  // Fetch deals with current filters
  const fetchDeals = useCallback(async (filters?: DealDashboardFilters) => {
    if (isFetchingRef.current) {
      console.log('⚠️ Already fetching deals, skipping duplicate request')
      return
    }

    try {
      isFetchingRef.current = true
      setError(null)
      console.log('🔄 Fetching deals with filters:', filters || apiFilters)

      const response = await DealAPI.listDeals(0, 1000, filters || apiFilters)
      console.log('✅ Deals fetched successfully:', response.deals?.length || 0, 'deals')
      
      setAllDeals(response.deals || [])
      setFilteredDeals(response.deals || [])
    } catch (err: any) {
      console.error('❌ Error fetching deals:', err)
      setError('Failed to load deals. Please try again.')
      toast({
        title: "Error",
        description: "Failed to load deals. Please try again.",
        variant: "destructive",
      })
    } finally {
      isFetchingRef.current = false
    }
  }, [apiFilters, toast])

  // Load user preferences first
  useEffect(() => {
    const loadPreferences = async () => {
      if (!isAuthenticated) return
      
      try {
        const preferences = await DealAPI.getDealPreferences()
        if (preferences.deal_dashboard_filters) {
          setUserPreferences(preferences.deal_dashboard_filters)
        } else {
          // No preferences found, use defaults
          setUserPreferences({
            status: ['new', 'triage', 'reviewed', 'excluded', 'rejected', 'approved', 'negotiating', 'closed'],
            assigned_to_me: false,
            sort_by: 'updated_at',
            sort_dir: 'desc'
          })
        }
      } catch (error) {
        console.warn('Failed to load user preferences:', error)
        // Use defaults on error
        setUserPreferences({
          status: ['new', 'triage', 'reviewed', 'excluded', 'rejected', 'approved', 'negotiating', 'closed'],
          assigned_to_me: false,
          sort_by: 'updated_at',
          sort_dir: 'desc'
        })
      }
    }
    
    loadPreferences()
  }, [isAuthenticated])

  // Initialize and fetch deals once preferences are loaded
  useEffect(() => {
    if (isAuthenticated && !isInitialized && userPreferences !== null) {
      console.log('🚀 Initializing deals page with preferences:', userPreferences)
      fetchDeals(apiFilters)
      setIsInitialized(true)
    }
  }, [isAuthenticated, isInitialized, userPreferences, fetchDeals, apiFilters])

  // Refetch when URL params change (but only after initial load)
  useEffect(() => {
    if (isAuthenticated && isInitialized && userPreferences !== null) {
      console.log('🔄 URL params changed, refetching deals')
      // Only refetch if this is not the initial load
      fetchDeals(apiFilters)
    }
  }, [searchParams, isAuthenticated, isInitialized, userPreferences, fetchDeals, apiFilters])

  // Debounced search function
  const debouncedSearch = useDebouncedCallback(
    (searchValue: string) => {
      if (isAuthenticated && isInitialized && userPreferences !== null) {
        console.log('🔍 Debounced search triggered:', searchValue)
        setIsSearching(true)
        const searchFilters = { ...currentFilters }
        if (searchValue.trim()) {
          searchFilters.search = searchValue.trim()
        }
        fetchDeals(searchFilters).finally(() => {
          setIsSearching(false)
        })
      }
    },
    500 // 500ms delay
  )

  // Calculate deal counts for filters
  const dealCounts = useMemo(() => ({
    all: allDeals.length,
    new: allDeals.filter(d => d && d.status === DealStatus.NEW).length,
    triage: allDeals.filter(d => d && d.status === DealStatus.TRIAGE).length,
    reviewed: allDeals.filter(d => d && d.status === DealStatus.REVIEWED).length,
    approved: allDeals.filter(d => d && d.status === DealStatus.APPROVED).length,
    negotiating: allDeals.filter(d => d && d.status === DealStatus.NEGOTIATING).length,
    excluded: allDeals.filter(d => d && d.status === DealStatus.EXCLUDED).length,
    rejected: allDeals.filter(d => d && d.status === DealStatus.REJECTED).length,
    closed: allDeals.filter(d => d && d.status === DealStatus.CLOSED).length,
  }), [allDeals])

  const handleNewDeal = () => {
    setShowNewDealModal(true)
  }

  const handleBulkImport = () => {
    setShowExcelUploadModal(true)
  }

  const handleDeleteDeal = async (dealId: string): Promise<boolean> => {
    if (isDeleting) {
      console.log('⚠️ Delete operation already in progress')
      return false
    }

    try {
      setIsDeleting(true)
      console.log('🗑️ Deleting deal:', dealId)
      
      await DealAPI.deleteDeal(dealId)
      console.log('✅ Deal deleted successfully')
      
      // Refetch deals from API to ensure UI is in sync
      // Use a fresh fetch to avoid stale closure issues
      await fetchDeals(apiFilters)
      
      toast({
        title: "Deal deleted",
        description: "Deal has been deleted successfully.",
      })
      
      return true
    } catch (error: any) {
      console.error('❌ Error deleting deal:', error)
      toast({
        title: "Error",
        description: "Failed to delete deal. Please try again.",
        variant: "destructive",
      })
      return false
    } finally {
      setIsDeleting(false)
    }
  }

  const handleSearchChange = (search: string) => {
    setSearchTerm(search)
    debouncedSearch(search)
  }

  const handleFilterChange = (filters: DealDashboardFilters) => {
    // The filters are already applied via URL params, so we just need to refetch
    fetchDeals(filters)
  }

  // Stable callback functions to prevent unnecessary re-renders
  const handleRefresh = useCallback(() => {
    fetchDeals(apiFilters)
  }, [fetchDeals, apiFilters])

  return (
    <Shell>
      <div className="space-y-8" role="main" aria-label="Deals management page">
        {/* Show global pitch deck parsing status if a job is in progress */}
        {parseJobState.status === 'processing' && (
          <div className="mb-4" role="status" aria-live="polite">
            <PitchDeckParserStatus onRefresh={handleRefresh} />
          </div>
        )}
        {/* Show loading while checking authentication */}
        {loading ? (
          <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
            <div className="size-8 animate-spin rounded-full border-b-2 border-blue-600" aria-hidden="true"></div>
            <span className="ml-2">Checking authentication...</span>
          </div>
        ) : !isAuthenticated ? (
          <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
            <div className="text-center">
              <p className="text-muted-foreground">Redirecting to login...</p>
            </div>
          </div>
        ) : (
          <>
            <DealsHeader
              onSearchChange={handleSearchChange}
              onFilterChange={handleFilterChange}
              totalDeals={filteredDeals.length}
              dealCounts={dealCounts}
              loading={loading}
              onBulkImport={handleBulkImport}
              searchValue={searchTerm}
              isSearching={isSearching}
            />

            <DealsGrid
              deals={filteredDeals}
              loading={loading || isDeleting || isSearching}
              error={error}
              onNewDeal={handleNewDeal}
              onDeleteDeal={handleDeleteDeal}
            />

            {/* New Deal Modal */}
            <NewDealModal
              open={showNewDealModal}
              onOpenChange={setShowNewDealModal}
              onRefresh={handleRefresh}
            />

            {/* Excel Upload Modal */}
            <ExcelUploadModal
              open={showExcelUploadModal}
              onOpenChange={setShowExcelUploadModal}
              onUploadComplete={handleRefresh}
            />
          </>
        )}
      </div>
    </Shell>
  )
} 