"use client"

import Link from "next/link"
import { dashboardConfig } from "@/config/dashboard"
import { MainNav } from "@/components/main-nav"
import { SiteFooter } from "@/components/site-footer"
import { UserAccountNav } from "@/components/user-account-nav"
import { ProtectedRoute } from "@/components/protected-route"
import { OrganizationSelector } from "@/components/org-selector"
import { EnhancedSidebar } from "@/components/enhanced-sidebar"
import { useAuth } from "@/lib/auth-context"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import { Home, FileText, BarChart3, Target, Settings, Zap } from "lucide-react"
import { Icons } from "@/components/icons"
import { OrbitAssistant } from "@/components/ui/orbit-assistant"

// Enhanced tour system imports
import { EnhancedTourProvider } from "@/components/core/tour/enhanced-tour-provider"
import { EnhancedTourMenu, EnhancedFloatingTourButton } from "@/components/core/tour/enhanced-tour-menu"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="mb-4 size-12 animate-spin rounded-full border-y-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <EnhancedTourProvider>
      <DashboardLayoutContent
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
      >
        {children}
      </DashboardLayoutContent>
    </EnhancedTourProvider>
  );
}

function DashboardLayoutContent({
  children,
  sidebarOpen,
  setSidebarOpen,
  mobileMenuOpen,
  setMobileMenuOpen,
}: {
  children: React.ReactNode;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean | ((prev: boolean) => boolean)) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
}) {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      {/* Top-level Layout: Horizontal Flex Row - Full Screen, No Gaps */}
      <div className="flex h-screen w-full flex-1 overflow-hidden">
        {/* Vertical Sidebar - Always Left, Full Height */}
        <aside
          data-tour="sidebar"
          className={cn(
            "flex h-full shrink-0 flex-col",
            // Desktop: fixed width, Mobile: hidden (will show as overlay)
            "hidden md:flex",
            sidebarOpen ? "w-[220px]" : "w-[72px]"
          )}
        >
          <EnhancedSidebar
            items={dashboardConfig.sidebarNav}
            isCollapsed={!sidebarOpen}
            onToggleCollapse={() => setSidebarOpen(!sidebarOpen)}
          />
        </aside>

        {/* Main Content Area - Fill Remaining Space */}
        <div className="flex h-full min-w-0 flex-1 flex-col overflow-hidden">
          {/* Header - No Margins */}
          <header
            data-tour="dashboard-header"
            className={cn(
              "z-30 shrink-0 border-b bg-background"
            )}
          >
            <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
              <div className="flex items-center gap-3">
                {/* Mobile Menu Button */}
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className={cn(
                    "rounded-xl p-2 transition-all duration-200 md:hidden",
                    "touch-target hover:bg-accent active:scale-95",
                    "focus:outline-none focus:ring-2 focus:ring-ring"
                  )}
                  aria-label="Toggle mobile menu"
                >
                  <svg
                    className="size-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                    />
                  </svg>
                </button>
                {/* Desktop Navigation */}
                <div className="hidden items-center gap-4 md:flex">
                  <MainNav items={dashboardConfig.mainNav} />
                  <OrganizationSelector />
                </div>

                {/* Mobile: Show org selector only */}
                <div className="md:hidden">
                  <OrganizationSelector />
                </div>
              </div>

              {/* Header Actions */}
              <div className="flex items-center gap-2 md:gap-4">
                {/* Enhanced Tour Menu */}
                <EnhancedTourMenu variant="icon" />
                
                {/* User Account Navigation */}
                <UserAccountNav 
                  user={{
                    name: user?.name || null,
                    image: null,
                    email: user?.email || null,
                  }} 
                />
              </div>
            </div>
          </header>

          {/* Mobile Menu Overlay */}
          {mobileMenuOpen && (
            <div className="fixed inset-0 z-50 md:hidden">
              {/* Backdrop */}
              <div 
                className="absolute inset-0 bg-black/20 backdrop-blur-sm"
                onClick={() => setMobileMenuOpen(false)}
              />
              
              {/* Menu Panel */}
              <div className="absolute left-0 top-0 h-full w-80 bg-background border-r shadow-xl">
                {/* Menu Header */}
                <div className="flex h-16 items-center justify-between border-b px-6">
                  <div className="flex items-center gap-2">
                    <Icons.logo className="size-6" />
                    <span className="font-bold">TractionX</span>
                  </div>
                  <button
                    onClick={() => setMobileMenuOpen(false)}
                    className="rounded-lg p-2 hover:bg-accent"
                  >
                    <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex-1 overflow-y-auto p-6">
                  <div className="space-y-3">
                    {dashboardConfig.sidebarNav.map((item, index) => {
                      const iconMap = {
                        dashboard: Home,
                        premium: Zap,
                        form: FileText,
                        post: BarChart3,
                        page: Target,
                        settings: Settings,
                      } as const;
                      
                      const iconKey = item.icon as keyof typeof iconMap;
                      const Icon = iconMap[iconKey] || Home;
                      
                      return (
                        <Link
                          key={index}
                          href={item.href || '#'}
                          className="touch-target flex items-center gap-4 rounded-xl p-4 text-lg font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground active:scale-[0.98]"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <div className="flex size-6 items-center justify-center">
                            <Icon className="size-5 text-muted-foreground" />
                          </div>
                          <span className="text-lg font-medium">{item.title}</span>
                        </Link>
                      );
                    })}
                  </div>
                </nav>
              </div>
            </div>
          )}

          {/* Main Content - Fill Remaining Height */}
          <main className="min-w-0 flex-1 overflow-y-auto overflow-x-hidden px-4 py-6 md:px-6 lg:px-8">
            {children}
          </main>

          {/* Footer - No Margins */}
          <SiteFooter className="shrink-0 border-t" />
        </div>

        {/* Orbit AI Assistant - Floating Widget */}
        <OrbitAssistant />
      </div>
    </ProtectedRoute>
  );
}
