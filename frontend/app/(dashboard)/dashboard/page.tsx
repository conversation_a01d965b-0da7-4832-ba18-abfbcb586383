"use client"

import { useState, useEffect } from "react"
import { DashboardHeader } from "@/components/header"
import { DashboardShell } from "@/components/shell"
import {
  PremiumSummaryTiles,
  PremiumInsightsCharts,
  PremiumQuickActions,
  OnboardingFlow
} from "@/components/core/dashboard"
import { useDashboard } from "@/lib/hooks/use-dashboard"
import { useDashboardInsights } from "@/lib/hooks/use-dashboard-insights"
import { useAuth } from "@/lib/auth-context"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Zap, TrendingUp, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Play } from "lucide-react"

// Enhanced tour system imports
import { useAutoTour } from "@/components/core/tour/enhanced-tour-provider"
import { TourAPI, UserMeResponse } from "@/lib/api/tour-api"

// Org Thesis Prompt
import { OrgThesisPrompt } from "@/components/core/settings/org-thesis-prompt"

export default function PremiumDashboardPage() {
  const { user } = useAuth()
  const { data: dashboardData, loading: summaryLoading, error: summaryError, refetch: refetchSummary } = useDashboard()
  const { data: insightsData, loading: insightsLoading, error: insightsError, refetch: refetchInsights } = useDashboardInsights()
  
  // Enhanced tour system
  const { triggerOnboardingFlow, shouldShowFirstTimeTour, isEngineRunning, engineProgress } = useAutoTour()
  
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [userTourInfo, setUserTourInfo] = useState<UserMeResponse | null>(null)
  const [showTourPrompt, setShowTourPrompt] = useState(false)
  const [tourLoading, setTourLoading] = useState(false)

  // Determine if we should show onboarding
  const shouldShowOnboarding = dashboardData &&
    (!dashboardData.onboarding.has_form || !dashboardData.onboarding.has_thesis)

  // Fetch user tour info
  useEffect(() => {
    const fetchUserTourInfo = async () => {
      try {
        const info = await TourAPI.getUserInfo()
        setUserTourInfo(info)
        
        // Show tour prompt if should_prompt_tour is true and MAIN_ONBOARDING_TOUR not completed
        const hasCompletedMainTour = info.tour_progress?.completed?.includes('onboarding') || false
        const shouldPrompt = info.should_prompt_tour && !hasCompletedMainTour
        setShowTourPrompt(shouldPrompt)
        
        console.log('🎯 Tour prompt logic:', {
          should_prompt_tour: info.should_prompt_tour,
          hasCompletedMainTour,
          shouldPrompt,
          completedTours: info.tour_progress?.completed
        })
      } catch (error) {
        console.error('Failed to fetch user tour info:', error)
      }
    }

    if (user) {
      fetchUserTourInfo()
    }
  }, [user])

  // Auto-trigger tour if conditions are met (disabled for new simplified approach)
  useEffect(() => {
    // Disabled auto-trigger in favor of manual tour prompt
    // This prevents automatic tour from conflicting with the new simplified approach
  }, [shouldShowFirstTimeTour, isEngineRunning, triggerOnboardingFlow])

  // Handle take tour
  const handleTakeTour = async () => {
    setTourLoading(true)
    try {
      console.log('🚀 Starting tour via Take Tour button')
      triggerOnboardingFlow()
      setShowTourPrompt(false)
    } catch (error) {
      console.error('Failed to start tour:', error)
    } finally {
      setTourLoading(false)
    }
  }

  // Handle skip tour  
  const handleSkipTour = async () => {
    setTourLoading(true)
    try {
      console.log('⏭️ Skipping tour')
      await TourAPI.completeTour('onboarding')
      setShowTourPrompt(false)
      
      // Refresh user info to reflect completion
      const info = await TourAPI.getUserInfo()
      setUserTourInfo(info)
    } catch (error) {
      console.error('Failed to skip tour:', error)
    } finally {
      setTourLoading(false)
    }
  }

  // Loading state
  if (summaryLoading || insightsLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="AI-powered investment intelligence at your fingertips"
          showOrgName={true}
        />
        <div className="space-y-6 md:space-y-8">
          {/* Mobile-first skeleton grid */}
          <div className="grid grid-cols-1 gap-4 xs:grid-cols-2 md:grid-cols-4 md:gap-6">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32 rounded-xl" />
            ))}
          </div>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8">
            <Skeleton className="h-80 rounded-xl md:h-96" />
            <Skeleton className="h-80 rounded-xl md:h-96" />
          </div>
        </div>
      </DashboardShell>
    )
  }

  // Error state
  if (summaryError || insightsError) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="AI-powered investment intelligence at your fingertips"
          showOrgName={true}
        />
        <Alert>
          <AlertCircle className="size-4" />
          <AlertDescription>
            {summaryError || insightsError || "Failed to load dashboard data. Please try again."}
          </AlertDescription>
        </Alert>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        data-tour="dashboard-header"
        heading="Dashboard"
        text="AI-powered investment intelligence at your fingertips"
        showOrgName={true}
        tourType="dashboard"
      >
        <div className="flex items-center gap-3">
          {/* Tour Engine Status Badge */}
          {/* {isEngineRunning && (
            <Badge 
              variant="outline" 
              className="animate-pulse bg-purple-50 text-purple-700 border-purple-300"
            >
              <Zap className="size-3 mr-1" />
              Tour Active
            </Badge>
          )} */}
          
          {/* Premium badge */}
          {/* <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0">
            <TrendingUp className="size-3 mr-1" />
            Premium
          </Badge> */}
        </div>
      </DashboardHeader>

      <div className="space-y-6 md:space-y-8 pb-24">
        {/* Show onboarding for incomplete setup */}
        {/* {shouldShowOnboarding && (
          <OnboardingFlow
            userName={user?.name}
            onboardingStatus={dashboardData.onboarding}
            onDismiss={() => setShowOnboarding(false)}
            onRefresh={refetchSummary}
          />
        )} */}

        {/* Org Thesis Setup Prompt */}
        <OrgThesisPrompt variant="banner" />

        {/* Simple Tour Prompt */}
        {showTourPrompt && (
          <div className="rounded-lg border bg-gradient-to-r from-purple-50 to-blue-50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">
                  Welcome to TractionX!
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Take a guided tour to discover powerful features and get the most out of the platform
                </p>
                
                <div className="flex items-center gap-3 mt-3">
                  <Button 
                    onClick={handleTakeTour}
                    size="sm"
                    disabled={tourLoading}
                    className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                  >
                    <Play className="size-3 mr-1" />
                    {tourLoading ? 'Starting...' : 'Take a Tour'}
                  </Button>
                  
                  <Button
                    onClick={handleSkipTour}
                    size="sm"
                    variant="ghost"
                    disabled={tourLoading}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    Skip Tour
                  </Button>
                </div>
              </div>
              
              <Button
                onClick={() => setShowTourPrompt(false)}
                variant="ghost"
                size="sm"
                className="shrink-0 text-gray-400 hover:text-gray-600 p-1"
              >
                <X className="size-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Premium Summary Tiles */}
        <div data-tour="quick-stats">
          <PremiumSummaryTiles dashboardData={{ insights: insightsData }} />
        </div>

        {/* Premium Insights Charts */}
        <div data-tour="deals-section">
          {insightsData && <PremiumInsightsCharts insightsData={insightsData} />}
        </div>

        {/* Premium Quick Actions - Sticky Footer */}
        <div data-tour="recent-activity">
          <PremiumQuickActions />
        </div>
      </div>
    </DashboardShell>
  )
} 