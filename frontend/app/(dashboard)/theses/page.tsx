"use client"

import { Shell } from "@/components/shell"
import { ThesesList } from "@/components/core/thesis-builder/theses-list"
import { useAuth } from "@/lib/auth-context"
import { TourBadge } from "@/components/core/tour/enhanced-tour-provider"
import { mobileRetreat, visualRetreat } from "@/lib/utils/responsive"
import { useTourEngine } from "@/lib/hooks/use-tour-engine"
import { useEffect } from "react"

export default function ThesesPage() {
  const { isAuthenticated } = useAuth()
  const tourEngine = useTourEngine()

  // Auto-trigger tour disabled for unified onboarding
  // The unified onboarding tour handles all page navigation automatically

  if (!isAuthenticated) {
    return null // This is handled by middleware
  }

  return (
    <Shell>
      <ThesesList />
    </Shell>
  )
}
