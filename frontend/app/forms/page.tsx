"use client"

import { useState, useEffect, useMem<PERSON>, use<PERSON>allback, useRef } from 'react'
import Link from "next/link";
import { Plus, MoreHorizontal, Edit, Eye, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardHeader } from "@/components/header";
import { DashboardShell } from "@/components/shell";
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { FormAPI } from "@/lib/api/form-api";
import { Form } from "@/lib/types/form";
import { useAuth } from "@/lib/auth-context";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { useTourEngine } from "@/lib/hooks/use-tour-engine"

export default function FormsPage() {
  const { isAuthenticated, loading, user } = useAuth()
  const { toast: toastHook } = useToast()

  // State management
  const [allForms, setAllForms] = useState<Form[]>([])
  const [error, setError] = useState<string | null>(null)
  const [showNewFormModal, setShowNewFormModal] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [formToDelete, setFormToDelete] = useState<Form | null>(null)
  const [deleting, setDeleting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isNavigatingToCreate, setIsNavigatingToCreate] = useState(false)
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)

  const isFetchingRef = useRef(false)
  const tourEngine = useTourEngine()

  // Auto-trigger tour disabled for unified onboarding
  // The unified onboarding tour handles all page navigation automatically

  // ✅ Removed unnecessary useCallback - not passing to child components

  // Initialize and fetch forms once authenticated
  useEffect(() => {
    if (isAuthenticated && !isInitialized) {

      (async () => {
        try {
          isFetchingRef.current = true
          console.log('🔄 Fetching forms from API')
          setError(null)

          const formsList = await FormAPI.listForms()
          console.log('✅ Forms fetched:', formsList?.length || 0, 'forms')
          
          if (Array.isArray(formsList)) {
            setAllForms(formsList)
          } else {
            console.warn("⚠️ API returned non-array response:", formsList)
            setAllForms([])
          }
        } catch (err: any) {
          console.error('❌ Error fetching forms:', err)
          setError('Failed to load forms. Please try again.')
          toastHook({
            title: "Error",
            description: "Failed to load forms. Please try again.",
            variant: "destructive",
          })
        } finally {
          isFetchingRef.current = false
        }
      })()
      setIsInitialized(true)
    }
  }, [isAuthenticated, isInitialized]) // ✅ Clean, safe - no fetchForms dependency

  const handleNewForm = () => {
    setShowNewFormModal(true)
  }

  const handleDeleteForm = async (formId: string): Promise<boolean> => {
    if (isDeleting) {
      console.log('⚠️ Delete operation already in progress')
      return false
    }

    try {
      setIsDeleting(true)
      console.log('🗑️ Deleting form:', formId)
      
      await FormAPI.deleteForm(formId)
      console.log('✅ Form deleted successfully')
      
      // ✅ Direct refetch without state tracking
      try {
        isFetchingRef.current = true
        const formsList = await FormAPI.listForms()
        console.log("✅ Forms refetched:", formsList?.length || 0, 'forms')
        
        if (Array.isArray(formsList)) {
          setAllForms(formsList)
        } else {
          setAllForms([])
        }
      } catch (fetchError) {
        console.error("❌ Error refetching forms:", fetchError)
        setError('Failed to refresh forms list.')
      } finally {
        isFetchingRef.current = false
      }
      
      toastHook({
        title: "Form deleted",
        description: "Form has been deleted successfully.",
      })
      
      return true
    } catch (error: any) {
      console.error('❌ Error deleting form:', error)
      toastHook({
        title: "Error",
        description: "Failed to delete form. Please try again.",
        variant: "destructive",
      })
      return false
    } finally {
      setIsDeleting(false)
      setOpenDropdownId(null) // Ensure dropdown is closed after successful deletion
    }
  }

  // ✅ Simple refresh function without useCallback
  const handleRefresh = () => {
    if (isFetchingRef.current) return;
    
    (async () => {
      try {
        isFetchingRef.current = true
        const formsList = await FormAPI.listForms()
        if (Array.isArray(formsList)) {
          setAllForms(formsList)
        }
      } catch (error) {
        console.error("❌ Error refreshing forms:", error)
      } finally {
        isFetchingRef.current = false
      }
    })()
  }

  return (
    <DashboardShell>
      <div className="space-y-8" role="main" aria-label="Forms management page">
        {/* Show loading while checking authentication */}
        {loading ? (
          <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
            <div className="size-8 animate-spin rounded-full border-b-2 border-blue-600" aria-hidden="true"></div>
            <span className="ml-2">Checking authentication...</span>
          </div>
        ) : !isAuthenticated ? (
          <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
            <div className="text-center">
              <p className="text-muted-foreground">Redirecting to login...</p>
            </div>
          </div>
        ) : (
          <>
            <DashboardHeader
              data-tour="forms-header"
              heading="Forms"
              text="Create and manage your forms"
              tourType="forms"
            >
              <div className="flex items-center gap-3">
                <Link href="/forms/new">
                  <Button 
                    data-tour="form-creation"
                    disabled={isNavigatingToCreate}
                    onClick={() => {
                      if (!isNavigatingToCreate) {
                        setIsNavigatingToCreate(true);
                        // Reset after a delay to allow navigation
                        setTimeout(() => setIsNavigatingToCreate(false), 2000);
                      }
                    }}
                  >
                    <Plus className="size-4" />
                    {isNavigatingToCreate && <span className="ml-2">Creating...</span>}
                  </Button>
                </Link>
              </div>
            </DashboardHeader>

            <div>
              {!isInitialized ? (
                // Loading skeleton
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3" role="status" aria-live="polite" aria-label="Loading forms">
                  {[1, 2, 3].map((i) => (
                    <Card key={i} className="overflow-hidden">
                      <CardHeader className="gap-2">
                        <Skeleton className="h-5 w-1/2" />
                        <Skeleton className="h-4 w-full" />
                      </CardHeader>
                      <CardContent>
                        <Skeleton className="h-4 w-full" />
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Skeleton className="h-10 w-24" />
                        <Skeleton className="h-10 w-24" />
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : error ? (
                <div className="flex items-center justify-center p-8" role="alert" aria-live="assertive">
                  <div className="text-center">
                    <p className="text-red-600">{error}</p>
                    <Button onClick={handleRefresh} className="mt-4">
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : !allForms || allForms.length === 0 ? (
                <div role="status" aria-live="polite">
                  <EmptyPlaceholder>
                    <EmptyPlaceholder.Icon name="post" />
                    <EmptyPlaceholder.Title>No forms created</EmptyPlaceholder.Title>
                    <EmptyPlaceholder.Description>
                      You don&apos;t have any forms yet. Start creating forms to collect data from your users.
                    </EmptyPlaceholder.Description>
                    <Link href="/forms/new">
                      <Button 
                        variant="outline"
                        disabled={isNavigatingToCreate}
                        onClick={() => {
                          if (!isNavigatingToCreate) {
                            setIsNavigatingToCreate(true);
                            setTimeout(() => setIsNavigatingToCreate(false), 2000);
                          }
                        }}
                      >
                        Create your first form
                        {isNavigatingToCreate && <span className="ml-2">...</span>}
                      </Button>
                    </Link>
                  </EmptyPlaceholder>
                </div>
              ) : (
                <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3" data-tour="form-management" role="grid" aria-label="Forms grid">
                  {allForms && Array.isArray(allForms) && allForms.map((form, index) => {
                    const formId = form._id || form.id;
                    if (!formId) {
                      console.warn('Form missing ID, skipping:', form);
                      return null;
                    }
                    
                    return (
                    <Card 
                      key={formId || `form-${index}`} 
                      className="group relative transition-all duration-200 hover:shadow-md hover:-translate-y-1"
                      data-tour={index === 0 ? "form-card" : undefined}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-2">
                          <div className="min-w-0 flex-1">
                            <CardTitle className="line-clamp-2 text-lg font-semibold group-hover:text-blue-600 transition-colors">
                              {form.name}
                            </CardTitle>
                            {form.description && (
                              <CardDescription className="line-clamp-2 mt-1">
                                {form.description}
                              </CardDescription>
                            )}
                          </div>
                          <DropdownMenu 
                            open={openDropdownId === (form._id || form.id)} 
                            onOpenChange={(open) => {
                              const formId = form._id || form.id;
                              if (open && formId) {
                                setOpenDropdownId(formId);
                              } else {
                                setOpenDropdownId(null);
                              }
                            }}
                          >
                            <DropdownMenuTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                data-tour={index === 0 ? "form-actions" : undefined}
                                aria-label="Form actions menu"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent 
                              className="w-48"
                              onCloseAutoFocus={(e) => {
                                // Prevent focus from being trapped in the dropdown
                                e.preventDefault();
                              }}
                            >
                              <DropdownMenuItem 
                                asChild
                                onSelect={(e) => {
                                  // Prevent default selection behavior that might cause focus issues
                                  e.preventDefault();
                                }}
                              >
                                <Link href={`/forms/${form._id || form.id}`} className="flex w-full">
                                  <Edit className="mr-2 size-4 text-muted-foreground" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                asChild
                                onSelect={(e) => {
                                  // Prevent default selection behavior that might cause focus issues
                                  e.preventDefault();
                                }}
                              >
                                <Link href={`/preview/${form._id || form.id}`} className="flex w-full">
                                  <Eye className="mr-2 size-4 text-muted-foreground" />
                                  Preview
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => {
                                  setOpenDropdownId(null); // Close dropdown first
                                  setFormToDelete(form);
                                  setDeleteDialogOpen(true);
                                }}
                                className="flex w-full text-destructive focus:text-destructive hover:text-destructive"
                                onSelect={(e) => {
                                  // Prevent default selection behavior that might cause focus issues
                                  e.preventDefault();
                                }}
                              >
                                <Trash2 className="mr-2 size-4 text-destructive" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>

                      {/* Status and Updated Row */}
                      <div className="flex items-center justify-between px-3 pb-3">
                        <span className={form.is_active
                          ? 'inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 text-xs font-medium shadow-sm border border-blue-200'
                          : 'inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-500 text-xs font-medium border border-gray-200'}>
                          {form.is_active ? 'Active' : 'Draft'}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Updated {form.updated_at ? new Date(form.updated_at * 1000).toLocaleDateString() : 'N/A'}
                        </span>
                      </div>

                      {/* Description */}
                      <CardDescription className="line-clamp-2 text-sm leading-relaxed px-3 pb-3">
                        {form.description || "No description provided"}
                      </CardDescription>

                      {/* Metadata Section */}
                      <div className="space-y-3 px-3 pb-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Sections:</span>
                          <span className="inline-flex items-center justify-center px-2 py-1 text-xs bg-muted rounded-full font-medium">
                            {form.sections?.length || 0} sections
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Status:</span>
                          <span className={`font-medium ${form.is_active ? 'text-gray-900' : 'text-gray-500'}`}>
                            {form.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>

                      {/* Bottom Section - Edit Button */}
                      <div className="pt-4 px-3 pb-3">
                        <Link href={`/forms/${form._id || form.id}`}>
                          <Button variant="outline" size="sm" className="w-full">
                            <Edit className="mr-2 size-4" />
                            Edit Form
                          </Button>
                        </Link>
                      </div>
                    </Card>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={deleteDialogOpen}
              title="Delete Form"
              description={`Are you sure you want to delete the form "${formToDelete?.name || ''}"? This action cannot be undone.`}
              onCancel={() => { 
                setDeleteDialogOpen(false); 
                setFormToDelete(null); 
                setOpenDropdownId(null); // Ensure dropdown is closed when modal closes
              }}
              onConfirm={async () => {
                if (!formToDelete) return;
                const formId = String(formToDelete._id || formToDelete.id);
                if (!formId || formId === 'undefined') {
                  toastHook({
                    title: 'Error',
                    description: 'Invalid form ID.',
                    variant: 'destructive'
                  });
                  return;
                }
                
                const success = await handleDeleteForm(formId);
                if (success) {
                  setDeleteDialogOpen(false);
                  setFormToDelete(null);
                  setOpenDropdownId(null); // Ensure dropdown is closed when modal closes
                }
              }}
              loading={deleting}
            />
          </>
        )}
      </div>
    </DashboardShell>
  );
}
