import {Inter as FontSans} from "next/font/google"

import "@/styles/globals.css"
import {siteConfig} from "@/config/site"
import {cn} from "@/lib/utils"
import {Toaster} from "@/components/ui/toaster"
import {Analytics} from "@/components/analytics"
import {TailwindIndicator} from "@/components/tailwind-indicator"
import {Providers} from "./providers"

// Inter font for the entire app
const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

interface RootLayoutProps {
  children: React.ReactNode
}

export const metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: [
    "Private Market",
    "Investment Platform", 
    "Startup Intelligence",
    "AI Investment Analysis",
    "Deal Flow Management",
    "Investment Thesis",
    "Venture Capital",
    "Private Equity",
    "Startup Evaluation",
    "Investment OS"
  ],
  authors: [
    {
      name: "TractionX",
      url: "https://tractionx.ai",
    },
  ],
  creator: "TractionX",
  metadataBase: new URL(siteConfig.url),
  alternates: {
    canonical: "/", 
  },
  themeColor: [
    {media: "(prefers-color-scheme: light)", color: "#ffffff"},
  ],
  openGraph: {
    type: "website",
    locale: "en_US",
    url: siteConfig.url,
    title: "TractionX — Private Market Investment OS",
    description: "From first signal to exit — discover, evaluate, and act on high-potential startups with AI-powered insights.",
    siteName: siteConfig.name,
    images: [
      {
        url: "/images/traction.svg",
        width: 1200,
        height: 630,
        alt: "TractionX - Private Market Investment OS",
        type: "image/svg+xml",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "TractionX — Private Market Investment OS",
    description: "AI-powered investment intelligence for private markets.",
    images: ["/images/traction.svg"],
    creator: "@tractionx",
    site: "@tractionx",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      {
        url: "/favicon.ico",
        sizes: "any",
      },
      {
        url: "/favicon.svg",
        type: "image/svg+xml",
      },
      {
        url: "/favicon-96x96.png",
        type: "image/png",
        sizes: "96x96",
      },
    ],
    shortcut: "/favicon.ico",
    apple: [
      {
        url: "/apple-touch-icon.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
    other: [
      {
        rel: "mask-icon",
        url: "/images/logo.svg",
        color: "#000000",
      },
    ],
  },
  manifest: "/site.webmanifest",
  verification: {
    // Add when you have these set up
    // google: "your-google-verification-code",
    // yandex: "your-yandex-verification-code",
  },
}

export default function RootLayout({children}: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
    <head>
      {/* Additional meta tags for enhanced social sharing */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="TractionX" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* PWA theme color - Light mode only */}
      <meta name="theme-color" content="#ffffff" />
      
      {/* Additional Open Graph tags for different platforms */}
      <meta property="og:locale" content="en_US" />
      <meta property="og:site_name" content="TractionX" />
      <meta property="article:author" content="TractionX" />
      
      {/* LinkedIn specific */}
      <meta property="og:type" content="website" />
      
      {/* WhatsApp optimized */}
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content="TractionX - Private Market Investment OS" />
    </head>
    <body
      className={cn(
        "flex min-h-screen flex-col bg-tx-surface font-sans antialiased",
        fontSans.variable
      )}
    >
    <Providers>
      <div className="flex min-h-screen flex-1 flex-col">
        {children}
      </div>
      <Analytics/>
                    <Toaster/>
      <TailwindIndicator/>
    </Providers>
    </body>
{/* <body
  className={cn(
    "min-h-screen bg-tx-surface font-sans antialiased overflow-hidden", // <-- control outer scroll
    fontSans.variable,
    fontHeading.variable
  )}
>
  <div className="h-screen w-screen overflow-auto">
    <div className="scale-[0.75] origin-top-left w-[117.65%] h-[117.65%]">
      <Providers>
        <div className="flex min-h-screen flex-1 flex-col">
          {children}
        </div>
        <Analytics />
        <Toaster />
        <TailwindIndicator />
      </Providers>
    </div>
  </div>
</body> */}
    </html>
  )
}
