"""
PDL Founder Enrichment Task for TractionX Data Pipeline Service.

This task handles PDL-based founder data enrichment with clean separation of concerns.
Focuses purely on PDL data processing logic while delegating service interactions.
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.pdl.founder import PDLJobContext
from app.etl.founder.pdl.processor import PDLDataProcessor
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class PDLEnrichmentTask(BaseTask):
    """Task for PDL-based founder data enrichment."""

    def __init__(self):
        super().__init__("enrich_founder_pdl_data")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.data_processor: Optional[PDLDataProcessor] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.data_processor:
            self.data_processor = PDLDataProcessor(self.rds_storage, self.s3_storage)

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for PDL founder enrichment."""
        data = extract_job_data(payload)
        required_fields = ["founder_id", "org_id", "company_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute PDL-based founder data enrichment.

        Args:
            payload: Job payload with founder information

        Returns:
            Enrichment result with founder data and metadata
        """
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        # Store raw data to S3
        s3_key = await self._store_raw_data(data, job_context)

        # Fetch and process PDL data
        pdl_result = await self._fetch_and_process_pdl_data(job_context, s3_key)

        return {
            "founder_uuid": pdl_result.get("founder_uuid"),
            "founder_id": job_context.founder_id,
            "company_id": job_context.company_id,
            "org_id": job_context.org_id,
            "s3_key": s3_key,
            "processing_status": "completed",
            "records_created": pdl_result.get("records_created", {}),
        }

    def _extract_job_context(self, job_data: Dict[str, Any]) -> PDLJobContext:
        """Extract job context from payload."""
        logger.info(f"Job data: {job_data}")
        logger.info(f"Job data type: {type(job_data)}")

        # Extract founder info from multiple possible locations
        founder_name = (
            job_data.get("founder_name")
            or job_data.get("data", {}).get("founder_name")
            or job_data.get("entity_data", {}).get("name")
            or ""
        )

        founder_name = job_data.get("data", {}).get("name", "")
        linkedin_url = job_data.get("data", {}).get("linkedin_url", "")
        company_id = job_data.get("data", {}).get("org_url", "")
        current_job_title = job_data.get("data", {}).get("current_job_title", "")
        current_company = job_data.get("data", {}).get("current_company", "")

        logger.info(f"LinkedIn URL: {linkedin_url}")
        if not linkedin_url:
            raise ValueError("Missing LinkedIn URL")

        # Generate founder_id if not provided
        founder_id = job_data.get("entity_id")
        if not founder_id:
            founder_id = self.data_processor._generate_founder_uuid(  # type: ignore
                founder_name, linkedin_url
            )

        return PDLJobContext(
            job_id=job_data.get("job_id", "unknown"),
            founder_id=founder_id,
            company_id=company_id,
            org_id=job_data.get("org_id", ""),
            founder_name=founder_name,
            linkedin_url=linkedin_url,
            data={
                "current_job_title": current_job_title,
                "current_company": current_company,
            },
        )

    def _validate_job_context(self, job_context: PDLJobContext) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.founder_name:
            return "Missing founder_name"
        if not job_context.linkedin_url:
            return "Missing linkedin_url"
        return None

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: PDLJobContext
    ) -> str:
        """Store raw job data to S3 for audit trail."""

        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"datapipelines/requests/founders/pdl/{job_context.founder_id}/raw_data_{timestamp}.json"

        # Store data
        if self.s3_storage:
            await self.s3_storage.put_object(s3_key, json.dumps(job_data, default=str))

        return s3_key

    async def _fetch_and_process_pdl_data(
        self, job_context: PDLJobContext, s3_key: str
    ) -> Dict[str, Any]:
        """Fetch PDL data and process it."""
        try:
            # Import here to avoid circular imports
            from app.clients.pdl import get_pdl_client

            # Get PDL client for founder enrichment
            pdl_client = await get_pdl_client()

            # Enrich with PDL
            pdl_result = await pdl_client.enrich_person(
                profile=job_context.linkedin_url,
                min_likelihood=2,
                include_if_matched=False,
                titlecase=False,
                pretty=False,
            )

            if not pdl_result.success:
                return {
                    "success": False,
                    "error": f"PDL enrichment failed: {pdl_result.raw_response}",
                    "founder_id": job_context.founder_id,
                }

            # Process the PDL data
            if self.data_processor:
                result = await self.data_processor.process(
                    pdl_result.data,  # type: ignore
                    s3_key,
                    job_context,
                )
                logger.info(f"Result from PDL processing: {result}")

                return result
            else:
                return {
                    "success": False,
                    "error": "PDL processor not initialized",
                    "founder_id": job_context.founder_id,
                }

        except Exception as e:
            error_msg = f"PDL fetch and process failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "founder_id": job_context.founder_id,
            }


async def enrich_founder_pdl_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    PDL-based founder enrichment task.

    This function processes PDL-based founder enrichment requests by:
    1. Validating input data and generating founder_id if needed
    2. Storing raw data to S3 for audit trails
    3. Fetching PDL data from API
    4. Processing and normalizing data into structured database tables

    Args:
        payload: Enrichment job configuration containing:
            - founder_id: Founder identifier
            - company_id: Company identifier
            - org_id: Organization identifier
            - founder_name: Founder's name
            - linkedin_url: LinkedIn URL
            - job_id: (optional) Unique job identifier

    Returns:
        Processing result with success status and metadata
    """
    task = PDLEnrichmentTask()

    try:
        await task.initialize()
        return await task.process(payload)
    finally:
        await task.cleanup()


def enrich_founder_pdl_data_sync(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async PDL founder enrichment task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(enrich_founder_pdl_data(payload))
