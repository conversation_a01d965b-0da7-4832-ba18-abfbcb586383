"""
Founder Spider Chart Task for TractionX Data Pipeline Service.

This task handles the generation of founder signals and spider chart data
by analyzing founder experiences, education, and skills using LLM.
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.etl.founder.spidy_chart.processor import SpiderChartProcessor
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class SpiderChartTask(BaseTask):
    """Task for generating founder spider chart data."""

    def __init__(self):
        super().__init__("generate_founder_spider_chart")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.data_processor: Optional[SpiderChartProcessor] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.data_processor:
            self.data_processor = SpiderChartProcessor(
                self.rds_storage, self.s3_storage
            )

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for spider chart generation."""
        data = extract_job_data(payload)
        required_fields = ["founder_id", "org_id", "company_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute founder spider chart generation.

        Args:
            payload: Job payload with founder information

        Returns:
            Spider chart generation result with signals and metadata
        """
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        # Store raw data to S3
        s3_key = await self._store_raw_data(data, job_context)

        # Process founder signals
        spider_chart_result = await self._generate_spider_chart_data(
            job_context, s3_key
        )

        return {
            "founder_id": job_context.founder_id,
            "org_id": job_context.org_id,
            "company_id": job_context.company_id,
            "s3_key": s3_key,
            "processing_status": "completed",
            "spider_chart_data": spider_chart_result.get("signal_output", {}),
            "stored_data": spider_chart_result.get("stored_data", {}),
        }

    def _extract_job_context(self, job_data: Dict[str, Any]) -> "SpiderChartJobContext":
        """Extract job context from payload."""
        founder_id = job_data.get("founder_id")
        org_id = job_data.get("org_id")
        company_id = job_data.get("company_id")

        if not founder_id:
            raise ValueError("Missing founder_id")

        if not org_id:
            raise ValueError("Missing org_id")

        if not company_id:
            raise ValueError("Missing company_id")

        return SpiderChartJobContext(
            job_id=job_data.get("job_id", "unknown"),
            founder_id=founder_id,
            org_id=org_id,
            company_id=company_id,
        )

    def _validate_job_context(
        self, job_context: "SpiderChartJobContext"
    ) -> Optional[str]:
        """Validate job context."""
        if not job_context.founder_id:
            return "Missing founder_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.company_id:
            return "Missing company_id"
        return None

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: "SpiderChartJobContext"
    ) -> str:
        """Store raw job data to S3 for audit trail."""
        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"founders/spider_chart/{job_context.org_id}/{job_context.founder_id}/raw_data_{timestamp}.json"

        # Store data
        if self.s3_storage:
            await self.s3_storage.put_object(s3_key, json.dumps(job_data, default=str))

        return s3_key

    async def _generate_spider_chart_data(
        self, job_context: "SpiderChartJobContext", s3_key: str
    ) -> Dict[str, Any]:
        """Generate spider chart data for founder."""
        try:
            if not self.data_processor:
                raise ValueError("Data processor not initialized")

            # Process founder signals using the ETL processor
            result = await self.data_processor.process_founder_signals(
                founder_id=job_context.founder_id,
                org_id=job_context.org_id,
                company_id=job_context.company_id,
            )

            self.logger.info(
                f"Generated spider chart data for founder {job_context.founder_id}",
                score=result.get("signal_output", {}).get("score"),
                tags=result.get("signal_output", {}).get("tags", []),
            )

            return result

        except Exception as e:
            self.logger.error(f"Error generating spider chart data: {e}")
            raise


class SpiderChartJobContext:
    """Job context for spider chart generation."""

    def __init__(
        self,
        job_id: str,
        founder_id: str,
        org_id: str,
        company_id: str,
    ):
        self.job_id = job_id
        self.founder_id = founder_id
        self.org_id = org_id
        self.company_id = company_id


async def generate_founder_spider_chart(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate founder spider chart data.

    Args:
        payload: Job payload with founder information

    Returns:
        Spider chart generation result
    """
    task = SpiderChartTask()
    try:
        await task.initialize()
        result = await task.process(payload)
        return result
    finally:
        await task.cleanup()


def generate_founder_spider_chart_sync(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for generate_founder_spider_chart.

    Args:
        payload: Job payload with founder information

    Returns:
        Spider chart generation result
    """
    return asyncio.run(generate_founder_spider_chart(payload))
