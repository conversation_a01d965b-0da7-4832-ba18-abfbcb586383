"""
LinkedIn Founder Enrichment Task for TractionX Data Pipeline Service.

This task handles LinkedIn-based founder data enrichment with clean separation of concerns.
Focuses purely on LinkedIn data processing logic while delegating service interactions.
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.linkedin.founder import LinkedInJobContext
from app.etl.founder.linkedin.brightdata import (
    BrightDataLinkedInDataProcessor,
)
from app.models.job_tracking import JobType
from app.queueing.interfaces import JobPriority
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class LinkedInEnrichmentTask(BaseTask):
    """Task for LinkedIn-based founder data enrichment."""

    def __init__(self):
        super().__init__("enrich_founder_linkedin_data")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.data_processor: Optional[BrightDataLinkedInDataProcessor] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.data_processor:
            self.data_processor = BrightDataLinkedInDataProcessor(
                self.rds_storage, self.s3_storage
            )

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for LinkedIn founder enrichment."""
        data = extract_job_data(payload)
        required_fields = ["company_id", "org_id", "founder_name", "founder_linkedin"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute LinkedIn-based founder data enrichment.

        Args:
            payload: Job payload with founder information

        Returns:
            Enrichment result with founder data and metadata
        """
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        # Store raw data to S3
        s3_key = await self._store_raw_data(data, job_context)

        # Fetch and process LinkedIn data
        linkedin_result = await self._fetch_and_process_linkedin_data(
            job_context, s3_key
        )

        # Trigger spider chart processor task if LinkedIn enrichment was successful
        if linkedin_result.get("success", True) and (
            linkedin_result.get("founder_id") or job_context.founder_id
        ):
            await self._trigger_spider_chart_task(job_context, linkedin_result, data)

        return {
            "founder_uuid": linkedin_result.get("founder_uuid"),
            "founder_id": job_context.founder_id,
            "company_id": job_context.company_id,
            "org_id": job_context.org_id,
            "s3_key": s3_key,
            "processing_status": "completed",
            "records_created": linkedin_result.get("records_created", {}),
        }

    def _extract_job_context(self, job_data: Dict[str, Any]) -> LinkedInJobContext:
        """Extract job context from payload."""
        # Handle nested form_data structure
        logger.info(f"Job data: {job_data}")
        logger.info(f"Job data type: {type(job_data)}")

        # Try multiple possible data locations
        data = job_data.get("data", {})
        entity_data = job_data.get("entity_data", {})
        entity_data_inner = entity_data.get("data", {})

        # Extract founder info from multiple possible locations
        founder_name = (
            data.get("founder_name")
            or entity_data.get("founder_name")
            or entity_data_inner.get("name")
            or job_data.get("founder_name")
            or ""
        )

        founder_name = job_data.get("entity_data", {}).get("name", "")
        linkedin_url = job_data.get("entity_data", {}).get("linkedin_url", "")
        company_id = job_data.get("entity_data", {}).get("org_url", "")
        current_job_title = job_data.get("entity_data", {}).get("current_job_title", "")
        current_company = job_data.get("entity_data", {}).get("current_company", "")

        logger.info(f"LinkedIn URL: {linkedin_url}")
        if not linkedin_url:
            raise ValueError("Missing LinkedIn URL")

        # Generate founder_id if not provided
        founder_id = job_data.get("entity_id")
        if not founder_id:
            founder_id = self.data_processor._generate_founder_uuid(  # type: ignore
                founder_name, linkedin_url
            )

        return LinkedInJobContext(
            job_id=job_data.get("job_id", "unknown"),
            founder_id=founder_id,
            company_id=company_id,
            org_id=job_data.get("org_id"),  # type: ignore
            founder_name=founder_name,
            linkedin_url=linkedin_url,
            data={
                "current_job_title": current_job_title,
                "current_company": current_company,
            },
        )

    def _validate_job_context(self, job_context: LinkedInJobContext) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.founder_name:
            return "Missing founder_name"
        if not job_context.linkedin_url:
            return "Missing founder_linkedin"
        return None

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: LinkedInJobContext
    ) -> str:
        """Store raw job data to S3 for audit trail."""

        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"founders/linkedin/{job_context.org_id}/{job_context.founder_id}/raw_data_{timestamp}.json"

        # Store data
        if self.s3_storage:
            await self.s3_storage.put_object(s3_key, json.dumps(job_data, default=str))

        return s3_key

    async def _fetch_and_process_linkedin_data(
        self, job_context: LinkedInJobContext, s3_key: str
    ) -> Dict[str, Any]:
        """Fetch LinkedIn data and process it."""
        try:
            # Import here to avoid circular imports
            from app.services.founder.linkedin.brightdata import get_brightdata_service

            # Get BrightData service for LinkedIn enrichment
            brightdata_service = await get_brightdata_service()

            # Enrich with BrightData (LinkedIn-focused)
            brightdata_result = await brightdata_service.scrape_linkedin_profile(
                founder_id=job_context.founder_id,
                linkedin_url=job_context.linkedin_url,
                org_id=job_context.org_id,
            )

            if not brightdata_result.success:
                return {
                    "success": False,
                    "error": f"LinkedIn enrichment failed: {brightdata_result.result}",
                    "founder_id": job_context.founder_id,
                }

            # Process BrightData result
            # raw_data = brightdata_result["raw_data"]

            # Process the LinkedIn data
            if self.data_processor:
                result = await self.data_processor.process(
                    brightdata_result.result[0]
                    if isinstance(brightdata_result.result, list)
                    else brightdata_result.result,
                    brightdata_result.s3_key,
                    job_context,
                )
                logger.info(f"Result from cleaning: {result}")

                return result
            else:
                return {
                    "success": False,
                    "error": "LinkedIn processor not initialized",
                    "founder_id": job_context.founder_id,
                }

        except Exception as e:
            error_msg = f"LinkedIn fetch and process failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "founder_id": job_context.founder_id,
            }

    async def _trigger_spider_chart_task(
        self,
        job_context: LinkedInJobContext,
        linkedin_result: Dict[str, Any],
        original_data: Dict[str, Any],
    ) -> None:
        """
        Trigger the spider chart processor task using scalable job orchestration.

        This method uses the queue service to create and trigger the spider chart task
        and coordinates with parent job if one exists.
        """
        try:
            # Get queue service to create job
            from app.utils.api import get_v2_queue_service

            queue_service = await get_v2_queue_service()

            # Determine founder_id - prefer from result, fallback to context
            founder_id = linkedin_result.get("founder_id") or job_context.founder_id

            if not founder_id:
                logger.warning("No founder_id available, skipping spider chart task")
                return

            # Create spider chart job payload
            spider_chart_payload = {
                "founder_id": founder_id,
                "org_id": job_context.org_id,
                "company_id": job_context.company_id,
                "linkedin_job_id": job_context.job_id,
                "linkedin_result": linkedin_result,
            }

            # Get parent job ID if this processor has one
            parent_job_id = original_data.get("parent_job_id")

            # Create the spider chart job using the queue service
            spider_chart_job_id = await queue_service.create_job(
                entity_type="founder",
                entity_id=founder_id,
                job_type=JobType.FOUNDER_SPIDER_CHART,
                payload=spider_chart_payload,
                metadata={
                    "pipeline_id": original_data.get("pipeline_id"),
                    "org_id": job_context.org_id,
                    "parent_job_id": parent_job_id,
                    "job_group": f"founder_enrichment_{founder_id}",
                },
                config={
                    "enable_processing": True,
                    "store_to_spider_chart": True,
                },
                priority=JobPriority.NORMAL,
                parent_job_id=parent_job_id,
            )

            logger.info(
                f"Triggered spider chart task: {spider_chart_job_id}",
                founder_id=founder_id,
                company_id=job_context.company_id,
            )

            # Coordinate with parent job if one exists
            if parent_job_id:
                # Add spider chart job as dependency to parent job
                await queue_service.coordinate_child_job(
                    child_job_id=spider_chart_job_id, parent_job_id=parent_job_id
                )

        except Exception as e:
            logger.error(f"Failed to trigger spider chart task: {e}")
            # Don't fail the entire processor task if spider chart triggering fails


async def enrich_founder_linkedin_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    LinkedIn-based founder enrichment task.

    This function processes LinkedIn-based founder enrichment requests by:
    1. Validating input data and generating founder_id if needed
    2. Storing raw data to S3 for audit trails
    3. Fetching LinkedIn data from BrightData API
    4. Processing and normalizing data into structured database tables

    Args:
        payload: Enrichment job configuration containing:
            - company_id: Company identifier
            - org_id: Organization identifier
            - founder_name: Founder's name
            - founder_linkedin: LinkedIn URL
            - founder_id: (optional) Pre-generated founder ID
            - job_id: (optional) Unique job identifier

    Returns:
        Processing result with success status and metadata
    """
    task = LinkedInEnrichmentTask()

    try:
        await task.initialize()
        return await task.process(payload)
    finally:
        await task.cleanup()


def enrich_founder_linkedin_data_sync(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async LinkedIn founder enrichment task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(enrich_founder_linkedin_data(payload))
