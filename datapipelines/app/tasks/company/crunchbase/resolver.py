"""
Crunchbase Company URL Resolver Task for TractionX Data Pipeline Service.

This task handles Crunchbase URL resolution for companies with clean separation of concerns.
Focuses purely on URL resolution logic while delegating service interactions.
Implements Phase 1: Conditional BrightData scraping trigger with scalable job orchestration.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.crunchbase.company import CrunchbaseCompanyJobContext
from app.models.company import CompanyResolverData
from app.models.crunchbase import CrunchbaseResolverInput
from app.models.job_tracking import JobType
from app.queueing.interfaces import JobPriority
from app.services.company.crunchbase import get_crunchbase_resolver_service
from app.services.jobs.factory import get_job_service
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class CrunchbaseCompanyResolverTask(BaseTask):
    """Task for resolving Crunchbase URLs for companies with conditional scraping."""

    def __init__(self):
        super().__init__("resolve_crunchbase_company_url_task")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.job_service = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.job_service:
            self.job_service = await get_job_service()

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for Crunchbase company URL resolution."""
        data = extract_job_data(payload)
        required_fields = ["company_domain", "company_id", "org_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute Crunchbase company URL resolution with conditional scraping.

        Args:
            payload: Job payload with company domain and metadata

        Returns:
            Resolution result with URL and metadata
        """
        start_time = time.time()
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        # Extract configuration (Phase 1: Basic conditional logic)
        enable_scraping = data.get("enable_scraping", True)  # Default: True
        scraping_mode = data.get("scraping_mode", "auto")  # Default: auto

        logger.info(
            "Crunchbase resolver configuration",
            company_domain=job_context.company_domain,
            org_id=job_context.org_id,
            enable_scraping=enable_scraping,
            scraping_mode=scraping_mode,
        )

        # Store raw data to S3
        s3_key = await self._store_raw_data(data, job_context)

        # Create input model for Crunchbase resolver
        input_data = CrunchbaseResolverInput(
            company_domain=job_context.company_domain,
            company_description=data.get("company_description"),
            org_id=job_context.org_id,
            job_id=job_context.job_id,
        )

        # Get resolver service
        resolver_service = await get_crunchbase_resolver_service()

        # Resolve Crunchbase URL
        result = await resolver_service.resolve_crunchbase_url(input_data)

        # Determine if scraping should be triggered
        scraping_triggered = False
        brightdata_snapshot_id = None

        if (
            enable_scraping
            and result.crunchbase_url
            and result.crunchbase_url != "NO_MATCH"
        ):
            if scraping_mode == "always":
                scraping_triggered = True
            elif scraping_mode == "auto":
                # Auto logic: Only scrape if URL looks promising
                scraping_triggered = self._is_url_promising(result.crunchbase_url)
            elif scraping_mode == "never":
                scraping_triggered = False
            else:
                scraping_triggered = True  # Default to True for backward compatibility

        # Trigger BrightData scraping if needed
        if scraping_triggered:
            try:
                logger.info(
                    f"Triggering BrightData scraping for: {result.crunchbase_url}"
                )
                if result.crunchbase_url and result.crunchbase_url != "NO_MATCH":
                    # For Crunchbase, the BrightData scraping is already triggered in the resolver service
                    # We just need to check if we got a snapshot ID
                    if result.brightdata_snapshot_id:
                        brightdata_snapshot_id = result.brightdata_snapshot_id
                        logger.info(
                            f"BrightData scraping triggered successfully: {brightdata_snapshot_id}"
                        )

                        # Trigger scraper task using scalable job orchestration
                        await self._trigger_scraper_task(
                            job_context, brightdata_snapshot_id, data
                        )
                    else:
                        logger.warning(
                            "No BrightData snapshot ID returned from resolver service"
                        )
                else:
                    logger.warning(
                        "Crunchbase URL is None or NO_MATCH, skipping BrightData scraping"
                    )

            except Exception as e:
                logger.error(f"Failed to trigger BrightData scraping: {e}")
                # Continue without scraping - don't fail the entire task

        # Store result in CompanyResolverData table
        try:
            resolved_url_data = CompanyResolverData(
                company_id=job_context.company_id,
                org_id=job_context.org_id,
                company_domain=job_context.company_domain,
                resolver_type="crunchbase",
                resolved_url=result.crunchbase_url
                if result.crunchbase_url != "NO_MATCH"
                else None,
                confidence_score=1.0,  # Default confidence score
                status=result.status,
                s3_raw_data_key=s3_key,
                error_message=result.error_message,
                error_stage=result.error_stage,
                resolved_at=datetime.now(timezone.utc)
                if result.status == "resolved"
                else None,
            )

            # Store in database using RDSStorageV2
            if self.rds_storage:
                resolved_url_id = await self.rds_storage.upsert(
                    table_name="company.resolved_urls",
                    data=resolved_url_data.model_dump(for_rds=True),
                    key_fields=["company_id", "org_id", "resolver_type"],
                )

                logger.info(f"Stored resolved URL data: {resolved_url_id}")
            else:
                logger.error("RDS storage not initialized")

        except Exception as e:
            logger.error(f"Failed to store resolved URL data: {e}")
            # Continue - don't fail the entire task if storage fails

        # Store result to S3 for audit
        await self._store_result_to_s3(result, job_context, s3_key)

        return {
            "success": result.status in ["triggered", "no_match", "completed"],
            "status": result.status,
            "crunchbase_url": result.crunchbase_url
            if result.crunchbase_url != "NO_MATCH"
            else None,
            "brightdata_snapshot_id": brightdata_snapshot_id,
            "scraping_triggered": scraping_triggered,
            "scraping_mode": scraping_mode,
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

    async def _trigger_scraper_task(
        self,
        job_context: CrunchbaseCompanyJobContext,
        snapshot_id: str,
        original_data: Dict[str, Any],
    ) -> None:
        """
        Trigger Crunchbase scraper task when URL is resolved.

        Args:
            job_context: Job context
            snapshot_id: BrightData snapshot ID
            original_data: Original job data
        """
        try:
            # Get queue service to create job
            from app.utils.api import get_v2_queue_service

            queue_service = await get_v2_queue_service()

            # Create scraper job payload
            scraper_payload = {
                "snapshot_id": snapshot_id,
                "company_domain": job_context.company_domain,
                "company_id": job_context.company_id,
                "org_id": job_context.org_id,
                "crunchbase_url": original_data.get("crunchbase_url"),
                "resolver_job_id": job_context.job_id,
            }

            # Get parent job ID if this resolver has one
            parent_job_id = original_data.get("parent_job_id")

            # Create the scraper job using the queue service
            scraper_job_id = await queue_service.create_job(
                entity_type="company",
                entity_id=job_context.company_id,
                job_type=JobType.COMPANY_CRUNCHBASE_PROCESSOR,
                payload=scraper_payload,
                metadata={
                    "pipeline_id": original_data.get("pipeline_id"),
                    "org_id": job_context.org_id,
                    "parent_job_id": parent_job_id,
                    "job_group": f"crunchbase_enrichment_{job_context.company_id}",
                },
                config={
                    "enable_processing": True,
                    "store_to_resolved_urls": True,
                },
                priority=JobPriority.NORMAL,
                parent_job_id=parent_job_id,
            )

            logger.info(
                f"Triggered scraper task: {scraper_job_id}",
                snapshot_id=snapshot_id,
                company_id=job_context.company_id,
            )

            # Coordinate with parent job if one exists
            if parent_job_id:
                # Add scraper job as dependency to parent job
                await queue_service.coordinate_child_job(
                    child_job_id=scraper_job_id, parent_job_id=parent_job_id
                )

        except Exception as e:
            logger.error(f"Failed to trigger scraper task: {e}")
            # Don't fail the entire resolver task if scraper triggering fails

    def _is_url_promising(self, crunchbase_url: str) -> bool:
        """
        Determine if a Crunchbase URL looks promising for scraping.

        Args:
            crunchbase_url: Crunchbase URL to evaluate

        Returns:
            True if URL looks promising, False otherwise
        """
        if not crunchbase_url or crunchbase_url == "NO_MATCH":
            return False

        # Basic heuristics for promising URLs
        promising_indicators = [
            "crunchbase.com",
        ]

        return any(
            indicator in crunchbase_url.lower() for indicator in promising_indicators
        )

    def _extract_job_context(
        self, job_data: Dict[str, Any]
    ) -> CrunchbaseCompanyJobContext:
        """Extract job context from payload."""
        return CrunchbaseCompanyJobContext(
            job_id=job_data.get("job_id", "unknown"),
            company_id=job_data["company_id"],
            org_id=job_data["org_id"],
            company_domain=job_data["company_domain"],
            data=job_data,
            brightdata_snapshot_id=job_data.get("snapshot_id"),
        )

    def _validate_job_context(
        self, job_context: CrunchbaseCompanyJobContext
    ) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_domain:
            return "Missing company_domain"
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        return None

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: CrunchbaseCompanyJobContext
    ) -> str:
        """Store raw data to S3."""
        if not self.s3_storage:
            raise Exception("S3 storage not initialized")

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"crunchbase_resolver_raw/{job_context.org_id}/{job_context.company_domain}/{timestamp}_raw.json"

        s3_data = {
            "job_context": job_context._asdict(),
            "job_data": job_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await self.s3_storage.put_object(s3_key, s3_data)
        if not success:
            raise Exception(f"Failed to store raw data to S3: {s3_key}")

        logger.info(f"Stored raw data to S3: {s3_key}")
        return s3_key

    async def _store_result_to_s3(
        self, result: Any, job_context: CrunchbaseCompanyJobContext, s3_key: str
    ) -> None:
        """Store result to S3 for audit."""
        if not self.s3_storage:
            logger.error("S3 storage not initialized")
            return

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        result_s3_key = f"crunchbase_resolver_results/{job_context.org_id}/{job_context.company_domain}/{timestamp}_result.json"

        s3_data = {
            "job_context": job_context._asdict(),
            "result": result.model_dump(),
            "s3_raw_data_key": s3_key,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await self.s3_storage.put_object(result_s3_key, s3_data)
        if success:
            logger.info(f"Stored result to S3: {result_s3_key}")
        else:
            logger.error(f"Failed to store result to S3: {result_s3_key}")


async def resolve_crunchbase_company_url_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Main task function for Crunchbase company URL resolution.

    This function is called by the job queue system and handles:
    - Input validation
    - Service initialization
    - URL resolution
    - Result storage

    Args:
        payload: Job payload with company domain and metadata

    Returns:
        Resolution result with URL and metadata
    """
    task = CrunchbaseCompanyResolverTask()
    await task.initialize()

    try:
        result = await task.process(payload)
        return result
    finally:
        await task.cleanup()
