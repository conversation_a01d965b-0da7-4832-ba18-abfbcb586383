"""
PitchBook Company URL Resolver Task for TractionX Data Pipeline Service.

This task handles PitchBook URL resolution for companies with clean separation of concerns.
Focuses purely on URL resolution logic while delegating service interactions.
Implements Phase 1: Conditional BrightData scraping trigger with scalable job orchestration.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.pitchbook.company import PitchBookCompanyJobContext
from app.models.company import CompanyResolverData
from app.models.job_tracking import JobType
from app.models.pitchbook import PitchBookResolverInput
from app.queueing.interfaces import JobPriority
from app.services.company.pitchbook import get_pitchbook_resolver_service
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class PitchBookCompanyResolverTask(BaseTask):
    """Task for resolving PitchBook URLs for companies with conditional scraping."""

    def __init__(self):
        super().__init__("resolve_pitchbook_company_url_task")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for PitchBook company URL resolution."""
        data = extract_job_data(payload)
        required_fields = ["company_domain", "company_id", "org_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute PitchBook company URL resolution with conditional scraping.

        Args:
            payload: Job payload with company domain and metadata

        Returns:
            Resolution result with URL and metadata
        """
        start_time = time.time()
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        # Extract configuration (Phase 1: Basic conditional logic)
        enable_scraping = data.get("enable_scraping", True)  # Default: True
        scraping_mode = data.get("scraping_mode", "auto")  # Default: auto

        logger.info(
            "PitchBook resolver configuration",
            company_domain=job_context.company_domain,
            org_id=job_context.org_id,
            enable_scraping=enable_scraping,
            scraping_mode=scraping_mode,
        )

        # Store raw data to S3
        s3_key = await self._store_raw_data(data, job_context)

        # Create input model for PitchBook resolver
        input_data = PitchBookResolverInput(
            company_domain=job_context.company_domain,
            company_description=data.get("company_description"),
            org_id=job_context.org_id,
            job_id=job_context.job_id,
        )

        # Get resolver service
        resolver_service = await get_pitchbook_resolver_service()

        # Resolve PitchBook URL
        result = await resolver_service.resolve_pitchbook_url(input_data)

        # Determine if scraping should be triggered
        scraping_triggered = False
        brightdata_snapshot_id = None

        if (
            enable_scraping
            and result.pitchbook_url
            and result.pitchbook_url != "NO_MATCH"
        ):
            if scraping_mode == "always":
                scraping_triggered = True
            elif scraping_mode == "auto":
                # Auto logic: Only scrape if URL looks promising
                scraping_triggered = self._is_url_promising(result.pitchbook_url)
            elif scraping_mode == "never":
                scraping_triggered = False
            else:
                scraping_triggered = True  # Default to True for backward compatibility

        # Trigger BrightData scraping if needed
        if scraping_triggered:
            try:
                logger.info(
                    f"Triggering BrightData scraping for: {result.pitchbook_url}"
                )
                if result.pitchbook_url and result.pitchbook_url != "NO_MATCH":
                    brightdata_result = (
                        await resolver_service.trigger_brightdata_scrape(
                            result.pitchbook_url
                        )
                    )

                    if brightdata_result["success"]:
                        brightdata_snapshot_id = brightdata_result["snapshot_id"]
                        logger.info(
                            f"BrightData scraping triggered successfully: {brightdata_snapshot_id}"
                        )

                        # Trigger scraper task using scalable job orchestration
                        await self._trigger_scraper_task(
                            job_context, brightdata_snapshot_id, data
                        )
                    else:
                        logger.warning(
                            f"BrightData scraping failed: {brightdata_result.get('error')}"
                        )
                else:
                    logger.warning(
                        "PitchBook URL is None or NO_MATCH, skipping BrightData scraping"
                    )

            except Exception as e:
                logger.error(f"Failed to trigger BrightData scraping: {e}")
                # Continue without scraping - don't fail the entire task

        # Store result in CompanyResolverData table
        try:
            resolved_url_data = CompanyResolverData(
                company_id=job_context.company_id,
                org_id=job_context.org_id,
                company_domain=job_context.company_domain,
                resolver_type="pitchbook",
                resolved_url=result.pitchbook_url
                if result.pitchbook_url != "NO_MATCH"
                else None,
                confidence_score=result.confidence_score or 1.0,
                status=result.status,
                s3_raw_data_key=s3_key,
                error_message=result.error_message,
                error_stage=None,  # PitchBookResolverOutput doesn't have error_stage
                resolved_at=datetime.now(timezone.utc)
                if result.status == "resolved"
                else None,
            )

            # Store in database using RDSStorageV2
            if self.rds_storage:
                resolved_url_id = await self.rds_storage.upsert(
                    table_name="company.resolved_urls",
                    data=resolved_url_data.model_dump(for_rds=True),
                    key_fields=["company_id", "org_id", "resolver_type"],
                )

                logger.info(f"Stored resolved URL data: {resolved_url_id}")
            else:
                logger.error("RDS storage not initialized")

        except Exception as e:
            logger.error(f"Failed to store resolved URL data: {e}")

        # Prepare return result
        return_result = {
            "success": result.status
            in ["resolved", "triggered", "no_match", "completed"],
            "status": result.status,
            "pitchbook_url": result.pitchbook_url,
            "brightdata_snapshot_id": brightdata_snapshot_id,
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        logger.info(
            "PitchBook resolver task completed successfully",
            company_domain=job_context.company_domain,
            status=result.status,
            pitchbook_url=result.pitchbook_url,
            processing_time=return_result["processing_time"],
        )

        return return_result

    async def _trigger_scraper_task(
        self,
        job_context: PitchBookCompanyJobContext,
        snapshot_id: str,
        original_data: Dict[str, Any],
    ) -> None:
        """
        Trigger PitchBook scraper task for data processing.

        Args:
            job_context: PitchBook company job context
            snapshot_id: BrightData snapshot ID
            original_data: Original job data
        """
        try:
            logger.info(
                f"Triggering PitchBook scraper task for snapshot {snapshot_id}",
                company_domain=job_context.company_domain,
            )

            # Get queue service to create job
            from app.utils.api import get_v2_queue_service

            queue_service = await get_v2_queue_service()

            # Create scraper job payload
            scraper_payload = {
                "snapshot_id": snapshot_id,
                "company_domain": job_context.company_domain,
                "company_id": job_context.company_id,
                "org_id": job_context.org_id,
                "pitchbook_url": job_context.pitchbook_url,
                "brightdata_snapshot_id": snapshot_id,
                "resolver_job_id": job_context.job_id,
            }

            # Get parent job ID if this resolver has one
            parent_job_id = original_data.get("parent_job_id")

            # Create the scraper job using the queue service
            scraper_job_id = await queue_service.create_job(
                entity_type="company",
                entity_id=job_context.company_id,
                job_type=JobType.COMPANY_PITCHBOOK_PROCESSOR,
                payload=scraper_payload,
                metadata={
                    "pipeline_id": original_data.get("pipeline_id"),
                    "org_id": job_context.org_id,
                    "parent_job_id": parent_job_id,
                    "job_group": f"pitchbook_enrichment_{job_context.company_id}",
                },
                config={
                    "enable_processing": True,
                    "store_to_resolved_urls": True,
                },
                priority=JobPriority.NORMAL,
                parent_job_id=parent_job_id,
            )

            logger.info(
                f"Triggered scraper task: {scraper_job_id}",
                snapshot_id=snapshot_id,
                company_id=job_context.company_id,
            )

            # Coordinate with parent job if one exists
            if parent_job_id:
                # Add scraper job as dependency to parent job
                await queue_service.coordinate_child_job(
                    child_job_id=scraper_job_id, parent_job_id=parent_job_id
                )

        except Exception as e:
            logger.error(f"Failed to trigger scraper task: {e}")
            # Don't fail the entire resolver task if scraper triggering fails

    def _is_url_promising(self, pitchbook_url: str) -> bool:
        """
        Check if a PitchBook URL looks promising for scraping.

        Args:
            pitchbook_url: PitchBook URL to check

        Returns:
            True if URL looks promising, False otherwise
        """
        if not pitchbook_url:
            return False

        # Check if it's a valid PitchBook domain
        if "pitchbook.com" in pitchbook_url:
            return True

        return False

    def _extract_job_context(
        self, job_data: Dict[str, Any]
    ) -> PitchBookCompanyJobContext:
        """Extract job context from job data."""
        return PitchBookCompanyJobContext(
            job_id=job_data.get("job_id", ""),
            company_id=job_data["company_id"],
            org_id=job_data["org_id"],
            company_domain=job_data["company_domain"],
            data=job_data,
        )

    def _validate_job_context(
        self, job_context: PitchBookCompanyJobContext
    ) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_domain:
            return "Missing company_domain"
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        return None

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: PitchBookCompanyJobContext
    ) -> str:
        """Store raw job data to S3."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"pitchbook_resolver_raw/{job_context.org_id}/{job_context.company_domain}/{timestamp}_raw.json"

        s3_data = {
            "job_context": job_context._asdict(),
            "job_data": job_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if self.s3_storage:
            success = await self.s3_storage.put_object(s3_key, s3_data)
            if not success:
                logger.warning(f"Failed to store raw data to S3: {s3_key}")
        else:
            logger.warning("S3 storage not available, skipping raw data storage")

        return s3_key

    async def _store_result_to_s3(
        self, result: Any, job_context: PitchBookCompanyJobContext, s3_key: str
    ) -> None:
        """Store resolution result to S3."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        result_key = f"pitchbook_resolver_results/{job_context.org_id}/{job_context.company_domain}/{timestamp}_result.json"

        s3_data = {
            "job_context": job_context._asdict(),
            "result": result.model_dump() if hasattr(result, "model_dump") else result,
            "s3_raw_data_key": s3_key,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if self.s3_storage:
            success = await self.s3_storage.put_object(result_key, s3_data)
            if not success:
                logger.warning(f"Failed to store result to S3: {result_key}")
        else:
            logger.warning("S3 storage not available, skipping result storage")

    async def _store_resolver_data(
        self, result: Any, job_context: PitchBookCompanyJobContext
    ) -> None:
        """Store resolver data to database."""
        try:
            resolver_data = CompanyResolverData(
                company_id=job_context.company_id,
                org_id=job_context.org_id,
                company_domain=job_context.company_domain,
                resolver_type="pitchbook",
                resolved_url=result.pitchbook_url,
                confidence_score=result.confidence_score,
                status=result.status,
                error_message=result.error_message,
                s3_raw_data_key=None,  # Will be set by the calling method
                error_stage=None,  # Will be set if there's an error
                resolved_at=datetime.now(timezone.utc),
            )

            if self.rds_storage:
                await self.rds_storage.insert("company.resolved_urls", resolver_data)
                logger.info(
                    f"Stored resolver data for company {job_context.company_id}"
                )
            else:
                logger.warning(
                    "RDS storage not available, skipping resolver data storage"
                )

        except Exception as e:
            logger.error(f"Failed to store resolver data: {e}", exc_info=True)

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: Optional[str]
    ) -> None:
        """Store processing error for debugging."""
        try:
            error_data = {
                "error": error_msg,
                "payload": payload,
                "s3_key": s3_key,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            error_key = f"errors/pitchbook_resolver/{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}_{hash(error_msg) % 10000}.json"

            if self.s3_storage:
                await self.s3_storage.put_object(error_key, error_data)
                logger.info(f"Stored processing error to S3: {error_key}")
            else:
                logger.warning("S3 storage not available, skipping error storage")

        except Exception as e:
            logger.warning(f"Failed to store processing error: {e}")


async def resolve_pitchbook_company_url_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for PitchBook company URL resolution.

    This function is called by the job queue system and handles:
    - Task initialization
    - URL resolution
    - Result storage
    - Error handling

    Args:
        payload: Job payload containing company domain and metadata

    Returns:
        Processing result with status and data
    """
    task = PitchBookCompanyResolverTask()

    try:
        await task.initialize()
        return await task.process(payload)
    finally:
        await task.cleanup()
