"""
PitchBook Company Scraper Task for TractionX Data Pipeline Service.

This task handles PitchBook company data scraping from BrightData.
Focuses purely on scraping logic while delegating service interactions.
Updated to work with new company.resolved_urls table structure.
Uses PitchBookCompanyDataProcessor for data processing and cleaning.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.pitchbook.company import PitchBookCompanyJobContext
from app.etl.company.pitchbook.processor import PitchBookCompanyDataProcessor
from app.services.company.pitchbook import get_pitchbook_resolver_service
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class PitchBookCompanyScraperTask(BaseTask):
    """Task for scraping PitchBook company data with company.resolved_urls table integration."""

    def __init__(self):
        super().__init__("scrape_pitchbook_company_data_task")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.processor: Optional[PitchBookCompanyDataProcessor] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.processor:
            self.processor = PitchBookCompanyDataProcessor(
                self.rds_storage, self.s3_storage
            )

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for PitchBook company scraping."""
        data = extract_job_data(payload)
        required_fields = ["snapshot_id", "company_domain", "company_id", "org_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute PitchBook company data scraping with company.resolved_urls table integration.

        Args:
            payload: Job payload with snapshot ID and metadata

        Returns:
            Scraping result with data and metadata
        """
        start_time = time.time()
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        try:
            # Step 1: Poll for snapshot completion
            if not job_context.brightdata_snapshot_id:
                return {
                    "success": False,
                    "error": "Missing brightdata_snapshot_id",
                    "processing_time": time.time() - start_time,
                }

            logger.info(
                f"Polling for BrightData snapshot completion: {job_context.brightdata_snapshot_id}"
            )
            poll_result = await self._poll_brightdata_snapshot_completion(
                job_context.brightdata_snapshot_id
            )
            if not poll_result["success"]:
                return {
                    "success": False,
                    "error": f"Snapshot polling failed: {poll_result['error']}",
                    "processing_time": time.time() - start_time,
                    "brightdata_status": poll_result.get("status", "poll_failed"),
                }

            logger.info(
                f"BrightData snapshot ready: {job_context.brightdata_snapshot_id}"
            )

            # Step 2: Download snapshot data
            download_result = await self._download_brightdata_snapshot(
                job_context.brightdata_snapshot_id
            )
            if not download_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to download snapshot: {download_result['error']}",
                    "processing_time": time.time() - start_time,
                }

            raw_data = download_result["data"]
            logger.info(
                "BrightData snapshot downloaded successfully",
                snapshot_id=job_context.brightdata_snapshot_id,
                data_keys=list(raw_data.keys())
                if isinstance(raw_data, dict)
                else "not_dict",
            )

            # Step 3: Store raw data to S3
            s3_key = await self._store_raw_data(raw_data, job_context)

            # Step 4: Process the data using PitchBookCompanyDataProcessor
            if not self.processor:
                raise Exception("PitchBookCompanyDataProcessor not initialized")

            processing_result = await self.processor.process(
                payload=raw_data, s3_raw_data_key=s3_key, job_context=job_context
            )

            if not processing_result["success"]:
                return {
                    "success": False,
                    "error": f"Data processing failed: {processing_result['error']}",
                    "processing_time": time.time() - start_time,
                }

            # Step 5: Update S3 data to include processed data
            await self._update_s3_data(raw_data, processing_result, job_context, s3_key)

            # Step 6: Prepare return result
            return_result = {
                "success": True,
                "status": "completed",
                "s3_key": s3_key,
                "processing_time": time.time() - start_time,
            }

            logger.info(
                "PitchBook company data scraping completed successfully",
                snapshot_id=job_context.brightdata_snapshot_id,
                company_domain=job_context.company_domain,
                processing_time=return_result["processing_time"],
                s3_key=s3_key,
            )

            return return_result

        except Exception as e:
            error_msg = f"PitchBook company scraping task failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

    def _extract_job_context(
        self, job_data: Dict[str, Any]
    ) -> PitchBookCompanyJobContext:
        """Extract job context from job data."""
        return PitchBookCompanyJobContext(
            job_id=job_data.get("job_id", ""),
            company_id=job_data["company_id"],
            org_id=job_data["org_id"],
            company_domain=job_data["company_domain"],
            data=job_data,
            pitchbook_url=job_data.get("pitchbook_url"),
            brightdata_snapshot_id=job_data.get("brightdata_snapshot_id"),
        )

    def _validate_job_context(
        self, job_context: PitchBookCompanyJobContext
    ) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_domain:
            return "Missing company_domain"
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.brightdata_snapshot_id:
            return "Missing brightdata_snapshot_id"
        return None

    async def _poll_brightdata_snapshot_completion(
        self, snapshot_id: str
    ) -> Dict[str, Any]:
        """
        Poll for BrightData snapshot completion.

        Args:
            snapshot_id: BrightData snapshot ID

        Returns:
            Dictionary with success status and error if any
        """
        max_poll_time = 600  # 10 minutes
        poll_interval = 10  # 10 seconds
        start_time = time.time()

        while time.time() - start_time < max_poll_time:
            try:
                # Get resolver service for BrightData operations
                resolver_service = await get_pitchbook_resolver_service()

                # Check snapshot progress using BrightData API
                if not resolver_service.brightdata_client:
                    return {
                        "success": False,
                        "status": "error",
                        "error": "BrightData client not initialized",
                    }

                response = await resolver_service.brightdata_client.get(
                    f"/datasets/v3/progress/{snapshot_id}"
                )
                response.raise_for_status()
                result = response.json()

                status = result.get("status")
                logger.info(f"Polling status: {status} for {snapshot_id}")

                if status == "ready":
                    return {
                        "success": True,
                        "status": "ready",
                    }
                elif status in ["error", "failed"]:
                    return {
                        "success": False,
                        "status": "error",
                        "error": f"Job failed with status: {status}",
                    }

                # Wait before next poll
                await asyncio.sleep(poll_interval)

            except Exception as e:
                logger.warning(f"Poll error: {e}")
                await asyncio.sleep(poll_interval)

        # Timeout
        return {
            "success": False,
            "status": "timeout",
            "error": f"Polling timed out after {max_poll_time} seconds",
        }

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """
        Download BrightData snapshot data.

        Args:
            snapshot_id: BrightData snapshot ID

        Returns:
            Dictionary with success status and data if successful
        """
        try:
            # Get resolver service for BrightData operations
            resolver_service = await get_pitchbook_resolver_service()

            # Download snapshot data
            download_result = await resolver_service._download_brightdata_snapshot(
                snapshot_id
            )

            if not download_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to download snapshot: {download_result['error']}",
                }

            return {
                "success": True,
                "data": download_result["data"],
            }

        except Exception as e:
            logger.error(f"Error downloading BrightData snapshot: {e}", exc_info=True)
            return {
                "success": False,
                "error": f"Download failed: {str(e)}",
            }

    async def _store_raw_data(
        self, raw_data: Any, job_context: PitchBookCompanyJobContext
    ) -> str:
        """Store raw data to S3."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"brightdata_company_pitchbook/{job_context.company_domain}_{timestamp}.json"

        s3_data = {
            "snapshot_id": job_context.brightdata_snapshot_id,
            "company_domain": job_context.company_domain,
            "org_id": job_context.org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if self.s3_storage:
            success = await self.s3_storage.put_object(s3_key, s3_data)
            if success:
                logger.info(f"Raw BrightData data stored to S3: {s3_key}")
            else:
                logger.error(f"Failed to store raw BrightData data to S3: {s3_key}")
        else:
            logger.warning("S3 storage not available, skipping raw data storage")

        return s3_key

    async def _update_s3_data(
        self,
        raw_data: Any,
        processed_data: Any,
        job_context: PitchBookCompanyJobContext,
        s3_key: str,
    ) -> None:
        """Update S3 data to include processed data."""
        try:
            updated_s3_data = {
                "snapshot_id": job_context.brightdata_snapshot_id,
                "company_domain": job_context.company_domain,
                "org_id": job_context.org_id,
                "raw_data": raw_data,
                "processed_data": processed_data,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            if self.s3_storage:
                success = await self.s3_storage.put_object(s3_key, updated_s3_data)
                if success:
                    logger.info(f"Updated S3 data with processed data: {s3_key}")
                else:
                    logger.error(
                        f"Failed to update S3 data with processed data: {s3_key}"
                    )
            else:
                logger.warning("S3 storage not available, skipping S3 data update")

        except Exception as e:
            logger.error(f"Error updating S3 data: {e}", exc_info=True)


async def scrape_pitchbook_company_data_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for PitchBook company data scraping.

    This function is called by the job queue system and handles:
    - Task initialization
    - Data scraping
    - Data processing
    - Result storage
    - Error handling

    Args:
        payload: Job payload with snapshot ID and metadata

    Returns:
        Processing result with status and data
    """
    task = PitchBookCompanyScraperTask()

    try:
        await task.initialize()
        return await task.process(payload)
    finally:
        await task.cleanup()
