"""
Apollo Company Data Processor Task for TractionX Data Pipeline Service.

This task handles Apollo company data processing from API.
Focuses purely on API data fetching and processing logic while delegating service interactions.
Updated to work with new company.resolved_urls table structure.
Uses ApolloCompanyDataProcessor for data processing and cleaning.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.apollo.company import ApolloCompanyJobContext
from app.etl.company.apollo.processor import ApolloCompanyDataProcessor
from app.services.company.apollo import get_apollo_service
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields

logger = get_logger(__name__)


class ApolloCompanyProcessorTask(BaseTask):
    """Task for processing Apollo company data with company.resolved_urls table integration."""

    def __init__(self):
        super().__init__("process_apollo_company_data_task")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.processor: Optional[ApolloCompanyDataProcessor] = None
        self.apollo_service = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        await super().initialize()

        if not self.rds_storage:
            self.rds_storage = RDSStorageV2()
            await self.rds_storage.initialize()

        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

        if not self.processor:
            self.processor = ApolloCompanyDataProcessor(
                self.rds_storage, self.s3_storage
            )

        if not self.apollo_service:
            self.apollo_service = await get_apollo_service()

    async def cleanup(self) -> None:
        """Clean up task resources."""
        await super().cleanup()

        if self.rds_storage:
            await self.rds_storage.cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for Apollo company processing."""
        data = extract_job_data(payload)
        required_fields = ["company_domain", "company_id", "org_id"]
        return validate_required_fields(data, required_fields)

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute Apollo company data processing with company.resolved_urls table integration.

        Args:
            payload: Job payload with company domain and metadata

        Returns:
            Processing result with data and metadata
        """
        start_time = time.time()
        data = extract_job_data(payload)

        # Extract job context
        job_context = self._extract_job_context(data)

        # Validate job context
        validation_error = self._validate_job_context(job_context)
        if validation_error:
            raise Exception(validation_error)

        try:
            # Step 1: Fetch Apollo data from API
            logger.info(
                f"Fetching Apollo data for company: {job_context.company_domain}"
            )

            apollo_data = await self._fetch_apollo_data(job_context)
            if not apollo_data:
                return {
                    "success": False,
                    "error": "Failed to fetch Apollo data",
                    "processing_time": time.time() - start_time,
                }

            logger.info(
                "Apollo data fetched successfully",
                company_domain=job_context.company_domain,
                data_keys=list(apollo_data.keys())
                if isinstance(apollo_data, dict)
                else "not_dict",
            )

            # Step 2: Store raw data to S3
            s3_key = await self._store_raw_data(apollo_data, job_context)

            # Step 3: Process the data using ApolloCompanyDataProcessor
            if not self.processor:
                raise Exception("ApolloCompanyDataProcessor not initialized")

            processing_result = await self.processor.process(
                payload=apollo_data, s3_raw_data_key=s3_key, job_context=job_context
            )

            if not processing_result["success"]:
                return {
                    "success": False,
                    "error": f"Data processing failed: {processing_result['error']}",
                    "processing_time": time.time() - start_time,
                }

            # Step 4: Update S3 data to include processed data
            await self._update_s3_data(
                apollo_data, processing_result, job_context, s3_key
            )

            # Step 5: Prepare return result
            return_result = {
                "success": True,
                "status": "completed",
                "s3_key": s3_key,
                "processing_time": time.time() - start_time,
            }

            logger.info(
                "Apollo company data processing completed successfully",
                company_domain=job_context.company_domain,
                processing_time=return_result["processing_time"],
                s3_key=s3_key,
            )

            return return_result

        except Exception as e:
            error_msg = f"Apollo company processing task failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

    async def _fetch_apollo_data(
        self, job_context: ApolloCompanyJobContext
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch Apollo data for the company.

        Args:
            job_context: Apollo company job context

        Returns:
            Apollo data dictionary or None if failed
        """
        try:
            if not self.apollo_service:
                logger.error("Apollo service not initialized")
                return None

            # Fetch Apollo data using the service
            apollo_result = await self.apollo_service.fetch_company_data(
                domain=job_context.company_domain,
                company_name=job_context.data.get("company_name", ""),
                org_id=job_context.org_id,
            )

            if not apollo_result or not apollo_result.get("success"):
                logger.error(
                    f"Apollo fetch failed: {apollo_result.get('error', 'Unknown error')}"
                )
                return None

            return apollo_result.get("data", {})

        except Exception as e:
            logger.error(f"Error fetching Apollo data: {e}", exc_info=True)
            return None

    def _extract_job_context(self, job_data: Dict[str, Any]) -> ApolloCompanyJobContext:
        """Extract job context from job data."""
        return ApolloCompanyJobContext(
            job_id=job_data.get("job_id", ""),
            company_id=job_data["company_id"],
            org_id=job_data["org_id"],
            company_domain=job_data["company_domain"],
            data=job_data,
            apollo_id=job_data.get("apollo_id"),
        )

    def _validate_job_context(
        self, job_context: ApolloCompanyJobContext
    ) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_domain:
            return "Missing company_domain"
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        return None

    async def _store_raw_data(
        self, raw_data: Any, job_context: ApolloCompanyJobContext
    ) -> str:
        """Store raw data to S3."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"apollo_company_data/{job_context.company_domain}_{timestamp}.json"

        s3_data = {
            "company_domain": job_context.company_domain,
            "org_id": job_context.org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        if self.s3_storage:
            success = await self.s3_storage.put_object(s3_key, s3_data)
            if success:
                logger.info(f"Raw Apollo data stored to S3: {s3_key}")
            else:
                logger.error(f"Failed to store raw Apollo data to S3: {s3_key}")
        else:
            logger.warning("S3 storage not available, skipping raw data storage")

        return s3_key

    async def _update_s3_data(
        self,
        raw_data: Any,
        processed_data: Any,
        job_context: ApolloCompanyJobContext,
        s3_key: str,
    ) -> None:
        """Update S3 data to include processed data."""
        try:
            updated_s3_data = {
                "company_domain": job_context.company_domain,
                "org_id": job_context.org_id,
                "raw_data": raw_data,
                "processed_data": processed_data,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            if self.s3_storage:
                success = await self.s3_storage.put_object(s3_key, updated_s3_data)
                if success:
                    logger.info(f"Updated S3 data with processed data: {s3_key}")
                else:
                    logger.error(
                        f"Failed to update S3 data with processed data: {s3_key}"
                    )
            else:
                logger.warning("S3 storage not available, skipping S3 data update")

        except Exception as e:
            logger.error(f"Error updating S3 data: {e}", exc_info=True)


async def process_apollo_company_data_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for Apollo company data processing.

    This function is called by the job queue system and handles:
    - Task initialization
    - API data fetching
    - Data processing
    - Result storage
    - Error handling

    Args:
        payload: Job payload with company domain and metadata

    Returns:
        Processing result with status and data
    """
    task = ApolloCompanyProcessorTask()

    try:
        await task.initialize()
        return await task.process(payload)
    finally:
        await task.cleanup()
