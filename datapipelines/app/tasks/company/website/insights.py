"""
Website Insights Task for TractionX Data Pipeline Service.

This task generates insights from website content with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

import json
import re
from typing import Any, Dict, List

from app.clients.ai.together import get_together_client
from app.clients.brightdata import get_brightdata_client
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields
from bs4 import BeautifulSoup


class WebsiteInsightsTask(BaseTask):
    """Task for generating insights from website content."""

    def __init__(self):
        super().__init__("generate_website_insights_task")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for website insights generation."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["url"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute website insights generation.

        Args:
            payload: Job payload with URL and metadata

        Returns:
            Generation result with insights data
        """
        data = extract_job_data(payload)
        url = data["url"]
        company_domain = data.get("company_domain", "")

        # Step 1: Fetch HTML via BrightData
        html_content = await self._fetch_html_with_brightdata(url)
        if not html_content:
            raise Exception(f"Failed to fetch HTML for URL: {url}")

        # Step 2: Extract visible text
        visible_text = self._extract_visible_text(html_content)
        if not visible_text.strip():
            raise Exception(f"No visible text extracted from URL: {url}")

        # Step 3: Generate insights using LLM
        insights = await self._generate_insights_for_page(url, visible_text)

        return {
            "url": url,
            "company_domain": company_domain,
            "insights": insights,
            "text_length": len(visible_text),
            "processing_status": "completed",
        }

    async def _fetch_html_with_brightdata(self, url: str) -> str:
        """Fetch HTML content using BrightData."""
        brightdata_client = await get_brightdata_client()
        response = await brightdata_client.fetch_webpage(url)

        if (
            not response
            or not response.get("success")
            or not response.get("data", {}).get("html")
        ):
            raise Exception(f"BrightData returned no HTML for URL: {url}")

        return response["data"]["html"]

    def _extract_visible_text(self, html: str) -> str:
        """Extract visible text from HTML content."""
        try:
            soup = BeautifulSoup(html, "html.parser")

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Get text and clean it up
            text = soup.get_text()

            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = " ".join(chunk for chunk in chunks if chunk)

            return text

        except Exception as e:
            self.logger.error(f"Error extracting text from HTML: {e}")
            raise

    def _chunk_text_safely(
        self, text: str, max_chars: int = 8000, min_chunk_chars: int = 1000
    ) -> List[str]:
        """Split text into chunks for LLM processing."""
        if len(text) <= max_chars:
            return [text]

        chunks = []
        current_chunk = ""

        # Split by sentences to avoid breaking mid-sentence
        sentences = re.split(r"[.!?]+", text)

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # If adding this sentence would exceed max_chars, start a new chunk
            if (
                len(current_chunk) + len(sentence) > max_chars
                and len(current_chunk) >= min_chunk_chars
            ):
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence

        # Add the last chunk if it has content
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _generate_page_insights_prompt(self, url: str, text: str) -> str:
        """Generate prompt for LLM insights generation."""
        return f"""
        Analyze the following website content and extract investment-relevant insights.
        
        URL: {url}
        
        Website Content:
        {text[:4000]}  # Limit to first 4000 chars for prompt
        
        Please provide insights in the following JSON format:
        {{
            "company_overview": {{
                "business_model": "Brief description of how the company makes money",
                "target_market": "Who they serve",
                "value_proposition": "What problem they solve"
            }},
            "product_services": {{
                "main_products": ["List of main products/services"],
                "key_features": ["Key features or capabilities"],
                "technology_stack": ["Technologies mentioned"]
            }},
            "market_positioning": {{
                "industry": "Primary industry/sector",
                "competitive_advantages": ["List of competitive advantages"],
                "partnerships": ["Key partnerships mentioned"]
            }},
            "growth_indicators": {{
                "expansion_signals": ["Signs of growth or expansion"],
                "customer_indicators": ["Customer-related signals"],
                "team_growth": ["Team or hiring signals"]
            }},
            "investment_signals": {{
                "funding_indicators": ["Signs of funding or investment"],
                "scalability": "Assessment of scalability potential",
                "risk_factors": ["Potential risk factors"]
            }}
        }}
        
        Focus on actionable insights for investors. Be concise but comprehensive.
        """

    def _fix_and_parse_json(self, raw_text: str) -> Dict[str, Any]:
        """Fix and parse JSON response from LLM."""
        try:
            # Try to extract JSON from the response
            json_match = re.search(r"\{.*\}", raw_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # If no JSON found, return a basic structure
                return {
                    "error": "Could not parse JSON response",
                    "raw_response": raw_text[:500],
                }
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON parsing failed: {e}")
            return {
                "error": f"JSON parsing failed: {str(e)}",
                "raw_response": raw_text[:500],
            }

    async def _generate_insights_for_page(self, url: str, text: str) -> Dict[str, Any]:
        """Generate insights using LLM."""
        try:
            # Chunk text if too long
            text_chunks = self._chunk_text_safely(text)

            all_insights = []
            for i, chunk in enumerate(text_chunks):
                prompt = self._generate_page_insights_prompt(url, chunk)

                # Get LLM response
                together_client = await get_together_client()
                messages = [{"role": "user", "content": prompt}]
                response = await together_client.create_chat_completion(messages)

                if (
                    response
                    and response.get("success")
                    and response.get("data", {}).get("content")
                ):
                    insights = self._fix_and_parse_json(response["data"]["content"])
                    all_insights.append(insights)
                else:
                    self.logger.warning(f"No response from LLM for chunk {i}")

            # Combine insights from all chunks
            if len(all_insights) == 1:
                return all_insights[0]
            else:
                # Merge insights from multiple chunks
                merged_insights = self._merge_insights(all_insights)
                return merged_insights

        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return {"error": f"Insights generation failed: {str(e)}"}

    def _merge_insights(self, insights_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge insights from multiple text chunks."""
        merged = {
            "company_overview": {},
            "product_services": {},
            "market_positioning": {},
            "growth_indicators": {},
            "investment_signals": {},
        }

        for insights in insights_list:
            if "error" in insights:
                continue

            for category, data in insights.items():
                if isinstance(data, dict):
                    if category not in merged:
                        merged[category] = {}

                    for key, value in data.items():
                        if key not in merged[category]:
                            merged[category][key] = value
                        elif isinstance(value, list) and isinstance(
                            merged[category][key], list
                        ):
                            # Merge lists, avoiding duplicates
                            merged[category][key].extend([
                                v for v in value if v not in merged[category][key]
                            ])

        return merged


# Task instance for job queue
_website_insights_task = WebsiteInsightsTask()


async def generate_website_insights_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for website insights generation.

    Args:
        payload: Job payload containing URL and metadata

    Returns:
        Processing result with status and data
    """
    return await _website_insights_task.execute(payload)
