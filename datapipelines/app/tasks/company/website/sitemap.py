"""
Sitemap Generator Task for TractionX Data Pipeline Service.

This task handles sitemap generation with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

from typing import Any, Dict

from app.services.sitemap_generator import get_sitemap_generator
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields


class SitemapGeneratorTask(BaseTask):
    """Task for generating sitemaps for company websites."""

    def __init__(self):
        super().__init__("generate_sitemap_task")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for sitemap generation."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["domain"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute sitemap generation.

        Args:
            payload: Job payload with domain and optional metadata

        Returns:
            Generation result with sitemap URL and metadata
        """
        data = extract_job_data(payload)
        domain = data["domain"]
        page_title = data.get("page_title", "")
        company_domain = data.get("company_domain", domain)

        # Get sitemap generator service
        sitemap_generator = await get_sitemap_generator()

        # Generate sitemap
        result = await sitemap_generator.generate_sitemap(domain, page_title)

        return {
            "sitemap_url": result.get("sitemap_url"),
            "domain": domain,
            "company_domain": company_domain,
            "page_count": result.get("page_count", 0),
            "metadata": {
                "status": result.get("status"),
                "processing_time": result.get("processing_time"),
            },
        }


# Task instance for job queue
_sitemap_generator_task = SitemapGeneratorTask()


async def generate_sitemap_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for sitemap generation.

    Args:
        payload: Job payload containing domain and optional metadata

    Returns:
        Processing result with status and data
    """
    return await _sitemap_generator_task.execute(payload)
