"""
Base Task Infrastructure for TractionX Data Pipeline Service.

This module provides the foundation for all tasks with proper separation of concerns.
Tasks focus purely on data processing logic while delegating service interactions
and job lifecycle management to appropriate layers.
"""

import time
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs.logging import get_logger, get_org_context, set_org_context
from app.storage.s3_storage import S3Storage


class TaskResult:
    """Standardized task result structure."""

    def __init__(
        self,
        success: bool,
        data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        processing_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.success = success
        self.data = data or {}
        self.error = error
        self.processing_time = processing_time
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for job queue."""
        return {
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "processing_time": self.processing_time,
            "metadata": self.metadata,
        }


class BaseTask(ABC):
    """
    Abstract base class for all tasks.

    Provides common functionality:
    - Timing and logging
    - Error handling and result formatting
    - S3 storage for audit trails
    - Input validation
    - Organization context management
    """

    def __init__(self, task_name: str):
        self.task_name = task_name
        # Initialize logger without org_id - will be set during execution
        self.logger = get_logger(f"tasks.{task_name}")
        self.s3_storage: Optional[S3Storage] = None

    async def initialize(self) -> None:
        """Initialize task dependencies."""
        if not self.s3_storage:
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

    async def cleanup(self) -> None:
        """Clean up task resources."""
        if self.s3_storage:
            await self.s3_storage.cleanup()

    async def execute(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main task execution entry point.

        Args:
            payload: Job payload containing task-specific data

        Returns:
            Standardized task result
        """
        start_time = time.time()

        try:
            # Set organization context from payload for logging
            org_id = self._extract_org_id(payload)
            if org_id:
                set_org_context(org_id)
                # Re-bind logger with org context
                self.logger = get_logger(f"tasks.{self.task_name}").bind(org_id=org_id)

            await self.initialize()

            # Validate input
            validation_error = self.validate_input(payload)
            if validation_error:
                return TaskResult(
                    success=False,
                    error=validation_error,
                    processing_time=time.time() - start_time,
                ).to_dict()

            # Execute task-specific logic
            result = await self.process(payload)

            # Store audit trail
            await self.store_audit_trail(payload, result, start_time)

            return TaskResult(
                success=True,
                data=result,
                processing_time=time.time() - start_time,
                metadata=self.get_metadata(payload),
            ).to_dict()

        except Exception as e:
            error_msg = f"Task execution failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error audit trail
            await self.store_error_audit_trail(payload, error_msg, start_time)

            return TaskResult(
                success=False,
                error=error_msg,
                processing_time=time.time() - start_time,
                metadata=self.get_metadata(payload),
            ).to_dict()

        finally:
            await self.cleanup()

    def _extract_org_id(self, payload: Dict[str, Any]) -> Optional[str]:
        """
        Extract organization ID from payload.

        This follows the industry standard pattern of extracting tenant context
        from the request/job payload and propagating it through the execution.

        Args:
            payload: Job payload

        Returns:
            Organization ID if found, None otherwise
        """
        # Try multiple common locations for org_id
        org_id = (
            payload.get("org_id")
            or payload.get("organization_id")
            or payload.get("tenant_id")
            or payload.get("job_args", {}).get("org_id")
            or payload.get("job_args", {}).get("organization_id")
            or payload.get("job_args", {}).get("tenant_id")
        )

        if org_id:
            self.logger.debug("Extracted org_id from payload", org_id=org_id)

        return org_id

    @abstractmethod
    def validate_input(self, payload: Dict[str, Any]) -> Optional[str]:
        """
        Validate task input.

        Args:
            payload: Job payload

        Returns:
            Error message if validation fails, None if valid
        """
        pass

    @abstractmethod
    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute task-specific processing logic.

        Args:
            payload: Validated job payload

        Returns:
            Processing result data
        """
        pass

    def get_metadata(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from payload for audit trail."""
        return {
            "task_name": self.task_name,
            "org_id": get_org_context(),
            "job_id": payload.get("job_id"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    async def store_audit_trail(
        self, payload: Dict[str, Any], result: Dict[str, Any], start_time: float
    ) -> None:
        """Store successful execution audit trail to S3."""
        if not self.s3_storage:
            return

        try:
            audit_data = {
                "task_name": self.task_name,
                "org_id": get_org_context(),
                "job_id": payload.get("job_id"),
                "status": "success",
                "payload": payload,
                "result": result,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "processing_time": time.time() - start_time,
            }

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            s3_key = f"task_audit/{self.task_name}/{timestamp}_success.json"

            await self.s3_storage.put_object(s3_key, audit_data)

        except Exception as e:
            self.logger.warning(f"Failed to store audit trail: {e}")

    async def store_error_audit_trail(
        self, payload: Dict[str, Any], error: str, start_time: float
    ) -> None:
        """Store error execution audit trail to S3."""
        if not self.s3_storage:
            return

        try:
            audit_data = {
                "task_name": self.task_name,
                "org_id": get_org_context(),
                "job_id": payload.get("job_id"),
                "status": "error",
                "payload": payload,
                "error": error,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "processing_time": time.time() - start_time,
            }

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            s3_key = f"task_audit/{self.task_name}/{timestamp}_error.json"

            await self.s3_storage.put_object(s3_key, audit_data)

        except Exception as e:
            self.logger.warning(f"Failed to store error audit trail: {e}")


def extract_job_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract job data from payload structure.

    Handles both direct payload and nested job_args structure.
    """
    if "job_args" in payload:
        return payload["job_args"]
    return payload


def validate_required_fields(
    payload: Dict[str, Any], required_fields: list
) -> Optional[str]:
    """
    Validate that required fields are present in payload.

    Args:
        payload: Job payload
        required_fields: List of required field names

    Returns:
        Error message if validation fails, None if valid
    """
    missing_fields = [field for field in required_fields if field not in payload]

    if missing_fields:
        return f"Missing required fields: {', '.join(missing_fields)}"

    return None
