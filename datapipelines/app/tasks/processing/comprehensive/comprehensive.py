"""
Comprehensive Company Data Processing Task for TractionX Data Pipeline Service.

This task handles comprehensive company data processing with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

from datetime import datetime, timezone
from typing import Any, Dict
from uuid import uuid4

from app.models.job_tracking import JobType
from app.tasks.base import BaseTask, extract_job_data, validate_required_fields
from app.utils.jobs import JobBuilder


class ComprehensiveEnrichmentTask(BaseTask):
    """Task for comprehensive company data enrichment."""

    def __init__(self):
        super().__init__("enrich_company_data_comprehensive")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for comprehensive enrichment."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["company_domain", "org_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute comprehensive company data enrichment.

        Args:
            payload: Job payload with company metadata

        Returns:
            Enrichment result with job orchestration details
        """
        data = extract_job_data(payload)
        company_domain = data["company_domain"]
        org_id = data["org_id"]
        company_description = data.get("company_description", "")

        # Create unique job group ID for this enrichment session
        job_group_id = f"company_enrichment_{company_domain}_{uuid4().hex[:8]}"

        # Create parent coordinator job
        parent_job = (
            await JobBuilder()
            .for_entity("company", company_domain)
            .with_job_type(JobType.COMPANY_ENRICH_COMPREHENSIVE)
            .with_payload(
                company_domain=company_domain,
                org_id=org_id,
                company_description=company_description,
                job_group=job_group_id,
            )
            .with_job_group(job_group_id)
            .with_orchestration_pattern("comprehensive_enrichment")
            .create()
        )

        return {
            "job_group_id": job_group_id,
            "parent_job_id": str(parent_job.id),
            "company_domain": company_domain,
            "org_id": org_id,
            "orchestration_status": "initiated",
            "processing_status": "completed",
        }


class ComprehensiveMergeTask(BaseTask):
    """Task for merging comprehensive enrichment results."""

    def __init__(self):
        super().__init__("merge_comprehensive_enrichment_results")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for comprehensive merge."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["job_group_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute comprehensive enrichment results merge.

        Args:
            payload: Job payload with job group ID

        Returns:
            Merge result with combined data
        """
        data = extract_job_data(payload)
        job_group_id = data["job_group_id"]

        # Merge all enrichment results
        merged_data = await self._merge_enrichment_results(job_group_id)

        return {
            "job_group_id": job_group_id,
            "merged_data": merged_data,
            "merge_status": "completed",
            "processing_status": "completed",
        }

    async def _merge_enrichment_results(self, job_group_id: str) -> Dict[str, Any]:
        """Merge all enrichment results for a job group."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "company_domain": "example.com",
            "org_id": "org123",
            "enrichment_sources": ["crunchbase", "linkedin", "website"],
            "merged_at": datetime.now(timezone.utc).isoformat(),
        }


class CoreCompanyDataMergeTask(BaseTask):
    """Task for merging core company data results."""

    def __init__(self):
        super().__init__("merge_core_company_data_results")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for core data merge."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["job_group_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute core company data merge.

        Args:
            payload: Job payload with job group ID

        Returns:
            Merge result with core company data
        """
        data = extract_job_data(payload)
        job_group_id = data["job_group_id"]

        # Merge core company data
        core_data = await self._merge_core_data(job_group_id)

        return {
            "job_group_id": job_group_id,
            "core_data": core_data,
            "merge_status": "completed",
            "processing_status": "completed",
        }

    async def _merge_core_data(self, job_group_id: str) -> Dict[str, Any]:
        """Merge core company data for a job group."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "company_name": "Example Company",
            "domain": "example.com",
            "industry": "Technology",
            "founded_year": 2020,
            "employee_count": 100,
        }


class WebsiteInsightsMergeTask(BaseTask):
    """Task for merging website insights results."""

    def __init__(self):
        super().__init__("merge_website_insights_results")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for website insights merge."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["job_group_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute website insights merge.

        Args:
            payload: Job payload with job group ID

        Returns:
            Merge result with website insights
        """
        data = extract_job_data(payload)
        job_group_id = data["job_group_id"]

        # Merge website insights
        insights_data = await self._merge_website_insights(job_group_id)

        return {
            "job_group_id": job_group_id,
            "insights_data": insights_data,
            "merge_status": "completed",
            "processing_status": "completed",
        }

    async def _merge_website_insights(self, job_group_id: str) -> Dict[str, Any]:
        """Merge website insights for a job group."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "total_pages": 10,
            "insights_summary": {
                "business_model": "SaaS",
                "target_market": "Enterprise",
                "key_features": ["Feature 1", "Feature 2"],
            },
            "merged_at": datetime.now(timezone.utc).isoformat(),
        }


class ComprehensiveDataProcessorTask(BaseTask):
    """Task for processing comprehensive enrichment data."""

    def __init__(self):
        super().__init__("process_comprehensive_enrichment_data_task")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for comprehensive data processing."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["company_domain", "org_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute comprehensive enrichment data processing.

        Args:
            payload: Job payload with company metadata

        Returns:
            Processing result with comprehensive data
        """
        data = extract_job_data(payload)
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Process comprehensive enrichment data
        processed_data = await self._process_comprehensive_data(company_domain, org_id)

        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "comprehensive_data": processed_data,
            "processing_status": "completed",
        }

    async def _process_comprehensive_data(
        self, company_domain: str, org_id: str
    ) -> Dict[str, Any]:
        """Process comprehensive enrichment data."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "enrichment_sources": ["crunchbase", "linkedin", "website"],
            "data_quality_score": 0.85,
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }


# Task instances for job queue
_comprehensive_enrichment_task = ComprehensiveEnrichmentTask()
_comprehensive_merge_task = ComprehensiveMergeTask()
_core_data_merge_task = CoreCompanyDataMergeTask()
_website_insights_merge_task = WebsiteInsightsMergeTask()
_comprehensive_data_processor_task = ComprehensiveDataProcessorTask()


async def enrich_company_data_comprehensive(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for comprehensive company data enrichment.

    Args:
        payload: Job payload containing company metadata

    Returns:
        Processing result with status and data
    """
    return await _comprehensive_enrichment_task.execute(payload)


async def merge_comprehensive_enrichment_results(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Main task function for merging comprehensive enrichment results.

    Args:
        payload: Job payload containing job group ID

    Returns:
        Processing result with status and data
    """
    return await _comprehensive_merge_task.execute(payload)


async def merge_core_company_data_results(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for merging core company data results.

    Args:
        payload: Job payload containing job group ID

    Returns:
        Processing result with status and data
    """
    return await _core_data_merge_task.execute(payload)


async def merge_website_insights_results(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for merging website insights results.

    Args:
        payload: Job payload containing job group ID

    Returns:
        Processing result with status and data
    """
    return await _website_insights_merge_task.execute(payload)


async def process_comprehensive_enrichment_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Main task function for processing comprehensive enrichment data.

    Args:
        payload: Job payload containing company metadata

    Returns:
        Processing result with status and data
    """
    return await _comprehensive_data_processor_task.execute(payload)
