"""
ETL Data Processing Task for TractionX Data Pipeline Service.

This task handles ETL data processing with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

from datetime import datetime, timezone
from typing import Any, Dict

from app.tasks.base import BaseTask, extract_job_data, validate_required_fields


class ETLMergeTask(BaseTask):
    """Task for merging enrichment data through ETL process."""

    def __init__(self):
        super().__init__("merge_enrichment_data")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for ETL merge."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["company_domain", "org_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute ETL data merge.

        Args:
            payload: Job payload with company metadata

        Returns:
            Merge result with ETL processed data
        """
        data = extract_job_data(payload)
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Perform ETL merge
        merged_data = await self._merge_enrichment_data(company_domain, org_id)

        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "merged_data": merged_data,
            "etl_status": "completed",
            "processing_status": "completed",
        }

    async def _merge_enrichment_data(
        self, company_domain: str, org_id: str
    ) -> Dict[str, Any]:
        """Merge enrichment data through ETL process."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "enrichment_sources": ["crunchbase", "linkedin", "apollo"],
            "data_quality_score": 0.9,
            "merged_at": datetime.now(timezone.utc).isoformat(),
        }


# Task instance for job queue
_etl_merge_task = ETLMergeTask()


async def merge_enrichment_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for ETL data merge.

    Args:
        payload: Job payload containing company metadata

    Returns:
        Processing result with status and data
    """
    return await _etl_merge_task.execute(payload)
