"""
News Aggregation Task for TractionX Data Pipeline Service.

This task handles news data aggregation with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List

from app.tasks.base import BaseTask, extract_job_data, validate_required_fields


class NewsAggregationTask(BaseTask):
    """Task for aggregating news data."""

    def __init__(self):
        super().__init__("aggregate_news_data")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for news aggregation."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["company_domain", "org_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute news data aggregation.

        Args:
            payload: Job payload with company metadata

        Returns:
            Aggregation result with news data
        """
        data = extract_job_data(payload)
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Aggregate news data
        news_data = await self._aggregate_news_data(company_domain, org_id)

        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "news_data": news_data,
            "aggregation_status": "completed",
            "processing_status": "completed",
        }

    async def _aggregate_news_data(
        self, company_domain: str, org_id: str
    ) -> List[Dict[str, Any]]:
        """Aggregate news data for the company."""
        # This would be implemented by a service
        # For now, return a placeholder
        return [
            {
                "title": f"News about {company_domain}",
                "url": f"https://example.com/news/{company_domain}",
                "published_date": datetime.now(timezone.utc).isoformat(),
                "source": "Example News",
                "summary": f"Latest news about {company_domain}",
            }
        ]


# Task instance for job queue
_news_aggregation_task = NewsAggregationTask()


async def aggregate_news_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for news data aggregation.

    Args:
        payload: Job payload containing company metadata

    Returns:
        Processing result with status and data
    """
    return await _news_aggregation_task.execute(payload)
