"""
Sitemap Generator Task for TractionX Data Pipeline Service.

This task handles the complete sitemap extraction pipeline:
1. BrightData Web Unlocker for webpage fetching
2. HTML cleaning and text extraction
3. LLM-based sitemap analysis
4. Result storage and validation
5. Integration with barrier system for website insights triggering
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.queueing.factory import create_redis_backend
from app.queueing.interfaces import JobPriority
from app.queueing.service import QueueServiceV2
from app.services.sitemap_generator import get_sitemap_generator
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)

# Global V2 queue service instance
_v2_queue_service = None


async def get_v2_queue_service() -> QueueServiceV2:
    """Get or create the global V2 queue service instance."""
    global _v2_queue_service

    if _v2_queue_service is None:
        # Create enhanced backend for queue operations
        backend = await create_redis_backend(enhanced=True)

        _v2_queue_service = QueueServiceV2(backend)
        await _v2_queue_service.initialize()

        logger.info("Global V2 queue service initialized for sitemap generator")

    return _v2_queue_service


async def generate_sitemap_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for sitemap generation.

    This function is called by the job queue system and handles:
    - Input validation
    - Service initialization
    - Sitemap generation
    - Result storage
    - Barrier integration for website insights triggering

    Args:
        payload: Job payload containing domain and optional metadata

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info("Starting sitemap generation task", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "domain" not in data:
            return {
                "success": False,
                "error": "Missing required field: domain",
                "processing_time": time.time() - start_time,
            }

        domain = data["domain"]
        page_title = data.get("page_title", "")
        org_id = data.get("org_id", "unknown")
        barrier_group_id = data.get("barrier_group_id", "")
        company_domain = data.get("company_domain", "")

        # Check if this is part of a barrier system
        is_barrier_task = bool(barrier_group_id)

        # Get sitemap generator service
        try:
            logger.info("Getting sitemap generator service")
            sitemap_generator = await get_sitemap_generator()
            logger.info("Sitemap generator service obtained successfully")
        except Exception as e:
            error_msg = f"Failed to get sitemap generator service: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Generate sitemap
        try:
            logger.info("Starting sitemap generation")
            result = await sitemap_generator.generate_sitemap(
                domain=domain,
                page_title=page_title,
                org_id=org_id,
            )
            logger.info("Sitemap generation completed")
        except Exception as e:
            error_msg = f"Sitemap generation failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Store result to S3 for audit
        s3_storage = S3Storage()
        await s3_storage.initialize()

        s3_key = f"sitemap_generator_results/{domain}.json"

        # Prepare data for S3 storage
        s3_data = {
            "input": {
                "domain": domain,
                "page_title": page_title,
                "org_id": org_id,
                "barrier_group_id": barrier_group_id,
            },
            "output": result,
            "processing_time": time.time() - start_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await s3_storage.put_object(s3_key, s3_data)

        if success:
            logger.info(f"Successfully stored result to S3: {s3_key}")
            logger.info("Sitemap Generation Summary:")
            logger.info(f"  - Input domain: {domain}")
            logger.info(f"  - Output success: {result.get('success', False)}")
            logger.info(f"  - JS rendered app: {result.get('js_rendered_app', False)}")
            logger.info(f"  - URLs found: {len(result.get('urls', []))}")
            logger.info(f"  - Processing time: {time.time() - start_time:.2f}s")
            logger.info(f"  - Error message: {result.get('error_message', 'None')}")
            logger.info(f"  - Barrier group ID: {barrier_group_id}")
        else:
            logger.error(f"Failed to store result to S3: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - include URLs for completion handler
        return_result = {
            "success": result.get("success", False),
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
            "data": {
                "urls": result.get("urls", []),
                "js_rendered_app": result.get("js_rendered_app", False),
            },
        }

        # Log the return result
        logger.info("Returning result:")
        logger.info(f"  - Success: {return_result['success']}")
        logger.info(f"  - JS rendered app: {result.get('js_rendered_app', False)}")
        logger.info(f"  - URLs found: {len(result.get('urls', []))}")
        logger.info(f"  - Processing time: {return_result['processing_time']:.2f}s")
        logger.info(f"  - S3 Key: {s3_key}")

        # Handle website insights triggering based on barrier system
        if is_barrier_task:
            await _handle_barrier_website_insights(
                result=result,
                org_id=org_id,
                barrier_group_id=barrier_group_id,
                company_domain=company_domain,
            )
        else:
            # Legacy standalone mode - trigger directly
            await _handle_standalone_website_insights(
                result=result,
                org_id=org_id,
                domain=domain,
            )

        return return_result

    except Exception as e:
        error_msg = f"Sitemap generator task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def _handle_barrier_website_insights(
    result: Dict[str, Any],
    org_id: str,
    barrier_group_id: str,
    company_domain: str,
) -> None:
    """
    Handle website insights triggering for barrier-based sitemap generation.

    This function is called when sitemap generation is part of a barrier system.
    The actual website insights tasks will be triggered by the barrier completion
    handler in the comprehensive enrichment task.
    """
    try:
        urls = result.get("urls", [])
        logger.info(
            f"Barrier sitemap generation completed with {len(urls)} URLs",
            barrier_group_id=barrier_group_id,
            company_domain=company_domain,
            url_count=len(urls),
        )

        # Log URLs for debugging
        if urls:
            logger.info(
                f"URLs found for website insights: {urls[:5]}..."
            )  # Log first 5 URLs

        # Note: Website insights tasks will be triggered by the barrier completion handler
        # in the comprehensive enrichment task, not here.

    except Exception as e:
        logger.error(f"Error in barrier website insights handling: {e}")


async def _handle_standalone_website_insights(
    result: Dict[str, Any],
    org_id: str,
    domain: str,
) -> None:
    """
    Handle website insights triggering for standalone sitemap generation.

    This function is called when sitemap generation is not part of a barrier system.
    It directly triggers website insights tasks for each URL.
    """
    try:
        urls = result.get("urls", [])
        if not urls:
            logger.info("No URLs found for website insights generation")
            return

        logger.info(f"Triggering website insights for {len(urls)} URLs")

        # Get queue service
        queue_service = await get_v2_queue_service()

        # Trigger website insights task for each URL
        for i, url in enumerate(urls):
            insight_task_payload = {
                "url": url,
                "company_domain": domain,
                "org_id": org_id,
                "url_index": i,
            }

            await queue_service.enqueue_job(
                job_func="generate_website_insights",
                job_args=insight_task_payload,
                metadata={
                    "task_name": f"generate_website_insights_{i}",
                    "entity_type": "company",
                    "dependent_on": "generate_sitemap_task",
                    "url": url,
                },
                priority=JobPriority.NORMAL,
            )

            logger.info(
                f"Enqueued website insights task for URL {i + 1}/{len(urls)}: {url}"
            )

        logger.info(f"Successfully triggered {len(urls)} website insights tasks")

    except Exception as e:
        logger.error(f"Error in standalone website insights handling: {e}")


async def trigger_website_insights_task(
    url: str, org_id: str, barrier_group_id: Optional[str] = None
) -> None:
    """
    Legacy function for triggering website insights task.

    This function is kept for backward compatibility but is deprecated.
    Use the barrier system or _handle_standalone_website_insights instead.
    """
    logger.warning(
        "trigger_website_insights_task is deprecated. "
        "Use barrier system or _handle_standalone_website_insights instead."
    )

    try:
        # Get queue service
        queue_service = await get_v2_queue_service()

        # Prepare task payload
        insight_task_payload = {
            "url": url,
            "org_id": org_id,
        }

        if barrier_group_id:
            insight_task_payload["barrier_group_id"] = barrier_group_id

        # Enqueue the task
        await queue_service.enqueue_job(
            job_func="generate_website_insights",
            job_args=insight_task_payload,
            metadata={
                "task_name": "generate_website_insights",
                "entity_type": "company",
                "dependent_on": "generate_sitemap_task",
                "url": url,
            },
            priority=JobPriority.NORMAL,
        )

        logger.info(f"Legacy website insights task triggered for URL: {url}")

    except Exception as e:
        logger.error(f"Error triggering legacy website insights task: {e}")
