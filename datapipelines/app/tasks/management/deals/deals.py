"""
Deals Management Task for TractionX Data Pipeline Service.

This task handles deals management with clean separation of concerns.
Focuses purely on data processing logic while delegating service interactions.
"""

from datetime import datetime, timezone
from typing import Any, Dict

from app.tasks.base import BaseTask, extract_job_data, validate_required_fields


class DealsUpdateTask(BaseTask):
    """Task for updating deals with company data."""

    def __init__(self):
        super().__init__("update_deals_with_company_data_task")

    def validate_input(self, payload: Dict[str, Any]) -> str | None:
        """Validate required fields for deals update."""
        data = extract_job_data(payload)
        return validate_required_fields(data, ["company_domain", "org_id"])

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute deals update with company data.

        Args:
            payload: Job payload with company metadata

        Returns:
            Update result with deals update details
        """
        data = extract_job_data(payload)
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Update deals with company data
        update_data = await self._update_deals_with_company_data(company_domain, org_id)

        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "update_data": update_data,
            "update_time": datetime.now(timezone.utc).isoformat(),
            "processing_status": "completed",
        }

    async def _update_deals_with_company_data(
        self, company_domain: str, org_id: str
    ) -> Dict[str, Any]:
        """Update deals with company data."""
        # This would be implemented by a service
        # For now, return a placeholder
        return {
            "updated_deals": 2,
            "update_status": "completed",
            "enrichment_data_applied": {
                "company_info": True,
                "founder_data": True,
                "website_insights": False,
            },
        }


# Task instance for job queue
_deals_update_task = DealsUpdateTask()


async def update_deals_with_company_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Main task function for updating deals with company data.

    Args:
        payload: Job payload containing company metadata

    Returns:
        Processing result with status and data
    """
    return await _deals_update_task.execute(payload)
