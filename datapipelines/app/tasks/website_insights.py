"""
Website Page-Level Insight Summarizer for Deal Intelligence.

This task extracts high-signal, structured insights from startup websites
for investors, analysts, and deal teams to evaluate companies faster and more accurately.

Each job processes one URL and is part of the larger Website Intelligence Pipeline.
"""

import json
import re
import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from bs4 import BeautifulSoup

from app.clients.ai.together import get_together_client
from app.clients.brightdata import get_brightdata_client
from app.configs import get_logger
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


async def generate_website_insights_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate insights for a specific website URL.

    This task:
    1. Fetches HTML via BrightData Web Unlocker
    2. Extracts and chunks visible text
    3. Generates insights using LLM
    4. Returns structured JSON with investment-relevant insights

    Args:
        payload: Job payload containing URL and metadata

    Returns:
        Processing result with insights data
    """
    start_time = time.time()

    try:
        logger.info("Starting website insights generation", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate required input
        if "url" not in data:
            return {
                "success": False,
                "error": "Missing required field: url",
                "processing_time": time.time() - start_time,
            }

        url = data["url"]
        company_domain = data.get("company_domain", "")
        org_id = data.get("org_id", "unknown")
        barrier_group_id = data.get("barrier_group_id", "")

        logger.info(f"Processing URL: {url}")

        # Step 1: Fetch HTML via BrightData
        try:
            html_content = await _fetch_html_with_brightdata(url)
            if not html_content:
                return {
                    "success": False,
                    "error": f"Failed to fetch HTML for URL: {url}",
                    "processing_time": time.time() - start_time,
                }
        except Exception as e:
            error_msg = f"BrightData fetch failed for {url}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }
        logger.info(f"HTML content before extraction: {html_content[:1000]}")
        logger.info(f"HTML content length: {len(html_content)}")
        # Step 2: Extract visible text
        try:
            visible_text = extract_visible_text(html_content)
            if not visible_text.strip():
                return {
                    "success": False,
                    "error": f"No visible text extracted from URL: {url}",
                    "processing_time": time.time() - start_time,
                }
        except Exception as e:
            error_msg = f"Text extraction failed for {url}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }
        logger.info(f"Visible text before insights: {visible_text[:1000]}")
        logger.info(f"Visible text length: {len(visible_text)}")

        # Step 3: Generate insights
        try:
            insights_result = await _generate_insights_for_page(url, visible_text)
        except Exception as e:
            error_msg = f"Insights generation failed for {url}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Step 4: Store result to S3 for audit
        s3_storage = S3Storage()
        await s3_storage.initialize()

        from urllib.parse import urlparse

        parsed_url = urlparse(url)
        path = parsed_url.path.strip("/")

        if not path:
            slug = "homepage"
        else:
            # Replace any remaining slashes with underscores for S3 compatibility
            slug = path.replace("/", "_")

        s3_key = f"website_insights/{company_domain}/{slug}.json"

        s3_data = {
            "input": {
                "url": url,
                "company_domain": company_domain,
                "org_id": org_id,
                "barrier_group_id": barrier_group_id,
            },
            "output": insights_result,
            "processing_time": time.time() - start_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await s3_storage.put_object(s3_key, s3_data)
        if success:
            logger.info(f"Stored insights result to S3: {s3_key}")
        else:
            logger.error(f"Failed to store insights result to S3: {s3_key}")

        await s3_storage.cleanup()

        # Return success result - only return S3 key, not the data
        return {
            "success": True,
            "s3_key": s3_key,
            "barrier_group_id": barrier_group_id,
            "processing_time": time.time() - start_time,
        }

    except Exception as e:
        error_msg = f"Website insights task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def _fetch_html_with_brightdata(url: str) -> Optional[str]:
    """
    Fetch HTML content using BrightData Web Unlocker.

    Args:
        url: URL to fetch

    Returns:
        HTML content as string, or None if failed
    """
    try:
        brightdata_client = await get_brightdata_client()

        logger.info(f"Fetching HTML from BrightData for URL: {url}")
        response = await brightdata_client.fetch_webpage(
            url, render_js=True, format_type="raw"
        )

        if response and response.get("success"):
            return response.get("data", {})
        else:
            logger.error(f"BrightData request failed for {url}: {response}")
            return None

    except Exception as e:
        logger.error(f"BrightData client error for {url}: {e}")
        raise


def extract_visible_text(html: str) -> str:
    """
    Extract visible text from HTML, removing scripts, styles, and other non-content elements.

    Args:
        html: Raw HTML content

    Returns:
        Cleaned visible text
    """
    soup = BeautifulSoup(html, "html.parser")

    # Remove all scripts, styles, head, and noscript tags
    for tag in soup(["script", "style", "head", "noscript"]):
        tag.decompose()

    # Prefer structured sections like <main>, <article>, or big content divs
    main = soup.find("main") or soup.find("article")
    if main:
        text = main.get_text(separator="\n\n")
    else:
        text = soup.get_text(separator="\n\n")

    # Clean up repeated newlines
    return re.sub(r"\n{3,}", "\n\n", text).strip()


def chunk_text_safely(
    text: str, max_chars: int = 8000, min_chunk_chars: int = 1000
) -> List[str]:
    """
    Split text into chunks safely, avoiding breaking inside tag-heavy or meta sections.

    Args:
        text: Text to chunk
        max_chars: Maximum characters per chunk
        min_chunk_chars: Minimum characters per chunk

    Returns:
        List of text chunks
    """
    # Normalize line endings
    text = text.replace("\r\n", "\n").replace("\r", "\n").strip()

    # Split by 2+ newlines or forced hard break points
    blocks = re.split(r'\n{2,}|(?<=[";])\n', text)

    # Fallback: if blocks are too small, just chunk by characters
    if len(blocks) < 3:
        return [text[i : i + max_chars] for i in range(0, len(text), max_chars)]

    chunks, current = [], ""

    for block in blocks:
        block = block.strip()
        if not block:
            continue

        # If block is too large, slice it into safe chunks
        while len(block) > max_chars:
            chunks.append(block[:max_chars])
            block = block[max_chars:]

        # Accumulate into current chunk
        if len(current) + len(block) + 2 > max_chars:
            if len(current) >= min_chunk_chars:
                chunks.append(current.strip())
                current = block
            else:
                # Force join with next block
                current += "\n\n" + block
        else:
            current += "\n\n" + block

    if current.strip():
        chunks.append(current.strip())

    return chunks


def generate_page_insights_prompt(url: str, html2text: str) -> str:
    """
    Generate the prompt for LLM to extract insights from page content.

    Args:
        url: Page URL
        html2text: Extracted text content

    Returns:
        Formatted prompt string
    """
    return f"""You are an expert investment analyst reviewing company websites to extract insights useful for investors, VCs, and deal teams.

            ---

            ### GOAL
            Your job is to analyze the page content below and extract **any relevant structured insights**. This may include product descriptions, GTM, legal names, contact links, investor signals, and more. Each page may contain different types of information.
            You are analyzing a webpage to extract high-signal structured insights relevant to startup investors, venture analysts, and deal teams.

            Each page may contain different forms of information, but your job is to identify anything that helps investors understand what this company does, how it operates, and why it matters. What matters is the signal, not the noise. If you find signals about the same thing, pick the most relevant one.
            ---

            ### Page URL:
            {url}

            ---

            ### Full Page Text:
            \"\"\"
            {html2text.strip()}
            \"\"\"

            ---
            ### YOU MAY INCLUDE (if available):
            Type of business (B2B, B2C, or both)
            Legal or entity name, along with type like LLC, Inc, Pte. Ltd, LLP etc.
            Country or geography of operation
            Products and features
            Target customers or industries
            Stage of the startup
            Products/Services and features
            Team members and their roles
            Founder Socials along with their Role and Name
            Sector of the startup
            Target markets or geographies
            Information on the team, founders, along with their backgrounds, experience, and any other relevant information such as profiles, social media, etc.
            Pricing model or GTM
            Social Media Handles and their type (LinkedIn, Twitter, Facebook, Instagram, etc.)
            Contact/social handles or emails
            Compliance (GDPR, SOC2, etc)
            Any signs of fundraising, 
            Traction, or credibility (e.g. partner logos, backers)
            Any Press/News/Articles/Blogs/Podcasts/Videos/etc.
            Any Partnerships/Alliances/Collaborations/etc.
            Any Awards/Recognitions/etc.
            Any Funding/Investments/etc.
            Any Customers/Users/<USER>
            Any Other relevant information that is not mentioned above

            ---
            ### OUTPUT FORMAT RULES
            Return only the JSON object — no markdown, no explanations, no preamble.
            You can include as many insights as the page reasonably contains.
            Be smart — infer structure even if not explicitly stated.
            Make it concise, clean, and readable. Your answer will be directly parsed into a deal intelligence system.
            ---

            ### Output (JSON only):
            Return a **single JSON object**. No markdown. No prose. Only the JSON.

            Your object **must always include** these fields:

            ```json
            {{
            "source_url": "<page URL>",
            "insights": [ 
                {{
                "label": "<insight category or summary>",
                "value": "<short description>",
                "type": "<optional type e.g. product, legal, contact, team, fundraising, etc>"
                }}
            ]
            }}
```
"""


def fix_and_parse_json(raw_text: str) -> Optional[Dict[str, Any]]:
    """
    Fix common JSON parsing issues and return valid JSON object.

    Args:
        raw_text: Raw text that should contain JSON

    Returns:
        Parsed JSON object or None if parsing failed
    """
    logger.info(f"Raw text before parsing: {raw_text[:1000]}")
    logger.info(f"Raw text length: {len(raw_text)}")
    try:
        return json.loads(raw_text)
    except json.JSONDecodeError as e:
        logger.debug(f"JSON Decode Error: {e}")

        # Try to auto-fix common issues
        fixed_text = raw_text.strip()

        # Remove markdown backticks if present
        if fixed_text.startswith("```json"):
            fixed_text = re.sub(r"^```json", "", fixed_text)
            fixed_text = fixed_text.rstrip("`").strip()
        elif fixed_text.startswith("```"):
            fixed_text = fixed_text.lstrip("`").rstrip("`").strip()

        # Try to add missing end brackets if needed
        if fixed_text.count("{") > fixed_text.count("}"):
            fixed_text += "}"
        if fixed_text.count("[") > fixed_text.count("]"):
            fixed_text += "]"

        # Attempt parse again
        try:
            return json.loads(fixed_text)
        except Exception as e2:
            logger.warning(f"Still invalid JSON after fix: {e2}")
            return None


async def _generate_insights_for_page(url: str, html2text: str) -> Dict[str, Any]:
    """
    Generate insights for a page by chunking text and processing with LLM.

    Args:
        url: Page URL
        html2text: Extracted text content

    Returns:
        Dictionary with source_url and insights list
    """
    chunks = chunk_text_safely(html2text)
    all_insights = []

    # Initialize Together AI client
    client = await get_together_client()

    logger.info(f"Processing {len(chunks)} chunks for URL: {url}")

    for i, chunk in enumerate(chunks):
        logger.info(f"Processing chunk {i + 1}/{len(chunks)} for URL: {url}")

        prompt = generate_page_insights_prompt(url, chunk)

        try:
            # Use Together AI model as specified in PRD
            response = await client.create_chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                temperature=0.1,  # Low temperature for consistent output
            )

            if response["success"]:
                output = response["content"]
                if output:
                    output = output.strip()
                else:
                    output = ""
                data = fix_and_parse_json(output)
            else:
                logger.error(
                    f"LLM call failed: {response.get('error', 'Unknown error')}"
                )
                continue

            if data and "insights" in data:
                all_insights.extend(data["insights"])
                logger.info(
                    f"Extracted {len(data['insights'])} insights from chunk {i + 1}"
                )
            else:
                logger.warning(
                    f"No valid insights found in chunk {i + 1} for URL: {url}"
                )

        except Exception as e:
            logger.error(f"Error processing chunk {i + 1} for URL {url}: {e}")
            # Continue with next chunk instead of failing completely
            time.sleep(2)  # Brief pause before retrying
            continue

    # Deduplicate insights based on label and value
    seen_insights = set()
    unique_insights = []

    for insight in all_insights:
        # Create a key for deduplication
        insight_key = f"{insight.get('label', '')}:{insight.get('value', '')}"
        if insight_key not in seen_insights:
            seen_insights.add(insight_key)
            unique_insights.append(insight)

    logger.info(f"Generated {len(unique_insights)} unique insights for URL: {url}")

    return {"source_url": url, "insights": unique_insights}
