"""
LinkedIn URL Resolver Task for TractionX Data Pipeline Service.

This task handles the complete LinkedIn URL resolution pipeline:
1. <PERSON><PERSON> search for LinkedIn URLs
2. LLM-based link selection
3. BrightData scraping trigger
4. Data storage and processing
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict

from app.configs import get_logger
from app.models.linkedin import LinkedInResolverInput
from app.services.company.linkedin import get_linkedin_resolver_service
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


async def resolve_linkedin_url_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for LinkedIn URL resolution.

    This function is called by the job queue system and handles:
    - Input validation
    - Service initialization
    - URL resolution
    - Result storage

    Args:
        payload: Job payload containing company domain and optional description

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info("Starting LinkedIn URL resolver task", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "company_domain" not in data:
            return {
                "success": False,
                "error": "Missing required field: company_domain",
                "processing_time": time.time() - start_time,
            }

        # Create input model
        input_data = LinkedInResolverInput(
            company_domain=data["company_domain"],
            company_description=data.get("company_description"),
            org_id=data.get("org_id"),
            job_id=data.get("job_id"),
        )

        # Get resolver service
        try:
            logger.info("Getting LinkedIn resolver service")
            resolver_service = await get_linkedin_resolver_service()
            logger.info("LinkedIn resolver service obtained successfully")
        except Exception as e:
            error_msg = f"Failed to get LinkedIn resolver service: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Resolve LinkedIn URL
        try:
            logger.info("Starting LinkedIn URL resolution")
            result = await resolver_service.resolve_linkedin_url(input_data)
            logger.info("LinkedIn URL resolution completed")
        except Exception as e:
            error_msg = f"LinkedIn URL resolution failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Store result to S3 for audit
        s3_storage = S3Storage()
        await s3_storage.initialize()

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"linkedin_resolver_results/{input_data.org_id or 'unknown'}/{input_data.company_domain}/{timestamp}_result.json"

        # Prepare data for S3 storage
        s3_data = {
            "input": input_data.model_dump(),
            "output": result.model_dump(),
            "processing_time": time.time() - start_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await s3_storage.put_object(s3_key, s3_data)

        if success:
            logger.info(f"Successfully stored result to S3: {s3_key}")
            logger.info("S3 Data Summary:")
            logger.info(f"  - Input domain: {input_data.company_domain}")
            logger.info(f"  - Output status: {result.status}")
            logger.info(f"  - LinkedIn URL: {result.linkedin_url or 'N/A'}")
            logger.info(
                f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
            )
            logger.info(f"  - Processing time: {time.time() - start_time:.2f}s")
            logger.info(f"  - Error message: {result.error_message or 'None'}")
            logger.info(f"  - LLM Decision: {result.llm_decision or 'N/A'}")
            logger.info(f"  - Serper Results Count: {result.serper_results_count or 0}")
        else:
            logger.error(f"Failed to store result to S3: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": result.status in ["triggered", "no_match", "completed"],
            "status": result.status,
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        # Log the return result
        logger.info("Returning result:")
        logger.info(f"  - Success: {return_result['success']}")
        logger.info(f"  - Status: {return_result['status']}")
        logger.info(f"  - LinkedIn URL: {result.linkedin_url or 'N/A'}")
        logger.info(
            f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
        )
        logger.info(f"  - Processing time: {return_result['processing_time']:.2f}s")
        logger.info(f"  - S3 Key: {s3_key}")

        return return_result

    except Exception as e:
        error_msg = f"LinkedIn resolver task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def process_brightdata_linkedin_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Process BrightData LinkedIn data from scraping.

    This task handles:
    - Polling for BrightData snapshot completion
    - Downloading BrightData snapshot
    - Processing and cleaning data
    - Storing to database
    - Updating enrichment status

    Args:
        payload: Job payload containing snapshot ID and metadata

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info(
            "Starting BrightData LinkedIn data processing task", payload=payload
        )

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        required_fields = ["snapshot_id", "company_domain", "org_id"]
        for field in required_fields:
            if field not in data:
                return {
                    "success": False,
                    "error": f"Missing required field: {field}",
                    "processing_time": time.time() - start_time,
                }

        snapshot_id = data["snapshot_id"]
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Get resolver service for BrightData operations
        resolver_service = await get_linkedin_resolver_service()

        # Step 1: Poll for snapshot completion
        logger.info(f"Polling for BrightData snapshot completion: {snapshot_id}")
        poll_result = await _poll_brightdata_snapshot_completion(
            snapshot_id, resolver_service
        )
        if not poll_result["success"]:
            return {
                "success": False,
                "error": f"Snapshot polling failed: {poll_result['error']}",
                "processing_time": time.time() - start_time,
                "brightdata_status": poll_result.get("status", "poll_failed"),
            }

        logger.info(f"BrightData snapshot ready: {snapshot_id}")

        # Step 2: Download snapshot data
        download_result = await resolver_service._download_brightdata_snapshot(
            snapshot_id
        )
        if not download_result["success"]:
            return {
                "success": False,
                "error": f"Failed to download snapshot: {download_result['error']}",
                "processing_time": time.time() - start_time,
            }

        raw_data = download_result["data"]
        logger.info(
            "BrightData snapshot downloaded successfully",
            snapshot_id=snapshot_id,
            data_keys=list(raw_data.keys())
            if isinstance(raw_data, dict)
            else "not_dict",
        )

        # Store raw data to S3
        s3_storage = S3Storage()
        await s3_storage.initialize()

        s3_key = f"brightdata_company_linkedin/{company_domain}.json"

        s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await s3_storage.put_object(s3_key, s3_data)
        if success:
            logger.info(f"Raw BrightData data stored to S3: {s3_key}")
        else:
            logger.error(f"Failed to store raw BrightData data to S3: {s3_key}")

        await s3_storage.cleanup()

        # Process the company data
        processing_result = await _process_linkedin_data(
            raw_data, company_domain, org_id, snapshot_id, s3_key
        )

        if not processing_result["success"]:
            return {
                "success": False,
                "error": f"Data processing failed: {processing_result['error']}",
                "processing_time": time.time() - start_time,
            }

        # Update S3 data to include processed data
        s3_storage = S3Storage()
        await s3_storage.initialize()

        updated_s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "processed_data": processing_result["data"],
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await s3_storage.put_object(s3_key, updated_s3_data)
        if success:
            logger.info(f"Updated S3 data with processed data: {s3_key}")
        else:
            logger.error(f"Failed to update S3 data with processed data: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": True,
            "status": "completed",
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        logger.info(
            "BrightData LinkedIn data processing completed successfully",
            snapshot_id=snapshot_id,
            company_domain=company_domain,
            processing_time=return_result["processing_time"],
            s3_key=s3_key,
        )

        return return_result

    except Exception as e:
        error_msg = f"BrightData LinkedIn data processing task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def _poll_brightdata_snapshot_completion(
    snapshot_id: str, resolver_service
) -> Dict[str, Any]:
    """
    Poll for BrightData snapshot completion.

    Args:
        snapshot_id: BrightData snapshot ID
        resolver_service: LinkedIn resolver service instance

    Returns:
        Dictionary with success status and error if any
    """
    import asyncio

    max_poll_time = 600  # 10 minutes
    poll_interval = 10  # 10 seconds
    start_time = time.time()

    while time.time() - start_time < max_poll_time:
        try:
            # Check snapshot progress using BrightData API
            if not resolver_service.brightdata_client:
                return {
                    "success": False,
                    "status": "error",
                    "error": "BrightData client not initialized",
                }

            response = await resolver_service.brightdata_client.get(
                f"/datasets/v3/progress/{snapshot_id}"
            )
            response.raise_for_status()
            result = response.json()

            status = result.get("status")
            logger.info(f"Polling status: {status} for {snapshot_id}")

            if status == "ready":
                return {
                    "success": True,
                    "status": "ready",
                }
            elif status in ["error", "failed"]:
                return {
                    "success": False,
                    "status": "error",
                    "error": f"Job failed with status: {status}",
                }

            # Wait before next poll
            await asyncio.sleep(poll_interval)

        except Exception as e:
            logger.warning(f"Poll error: {e}")
            await asyncio.sleep(poll_interval)

    # Timeout
    return {
        "success": False,
        "status": "timeout",
        "error": f"Polling timed out after {max_poll_time} seconds",
    }


async def _process_linkedin_data(
    raw_data: Any, company_domain: str, org_id: str, snapshot_id: str, s3_key: str
) -> Dict[str, Any]:
    """
    Process and clean LinkedIn company data from BrightData.

    Args:
        raw_data: Raw data from BrightData
        company_domain: Company domain
        org_id: Organization ID
        snapshot_id: BrightData snapshot ID
        s3_key: S3 key for raw data

    Returns:
        Dictionary with success status and processed data
    """
    try:
        logger.info(
            "Processing LinkedIn company data",
            company_domain=company_domain,
            snapshot_id=snapshot_id,
        )
        logger.info(f"Raw data from BrightData: {raw_data}")
        # Extract company data from raw response
        if type(raw_data) is list:
            company_data = raw_data[0]
        else:
            company_data = raw_data

        if not company_data:
            return {
                "success": False,
                "error": "No company data found in BrightData response",
            }

        logger.info(
            "Company data extracted from BrightData response",
            company_data_keys=list(company_data.keys())
            if isinstance(company_data, dict)
            else "not_dict",
        )

        # # Create processed data structure
        # processed_data = {
        #     "company_id": f"linkedin_{company_domain}_{snapshot_id}",
        #     "org_id": org_id,
        #     "linkedin_url": company_data.get("url") or company_data.get("linkedin_url"),
        #     "brightdata_snapshot_id": snapshot_id,
        #     "s3_raw_data_key": s3_key,
        #     "name": company_data.get("name"),
        #     "description": company_data.get("description") or company_data.get("about"),
        #     "website": company_data.get("website") or company_data.get("domain"),
        #     "domain": company_domain,
        #     "industry": company_data.get("industry"),
        #     "company_type": company_data.get("company_type"),
        #     "company_size": company_data.get("company_size")
        #     or company_data.get("size"),
        #     "founded_year": company_data.get("founded_year"),
        #     "employee_count": company_data.get("employee_count")
        #     or company_data.get("employees"),
        #     "employee_count_range": company_data.get("employee_count_range"),
        #     "headquarters": company_data.get("headquarters")
        #     or company_data.get("location"),
        #     "country": company_data.get("country"),
        #     "city": company_data.get("city"),
        #     "state": company_data.get("state"),
        #     "followers_count": company_data.get("followers_count")
        #     or company_data.get("followers"),
        #     "specialties": company_data.get("specialties", []),
        #     "about": company_data.get("about") or company_data.get("description"),
        #     "overview": company_data.get("overview"),
        #     "email": company_data.get("email"),
        #     "phone": company_data.get("phone"),
        #     "employees": company_data.get("employees", []),
        #     "executives": company_data.get("executives", []),
        #     "technologies": company_data.get("technologies", []),
        #     "keywords": company_data.get("keywords", []),
        #     "competitors": company_data.get("competitors", []),
        #     "last_updated": datetime.now(timezone.utc).isoformat(),
        #     "additional_data": company_data,
        # }

        # # Clean and validate data
        # processed_data = _clean_linkedin_data(processed_data)

        # logger.info(
        #     "LinkedIn company data processed successfully",
        #     company_name=processed_data.get("name"),
        #     employee_count=processed_data.get("employee_count"),
        #     followers_count=processed_data.get("followers_count"),
        # )

        return {
            "success": True,
            "data": company_data,
        }

    except Exception as e:
        error_msg = f"Failed to process LinkedIn data: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg}


def _clean_linkedin_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean and validate LinkedIn company data.

    Args:
        data: Raw company data

    Returns:
        Cleaned company data
    """
    cleaned_data = data.copy()

    # Clean employee count
    if cleaned_data.get("employee_count"):
        try:
            if isinstance(cleaned_data["employee_count"], str):
                # Extract numbers from strings like "50-100 employees"
                import re

                numbers = re.findall(r"\d+", cleaned_data["employee_count"])
                if numbers:
                    cleaned_data["employee_count"] = int(numbers[0])
                else:
                    cleaned_data["employee_count"] = None
            else:
                cleaned_data["employee_count"] = int(cleaned_data["employee_count"])
        except (ValueError, TypeError):
            cleaned_data["employee_count"] = None

    # Clean founded year
    if cleaned_data.get("founded_year"):
        try:
            cleaned_data["founded_year"] = int(cleaned_data["founded_year"])
        except (ValueError, TypeError):
            cleaned_data["founded_year"] = None

    # Clean followers count
    if cleaned_data.get("followers_count"):
        try:
            if isinstance(cleaned_data["followers_count"], str):
                # Extract numbers from strings like "1.2K followers"
                import re

                followers_str = cleaned_data["followers_count"].replace(",", "")
                if "K" in followers_str:
                    followers_str = followers_str.replace("K", "")
                    cleaned_data["followers_count"] = int(float(followers_str) * 1000)
                elif "M" in followers_str:
                    followers_str = followers_str.replace("M", "")
                    cleaned_data["followers_count"] = int(
                        float(followers_str) * 1000000
                    )
                else:
                    cleaned_data["followers_count"] = int(followers_str)
            else:
                cleaned_data["followers_count"] = int(cleaned_data["followers_count"])
        except (ValueError, TypeError):
            cleaned_data["followers_count"] = None

    # Ensure lists are actually lists
    list_fields = [
        "specialties",
        "employees",
        "executives",
        "technologies",
        "keywords",
        "competitors",
    ]
    for field in list_fields:
        if field in cleaned_data and not isinstance(cleaned_data[field], list):
            cleaned_data[field] = []

    return cleaned_data
