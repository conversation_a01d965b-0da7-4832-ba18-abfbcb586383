"""
Deal update task for enriching deal records with company data.
"""

import json
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import aiohttp

from app.configs import get_logger, settings
from app.storage.mongo_storage import DealsMongoStorage
from app.storage.rds_storage import RDSStorage


class DealUpdater:
    """
    Updates deal records in MongoDB with company data from PostgreSQL.

    This task runs after core company enrichment completes to populate
    deal records with missing company information.
    """

    def __init__(self, rds_storage: RDSStorage, deals_mongo_storage: DealsMongoStorage):
        self.logger = get_logger(__name__)
        self.rds = rds_storage
        self.deals_mongo = deals_mongo_storage

    async def update_deals_with_company_data(
        self, company_domain: str, org_id: str
    ) -> Dict[str, Any]:
        """
        Update deal records with company data for a specific domain.

        Args:
            company_domain: Company domain to update deals for
            org_id: Organization ID for context

        Returns:
            Dictionary with update results
        """
        try:
            self.logger.info(
                f"Starting deal updates for company domain: {company_domain}"
            )

            # Step 1: Get company data from PostgreSQL
            company_data = await self.rds.get_company_data_for_deal_updates(
                company_domain
            )
            if not company_data:
                return {
                    "success": False,
                    "error": f"No company data found for domain: {company_domain}",
                    "deals_updated": 0,
                }

            # Step 2: Get deals for this company domain
            deals = await self.deals_mongo.get_deals_by_company_domain(company_domain)
            if not deals:
                return {
                    "success": True,
                    "message": f"No deals found for company domain: {company_domain}",
                    "deals_updated": 0,
                }

            # Step 3: Extract logo URL from JSONB fields
            logo_url = self._extract_logo_url_from_company_data(company_data)

            # Step 4: Prepare updates for each deal
            updates_applied = 0
            for deal in deals:
                deal_updates = self._prepare_deal_updates(deal, company_data, logo_url)

                # Step 5: Download and store logo if we have a logo URL (always update logo)
                if logo_url:
                    s3_logo_url = await self._download_and_store_logo(
                        logo_url, str(deal["_id"])
                    )
                    if s3_logo_url:
                        # Always update logo_url, regardless of whether it already exists
                        if not deal_updates:
                            deal_updates = {}
                        deal_updates["logo_url"] = s3_logo_url
                        self.logger.info(f"Stored logo in S3: {s3_logo_url}")

                # Step 5: Download and store logo if we have a logo URL (always update logo)
                # if logo_url:
                #     s3_logo_url = await self._download_and_store_logo(
                #         logo_url, str(deal["_id"])
                #     )
                #     if s3_logo_url:
                #         # Always update logo_url, regardless of whether it already exists
                #         if not deal_updates:
                #             deal_updates = {}
                #         deal_updates["logo_url"] = s3_logo_url
                #         self.logger.info(f"Stored logo in S3: {s3_logo_url}")

                if deal_updates:
                    success = await self.deals_mongo.update_deal_fields(
                        str(deal["_id"]), deal_updates
                    )
                    if success:
                        updates_applied += 1
                        self.logger.info(
                            f"Updated deal {deal['_id']} with fields: {list(deal_updates.keys())}"
                        )

            self.logger.info(
                f"Completed deal updates for {company_domain}: {updates_applied}/{len(deals)} deals updated"
            )

            return {
                "success": True,
                "company_domain": company_domain,
                "org_id": org_id,
                "deals_found": len(deals),
                "deals_updated": updates_applied,
                "logo_url_found": logo_url is not None,
            }

        except Exception as e:
            self.logger.error(
                f"Error updating deals for company domain {company_domain}: {e}"
            )
            return {
                "success": False,
                "error": str(e),
                "company_domain": company_domain,
                "org_id": org_id,
                "deals_updated": 0,
            }

    async def _download_and_store_logo(
        self, logo_url: str, deal_id: str
    ) -> Optional[str]:
        """
        Download logo from URL and store it in S3.

        Args:
            logo_url: URL of the logo to download
            deal_id: Deal ID for S3 path organization

        Returns:
            S3 URL of the stored logo, or None if failed
        """
        try:
            self.logger.info(f"Downloading logo from: {logo_url}")

            # Download the logo
            logo_data = await self._download_file(logo_url)
            if not logo_data:
                self.logger.error(f"Failed to download logo from: {logo_url}")
                return None

            # Determine file extension from URL or content type
            content_type = logo_data.get("content_type", "image/jpeg")
            file_extension = self._get_file_extension(logo_url, content_type)
            if not file_extension:
                file_extension = "jpg"  # Default fallback

            # Generate S3 key
            s3_key = f"deals/{deal_id}/logo.{file_extension}"

            # Upload to S3 using boto3 directly
            s3_url = await self._upload_to_s3(
                file_data=logo_data["content"], s3_key=s3_key, content_type=content_type
            )

            if s3_url:
                self.logger.info(f"Successfully stored logo in S3: {s3_url}")
                return s3_url
            else:
                self.logger.error(f"Failed to upload logo to S3: {s3_key}")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading and storing logo: {e}")
            return None

    async def _upload_to_s3(
        self, file_data: bytes, s3_key: str, content_type: str
    ) -> Optional[str]:
        """
        Upload file data to S3.

        Args:
            file_data: File content as bytes
            s3_key: S3 key for the file
            content_type: Content type of the file

        Returns:
            S3 URL if successful, None otherwise
        """
        try:
            import boto3
            from botocore.exceptions import ClientError

            # Initialize S3 client
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION,
            )

            # Upload file
            s3_client.put_object(
                Bucket=settings.S3_BUCKET,
                Key=s3_key,
                Body=file_data,
                ContentType=content_type,
            )

            # Generate S3 URL
            # Option 1: Direct URL (requires bucket policy for public access)
            s3_url = f"https://{settings.S3_BUCKET}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"

            # Option 2: Presigned URL (if you don't want public access)
            # s3_url = s3_client.generate_presigned_url(
            #     'get_object',
            #     Params={'Bucket': settings.S3_BUCKET, 'Key': s3_key},
            #     ExpiresIn=3600 * 24 * 365  # 1 year
            # )

            return s3_url

        except ClientError as e:
            self.logger.error(f"S3 upload error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error uploading to S3: {e}")
            return None

    async def _download_file(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Download file from URL.

        Args:
            url: URL to download from

        Returns:
            Dictionary with content and content_type, or None if failed
        """
        try:
            timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
            headers = {"User-Agent": "Mozilla/5.0 (compatible; TractionX-Bot/1.0)"}

            async with aiohttp.ClientSession(
                timeout=timeout, headers=headers
            ) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.read()
                        content_type = response.headers.get(
                            "content-type", "image/jpeg"
                        )

                        self.logger.info(
                            f"Downloaded {len(content)} bytes, content-type: {content_type}"
                        )

                        # Validate content type
                        if not content_type.startswith("image/"):
                            self.logger.warning(
                                f"Non-image content type: {content_type} for URL: {url}"
                            )
                            return None

                        # Validate file size (max 5MB)
                        if len(content) > 5 * 1024 * 1024:
                            self.logger.warning(
                                f"Logo file too large: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        # Validate minimum file size (at least 1KB)
                        if len(content) < 1024:
                            self.logger.warning(
                                f"Logo file too small: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        return {"content": content, "content_type": content_type}
                    else:
                        self.logger.error(
                            f"Failed to download logo: HTTP {response.status} for URL: {url}"
                        )
                        return None

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error downloading file from {url}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error downloading file from {url}: {e}")
            return None

    def _get_file_extension(self, url: str, content_type: str) -> Optional[str]:
        """
        Determine file extension from URL or content type.

        Args:
            url: Original URL
            content_type: HTTP content type

        Returns:
            File extension (without dot), or None if cannot determine
        """
        try:
            # Try to get extension from URL
            parsed_url = urlparse(url)
            path = parsed_url.path.lower()

            # Common image extensions
            image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"]

            for ext in image_extensions:
                if path.endswith(ext):
                    return ext[1:]  # Remove the dot

            # Try to get extension from content type
            if content_type:
                # Map content types to extensions
                content_type_map = {
                    "image/jpeg": "jpg",
                    "image/jpg": "jpg",
                    "image/png": "png",
                    "image/gif": "gif",
                    "image/webp": "webp",
                    "image/svg+xml": "svg",
                }

                return content_type_map.get(content_type)

            return None

        except Exception as e:
            self.logger.error(f"Error determining file extension: {e}")
            return None

    def _extract_logo_url_from_company_data(
        self, company_data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Extract logo URL from JSONB fields in priority order.

        Priority: Crunchbase > LinkedIn > Apollo > PitchBook

        Args:
            company_data: Company data from PostgreSQL

        Returns:
            Logo URL if found, None otherwise
        """
        try:
            # Priority order for logo extraction
            sources = [
                ("cb_data", "Crunchbase"),
                ("linkedin_data", "LinkedIn"),
                ("apollo_data", "Apollo"),
                ("pb_data", "PitchBook"),
            ]

            for field_name, source_name in sources:
                jsonb_data = company_data.get(field_name)
                if not jsonb_data:
                    continue

                # Parse JSONB if it's a string
                if isinstance(jsonb_data, str):
                    try:
                        jsonb_data = json.loads(jsonb_data)
                    except json.JSONDecodeError:
                        continue

                # Extract logo URL based on source structure
                logo_url = self._extract_logo_from_source(jsonb_data, source_name)
                if logo_url:
                    self.logger.info(f"Found logo URL from {source_name}: {logo_url}")
                    return logo_url

            self.logger.info("No logo URL found in any source")
            return None

        except Exception as e:
            self.logger.error(f"Error extracting logo URL: {e}")
            return None

    def _extract_logo_from_source(
        self, data: Dict[str, Any], source_name: str
    ) -> Optional[str]:
        """
        Extract logo URL from a specific source's data structure.

        Args:
            data: Source-specific data dictionary
            source_name: Name of the data source

        Returns:
            Logo URL if found, None otherwise
        """
        try:
            if source_name == "Crunchbase":
                # Crunchbase logo field
                return data.get("image") or data.get("logo")

            elif source_name == "LinkedIn":
                # LinkedIn logo field
                return data.get("logo") or data.get("logo")

            elif source_name == "Apollo":
                # Apollo logo field
                return data.get("logo_url") or data.get("logo")

            return None

        except Exception as e:
            self.logger.error(f"Error extracting logo from {source_name}: {e}")
            return None

    def _prepare_deal_updates(
        self,
        deal: Dict[str, Any],
        company_data: Dict[str, Any],
        logo_url: Optional[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Prepare updates for a deal record based on company data.

        Only updates fields that are empty in the deal record.

        Args:
            deal: Deal document from MongoDB
            company_data: Company data from PostgreSQL
            logo_url: Extracted logo URL

        Returns:
            Dictionary of fields to update, or None if no updates needed
        """
        try:
            updates = {}

            # Update sector if empty
            if not deal.get("sector") and company_data.get("sector"):
                # Convert array to string if needed
                sector = company_data["sector"]
                if isinstance(sector, list):
                    sector = ", ".join(sector)
                updates["sector"] = sector

            # Update stage if empty
            if not deal.get("stage") and company_data.get("stage"):
                updates["stage"] = company_data["stage"]

            # Update geography if empty
            if not deal.get("geography") and company_data.get("geo_tags"):
                # Convert array to string if needed
                geo_tags = company_data["geo_tags"]
                if isinstance(geo_tags, list):
                    geo_tags = ", ".join(geo_tags)
                updates["geography"] = geo_tags

            # Update business_model if empty
            if not deal.get("business_model") and company_data.get("business_model"):
                updates["business_model"] = company_data["business_model"]

            # Note: logo_url will be updated separately after S3 upload

            return updates if updates else None

        except Exception as e:
            self.logger.error(f"Error preparing deal updates: {e}")
            return None


async def update_deals_with_company_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Task function for updating deals with company data.

    Args:
        payload: Dictionary containing:
            - company_domain: Company domain to update deals for
            - org_id: Organization ID for context

    Returns:
        Dictionary with update results
    """
    try:
        # Extract payload
        company_domain = payload.get("company_domain")
        org_id = payload.get("org_id")

        if not company_domain:
            return {"success": False, "error": "Missing required field: company_domain"}

        if not org_id:
            return {"success": False, "error": "Missing required field: org_id"}

        # Initialize storage
        rds_storage = RDSStorage()
        deals_mongo_storage = DealsMongoStorage()

        await rds_storage.initialize()
        await deals_mongo_storage.initialize()

        try:
            # Create updater and run updates
            updater = DealUpdater(rds_storage, deals_mongo_storage)
            result = await updater.update_deals_with_company_data(
                company_domain, org_id
            )

            return result

        finally:
            # Cleanup
            await rds_storage.cleanup()
            await deals_mongo_storage.cleanup()

    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Error in update_deals_with_company_data_task: {e}")
        return {"success": False, "error": str(e)}
