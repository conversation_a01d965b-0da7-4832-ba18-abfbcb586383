"""
Modern Company Enrichment Task for TractionX Data Pipeline Service.

This module provides a clean, production-ready implementation for processing
Apollo enrichment data into normalized database tables. Designed for scalability,
maintainability, and future engineer happiness.
"""

import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, NamedTuple, Optional

from app.configs import get_logger
from app.storage.rds_storage import RDSStorage
from app.storage.s3_storage import S3Storage
from app.etl.apollo_company import transform_apollo_company_payload

logger = get_logger(__name__)


class JobContext(NamedTuple):
    """Job context for company enrichment processing."""

    job_id: str
    company_id: str
    org_id: str
    company_name: Optional[str]
    domain: Optional[str]


class ApolloDataProcessor:
    """
    Comprehensive processor for Apollo enrichment data.
    Handles cleaning, validation, and storage.
    """

    def __init__(self, rds_storage: RDSStorage, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.ApolloDataProcessor")

    async def process_apollo_payload(
        self, payload: Dict[str, Any], s3_raw_data_key: str
    ) -> Dict[str, Any]:
        """
        Process a complete Apollo payload and store in normalized schema.

        Args:
            payload: Complete Apollo payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload.get("enrichment_data", {})
            metadata = payload.get("metadata", {})

            # Validate required fields
            validation_result = self._validate_apollo_data(enrichment_data, metadata)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "company_id": metadata.get("company_id"),
                }

            # Transform Apollo data to normalized structure
            transformed_data = transform_apollo_company_payload(
                enrichment_data, metadata["org_id"]
            )

            # Store in database with transaction
            await self._store_company_data(transformed_data, metadata, s3_raw_data_key)

            self.logger.info(
                f"Successfully processed Apollo data for company {metadata.get('company_id')}"
            )

            return {
                "success": True,
                "company_id": metadata.get("company_id"),
                "records_created": {
                    "keywords": len(transformed_data["keywords"]),
                    "technologies": len(transformed_data["technologies"]),
                    "departments": len(transformed_data["departments"]),
                },
            }

        except Exception as e:
            error_msg = f"Apollo processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    def _validate_apollo_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate required fields in Apollo data."""
        errors = []

        # Debug logging
        self.logger.info(
            f"Validating Apollo data with keys: {list(enrichment_data.keys())}"
        )
        self.logger.info(f"Metadata keys: {list(metadata.keys())}")

        # Check metadata
        required_metadata = ["company_id", "org_id"]
        for field in required_metadata:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check enrichment data - we need domain for Apollo
        # Handle both raw Apollo response (with organization wrapper) and direct data
        if "organization" in enrichment_data:
            # Raw Apollo response structure
            org_data = enrichment_data["organization"]
            domain = org_data.get("primary_domain", "").strip()
        else:
            # Direct data structure (fallback)
            domain = enrichment_data.get("domain", "").strip()

        if not domain:
            errors.append("Missing domain (required for Apollo enrichment)")
        else:
            self.logger.info(f"Found domain for validation: {domain}")

        validation_result = {"valid": len(errors) == 0, "errors": errors}
        self.logger.info(f"Validation result: {validation_result}")
        return validation_result

    async def _store_company_data(
        self, transformed_data: Dict[str, Any], metadata: Dict[str, Any], s3_key: str
    ) -> None:
        """Store company data in database with transaction."""
        company_data = transformed_data["company"]
        keywords = transformed_data["keywords"]
        technologies = transformed_data["technologies"]
        departments = transformed_data["departments"]

        # Add metadata to company data
        company_data.update({
            "company_id": metadata["company_id"],
            "org_id": metadata["org_id"],
            "s3_raw_data_key": s3_key,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        })

        # Use the new RDS method for transactional upsert
        await self.rds.upsert_company_with_relations(
            company_data=company_data,
            keywords=keywords,
            technologies=technologies,
            departments=departments,
        )


class CompanyEnrichmentService:
    """
    High-level service for company enrichment processing.
    Orchestrates the entire enrichment workflow.
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.CompanyEnrichmentService")
        self.rds_storage: Optional[RDSStorage] = None
        self.s3_storage: Optional[S3Storage] = None
        self.apollo_processor: Optional[ApolloDataProcessor] = None

    async def initialize(self) -> None:
        """Initialize the enrichment service."""
        self.rds_storage = RDSStorage()
        self.s3_storage = S3Storage()

        await self.rds_storage.initialize()
        await self.s3_storage.initialize()

        self.apollo_processor = ApolloDataProcessor(self.rds_storage, self.s3_storage)

        self.logger.info("Company enrichment service initialized")

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.rds_storage:
            await self.rds_storage.cleanup()
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("Company enrichment service cleaned up")

    async def process_enrichment_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a company enrichment job.

        Args:
            job_data: Job configuration with enrichment data or Apollo pull request

        Returns:
            Processing result with success status and metadata
        """
        try:
            # Extract job context
            job_context = self._extract_job_context(job_data)

            # Validate job context
            validation_error = self._validate_job_context(job_context)
            if validation_error:
                return self._create_error_response(job_context.job_id, validation_error)

            self.logger.info(
                "🚀 Starting company enrichment",
                job_id=job_context.job_id,
                company_name=job_context.company_name,
                domain=job_context.domain,
                company_id=job_context.company_id,
                org_id=job_context.org_id,
            )

            # Store raw data to S3
            s3_key = await self._store_raw_data(job_data, job_context)

            # Run enrichment pipeline
            return await self._fetch_and_process_apollo_data(job_context, s3_key)

        except Exception as e:
            error_msg = f"Company enrichment failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return self._create_error_response(
                job_data.get("job_id", "unknown"), error_msg, job_data.get("company_id")
            )

    async def _fetch_and_process_apollo_data(
        self, job_context: JobContext, s3_key: str
    ) -> Dict[str, Any]:
        """Fetch Apollo data and process it."""
        try:
            # Import here to avoid circular imports
            from app.pipelines.company import CompanyEnrichmentPipeline

            # Create pipeline instance
            pipeline = CompanyEnrichmentPipeline()
            await pipeline.initialize()

            try:
                # Prepare input data for Apollo enrichment
                input_data = {
                    "company_id": job_context.company_id,
                    "org_id": job_context.org_id,
                    "company_name": job_context.company_name,
                    "domain": job_context.domain,
                }

                # Fetch Apollo data
                self.logger.info(
                    "Fetching Apollo data",
                    company_name=job_context.company_name,
                    domain=job_context.domain,
                )
                apollo_result = await pipeline._process_data(input_data)

                if not apollo_result.success:
                    return self._create_error_response(
                        job_context.job_id,
                        f"Apollo fetch failed: {apollo_result.error_message}",
                    )

                # Extract Apollo data from result
                if not apollo_result.data:
                    self.logger.warning("No data returned from Apollo API")
                    return {
                        "success": False,
                        "job_id": job_context.job_id,
                        "company_id": job_context.company_id,
                        "error": "No Apollo data returned from API",
                        "s3_raw_data_key": s3_key,
                        "enrichment_status": "apollo_no_data",
                    }

                enrichment_data = apollo_result.data.get("apollo_data")
                if not enrichment_data:
                    self.logger.warning("No Apollo data found in API response")
                    return self._create_error_response(
                        job_context.job_id, "No Apollo data returned from API"
                    )

                # Get the raw Apollo API response from the metadata
                raw_apollo_response = enrichment_data.apollo_metadata.get(
                    "api_response", {}
                )

                # Convert Apollo data to the format expected by ApolloDataProcessor
                apollo_payload = {
                    "enrichment_data": raw_apollo_response,  # Use raw Apollo response, not model_dump()
                    "metadata": {
                        "company_id": job_context.company_id,
                        "org_id": job_context.org_id,
                        "company_name": job_context.company_name,
                        "domain": job_context.domain,
                        "confidence_score": apollo_result.metadata.get(
                            "confidence_score", 1.0
                        ),
                    },
                }

                # Process the Apollo data
                if self.apollo_processor:
                    result = await self.apollo_processor.process_apollo_payload(
                        apollo_payload, s3_key
                    )

                    if result["success"]:
                        return {
                            "success": True,
                            "job_id": job_context.job_id,
                            "company_id": job_context.company_id,
                            "records_created": result["records_created"],
                            "s3_raw_data_key": s3_key,
                            "apollo_fetched": True,
                        }
                    else:
                        return {
                            "success": False,
                            "job_id": job_context.job_id,
                            "company_id": job_context.company_id,
                            "error": result["error"],
                            "s3_raw_data_key": s3_key,
                        }
                else:
                    return self._create_error_response(
                        job_context.job_id, "Apollo processor not initialized"
                    )

            finally:
                await pipeline.cleanup()

        except Exception as e:
            error_msg = f"Apollo fetch and process failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return self._create_error_response(job_context.job_id, error_msg)

    def _extract_job_context(self, job_data: Dict[str, Any]) -> JobContext:
        """Extract job context from job data."""
        return JobContext(
            job_id=job_data.get("job_id", "unknown"),
            company_id=job_data.get("company_id", ""),  # type: ignore
            org_id=job_data.get("org_id", ""),  # type: ignore
            company_name=job_data.get("company_name", ""),
            domain=job_data.get("domain", ""),
        )

    def _validate_job_context(self, job_context: JobContext) -> Optional[str]:
        """Validate job context."""
        if not job_context.company_id:
            return "Missing company_id"
        if not job_context.org_id:
            return "Missing org_id"
        if not job_context.domain:
            return "Missing domain (required for Apollo enrichment)"
        return None

    def _create_error_response(
        self, job_id: str, error: str, company_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "success": False,
            "job_id": job_id,
            "company_id": company_id,
            "error": error,
        }

    async def _store_raw_data(
        self, job_data: Dict[str, Any], job_context: JobContext
    ) -> str:
        """Store raw job data to S3 for audit trail."""
        import json

        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"companies/{job_context.org_id}/{job_context.company_id}/raw_data_{timestamp}.json"

        # Store data
        if self.s3_storage:
            await self.s3_storage.put_object(s3_key, json.dumps(job_data, default=str))

        return s3_key


async def enrich_company_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Modern company enrichment using Apollo data with normalized database storage.

    This function processes company enrichment requests by:
    1. Validating input data
    2. Storing raw data to S3 for audit trails
    3. Fetching Apollo data from API
    4. Processing and normalizing data into structured database tables

    Args:
        job_data: Enrichment job configuration containing:
            - company_id: Company identifier
            - org_id: Organization identifier
            - company_name: Company name (optional if domain provided)
            - domain: Company domain (optional if company_name provided)
            - job_id: (optional) Unique job identifier

    Returns:
        Processing result with success status and metadata
    """
    service = CompanyEnrichmentService()

    try:
        await service.initialize()
        return await service.process_enrichment_job(job_data)
    finally:
        await service.cleanup()


def enrich_company_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async company enrichment task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(enrich_company_data(job_data))
