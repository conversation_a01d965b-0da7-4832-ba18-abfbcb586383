"""
Refactored Tasks for TractionX Data Pipeline Service.

This module provides a clean, organized task structure with proper separation of concerns.
Tasks are organized by entity type (company, founder) and focus purely on data processing logic.
"""

from typing import Any, Callable, Dict

from app.models.job_tracking import (
    JOB_TYPE_REGISTRY,
    JobType,
    get_function_name_for_job_type,
    get_job_type_info,
    get_job_types_by_category,
    get_job_types_by_entity_type,
    validate_job_type_for_entity,
)

# Import all task functions
from app.tasks.company.apollo import process_apollo_company_data_task
from app.tasks.company.crunchbase.processor import (
    scrape_crunchbase_company_data_task,
)
from app.tasks.company.enrichment import enrich_company_data
from app.tasks.company.linkedin import (
    resolve_linkedin_company_url_task,
    scrape_linkedin_company_data_task,
)
from app.tasks.company.pitchbook import (
    resolve_pitchbook_company_url_task,
    scrape_pitchbook_company_data_task,
)
from app.tasks.company.website import (
    generate_sitemap_task,
    generate_website_insights_task,
)
from app.tasks.crunchbase_resolver import process_brightdata_company_data_task
from app.tasks.founder.linkedin import enrich_founder_linkedin_data
from app.tasks.founder.pdl.processor import enrich_founder_pdl_data
from app.tasks.founder.spidy_chart.processor import (
    generate_founder_spider_chart,
)
from app.tasks.management.deals import update_deals_with_company_data_task
from app.tasks.management.enrichment import (
    cleanup_enrichment,
    get_enrichment_status,
    retry_failed_enrichment,
)
from app.tasks.processing.comprehensive import (
    enrich_company_data_comprehensive,
    merge_comprehensive_enrichment_results,
    merge_core_company_data_results,
    merge_website_insights_results,
    process_comprehensive_enrichment_data_task,
)
from app.tasks.processing.embeddings import generate_embeddings
from app.tasks.processing.etl import merge_enrichment_data
from app.tasks.processing.news import aggregate_news_data


def get_job_handlers() -> Dict[str, Callable]:
    """
    Get all available job handlers using the centralized registry.

    This function dynamically builds the job handlers dictionary from the
    centralized JOB_TYPE_REGISTRY, ensuring consistency between job types
    and their corresponding functions.
    """
    handlers = {}

    # Build handlers from registry
    for job_type, info in JOB_TYPE_REGISTRY.items():
        function_name = info.get("function")
        if function_name:
            # Import the function dynamically
            function = globals().get(function_name)
            if function:
                handlers[job_type.value] = function
            else:
                # Fallback: try to import from specific modules
                function = _get_function_by_name(function_name)
                if function:
                    handlers[job_type.value] = function

    return handlers


def _get_function_by_name(function_name: str) -> Callable | None:
    """Get function by name with fallback imports."""
    # Direct function mapping for functions not in globals
    function_map = {
        "process_pdl_enrichment_task": enrich_founder_pdl_data,
        "enrich_founder_linkedin_data": enrich_founder_linkedin_data,
        "enrich_company_data": enrich_company_data,
        "enrich_company_data_comprehensive": enrich_company_data_comprehensive,
        "process_brightdata_company_data_task": process_brightdata_company_data_task,
        "process_brightdata_linkedin_data_task": resolve_linkedin_company_url_task,
        "scrape_crunchbase_company_data_task": scrape_crunchbase_company_data_task,
        "scrape_linkedin_company_data_task": scrape_linkedin_company_data_task,
        "scrape_pitchbook_company_data_task": scrape_pitchbook_company_data_task,
        "resolve_pitchbook_url_task": resolve_pitchbook_company_url_task,
        "generate_founder_spider_chart_task": generate_founder_spider_chart,
        "process_apollo_company_data_task": process_apollo_company_data_task,
        "generate_sitemap_task": generate_sitemap_task,
        "generate_website_insights_task": generate_website_insights_task,
        "aggregate_news_data": aggregate_news_data,
        "generate_embeddings": generate_embeddings,
        "merge_enrichment_data": merge_enrichment_data,
        "merge_comprehensive_enrichment_results": merge_comprehensive_enrichment_results,
        "merge_core_company_data_results": merge_core_company_data_results,
        "merge_website_insights_results": merge_website_insights_results,
        "process_comprehensive_enrichment_data_task": process_comprehensive_enrichment_data_task,
        "get_enrichment_status": get_enrichment_status,
        "retry_failed_enrichment": retry_failed_enrichment,
        "cleanup_enrichment": cleanup_enrichment,
        "update_deals_with_company_data_task": update_deals_with_company_data_task,
    }

    return function_map.get(function_name)


def get_supported_job_types() -> Dict[str, Dict[str, Any]]:
    """
    Get all supported job types with their metadata.

    Returns:
        Dictionary mapping job type values to their metadata
    """
    return {
        job_type.value: {
            **info,
            "job_type": job_type.value,
            "category": info.get("category", "unknown"),
            "entity_types": info.get("entity_types", []),
            "required_fields": info.get("required_fields", []),
            "estimated_duration": info.get("estimated_duration", 60),
            "description": info.get("description", ""),
        }
        for job_type, info in JOB_TYPE_REGISTRY.items()
    }


def get_job_types_for_entity(entity_type: str) -> Dict[str, Dict[str, Any]]:
    """
    Get job types that support a specific entity type.

    Args:
        entity_type: The entity type (e.g., "company", "founder")

    Returns:
        Dictionary of job types that support the entity type
    """
    supported_types = get_job_types_by_entity_type(entity_type)
    return {job_type.value: get_job_type_info(job_type) for job_type in supported_types}


def get_job_types_by_category_name(category: str) -> Dict[str, Dict[str, Any]]:
    """
    Get job types by category.

    Args:
        category: The category (e.g., "enrichment", "website", "processing")

    Returns:
        Dictionary of job types in the category
    """
    category_types = get_job_types_by_category(category)
    return {job_type.value: get_job_type_info(job_type) for job_type in category_types}


# Export task functions for direct import
__all__ = [
    # Core functions
    "get_job_handlers",
    "get_supported_job_types",
    "get_job_types_for_entity",
    "get_job_types_by_category_name",
    "validate_job_type_for_entity",
    "get_function_name_for_job_type",
    # Job type enums
    "JobType",
    # Task functions (for backward compatibility)
    "enrich_company_data",
    "enrich_company_data_comprehensive",
    "enrich_founder_linkedin_data",
    "process_brightdata_company_data_task",
    "process_apollo_company_data_task",
    "generate_sitemap_task",
    "generate_website_insights_task",
    "aggregate_news_data",
    "generate_embeddings",
    "merge_enrichment_data",
    "merge_comprehensive_enrichment_results",
    "merge_core_company_data_results",
    "merge_website_insights_results",
    "process_comprehensive_enrichment_data_task",
    "get_enrichment_status",
    "retry_failed_enrichment",
    "cleanup_enrichment",
    "update_deals_with_company_data_task",
]
