"""
Logging configuration for TractionX Data Pipeline Service.
"""

import json
import logging
import sys
from typing import Any, Dict, Optional

import structlog  # type: ignore
from structlog.stdlib import ProcessorFormatter  # type: ignore

from app.configs.settings import settings


class ReadableJSONRenderer:
    """Custom JSON renderer that produces more readable output."""

    def __init__(self, indent: int = 2):
        self.indent = indent

    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: Dict[str, Any]
    ) -> str:
        # Extract the actual event if it's a JSON string
        if isinstance(event_dict.get("event"), str):
            try:
                # Try to parse if it's a JSON string
                event = json.loads(event_dict["event"])
                if isinstance(event, dict) and "event" in event:
                    # If it's a nested JSON, use the inner event
                    event_dict["event"] = event["event"]
                    # Merge other fields if they exist
                    for key, value in event.items():
                        if key != "event" and key not in event_dict:
                            event_dict[key] = value
            except json.JSONDecodeError:
                # If it's not JSON, keep it as is
                pass

        # Make logger name more readable by removing datapipelines prefix
        if "logger" in event_dict and event_dict["logger"].startswith("datapipelines."):
            event_dict["logger"] = event_dict["logger"].replace("datapipelines.", "")

        # Format the output
        return json.dumps(event_dict, indent=self.indent, ensure_ascii=False)


def configure_logging() -> None:
    """Configure structured logging for the application."""
    # Remove any existing handlers to avoid duplicates
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Configure structlog first
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            # Add context variables processor for org_id propagation
            structlog.contextvars.merge_contextvars,
            ReadableJSONRenderer(),  # Use our custom renderer # type: ignore
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure root logger with our custom formatter
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(
        ProcessorFormatter(
            processor=ReadableJSONRenderer(),  # Use our custom renderer # type: ignore
            foreign_pre_chain=[
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="iso"),
                # Add context variables processor for org_id propagation
                structlog.contextvars.merge_contextvars,
            ],
        )
    )

    # Set the root logger to use our handler
    root_logger.addHandler(handler)
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Set log levels for external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("rq.worker").setLevel(logging.INFO)


def get_logger(name: str, org_id: Optional[str] = None) -> structlog.BoundLogger:
    """Get a structured logger instance with optional org_id context."""
    logger = structlog.get_logger(name)

    # If org_id is provided, bind it to the logger context
    if org_id:
        logger = logger.bind(org_id=org_id)

    return logger


def set_org_context(org_id: str) -> None:
    """
    Set organization context for the current execution context.

    This uses structlog's contextvars to propagate org_id through the entire
    request/job lifecycle. This is the industry standard approach for
    multi-tenant applications.

    Args:
        org_id: Organization ID to set in context
    """
    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(org_id=org_id)


def get_org_context() -> Optional[str]:
    """
    Get the current organization context.

    Returns:
        Organization ID from current context, or None if not set
    """
    context = structlog.contextvars.get_contextvars()
    return context.get("org_id")


def clear_org_context() -> None:
    """Clear the current organization context."""
    structlog.contextvars.clear_contextvars()
