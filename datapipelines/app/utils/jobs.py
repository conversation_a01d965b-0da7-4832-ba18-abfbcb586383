"""
Job Utilities for TractionX Data Pipeline Service

This module provides utility functions for working with the job tracking system,
following the same elegant patterns as the backend system.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.models.job_tracking import JobType, TrackedJob
from app.services.jobs.factory import get_job_service


class JobBuilder:
    """
    Sophisticated job builder for creating tracked jobs with fluent interface.

    Supports job chaining, dependencies, scheduling, and complex configurations.

    # JobBuilder Architecture

    JobBuilder provides a fluent interface for creating complex job configurations.
    It handles all aspects of job creation including entity binding, payload setup,
    metadata management, orchestration patterns, and dependency management.

    # Core Attributes

    ## Entity Management
    - `_entity_type`: Type of entity (company, founder, deal, etc.)
    - `_entity_id`: Unique identifier for the entity

    ## Job Configuration
    - `_job_type`: Type of job to execute (from JobType enum)
    - `_payload`: Data passed to the job execution function
    - `_config`: Job-specific configuration settings
    - `_priority`: Job priority (0-100, higher = more important)

    ## Queue Management
    - `_queue_priority`: Queue-specific priority (high, normal, low)
    - `_queue_type`: Type of queue (default, fast, slow)
    - `_queue_job_type`: Function name to execute in queue

    ## Dependency Management
    - `_parent_job_id`: Parent job that spawned this job
    - `_depends_on`: List of job IDs this job depends on
    - `_scheduled_at`: Timestamp for delayed execution

    ## Orchestration Features
    - `_job_group`: Group name for coordinating related jobs
    - `_triggers`: Jobs to trigger when this job completes
    - `_wait_for`: Jobs to wait for before starting
    - `_orchestration_pattern`: Workflow pattern (sequential, parallel, etc.)
    - `_execution_conditions`: Conditions that must be met to execute
    - `_rollback_job_id`: Job to execute if this job fails
    - `_timeout_seconds`: Maximum execution time

    # Usage Examples

    ## Basic Job Creation
    ```python
    job = await JobBuilder()
        .for_entity("company", "acme.com")
        .with_job_type(JobType.ENRICH_COMPANY_DATA)
        .with_payload(domain="acme.com", description="Tech company")
        .create()
    ```

    ## Job with Dependencies
    ```python
    job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.GENERATE_WEBSITE_INSIGHTS)
        .depends_on(previous_job_id)
        .with_job_group("acme_enrichment")
        .create()
    ```

    ## Complex Orchestration
    ```python
    job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.ENRICH_COMPANY_COMPREHENSIVE)
        .with_payload(domain="acme.com")
        .with_metadata(pipeline_id="pipeline_123", org_id="org_456")
        .with_config(enable_ai_analysis=True, max_retries=3)
        .with_priority(80)
        .with_job_group("comprehensive_enrichment")
        .with_orchestration_pattern("sequential")
        .triggers("GENERATE_WEBSITE_INSIGHTS", "ANALYZE_COMPETITORS")
        .with_timeout(1800)  # 30 minutes
        .with_retry(max_attempts=3, backoff_factor=2.0)
        .create()
    ```

    ## Scheduled Job
    ```python
    import time
    scheduled_time = int(time.time()) + 3600  # 1 hour from now

    job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.REFRESH_COMPANY_DATA)
        .scheduled_at(scheduled_time)
        .with_queue_priority("low")
        .create()
    ```

    ## Conditional Job
    ```python
    job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.ANALYZE_FUNDING)
        .with_condition("has_funding_data", True)
        .with_condition("funding_amount", ">1000000")
        .create()
    ```

    ## Rollback Job
    ```python
    rollback_job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.REVERT_ENRICHMENT)
        .create()

    main_job = await JobBuilder()
        .for_company("acme.com")
        .with_job_type(JobType.ENRICH_COMPANY_DATA)
        .with_rollback(rollback_job.id)
        .create()
    ```
    """

    def __init__(self):
        # Entity Management
        self._entity_type: Optional[str] = None  # Type: company, founder, deal, etc.
        self._entity_id: Optional[str] = None  # Unique identifier for the entity

        # Job Configuration
        self._job_type: Optional[JobType] = None  # Type of job to execute
        self._payload: Dict[str, Any] = {}  # Data passed to job function
        self._metadata: Dict[str, Any] = {}  # Job metadata (pipeline_id, org_id, etc.)
        self._config: Dict[str, Any] = {}  # Job-specific configuration
        self._priority: int = 0  # Job priority (0-100)

        # Queue Management
        self._queue_priority: Optional[str] = None  # Queue priority: high, normal, low
        self._queue_type: Optional[str] = None  # Queue type: default, fast, slow
        self._queue_job_type: Optional[str] = None  # Function name to execute

        # Dependency Management
        self._parent_job_id: Optional[Union[str, UUID]] = None  # Parent job ID
        self._depends_on: List[Union[str, UUID]] = []  # Jobs this depends on
        self._scheduled_at: Optional[int] = None  # Execution timestamp

        # Enhanced Orchestration Fields
        self._job_group: Optional[str] = None  # Group for coordination
        self._triggers: List[str] = []  # Jobs to trigger on completion
        self._wait_for: List[str] = []  # Jobs to wait for
        self._orchestration_pattern: Optional[str] = None  # Workflow pattern
        self._execution_conditions: Dict[str, Any] = {}  # Execution conditions
        self._rollback_job_id: Optional[UUID] = None  # Rollback job if fails
        self._timeout_seconds: Optional[int] = None  # Max execution time

    def for_entity(self, entity_type: str, entity_id: Optional[str]) -> "JobBuilder":
        """
        Set the entity for this job.

        Args:
            entity_type: Type of entity (company, founder, deal, etc.)
            entity_id: Unique identifier for the entity

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.for_entity("company", "acme.com")
            job_builder.for_entity("founder", "john_doe_123")
            job_builder.for_entity("deal", "deal_456")
            ```
        """
        self._entity_type = entity_type
        self._entity_id = entity_id
        return self

    def with_job_type(self, job_type: Union[str, JobType]) -> "JobBuilder":
        """
        Set the job type to execute.

        Args:
            job_type: JobType enum or string representation

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_job_type(JobType.ENRICH_COMPANY_DATA)
            job_builder.with_job_type("enrich_company_data")
            ```
        """
        self._job_type = JobType(job_type) if isinstance(job_type, str) else job_type
        return self

    def with_payload(self, **payload) -> "JobBuilder":
        """
        Set the job payload (data passed to job function).

        Args:
            **payload: Key-value pairs for job payload

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_payload(
                domain="acme.com",
                description="Technology company",
                industry="SaaS"
            )
            ```
        """
        self._payload.update(payload)
        return self

    def with_metadata(self, **metadata) -> "JobBuilder":
        """
        Set job metadata (contextual information).

        Args:
            **metadata: Key-value pairs for job metadata

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_metadata(
                pipeline_id="pipeline_123",
                org_id="org_456",
                user_id="user_789",
                source="manual_trigger"
            )
            ```
        """
        self._metadata.update(metadata)
        return self

    def with_config(self, **config) -> "JobBuilder":
        """
        Set job-specific configuration settings.

        Args:
            **config: Key-value pairs for job configuration

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_config(
                enable_ai_analysis=True,
                max_retries=3,
                timeout_seconds=1800,
                use_cache=True
            )
            ```
        """
        self._config.update(config)
        return self

    def with_queue_config(
        self,
        job_type: Optional[str] = None,
        priority: Optional[str] = None,
        queue_type: Optional[str] = None,
    ) -> "JobBuilder":
        """
        Set queue-specific configuration.

        Args:
            job_type: Function name to execute in queue
            priority: Queue priority (high, normal, low)
            queue_type: Queue type (default, fast, slow)

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_queue_config(
                job_type="enrich_company_data",
                priority="high",
                queue_type="fast"
            )
            ```
        """
        if job_type:
            self._queue_job_type = job_type
        if priority:
            self._queue_priority = priority
        if queue_type:
            self._queue_type = queue_type
        return self

    def with_priority(self, priority: int) -> "JobBuilder":
        """
        Set job priority (0-100, higher = more important).

        Args:
            priority: Priority value (0-100)

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_priority(80)  # High priority
            job_builder.with_priority(50)  # Normal priority
            job_builder.with_priority(20)  # Low priority
            ```
        """
        self._priority = priority
        return self

    def with_parent(self, parent_job_id: Union[str, UUID]) -> "JobBuilder":
        """
        Set parent job ID (job that spawned this job).

        Args:
            parent_job_id: ID of the parent job

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_parent("job_123")
            job_builder.with_parent(parent_job.id)
            ```
        """
        self._parent_job_id = parent_job_id
        return self

    def depends_on(self, *job_ids: Union[str, UUID]) -> "JobBuilder":
        """
        Add job dependencies (jobs that must complete before this job starts).

        Args:
            *job_ids: Job IDs this job depends on

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.depends_on("job_123")
            job_builder.depends_on("job_123", "job_456", "job_789")
            ```
        """
        self._depends_on.extend(job_ids)
        return self

    def scheduled_at(self, timestamp: int) -> "JobBuilder":
        """
        Set scheduled execution time (Unix timestamp).

        Args:
            timestamp: Unix timestamp for execution

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            import time

            # Execute in 1 hour
            future_time = int(time.time()) + 3600
            job_builder.scheduled_at(future_time)

            # Execute at specific time
            specific_time = int(time.mktime(time.strptime("2024-01-01 12:00:00", "%Y-%m-%d %H:%M:%S")))
            job_builder.scheduled_at(specific_time)
            ```
        """
        self._scheduled_at = timestamp
        return self

    def with_retry(
        self,
        max_attempts: int = 3,
        backoff_factor: float = 2.0,
        max_backoff: int = 3600,
    ) -> "JobBuilder":
        """
        Configure retry behavior for failed jobs.

        Args:
            max_attempts: Maximum number of retry attempts
            backoff_factor: Exponential backoff multiplier
            max_backoff: Maximum backoff time in seconds

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_retry(
                max_attempts=5,
                backoff_factor=2.0,  # 2s, 4s, 8s, 16s, 32s
                max_backoff=3600     # Cap at 1 hour
            )
            ```
        """
        self._config["retry"] = {
            "max_attempts": max_attempts,
            "backoff_factor": backoff_factor,
            "max_backoff": max_backoff,
        }
        return self

    def with_timeout(self, timeout_seconds: int) -> "JobBuilder":
        """
        Set job execution timeout.

        Args:
            timeout_seconds: Maximum execution time in seconds

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_timeout(1800)  # 30 minutes
            job_builder.with_timeout(3600)  # 1 hour
            job_builder.with_timeout(300)   # 5 minutes
            ```
        """
        self._timeout_seconds = timeout_seconds
        self._config["timeout"] = timeout_seconds
        return self

    # Enhanced orchestration methods
    def with_job_group(self, group_name: str) -> "JobBuilder":
        """
        Set job group for coordination and monitoring.

        Args:
            group_name: Name of the job group

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_job_group("acme_enrichment")
            job_builder.with_job_group("pipeline_123_company_456")
            ```
        """
        self._job_group = group_name
        return self

    def triggers(self, *job_types: str) -> "JobBuilder":
        """
        Set jobs to trigger when this job completes successfully.

        Args:
            *job_types: Job types to trigger

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.triggers("GENERATE_WEBSITE_INSIGHTS")
            job_builder.triggers("ANALYZE_COMPETITORS", "UPDATE_DASHBOARD")
            ```
        """
        self._triggers.extend(job_types)
        return self

    def wait_for(self, *job_types: str) -> "JobBuilder":
        """
        Set jobs to wait for before starting this job.

        Args:
            *job_types: Job types to wait for

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.wait_for("ENRICH_COMPANY_DATA")
            job_builder.wait_for("ENRICH_COMPANY_DATA", "ANALYZE_FUNDING")
            ```
        """
        self._wait_for.extend(job_types)
        return self

    def with_orchestration_pattern(self, pattern: str) -> "JobBuilder":
        """
        Set orchestration pattern for workflow management.

        Args:
            pattern: Orchestration pattern (sequential, parallel, comprehensive, etc.)

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_orchestration_pattern("sequential")
            job_builder.with_orchestration_pattern("parallel")
            job_builder.with_orchestration_pattern("comprehensive")
            ```
        """
        self._orchestration_pattern = pattern
        return self

    def with_condition(self, condition_name: str, condition_value: Any) -> "JobBuilder":
        """
        Add execution condition that must be met to run this job.

        Args:
            condition_name: Name of the condition
            condition_value: Value or expression for the condition

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.with_condition("has_funding_data", True)
            job_builder.with_condition("funding_amount", ">1000000")
            job_builder.with_condition("company_size", ">50")
            ```
        """
        self._execution_conditions[condition_name] = condition_value
        return self

    def with_rollback(self, rollback_job_id: UUID) -> "JobBuilder":
        """
        Set rollback job to execute if this job fails.

        Args:
            rollback_job_id: ID of the rollback job

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            rollback_job = await JobBuilder()
                .for_company("acme.com")
                .with_job_type(JobType.REVERT_ENRICHMENT)
                .create()

            job_builder.with_rollback(rollback_job.id)
            ```
        """
        self._rollback_job_id = rollback_job_id
        return self

    # Convenience methods for common entity types
    def for_company(self, company_id: str) -> "JobBuilder":
        """
        Set entity as company (convenience method).

        Args:
            company_id: Company identifier (usually domain)

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.for_company("acme.com")
            job_builder.for_company("google.com")
            ```
        """
        return self.for_entity("company", company_id)

    def for_founder(self, founder_id: str) -> "JobBuilder":
        """
        Set entity as founder (convenience method).

        Args:
            founder_id: Founder identifier

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.for_founder("john_doe_123")
            job_builder.for_founder("jane_smith_456")
            ```
        """
        return self.for_entity("founder", founder_id)

    def for_deal(self, deal_id: str) -> "JobBuilder":
        """
        Set entity as deal (convenience method).

        Args:
            deal_id: Deal identifier

        Returns:
            JobBuilder instance for chaining

        Example:
            ```python
            job_builder.for_deal("deal_123")
            job_builder.for_deal("deal_456")
            ```
        """
        return self.for_entity("deal", deal_id)

    async def create(self, job_id: str) -> TrackedJob:
        """
        Create the tracked job with all configured settings.

        This method validates the configuration, creates the job in the database,
        and returns a TrackedJob instance.

        Returns:
            TrackedJob instance

        Raises:
            ValueError: If required fields are missing

        Example:
            ```python
            job = await JobBuilder()
                .for_company("acme.com")
                .with_job_type(JobType.ENRICH_COMPANY_DATA)
                .with_payload(domain="acme.com")
                .create()

            print(f"Created job: {job.id}")
            print(f"Status: {job.status}")
            ```
        """
        if not self._entity_type or not self._job_type:
            raise ValueError(
                "Entity type, entity ID, and job type must be set before creating job"
            )

        job_service = await get_job_service()

        # Prepare metadata with orchestration information
        metadata = self._metadata.copy()
        if self._job_group:
            metadata["job_group"] = self._job_group
        if self._triggers:
            metadata["triggers"] = self._triggers
        if self._wait_for:
            metadata["wait_for"] = self._wait_for
        if self._orchestration_pattern:
            metadata["orchestration_pattern"] = self._orchestration_pattern
        if self._execution_conditions:
            metadata["execution_conditions"] = self._execution_conditions
        if self._rollback_job_id:
            metadata["rollback_job_id"] = str(self._rollback_job_id)

        # Create the tracked job
        tracked_job = await job_service.create_job(
            job_id=job_id,
            entity_type=self._entity_type,
            entity_id=self._entity_id or "",
            job_type=self._job_type,
            payload=self._payload,
            metadata=metadata,
            config=self._config,
            priority=self._priority,
            parent_job_id=self._parent_job_id,
            depends_on=self._depends_on,
            scheduled_at=self._scheduled_at,
            queue_priority=self._queue_priority,
            queue_type=self._queue_type,
            queue_job_type=self._queue_job_type or str(self._job_type),
        )

        # Set enhanced fields after creation
        if self._job_group:
            tracked_job.job_group = self._job_group
        if self._triggers:
            tracked_job.triggers = self._triggers
        if self._wait_for:
            tracked_job.wait_for = self._wait_for
        if self._orchestration_pattern:
            tracked_job.orchestration_pattern = self._orchestration_pattern
        if self._execution_conditions:
            tracked_job.execution_conditions = self._execution_conditions
        if self._rollback_job_id:
            tracked_job.rollback_job_id = self._rollback_job_id

        return tracked_job
