"""
Pipeline utility functions for TractionX Data Pipeline Service.

This module contains utility functions extracted from the pipeline API
to improve code organization and reusability.
"""

from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.models.job_tracking import JobType
from app.queueing.factory import create_redis_backend
from app.queueing.service import QueueServiceV2

logger = get_logger(__name__)

# Global V2 queue service instance
_v2_queue_service: Optional[QueueServiceV2] = None


async def get_v2_queue_service() -> QueueServiceV2:
    """Get or create V2 queue service instance."""
    global _v2_queue_service
    if _v2_queue_service is None:
        backend = await create_redis_backend(enhanced=True)
        _v2_queue_service = QueueServiceV2(backend)
        await _v2_queue_service.initialize()
    return _v2_queue_service


def calculate_estimated_duration(total_jobs: int, pattern: str) -> Optional[int]:
    """Calculate estimated pipeline duration.

    Args:
        total_jobs: Total number of jobs in the pipeline
        pattern: Orchestration pattern (sequential, parallel, comprehensive, etc.)

    Returns:
        Estimated duration in seconds, or None if unable to estimate
    """
    base_time_per_job = 60  # 1 minute per job

    if pattern == "sequential":
        return total_jobs * base_time_per_job
    elif pattern == "parallel":
        return base_time_per_job * 2  # Parallel jobs + merge time
    elif pattern == "comprehensive":
        return 1800  # 30 minutes for comprehensive enrichment
    else:
        return total_jobs * base_time_per_job


def get_job_function_mapping() -> Dict[JobType, str]:
    """Get mapping of job types to function names.

    Returns:
        Dictionary mapping JobType enum values to function names
    """
    return {
        JobType.COMPANY_ENRICH_PDL: "company.enrich.pdl",
        JobType.COMPANY_LINKEDIN_PROCESSOR: "company.linkedin.processor",
        JobType.COMPANY_CRUNCHBASE_PROCESSOR: "company.crunchbase.processor",
        JobType.COMPANY_ENRICH_PITCHBOOK: "company.enrich.pitchbook",
        JobType.COMPANY_ENRICH_APOLLO: "company.enrich.apollo",
        JobType.COMPANY_ENRICH_COMPREHENSIVE: "company.enrich.comprehensive",
        JobType.COMPANY_RESOLVE_CRUNCHBASE: "company.resolve.crunchbase",
        JobType.COMPANY_RESOLVE_LINKEDIN: "company.resolve.linkedin",
        JobType.COMPANY_RESOLVE_PITCHBOOK: "company.resolve.pitchbook",
        JobType.COMPANY_WEBSITE_INSIGHTS: "company.website.insights",
        JobType.COMPANY_WEBSITE_SITEMAP: "company.website.sitemap",
    }


def create_job_metadata(
    pipeline_id: str,
    orchestration_pattern: str,
    entity_data: Dict[str, Any],
    pipeline_metadata: Dict[str, Any],
    **additional_metadata: Any,
) -> Dict[str, Any]:
    """Create standardized job metadata.

    Args:
        pipeline_id: Unique pipeline identifier
        orchestration_pattern: Orchestration pattern being used
        entity_data: Entity data dictionary
        pipeline_metadata: Pipeline-level metadata
        **additional_metadata: Additional metadata to include

    Returns:
        Combined metadata dictionary
    """
    return {
        "pipeline_id": pipeline_id,
        "orchestration_pattern": orchestration_pattern,
        "entity_data": entity_data,
        **pipeline_metadata,
        **additional_metadata,
    }


def create_queue_job_metadata(
    tracked_job_id: Optional[str],
    pipeline_id: str,
    entity_id: str,
    job_type: JobType,
    job_config_metadata: Dict[str, Any],
) -> Dict[str, Any]:
    """Create metadata for queue jobs.

    Args:
        tracked_job_id: ID of the tracked job if tracking is enabled
        pipeline_id: Unique pipeline identifier
        entity_id: Entity identifier
        job_type: Type of job being executed
        job_config_metadata: Job configuration metadata

    Returns:
        Queue job metadata dictionary
    """
    return {
        "tracked_job_id": tracked_job_id,
        "pipeline_id": pipeline_id,
        "entity_id": entity_id,
        "job_type": job_type.value,
        **job_config_metadata,
    }


def validate_pipeline_request(
    entities: List[Any],
    job_types: List[JobType],
    payload: Optional[Dict[str, Any]] = None,
) -> None:
    """Validate pipeline request parameters.

    Args:
        entities: List of entities to process
        job_types: List of job types to execute
        payload: Legacy payload for backward compatibility

    Raises:
        ValueError: If validation fails
    """
    # Allow empty entities if payload is provided (legacy mode)
    if not entities and not payload:
        raise ValueError("At least one entity must be specified")

    if not job_types:
        raise ValueError("At least one job type must be specified")


def convert_legacy_payload_to_entity_data(
    payload: Dict[str, Any], entity_id: str = "unknown"
) -> Dict[str, Any]:
    """Convert legacy payload format to entity data format.

    Args:
        payload: Legacy payload dictionary
        entity_id: Entity identifier to use

    Returns:
        Entity data dictionary
    """
    return {
        "entity_id": entity_id,
        "entity_type": "company",
        "data": {
            "name": payload.get("company_name"),
            "domain": payload.get("domain"),
            "description": payload.get("company_name"),
            "linkedin_url": payload.get("linkedin_url"),
            "crunchbase_url": payload.get("crunchbase_url"),
            "pitchbook_url": payload.get("pitchbook_url"),
            "metadata": payload.get("metadata", {}),
            **payload,  # Include all payload fields in data
        },
    }
