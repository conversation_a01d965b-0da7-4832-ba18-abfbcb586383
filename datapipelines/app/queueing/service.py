"""
V2 Queue Service with MongoDB Job Tracking

This module provides a V2 queue service that integrates with MongoDB job tracking,
following clean separation of concerns:
- Queue operations via QueueBackend (Redis)
- Job tracking via JobService (MongoDB only)
"""

from time import time
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.configs import get_logger
from app.models.job_tracking import JobStatus, JobType
from app.queueing.interfaces import JobPriority, JobResult, QueueBackend, QueueService
from app.services.jobs.factory import get_job_service

logger = get_logger(__name__)


class QueueServiceV2(QueueService):
    """
    V2 Queue Service with MongoDB job tracking.

    This service provides clean separation of concerns:
    - Queue operations: handled by QueueBackend (Redis)
    - Job tracking: handled by JobService (MongoDB only)
    """

    def __init__(self, backend: QueueBackend):
        """Initialize the V2 queue service with a backend."""
        super().__init__(backend)
        self._job_service = None

    async def get_job_service(self):
        """Get the job service instance."""
        if self._job_service is None:
            self._job_service = await get_job_service()
        return self._job_service

    async def initialize(self) -> None:
        """Initialize the V2 queue service."""
        await self.backend.initialize()
        await self.get_job_service()
        logger.info("V2 Queue Service initialized")

    async def cleanup(self) -> None:
        """Clean up the V2 queue service."""
        await self.backend.cleanup()
        if self._job_service:
            await self._job_service.cleanup()

    async def enqueue_job(
        self,
        job_func: str,
        job_args: Dict[str, Any],
        job_id: str,
        priority: JobPriority = JobPriority.NORMAL,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        # Job tracking parameters
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        job_type: Optional[Union[str, JobType]] = None,
        tracked_job_id: Optional[str] = None,
        dependencies: Optional[List[str]] = None,
    ) -> str:
        """
        Enqueue a job with proper job tracking integration.

        This method is used for direct job enqueueing without going through create_job().
        For new job creation, use create_job() instead.

        Args:
            job_func: Function name to execute
            job_args: Arguments to pass to the function
            priority: Job priority
            job_id: Required job ID (generated by JobCreatorService)
            delay_seconds: Delay before job becomes available
            retry_config: Retry configuration
            metadata: Additional metadata
            entity_type: Type of entity this job is associated with
            entity_id: ID of the entity
            job_type: Type of job for tracking
            tracked_job_id: Optional existing tracked job ID
            dependencies: List of job IDs this job depends on

        Returns:
            Job ID
        """
        # job_id should be provided - it's generated by JobCreatorService
        if not job_id:
            raise ValueError(
                "job_id is required - it should be generated by JobCreatorService"
            )

        # Step 1: Store dependencies in Redis if provided
        if dependencies:
            await self._store_job_dependencies_redis(job_id, dependencies)

        # Step 2: Enqueue to queue backend
        queue_job_id = await self.backend.enqueue(
            queue_name=priority.value,
            job_func=job_func,
            job_args=job_args,
            job_id=job_id,
            delay_seconds=delay_seconds,
            retry_config=retry_config,
        )

        # Step 3: Update job tracking if available
        if self._job_service and tracked_job_id:
            try:
                await self._job_service.update_job_status(
                    tracked_job_id, "queued", output={"queue_job_id": queue_job_id}
                )
            except Exception as e:
                logger.warning(
                    f"Failed to update job tracking for {tracked_job_id}: {e}"
                )

        logger.info(f"Job {job_id} enqueued successfully to {priority.value} queue")
        return job_id

    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get job status with proper job tracking integration."""
        # Step 1: Get queue job status from backend
        queue_result = await self.backend.get_job_status(job_id)
        if not queue_result:
            return None

        # Step 2: Try to find associated tracked job
        job_service = await self.get_job_service()
        tracked_job = None

        try:
            # Search for tracked job by queue job ID
            tracked_job = await job_service.get_job(job_id)

        except Exception as e:
            logger.warning(f"Error getting tracked job info for {job_id}: {e}")

        # Step 3: Create enhanced result with tracked job data
        if tracked_job:
            return JobResult(
                job_id=job_id,
                status=queue_result.status,
                result=queue_result.result,
                error_message=queue_result.error_message,
                started_at=queue_result.started_at,
                completed_at=queue_result.completed_at,
                processing_time=queue_result.processing_time,
                retry_count=queue_result.retry_count,
                progress=tracked_job.progress,  # type: ignore
            )

        return queue_result

    async def update_job_progress(self, job_id: str, progress) -> bool:
        """Update job progress with proper job tracking integration."""
        # Step 1: Update queue job progress (if backend supports it)
        queue_result = True
        if hasattr(self.backend, "update_job_progress"):
            queue_result = await self.backend.update_job_progress(job_id, progress)

        # Step 2: Update tracked job progress
        job_service = await self.get_job_service()
        tracked_result = False

        try:
            # Find tracked job by queue job ID
            tracked_job = await job_service.get_job(job_id)
            if tracked_job:
                tracked_result = await job_service.update_job_progress(
                    str(tracked_job.id),
                    progress,  # type: ignore
                )
            else:
                tracked_result = False
        except Exception as e:
            logger.warning(f"Error updating tracked job progress for {job_id}: {e}")
            tracked_result = False

        return queue_result and tracked_result

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job with proper job tracking integration."""
        # Step 1: Cancel queue job
        queue_result = await self.backend.cancel_job(job_id)

        # Step 2: Cancel tracked job
        job_service = await self.get_job_service()
        tracked_result = False

        try:
            # Find tracked job by queue job ID
            tracked_job = await job_service.get_job(job_id)
            if tracked_job:
                tracked_result = await job_service.cancel_job(str(tracked_job.id))
            else:
                tracked_result = False
        except Exception as e:
            logger.warning(f"Error canceling tracked job for {job_id}: {e}")
            tracked_result = False

        return queue_result and tracked_result

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a job with proper job tracking integration."""
        # Get job data to check retry logic
        job_data = await self.backend.get_job_data(job_id)
        if not job_data:
            return False

        retry_count = job_data.get("retry_count", 0)
        max_retries = job_data.get("retry_config", {}).get(
            "max_attempts", self.backend.max_retries
        )

        # Check if we should retry or move to DLQ
        if retry_count >= max_retries:
            # Move to DLQ in both Redis and MongoDB
            error_message = "Max retries exceeded"

            # Move to Redis DLQ
            await self.backend.move_to_dlq(job_id, error_message)

            # Move to MongoDB DLQ
            job_service = await self.get_job_service()
            try:
                await job_service.move_to_dlq(job_id, error_message)
                logger.info(
                    f"Job {job_id} moved to both Redis and MongoDB DLQ: {error_message}"
                )
            except Exception as e:
                logger.error(f"Failed to move job {job_id} to MongoDB DLQ: {e}")

            return False
        else:
            # Retry the job in both Redis and MongoDB
            queue_result = await self.backend.retry_job(job_id, delay_seconds)

            # Retry tracked job in MongoDB
            job_service = await self.get_job_service()
            tracked_result = False

            try:
                # Find tracked job by queue job ID
                tracked_job = await job_service.get_job(job_id)
                if tracked_job:
                    tracked_result = await job_service.retry_job(
                        str(tracked_job.id), delay_seconds
                    )
                else:
                    tracked_result = False
            except Exception as e:
                logger.warning(f"Error retrying tracked job for {job_id}: {e}")

            if queue_result and tracked_result:
                logger.info(
                    f"Job {job_id} retried successfully in both Redis and MongoDB"
                )
            else:
                logger.warning(f"Job {job_id} retry failed in one or both systems")

            return queue_result and tracked_result

    async def force_retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Force retry a job, bypassing max retries limit."""
        logger.info(f"Force retrying job {job_id}, bypassing max retries limit")

        # Force retry the job in Redis backend
        queue_result = await self.backend.retry_job(job_id, delay_seconds)

        if not queue_result:
            logger.error(f"Failed to force retry job {job_id} in Redis backend")
            return False

        # Try to update tracked job in MongoDB (optional - don't fail if this doesn't work)
        job_service = await self.get_job_service()
        tracked_result = False

        try:
            # Find tracked job by queue job ID
            tracked_job = await job_service.get_job(job_id)
            if tracked_job:
                tracked_result = await job_service.retry_job(str(job_id), delay_seconds)
                if tracked_result:
                    logger.info(f"Job {job_id} force retry updated in MongoDB tracking")
                else:
                    logger.warning(
                        f"Job {job_id} force retry failed in MongoDB tracking"
                    )
            else:
                logger.warning(
                    f"No tracked job found for {job_id} in MongoDB - continuing with Redis retry only"
                )
        except Exception as e:
            logger.warning(
                f"Error updating MongoDB tracking for force retry of {job_id}: {e}"
            )

        # Return success if Redis retry worked (MongoDB is optional)
        if queue_result:
            logger.info(
                f"Job {job_id} force retried successfully in Redis (MongoDB: {'success' if tracked_result else 'skipped/failed'})"
            )
            return True
        else:
            logger.error(f"Job {job_id} force retry failed in Redis backend")
            return False

    async def get_queue_stats(self, queue_name: Optional[str] = None):
        """Get queue statistics with proper job tracking integration."""
        # Step 1: Get queue stats from backend
        queue_stats = await self.backend.get_queue_stats(queue_name or "normal")

        # Step 2: Get job tracking stats
        job_service = await self.get_job_service()
        job_stats = None

        try:
            job_stats = await job_service.get_job_stats()
        except Exception as e:
            logger.warning(f"Error getting job tracking stats: {e}")

        # Step 3: Combine stats
        combined_stats = {
            "queue_stats": queue_stats,
            "job_tracking_stats": job_stats,
        }

        return combined_stats

    async def list_queues(self) -> List[str]:
        """List all available queues."""
        queues = await self.backend.list_queues()

        # Add DLQ if available
        try:
            job_service = await self.get_job_service()
            dlq_jobs = await job_service.get_dlq_jobs(limit=1)
            if dlq_jobs:
                queues.append("dlq")
        except Exception as e:
            logger.warning(f"Error checking DLQ: {e}")

        return queues

    async def purge_queue(self, queue_name: str) -> int:
        """Purge a queue with proper job tracking integration."""
        # Step 1: Purge queue
        purged_count = await self.backend.purge_queue(queue_name)

        # Step 2: Handle DLQ purging
        if queue_name == "dlq":
            try:
                job_service = await self.get_job_service()
                dlq_jobs = await job_service.get_dlq_jobs(limit=1000)
                for dlq_job in dlq_jobs:
                    await job_service.retry_dlq_job(dlq_job.tracked_job_id)
                purged_count = len(dlq_jobs)
            except Exception as e:
                logger.warning(f"Error purging DLQ: {e}")

        return purged_count

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check with proper job tracking integration."""
        # Step 1: Check queue backend health
        queue_health = await self.backend.health_check()

        # Step 2: Check job service health
        job_health = {}
        try:
            job_service = await self.get_job_service()
            job_stats = await job_service.get_job_stats()

            job_health = {
                "total_jobs": job_stats.total_jobs,
                "pending_jobs": job_stats.pending_jobs,
                "running_jobs": job_stats.running_jobs,
                "completed_jobs": job_stats.completed_jobs,
                "failed_jobs": job_stats.failed_jobs,
                "retrying_jobs": job_stats.retrying_jobs,
                "dead_letter_jobs": job_stats.dead_letter_jobs,
                "success_rate": job_stats.success_rate,
            }

            # Check for stuck jobs
            if job_stats.failed_jobs > 0 or job_stats.dead_letter_jobs > 0:
                queue_health["warnings"] = queue_health.get("warnings", [])
                queue_health["warnings"].append("Failed or dead letter jobs detected")

        except Exception as e:
            logger.warning(f"Error getting job service health: {e}")
            queue_health["warnings"] = queue_health.get("warnings", [])
            queue_health["warnings"].append(f"Error getting job service health: {e}")

        # Step 3: Combine health checks
        combined_health = {
            "queue_backend": queue_health,
            "job_tracking": job_health,
        }

        return combined_health

    # Job tracking specific methods

    async def get_tracked_job(self, tracked_job_id: str):
        """Get a tracked job by ID."""
        job_service = await self.get_job_service()
        return await job_service.get_job(tracked_job_id)

    async def get_entity_jobs(
        self,
        entity_type: str,
        entity_id: str,
        status: Optional[Union[str, JobStatus]] = None,
        job_type: Optional[Union[str, JobType]] = None,
    ):
        """Get all jobs for an entity."""
        job_service = await self.get_job_service()
        return await job_service.get_entity_jobs(
            entity_type, entity_id, status, job_type
        )

    async def get_dlq_jobs(self, limit: int = 100, offset: int = 0):
        """Get jobs from the dead letter queue."""
        job_service = await self.get_job_service()
        return await job_service.get_dlq_jobs(limit, offset)

    async def retry_dlq_job(self, tracked_job_id: str) -> bool:
        """Retry a job from the dead letter queue."""
        job_service = await self.get_job_service()
        return await job_service.retry_dlq_job(tracked_job_id)

    async def get_job_stats(
        self,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        job_type: Optional[Union[str, JobType]] = None,
        status: Optional[Union[str, JobStatus]] = None,
    ):
        """Get comprehensive job statistics."""
        job_service = await self.get_job_service()
        return await job_service.get_job_stats(entity_type, entity_id, job_type, status)

    async def complete_job(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Complete a job successfully.

        This method:
        1. Marks the job as completed in the queue backend
        2. Updates job tracking in MongoDB
        3. Marks the job as completed in Redis for dependency checking
        4. Triggers dependent jobs if any

        Args:
            job_id: ID of the job to complete
            result: Optional result data

        Returns:
            True if successful, False otherwise
        """
        try:
            # Step 1: Complete job in queue backend
            success = await self.backend.complete_job(job_id, result)
            if not success:
                logger.warning(f"Failed to complete job {job_id} in queue backend")
                return False

            # Step 2: Mark job as completed in Redis for dependency checking
            await self._mark_job_completed_redis(job_id)

            # Step 3: Update job tracking in MongoDB if available
            if self._job_service:
                try:
                    await self._job_service.update_job_status(
                        job_id, "completed", output={"result": result}
                    )
                except Exception as e:
                    logger.warning(f"Failed to update job tracking for {job_id}: {e}")

            logger.info(f"Job {job_id} completed successfully")
            return True

        except Exception as e:
            logger.error(f"Error completing job {job_id}: {e}")
            return False

    async def create_job(
        self,
        entity_type: str,
        entity_id: str,
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: JobPriority = JobPriority.NORMAL,
        parent_job_id: Optional[str] = None,
        depends_on: Optional[List[str]] = None,
        scheduled_at: Optional[str] = None,
        queue_priority: Optional[str] = None,
        queue_type: str = "default",
        queue_job_type: Optional[str] = None,
        job_group: Optional[str] = None,
        orchestration_pattern: Optional[str] = None,
        timeout: Optional[int] = None,
        retry: Optional[Dict[str, Any]] = None,
        triggers: Optional[List[str]] = None,
        wait_for: Optional[List[str]] = None,
        conditions: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Create a job with all parameters from the orchestrator.
        This is the single place where job creation happens.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            job_type: Type of job
            payload: Job payload
            metadata: Job metadata
            config: Job configuration
            priority: Job priority
            parent_job_id: Parent job ID
            depends_on: List of job IDs this job depends on
            scheduled_at: When to schedule the job
            queue_priority: Queue priority
            queue_type: Queue type
            queue_job_type: Queue job type
            job_group: Job group
            orchestration_pattern: Orchestration pattern
            timeout: Job timeout
            retry: Retry configuration
            triggers: Job triggers
            wait_for: Wait conditions
            conditions: Execution conditions

        Returns:
            Job ID
        """
        from app.services.jobs.job_creator import get_job_creator

        # Step 1: Create job using JobCreatorService (handles job_id generation and MongoDB storage)
        job_creator = await get_job_creator()
        tracked_job = await job_creator.create_job(
            entity_type=entity_type,
            entity_id=entity_id,
            job_type=job_type,
            payload=payload,
            metadata=metadata or {},
            config=config or {},
            priority=0,  # Default priority for job creator
            parent_job_id=parent_job_id,
            depends_on=depends_on or [],
            scheduled_at=int(time()) if scheduled_at else None,
            queue_priority=queue_priority
            or (priority.value if hasattr(priority, "value") else "normal"),
            queue_type=queue_type,
            queue_job_type=queue_job_type,
            job_group=job_group,
            orchestration_pattern=orchestration_pattern,
            timeout=timeout,
            retry=retry,
        )

        # Enqueue the job
        job_func = job_type.value if isinstance(job_type, JobType) else str(job_type)

        logger.info(
            f"Enqueuing job {tracked_job.job_id} for {entity_type}:{entity_id}, with priority {priority}"
        )

        await self.enqueue_job(
            job_func=job_func,
            job_args=payload,
            job_id=tracked_job.job_id,  # Use the job_id from tracked_job
            priority=priority,
            retry_config=retry,
            metadata={
                "tracked_job_id": str(tracked_job.id),  # Use the MongoDB _id
                "entity_type": entity_type,
                "entity_id": entity_id,
                "job_group": job_group,
                "orchestration_pattern": orchestration_pattern,
                "parent_job_id": parent_job_id,
                "depends_on": depends_on,
                "scheduled_at": scheduled_at,
                "queue_priority": queue_priority,
                "queue_type": queue_type,
                "queue_job_type": queue_job_type,
                "timeout": timeout,
                "triggers": triggers,
                "wait_for": wait_for,
                "conditions": conditions,
            },
            entity_type=entity_type,
            entity_id=entity_id,
            job_type=job_type,
            tracked_job_id=str(tracked_job.id),
            dependencies=depends_on,
        )

        logger.info(
            f"Created and enqueued job {tracked_job.job_id} for {entity_type}:{entity_id}"
        )
        return tracked_job.job_id

    async def fail_job(
        self, job_id: str, error_message: str, retry: bool = True
    ) -> bool:
        """Fail a job with proper job tracking integration."""
        # Fail job in queue backend (backend handles DLQ logic internally)
        job_data = await self.backend.get_job_data(job_id)
        if not job_data:
            return False

        queue_name = job_data.get("queue_name", "normal")
        retry_count = job_data.get("retry_count", 0)
        max_retries = job_data.get("retry_config", {}).get(
            "max_attempts", self.backend.max_retries
        )
        should_retry = retry and retry_count < max_retries
        result = await self.backend.fail_job(
            job_id, queue_name, error_message, retry, should_retry
        )
        if result:
            if should_retry:
                logger.info(
                    f"Job {job_id} failed and scheduled for retry: {error_message}"
                )

                # Update MongoDB tracked job status to RETRYING
                job_service = await self.get_job_service()
                try:
                    tracked_job = await job_service.get_job(job_id)
                    if tracked_job:
                        await job_service.update_job_status(
                            str(tracked_job.id), JobStatus.RETRYING, error=error_message
                        )
                        logger.info(
                            f"Job {job_id} status updated to RETRYING in MongoDB"
                        )
                except Exception as e:
                    logger.warning(
                        f"Failed to update job {job_id} status to RETRYING in MongoDB: {e}"
                    )
            else:
                logger.error(f"Job {job_id} failed permanently: {error_message}")
                await self.backend.move_to_dlq(job_id, error_message)

                # Move to MongoDB DLQ
                job_service = await self.get_job_service()
                try:
                    await job_service.move_to_dlq(job_id, error_message)
                    logger.info(
                        f"Job {job_id} moved to both Redis and MongoDB DLQ: {error_message}"
                    )
                except Exception as e:
                    logger.error(f"Failed to move job {job_id} to MongoDB DLQ: {e}")
            return True
        else:
            logger.warning(f"Failed to mark job {job_id} as failed atomically")
            return await self.backend.fallback_fail_job(
                job_id=job_id,
                error_message=error_message,
                retry=retry,
            )

    async def coordinate_child_job(
        self, child_job_id: str, parent_job_id: Optional[str] = None
    ) -> None:
        """
        Coordinate child job with parent job using dependency system.

        This method is called when a child job is created and needs to coordinate
        with its parent job. It adds the child job as a dependency to the parent
        and updates the parent job status accordingly.

        Args:
            child_job_id: ID of the child job that was just created
            parent_job_id: ID of the parent job (optional, can be extracted from child job)
        """
        try:
            if not self._job_service:
                self._job_service = await get_job_service()

            # If parent_job_id not provided, try to get it from the child job
            if not parent_job_id:
                child_job = await self._job_service.get_job(child_job_id)
                if child_job and child_job.parent_job_id:
                    parent_job_id = str(child_job.parent_job_id)
                else:
                    logger.debug(f"No parent_job_id found for child job {child_job_id}")
                    return

            if not parent_job_id:
                logger.debug(
                    f"No parent job to coordinate with for child job {child_job_id}"
                )
                return

            # Get the parent job
            parent_job = await self._job_service.get_job(parent_job_id)
            if not parent_job:
                logger.warning(
                    f"Parent job {parent_job_id} not found for child job {child_job_id}"
                )
                return

            # Add child job as dependency to parent job
            if child_job_id not in parent_job.depends_on:
                parent_job.add_dependency(UUID(child_job_id))

                # Update parent job status
                await self._job_service.update_job_status(
                    parent_job_id,
                    JobStatus.PENDING,  # Ensure it's pending if new dependency is added
                    output={
                        "dependency_added": child_job_id,
                        "completed_dependencies": parent_job.completed_dependencies,
                        "total_dependencies": parent_job.total_dependencies,
                    },
                )

                logger.info(
                    f"Added child job {child_job_id} as dependency to parent job {parent_job_id}",
                    completed_dependencies=parent_job.completed_dependencies,
                    total_dependencies=parent_job.total_dependencies,
                )

        except Exception as e:
            logger.error(
                f"Failed to coordinate child job {child_job_id} with parent {parent_job_id}: {e}"
            )

    async def mark_job_dependency_completed(
        self, completed_job_id: str, parent_job_id: Optional[str] = None
    ) -> None:
        """
        Mark a job dependency as completed and check if parent job is ready to execute.

        This method is called when a child job completes and needs to update
        its parent job's dependency status in both MongoDB and Redis.

        Args:
            completed_job_id: ID of the job that just completed
            parent_job_id: ID of the parent job (optional, can be extracted from completed job)
        """
        try:
            if not self._job_service:
                self._job_service = await get_job_service()

            # If parent_job_id not provided, try to get it from the completed job
            if not parent_job_id:
                completed_job = await self._job_service.get_job(completed_job_id)
                if completed_job and completed_job.parent_job_id:
                    parent_job_id = str(completed_job.parent_job_id)
                else:
                    logger.debug(
                        f"No parent_job_id found for completed job {completed_job_id}"
                    )
                    return

            if not parent_job_id:
                logger.debug(
                    f"No parent job to update for completed job {completed_job_id}"
                )
                return

            # Get the parent job
            parent_job = await self._job_service.get_job(parent_job_id)
            if not parent_job:
                logger.warning(
                    f"Parent job {parent_job_id} not found for completed job {completed_job_id}"
                )
                return

            # Check if this job is already a dependency (convert to UUID for comparison)
            from uuid import UUID

            completed_job_uuid = UUID(completed_job_id)

            # Check if the completed job is in the parent's dependencies
            dependency_found = False
            for dep_id in parent_job.depends_on:
                if str(dep_id) == completed_job_id:
                    dependency_found = True
                    break

            if dependency_found:
                # Mark dependency as completed
                parent_job.mark_dependency_completed(completed_job_uuid)

                # Update parent job status in MongoDB
                new_status = (
                    JobStatus.QUEUED
                    if parent_job.is_ready_to_execute()
                    else JobStatus.PENDING
                )
                await self._job_service.update_job_status(
                    parent_job_id,
                    new_status,
                    output={
                        "dependency_completed": completed_job_id,
                        "completed_dependencies": parent_job.completed_dependencies,
                        "total_dependencies": parent_job.total_dependencies,
                    },
                )

                logger.info(
                    f"Updated parent job {parent_job_id} dependency: {completed_job_id} completed",
                    completed_dependencies=parent_job.completed_dependencies,
                    total_dependencies=parent_job.total_dependencies,
                    new_status=new_status,
                )

                # If all dependencies are met, the job orchestrator will automatically trigger it
                if parent_job.is_ready_to_execute():
                    logger.info(
                        f"All dependencies met for parent job {parent_job_id}, ready to execute"
                    )
            else:
                logger.debug(
                    f"Job {completed_job_id} is not a dependency of parent job {parent_job_id}"
                )

        except Exception as e:
            logger.error(
                f"Failed to mark job dependency completed {completed_job_id} for parent {parent_job_id}: {e}"
            )

    async def dequeue_job(
        self,
        queue_names: List[str],
        timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """
        Dequeue a job with dependency checking and business logic.

        This method handles:
        1. Dequeueing jobs from the backend
        2. Checking dependencies using Redis as source of truth
        3. Putting jobs back in queue if dependencies not met
        4. Atomic operations to prevent race conditions

        Args:
            queue_names: List of queue names to try in priority order
            timeout: Timeout for blocking dequeue operations

        Returns:
            Job data if a job is successfully dequeued, None otherwise
        """
        logger.debug(f"Attempting to dequeue job from queues: {queue_names}")

        # Step 1: Try to dequeue a job from the backend
        job_data = await self.backend.dequeue(queue_names=queue_names, timeout=timeout)

        if not job_data:
            logger.debug("No jobs available in any queue")
            return None

        job_id = job_data.get("id")
        if not job_id:
            logger.warning("Dequeued job has no ID")
            return None

        logger.debug(f"Dequeued job {job_id} from backend")

        # Step 2: Check dependencies using Redis as source of truth
        if not await self._check_job_dependencies_redis(job_id):
            logger.debug(f"Job {job_id} dependencies not met, putting back in queue")

            # Put job back in the original queue
            queue_name = job_data.get("queue_name", "normal")
            await self.backend.enqueue(
                queue_name=queue_name,
                job_func=job_data.get("func", ""),
                job_args=job_data.get("args", {}),
                job_id=job_id,
                delay_seconds=0,
                retry_config=job_data.get("retry_config"),
            )

            # Try to dequeue another job recursively
            return await self.dequeue_job(queue_names, timeout=0)

        logger.info(f"Job {job_id} successfully dequeued with all dependencies met")
        return job_data

    async def _check_job_dependencies_redis(self, job_id: str) -> bool:
        """
        Check job dependencies using Redis as the source of truth.

        This method checks Redis for job dependencies and ensures all
        required dependencies are completed before allowing the job to be dequeued.

        Args:
            job_id: ID of the job to check dependencies for

        Returns:
            True if all dependencies are met, False otherwise
        """
        try:
            # Get Redis connection from backend
            if hasattr(self.backend, "get_connection"):
                conn = await self.backend.get_connection()
                if conn is None:
                    logger.warning("Could not get Redis connection")
                    return True
            else:
                logger.warning("Backend does not support get_connection method")
                return True

            # Check if job has dependencies stored in Redis
            dependency_key = f"{self.backend.key_prefix}:dependencies:{job_id}"
            dependencies = await conn.smembers(dependency_key)  # type: ignore

            if not dependencies:
                logger.debug(f"Job {job_id} has no dependencies in Redis")
                return True

            logger.debug(f"Job {job_id} has {len(dependencies)} dependencies to check")

            # Check each dependency
            for dep_job_id in dependencies:
                # Check if dependency job exists and is completed
                dep_status_key = f"{self.backend.key_prefix}:job:{dep_job_id}:status"
                dep_status = await conn.get(dep_status_key)  # type: ignore

                if not dep_status:
                    logger.debug(f"Dependency job {dep_job_id} not found in Redis")
                    return False

                if dep_status != "completed":
                    logger.debug(
                        f"Dependency job {dep_job_id} not completed (status: {dep_status})"
                    )
                    return False

            logger.debug(f"All dependencies met for job {job_id}")
            return True

        except Exception as e:
            logger.error(f"Error checking dependencies for job {job_id}: {e}")
            # If we can't check dependencies, allow the job to proceed
            # This prevents the system from getting stuck due to dependency check failures
            return True

    async def _store_job_dependencies_redis(
        self, job_id: str, dependencies: List[str]
    ) -> None:
        """
        Store job dependencies in Redis for fast dependency checking.

        Args:
            job_id: ID of the job
            dependencies: List of dependency job IDs
        """
        try:
            if not dependencies:
                return

            # Get Redis connection from backend
            if hasattr(self.backend, "get_connection"):
                conn = await self.backend.get_connection()
                if conn is None:
                    logger.warning("Could not get Redis connection")
                    return
            else:
                logger.warning("Backend does not support get_connection method")
                return

            dependency_key = f"{self.backend.key_prefix}:dependencies:{job_id}"

            # Store dependencies as a set
            for dep_job_id in dependencies:
                await conn.sadd(dependency_key, dep_job_id)  # type: ignore

            # Set expiration for dependency key (7 days)
            await conn.expire(dependency_key, 7 * 24 * 3600)  # type: ignore

            logger.debug(
                f"Stored {len(dependencies)} dependencies for job {job_id} in Redis"
            )

        except Exception as e:
            logger.error(f"Error storing dependencies for job {job_id}: {e}")

    async def _mark_job_completed_redis(self, job_id: str) -> None:
        """
        Mark a job as completed in Redis for dependency checking.

        Args:
            job_id: ID of the completed job
        """
        try:
            # Get Redis connection from backend
            if hasattr(self.backend, "get_connection"):
                conn = await self.backend.get_connection()
                if conn is None:
                    logger.warning("Could not get Redis connection")
                    return
            else:
                logger.warning("Backend does not support get_connection method")
                return

            status_key = f"{self.backend.key_prefix}:job:{job_id}:status"

            # Mark job as completed
            await conn.set(status_key, "completed")  # type: ignore

            # Set expiration for status key (7 days)
            await conn.expire(status_key, 7 * 24 * 3600)  # type: ignore

            logger.debug(f"Marked job {job_id} as completed in Redis")

        except Exception as e:
            logger.error(f"Error marking job {job_id} as completed in Redis: {e}")
