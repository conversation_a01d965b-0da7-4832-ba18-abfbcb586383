"""
V2 Redis Queue Backend with Atomic Operations and Enhanced Features

This module provides a V2 Redis backend implementation that includes:
- Atomic LUA scripts to prevent race conditions
- Dead letter queue support
- Enhanced retry logic with exponential backoff
- Proper QueueBackend interface implementation
"""

import json
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

import redis.asyncio as redis
from redis.asyncio.client import Redis as AsyncRedis

from app.configs import get_logger
from app.models.job_tracking import (
    DeadLetterJob,
    JobError,
    JobProgress,
    JobResult,
    JobStatus,
    JobType,
    QueueStats,
)
from app.queueing.interfaces import QueueBackend

logger = get_logger(__name__)


class RedisBackendV2(QueueBackend):
    """
    V2 Redis backend with atomic operations and enhanced features.

    This backend properly implements the QueueBackend interface and provides:
    - Atomic LUA scripts for race condition prevention
    - Dead letter queue support
    - Enhanced retry logic
    - Proper job tracking integration
    """

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "tx_datapipelines",
        dlq_enabled: bool = True,
        max_retries: int = 0,  # TODO: remove this, and make it 3
        retry_backoff_factor: float = 2.0,
        retry_max_backoff: int = 3600,
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.dlq_enabled = dlq_enabled
        self.max_retries = max_retries
        self.retry_backoff_factor = retry_backoff_factor
        self.retry_max_backoff = retry_max_backoff

        self._connection: Optional[AsyncRedis] = None
        self.queues = ["high", "normal", "low"]
        self.dlq_queue = f"{key_prefix}:dlq"

        # LUA script storage for atomic operations
        self.lua_scripts: Dict[str, str] = {}
        self.lua_script_shas: Dict[str, str] = {}

    async def get_connection(self) -> AsyncRedis:
        """Get Redis connection (lazy initialization)."""
        if not self._connection:
            self._connection = redis.from_url(self.redis_url, decode_responses=True)
        return self._connection

    async def initialize(self) -> None:
        """Initialize Redis queues, DLQ, and LUA scripts."""
        conn = await self.get_connection()

        # Initialize priority queues
        for queue in self.queues:
            queue_key = f"{self.key_prefix}:queue:{queue}"
            await conn.lpush(queue_key, "")  # type: ignore
            await conn.lpop(queue_key)  # type: ignore

        # Initialize DLQ if enabled
        if self.dlq_enabled:
            await conn.lpush(self.dlq_queue, "")  # type: ignore
            await conn.lpop(self.dlq_queue)  # type: ignore

        # Load LUA scripts for atomic operations
        await self._load_lua_scripts()

        logger.info("V2 Redis backend initialized with atomic operations")

    async def cleanup(self) -> None:
        """Clean up backend resources."""
        if self._connection:
            await self._connection.close()  # type: ignore
            self._connection = None

    async def cleanup_stale_locks(self) -> int:
        """Clean up stale locks that have expired."""
        conn = await self.get_connection()
        pattern = f"{self.key_prefix}:lock:*"

        try:
            # Get all lock keys
            keys = await conn.keys(pattern)  # type: ignore
            cleaned_count = 0

            for key in keys:
                # Check if lock is still valid (has TTL > 0)
                ttl = await conn.ttl(key)  # type: ignore
                if ttl <= 0:
                    await conn.delete(key)  # type: ignore
                    cleaned_count += 1

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} stale locks")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up stale locks: {e}")
            return 0

    async def _load_lua_scripts(self) -> None:
        """Load LUA scripts for atomic operations."""
        conn = await self.get_connection()

        # Atomic dequeue script - prevents race conditions
        self.lua_scripts["atomic_dequeue"] = """
            local queue_keys = {}
            local processing_keys = {}
            local queue_names = {}
            local num_queues = tonumber(ARGV[1])
            local timeout = tonumber(ARGV[2])
            local current_time = ARGV[3]
            
            -- Build queue and processing keys from arguments
            for i = 1, num_queues do
                queue_keys[i] = KEYS[i]
                processing_keys[i] = KEYS[i + num_queues]
                queue_names[i] = ARGV[3 + i]  -- Queue names passed as additional args
            end
            
            -- Try each queue in priority order
            for i = 1, num_queues do
                local queue_key = queue_keys[i]
                local processing_key = processing_keys[i]
                local queue_name = queue_names[i]
                
                -- Try to pop a job from this queue
                local job_id
                if timeout > 0 then
                    -- Use blocking pop with timeout
                    local result = redis.call('BRPOP', queue_key, timeout)
                    if result then
                        job_id = result[2]  -- BRPOP returns {key, value}
                    end
                else
                    -- Use non-blocking pop
                    job_id = redis.call('RPOP', queue_key)
                end
                
                if job_id then
                    -- Successfully got a job, move to processing atomically
                    redis.call('LPUSH', processing_key, job_id)
                    
                    return {job_id, queue_name}
                end
            end
            
            return nil  -- No jobs available in any queue
        """

        # Atomic complete job script
        self.lua_scripts["atomic_complete"] = """
            local processing_key = KEYS[1]
            local stats_key = KEYS[2]
            local job_key = KEYS[3]
            
            local job_id = ARGV[1]
            local result_data = ARGV[2]
            local completed_at = ARGV[3]
            local processing_time = ARGV[4]
            
            -- Remove from processing queue
            local removed = redis.call('LREM', processing_key, 0, job_id)
            
            if removed > 0 then
                -- Update job data with result
                redis.call('HSET', job_key, 
                    'status', 'completed',
                    'result', result_data,
                    'completed_at', completed_at,
                    'processing_time', processing_time
                )
                
                -- Update stats
                redis.call('HINCRBY', stats_key, 'running_count', -1)
                redis.call('HINCRBY', stats_key, 'completed_count', 1)
                redis.call('HINCRBY', stats_key, 'total_processed', 1)
                
                return 1
            end
            
            return 0
        """

        # Atomic fail job script with retry logic
        self.lua_scripts["atomic_fail"] = """
            local processing_key = KEYS[1]
            local job_key = KEYS[2]
            local stats_key = KEYS[3]
            local queue_key = KEYS[4]
            local delayed_key = KEYS[5]
            
            local job_id = ARGV[1]
            local error_message = ARGV[2]
            local current_time = ARGV[3]
            local should_retry = ARGV[4]
            local retry_delay = ARGV[5]
            
            -- Remove from processing queue
            local removed = redis.call('LREM', processing_key, 0, job_id)
            
            if removed > 0 then
                -- Update job data with error
                redis.call('HSET', job_key, 
                    'error_message', error_message,
                    'failed_at', current_time
                )
                
                if should_retry == '1' then
                    -- Increment retry count
                    redis.call('HINCRBY', job_key, 'retry_count', 1)
                    
                    -- Mark as retrying and re-queue
                    redis.call('HSET', job_key, 'status', 'retrying')
                    
                    if tonumber(retry_delay) > 0 then
                        -- Add to delayed queue for retry
                        local retry_at = tonumber(current_time) + tonumber(retry_delay)
                        redis.call('ZADD', delayed_key, retry_at, job_id)
                    else
                        -- Retry immediately
                        redis.call('LPUSH', queue_key, job_id)
                        redis.call('HINCRBY', stats_key, 'pending_count', 1)
                    end
                    
                    redis.call('HINCRBY', stats_key, 'running_count', -1)
                    return 1
                else
                    -- Mark as permanently failed
                    redis.call('HSET', job_key, 'status', 'failed')
                    
                    -- Update stats
                    redis.call('HINCRBY', stats_key, 'running_count', -1)
                    redis.call('HINCRBY', stats_key, 'failed_count', 1)
                    
                    return 0
                end
            end
            
            return 0
        """

        try:
            # Load scripts and store their SHA hashes
            for script_name, script_code in self.lua_scripts.items():
                sha = await conn.script_load(script_code)
                self.lua_script_shas[script_name] = sha
                logger.info(f"Loaded LUA script '{script_name}' with SHA: {sha[:8]}...")

        except Exception as e:
            logger.error(f"Failed to load LUA scripts: {e}")
            logger.warning("Will fall back to direct script execution")

    async def _execute_lua_script(
        self, script_name: str, keys: List[str], args: List[str]
    ) -> Any:
        """Execute a LUA script with fallback to direct execution."""
        conn = await self.get_connection()

        # Ensure all keys and args are strings
        str_keys = [str(k) for k in keys]
        str_args = [str(a) for a in args]

        try:
            # Try to use pre-loaded script SHA
            script_sha = self.lua_script_shas.get(script_name)
            if script_sha:
                try:
                    return await conn.evalsha(
                        script_sha, len(str_keys), *str_keys, *str_args
                    )  # type: ignore
                except Exception as e:
                    logger.warning(
                        f"Failed to execute LUA script SHA {script_name}, falling back: {e}"
                    )

            # Fallback: execute script directly
            script = self.lua_scripts.get(script_name)
            if script:
                return await conn.eval(script, len(str_keys), *str_keys, *str_args)  # type: ignore
            else:
                raise ValueError(f"LUA script '{script_name}' not found")

        except Exception as e:
            logger.error(f"Error executing LUA script '{script_name}': {e}")
            raise

    async def enqueue(
        self,
        queue_name: str,
        job_func: Union[str, JobType],
        job_args: Dict[str, Any],
        job_id: str,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Enqueue a job with atomic operations."""
        conn = await self.get_connection()

        job_func = job_func.value if isinstance(job_func, JobType) else job_func

        # Create job data
        job_data = {
            "id": job_id,
            "func": job_func,
            "args": job_args,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "retry_count": 0,
            "retry_config": retry_config or {},
            "status": "pending",
            "queue_name": queue_name,
        }

        # Store job data with JSON serialization
        job_key = f"{self.key_prefix}:job:{job_id}"

        # Convert dict values to JSON strings for Redis storage
        redis_data = {}
        for key, value in job_data.items():
            if isinstance(value, dict):
                try:
                    redis_data[key] = json.dumps(value)
                except Exception as e:
                    logger.error(f"Failed to serialize {key} to JSON: {e}")
                    redis_data[key] = json.dumps(str(value))
            else:
                redis_data[key] = str(value)

        await conn.hset(job_key, mapping=redis_data)  # type: ignore

        # Set TTL for job data (24 hours)
        await conn.expire(job_key, 86400)

        # Add to queue
        queue_key = f"{self.key_prefix}:queue:{queue_name}"
        if delay_seconds > 0:
            # Use sorted set for delayed jobs
            delayed_key = f"{self.key_prefix}:delayed:{queue_name}"
            score = time.time() + delay_seconds
            await conn.zadd(delayed_key, {job_id: score})
        else:
            await conn.lpush(queue_key, job_id)  # type: ignore

        logger.info(f"Job {job_id} enqueued to {queue_name}")
        return job_id

    async def dequeue(
        self,
        queue_names: List[str],
        timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """Dequeue a job with atomic operations."""

        # Check for delayed jobs first
        delayed_jobs = await self._process_delayed_jobs()
        if delayed_jobs:
            return delayed_jobs[0]

        conn = await self.get_connection()

        # Try each queue in priority order
        for queue_name in queue_names:
            queue_key = f"{self.key_prefix}:queue:{queue_name}"
            processing_key = f"{self.key_prefix}:processing:{queue_name}"

            try:
                # Use BRPOP for blocking operation or RPOP for non-blocking
                if timeout > 0:
                    result = await conn.brpop(queue_key, timeout=timeout)  # type: ignore
                    if not result:
                        continue
                    job_id = result[1] if isinstance(result, tuple) else result
                else:
                    job_id = await conn.rpop(queue_key)  # type: ignore
                    if not job_id:
                        continue

                # Ensure job_id is a string
                if not isinstance(job_id, str):
                    logger.warning(f"Invalid job_id type: {type(job_id)}")
                    continue

                # Use SETNX to create a lock for this specific job
                # This ensures only one worker can process this job
                lock_key = f"{self.key_prefix}:lock:{job_id}"
                lock_acquired = await conn.setnx(lock_key, "1")  # type: ignore

                if not lock_acquired:
                    # Another worker is already processing this job
                    # Put it back in the queue and try the next queue
                    await conn.lpush(queue_key, job_id)  # type: ignore
                    logger.warning(
                        f"RACE CONDITION PREVENTED: Job {job_id} already being processed, skipping"
                    )
                    continue

                # Set lock expiration (5 minutes)
                await conn.expire(lock_key, 300)  # type: ignore
                logger.debug(f"Lock acquired for job {job_id}")

                # Add to processing queue
                await conn.lpush(processing_key, job_id)  # type: ignore

                # Get job data
                job_data = await self.get_job_data(job_id)
                if job_data:
                    # Update job status
                    job_data["status"] = "running"
                    job_data["started_at"] = datetime.now(timezone.utc).isoformat()
                    await self._update_job_data(job_id, job_data)

                    logger.info(f"Job {job_id} dequeued from {queue_name} with lock")
                    return job_data
                else:
                    # Job data not found, clean up
                    await conn.lrem(processing_key, 0, job_id)  # type: ignore
                    await conn.delete(lock_key)  # type: ignore
                    logger.warning(
                        f"Job data not found for {job_id}, removed from processing"
                    )

            except Exception as e:
                logger.error(f"Error dequeuing from {queue_name}: {e}")
                continue

        return None

    async def _fallback_dequeue(
        self,
        queue_names: List[str],
        timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """Fallback dequeue method without atomic operations."""
        conn = await self.get_connection()

        # Try to get job from queues in priority order
        for queue_name in queue_names:
            queue_key = f"{self.key_prefix}:queue:{queue_name}"

            if timeout > 0:
                result = await conn.brpop(queue_key, timeout=timeout)  # type: ignore
            else:
                result = await conn.rpop(queue_key)  # type: ignore

            if result:
                job_id = result[1] if isinstance(result, tuple) else result

                # Get job data
                if isinstance(job_id, str):
                    job_data = await self.get_job_data(job_id)
                else:
                    job_data = None
                if job_data:
                    # Update job status
                    job_data["status"] = "running"
                    job_data["started_at"] = datetime.now(timezone.utc).isoformat()
                    if isinstance(job_id, str):
                        await self._update_job_data(job_id, job_data)
                    else:
                        logger.error(f"Job ID is not a string: {job_id}")

                    return job_data

        return None

    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get job status."""
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return None

        return JobResult(
            job_id=job_id,
            status=JobStatus(job_data.get("status", "pending")),
            result=job_data.get("result"),
            error_message=job_data.get("error_message"),
            started_at=self._parse_datetime(job_data.get("started_at")),
            completed_at=self._parse_datetime(job_data.get("completed_at")),
            processing_time=job_data.get("processing_time"),
            retry_count=job_data.get("retry_count", 0),
        )

    async def update_job_progress(self, job_id: str, progress: JobProgress) -> bool:
        """Update job progress."""
        # This is a placeholder - in a real implementation, you'd store progress
        # in the job data or in a separate progress tracking system
        logger.info(f"Updated progress for job {job_id}: {progress}")
        return True

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job."""
        conn = await self.get_connection()

        # Update job status
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return False

        job_data["status"] = "cancelled"
        job_data["cancelled_at"] = datetime.now(timezone.utc).isoformat()
        await self._update_job_data(job_id, job_data)

        # Remove from any queues
        for queue in self.queues:
            queue_key = f"{self.key_prefix}:queue:{queue}"
            await conn.lrem(queue_key, 0, job_id)  # type: ignore

        return True

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job with exponential backoff."""
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return False

        retry_count = job_data.get("retry_count", 0)

        # Calculate backoff delay if not provided
        if delay_seconds == 0:
            retry_config = job_data.get("retry_config", {})
            backoff_factor = retry_config.get(
                "backoff_factor", self.retry_backoff_factor
            )
            initial_delay = retry_config.get("initial_delay_seconds", 5)
            delay_seconds = min(
                initial_delay * (backoff_factor**retry_count), self.retry_max_backoff
            )

        # Update retry count and status
        job_data["retry_count"] = retry_count + 1
        job_data["status"] = "retrying"
        job_data["retry_at"] = datetime.now(timezone.utc).isoformat()
        await self._update_job_data(job_id, job_data)

        # Re-enqueue with delay (don't pass job_id to avoid creating new job entry)
        queue_name = job_data.get("queue_name", "normal")
        conn = await self.get_connection()

        if delay_seconds > 0:
            # Use sorted set for delayed jobs
            delayed_key = f"{self.key_prefix}:delayed:{queue_name}"
            score = time.time() + delay_seconds
            await conn.zadd(delayed_key, {job_id: score})
        else:
            # Add to queue immediately
            queue_key = f"{self.key_prefix}:queue:{queue_name}"
            await conn.lpush(queue_key, job_id)  # type: ignore

        logger.info(
            f"Job {job_id} scheduled for retry in {delay_seconds}s (attempt {retry_count + 1})"
        )
        return True

    async def complete_job(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Complete a job successfully."""
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return False

        queue_name = job_data.get("queue_name", "normal")
        processing_key = f"{self.key_prefix}:processing:{queue_name}"
        lock_key = f"{self.key_prefix}:lock:{job_id}"

        try:
            # Remove from processing queue
            conn = await self.get_connection()
            removed = await conn.lrem(processing_key, 0, job_id)  # type: ignore

            if removed > 0:
                # Calculate processing time
                started_at = self._parse_datetime(job_data.get("started_at"))
                processing_time = None
                if started_at:
                    processing_time = (
                        datetime.now(timezone.utc) - started_at
                    ).total_seconds()

                # Update job data
                job_data["status"] = "completed"
                job_data["result"] = result
                job_data["completed_at"] = datetime.now(timezone.utc).isoformat()
                job_data["processing_time"] = processing_time
                await self._update_job_data(job_id, job_data)

                # Release the lock
                await conn.delete(lock_key)  # type: ignore

                logger.info(f"Job {job_id} completed successfully")
                return True
            else:
                logger.warning(f"Job {job_id} not found in processing queue")
                return False

        except Exception as e:
            logger.error(f"Error completing job {job_id}: {e}")
            return False

    async def _fallback_complete_job(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Fallback complete job method without atomic operations."""
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return False

        # Calculate processing time
        started_at = self._parse_datetime(job_data.get("started_at"))
        processing_time = None
        if started_at:
            processing_time = (datetime.now(timezone.utc) - started_at).total_seconds()

        # Update job data
        job_data["status"] = "completed"
        job_data["result"] = result
        job_data["completed_at"] = datetime.now(timezone.utc).isoformat()
        job_data["processing_time"] = processing_time
        await self._update_job_data(job_id, job_data)

        return True

    async def fail_job(
        self,
        job_id: str,
        queue_name: str,
        error_message: str,
        retry: bool = True,
        should_retry: bool = True,
        retry_delay: int = 30,
    ) -> bool:
        """Fail a job."""

        try:
            # Remove from processing queue
            conn = await self.get_connection()
            processing_key = f"{self.key_prefix}:processing:{queue_name}"
            lock_key = f"{self.key_prefix}:lock:{job_id}"
            removed = await conn.lrem(processing_key, 0, job_id)  # type: ignore

            if removed > 0:
                # Update job data with error
                job_data = await self.get_job_data(job_id)
                if job_data:
                    job_data["error_message"] = error_message
                    job_data["failed_at"] = datetime.now(timezone.utc).isoformat()

                    if should_retry:
                        # Increment retry count and re-queue
                        job_data["retry_count"] = job_data.get("retry_count", 0) + 1
                        job_data["status"] = "retrying"
                        await self._update_job_data(job_id, job_data)

                        # Re-enqueue with delay
                        await self.retry_job(job_id, retry_delay)
                    else:
                        # Mark as permanently failed
                        job_data["status"] = "failed"
                        await self._update_job_data(job_id, job_data)

                        # Move to DLQ if enabled
                        if self.dlq_enabled:
                            await self.move_to_dlq(job_id, error_message)

                # Release the lock
                await conn.delete(lock_key)  # type: ignore

                logger.info(f"Job {job_id} failed successfully")
                return True
            else:
                logger.warning(f"Job {job_id} not found in processing queue")
                return False

        except Exception as e:
            logger.error(f"Error failing job {job_id}: {e}")
            return False

    async def fallback_fail_job(
        self,
        job_id: str,
        error_message: str,
        retry: bool = True,
    ) -> bool:
        """Fallback fail job method without atomic operations."""
        job_data = await self.get_job_data(job_id)
        if not job_data:
            return False

        # Update job data
        job_data["status"] = "failed"
        job_data["error_message"] = error_message
        job_data["failed_at"] = datetime.now(timezone.utc).isoformat()
        await self._update_job_data(job_id, job_data)

        # Handle retry or move to DLQ
        if retry:
            return await self.retry_job(job_id)
        else:
            await self.move_to_dlq(job_id, error_message)
            return True

    async def get_queue_stats(self, queue_name: str) -> QueueStats:
        """Get comprehensive queue statistics."""
        conn = await self.get_connection()

        # Get queue lengths
        queue_key = f"{self.key_prefix}:queue:{queue_name}"
        pending_count = await conn.llen(queue_key)  # type: ignore

        # Get running jobs (jobs with status "running")
        running_count = await self._count_jobs_by_status("running", queue_name)

        # Get completed/failed counts from stats
        stats = await self._get_job_stats(queue_name)

        return QueueStats(
            queue_name=queue_name,
            pending_count=pending_count,
            running_count=running_count,
            completed_count=stats.get("completed", 0),
            failed_count=stats.get("failed", 0),
            total_processed=stats.get("total", 0),
            avg_processing_time=stats.get("avg_processing_time"),
            last_job_processed=self._parse_datetime(stats.get("last_processed")),
        )

    async def list_queues(self) -> List[str]:
        """List all available queues."""
        queues = self.queues.copy()
        if self.dlq_enabled:
            queues.append("dlq")
        return queues

    async def purge_queue(self, queue_name: str) -> int:
        """Purge all jobs from a queue."""
        conn = await self.get_connection()

        if queue_name == "dlq":
            queue_key = self.dlq_queue
        else:
            queue_key = f"{self.key_prefix}:queue:{queue_name}"

        # Get all job IDs in the queue
        job_ids = await conn.lrange(queue_key, 0, -1)  # type: ignore

        # Remove job data
        for job_id in job_ids:
            job_key = f"{self.key_prefix}:job:{job_id}"
            await conn.delete(job_key)

        # Clear the queue
        await conn.delete(queue_key)

        logger.info(f"Purged {len(job_ids)} jobs from queue {queue_name}")
        return len(job_ids)

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        try:
            conn = await self.get_connection()
            await conn.ping()

            # Check queue health
            queue_health = {}
            for queue in self.queues:
                queue_key = f"{self.key_prefix}:queue:{queue}"
                length = await conn.llen(queue_key)  # type: ignore
                queue_health[queue] = {"length": length}

            return {
                "status": "healthy",
                "redis_connected": True,
                "queues": queue_health,
                "dlq_enabled": self.dlq_enabled,
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "redis_connected": False,
            }

    # Helper methods

    async def get_job_data(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job data from Redis."""
        conn = await self.get_connection()
        job_key = f"{self.key_prefix}:job:{job_id}"

        job_data = await conn.hgetall(job_key)  # type: ignore
        if not job_data:
            return None

        # Convert bytes to strings (Redis returns bytes)
        decoded_data = {}
        for key, value in job_data.items():
            if isinstance(value, bytes):
                decoded_data[key] = value.decode("utf-8")
            else:
                decoded_data[key] = value

        # Reverse the JSON string conversion that was done during enqueue
        # All dict values were converted to JSON strings, all other values to strings
        for key, value in decoded_data.items():
            if value and isinstance(value, str):
                # Try to parse as JSON first (for dict fields)
                try:
                    parsed_value = json.loads(value)
                    decoded_data[key] = parsed_value
                except json.JSONDecodeError:
                    # If it's not valid JSON, try to convert back to original type
                    if value.lower() in ["true", "false"]:
                        decoded_data[key] = value.lower() == "true"
                    elif value.isdigit():
                        decoded_data[key] = int(value)
                    elif (
                        value.replace(".", "").replace("-", "").isdigit()
                        and value.count(".") == 1
                    ):
                        decoded_data[key] = float(value)
                    # Otherwise keep as string (for fields like 'id', 'func', 'status', etc.)

        return decoded_data

    async def _update_job_data(self, job_id: str, job_data: Dict[str, Any]) -> None:
        """Update job data in Redis."""
        conn = await self.get_connection()
        job_key = f"{self.key_prefix}:job:{job_id}"

        # Convert dict values to JSON strings for Redis storage
        updates = {}
        for key, value in job_data.items():
            if isinstance(value, dict):
                try:
                    updates[key] = json.dumps(value)
                except Exception as e:
                    logger.error(f"Failed to serialize {key} to JSON: {e}")
                    updates[key] = json.dumps(str(value))
            else:
                updates[key] = str(value)

        await conn.hset(job_key, mapping=updates)  # type: ignore

    async def _process_delayed_jobs(self) -> List[Dict[str, Any]]:
        """Process delayed jobs that are ready to run."""
        conn = await self.get_connection()
        processed_jobs = []

        for queue_name in self.queues:
            delayed_key = f"{self.key_prefix}:delayed:{queue_name}"

            # Get jobs ready to run
            ready_jobs = await conn.zrangebyscore(delayed_key, 0, time.time())

            for job_id in ready_jobs:
                # Remove from delayed set
                await conn.zrem(delayed_key, job_id)

                # Get job data
                job_data = await self.get_job_data(job_id)
                if job_data:
                    # Add to appropriate queue
                    queue_key = f"{self.key_prefix}:queue:{queue_name}"
                    await conn.lpush(queue_key, job_id)  # type: ignore

                    processed_jobs.append(job_data)

        return processed_jobs

    async def move_to_dlq(self, job_id: str, reason: str) -> None:
        """Move a job to the dead letter queue."""
        if not self.dlq_enabled:
            return

        conn = await self.get_connection()
        job_data = await self.get_job_data(job_id)
        logger.debug(f"DLQ Job data: {job_data}")

        if job_data:
            # Convert job type from enum format to string format
            job_type = job_data.get("func", "")
            if job_type.startswith("JobType."):
                # Convert "JobType.COMPANY_RESOLVE_CRUNCHBASE" to "company.resolve.crunchbase"
                job_type = job_type.replace("JobType.", "").lower()
                # Convert from SNAKE_CASE to dot notation
                job_type = job_type.replace("_", ".")

            # Handle tracked_job_id - if None, create a default UUID
            tracked_job_id = job_data.get("tracked_job_id")
            if tracked_job_id is None:
                # Create a default UUID for jobs without tracked_job_id
                tracked_job_id = uuid.uuid4()

            try:
                # Create DLQ entry with proper error handling
                dlq_job = DeadLetterJob(
                    job_id=str(job_id),
                    tracked_job_id=tracked_job_id,
                    job_type=job_type,
                    entity_type=job_data.get("args", {}).get("entity_type", "unknown"),
                    entity_id=job_data.get("args", {}).get("entity_id", "unknown"),
                    reason=reason,
                    error_count=job_data.get("retry_count", 0),
                    last_error=JobError(
                        error_type="JobFailure",
                        error_message=job_data.get("error_message", reason),
                        error_traceback=None,
                        retry_count=job_data.get("retry_count", 0),
                    ),
                    retry_after=None,
                    original_payload=job_data.get("args", {}),
                    metadata={
                        "queue_name": job_data.get("queue_name"),
                        "retry_config": job_data.get("retry_config", {}),
                        "created_at": job_data.get("created_at"),
                        "started_at": job_data.get("started_at"),
                        "failed_at": job_data.get("failed_at"),
                        "retry_at": job_data.get("retry_at"),
                    },
                )

                # Store in DLQ
                dlq_key = f"{self.dlq_queue}:{job_id}"
                dlq_dict = dlq_job.model_dump(for_redis=True)

                await conn.hset(dlq_key, mapping=dlq_dict)  # type: ignore

                # Add to DLQ list
                await conn.lpush(self.dlq_queue, job_id)  # type: ignore

                logger.warning(f"Job {job_id} moved to DLQ: {reason}")

            except Exception as e:
                logger.error(f"Failed to move job {job_id} to DLQ: {e}")
                # Fallback: just log the failure and continue
                # The job will remain in the failed state but won't be in DLQ

    async def _get_job_stats(self, queue_name: str) -> Dict[str, Any]:
        """Get job statistics for a queue."""
        conn = await self.get_connection()
        stats_key = f"{self.key_prefix}:stats:{queue_name}"

        stats = await conn.hgetall(stats_key)  # type: ignore
        return {k: int(v) if v.isdigit() else v for k, v in stats.items()}

    async def _count_jobs_by_status(
        self, status: str, queue_name: Optional[str] = None
    ) -> int:
        """Count jobs by status."""
        # This is a simplified implementation
        # In production, you might want to use Redis sets or sorted sets for better performance
        return 0  # Placeholder

    async def _check_job_dependencies(self, job_id: str) -> bool:
        """
        Check if a job's dependencies are met before dequeuing.

        This method checks MongoDB for job dependencies and ensures all
        required dependencies are completed before allowing the job to be dequeued.

        Args:
            job_id: ID of the job to check dependencies for

        Returns:
            True if all dependencies are met, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from app.services.jobs.factory import get_job_service

            job_service = await get_job_service()
            if not job_service:
                logger.warning("Job service not available, skipping dependency check")
                return True  # Allow job to proceed if we can't check dependencies

            # Get the job from MongoDB
            tracked_job = await job_service.get_job(job_id)
            if not tracked_job:
                logger.debug(f"Job {job_id} not found in MongoDB, allowing to proceed")
                return True  # Allow job to proceed if not tracked in MongoDB

            # Check if job has dependencies
            if not tracked_job.depends_on or len(tracked_job.depends_on) == 0:
                logger.debug(f"Job {job_id} has no dependencies, allowing to proceed")
                return True

            # Check if all dependencies are completed
            for dep_job_id in tracked_job.depends_on:
                dep_job = await job_service.get_job(str(dep_job_id))
                if not dep_job:
                    logger.warning(
                        f"Dependency job {dep_job_id} not found for job {job_id}"
                    )
                    return False

                if dep_job.status != JobStatus.COMPLETED:
                    logger.debug(
                        f"Job {job_id} dependency {dep_job_id} not completed (status: {dep_job.status})"
                    )
                    return False

            logger.debug(f"All dependencies met for job {job_id}")
            return True

        except Exception as e:
            logger.error(f"Error checking dependencies for job {job_id}: {e}")
            # If we can't check dependencies, allow the job to proceed
            # This prevents the system from getting stuck due to dependency check failures
            return True

    def _parse_datetime(self, dt_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string."""
        if not dt_str:
            return None

        try:
            return datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        except ValueError:
            return None
