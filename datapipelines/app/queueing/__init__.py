"""
TractionX Data Pipeline Service - Queue System

This package provides a modular, priority-aware queue system with support for:
- Multiple priority queues (high, normal, low)
- Round-robin job processing
- Pluggable backends (RQ, Celery, Kafka, etc.)
- Comprehensive monitoring and health checks
- Watchdog support for development
"""

from app.queueing.factory import create_queue_service
from app.queueing.interfaces import QueueBackend, QueueService, QueueStats
from app.queueing.service import QueueServiceV2

__all__ = [
    "QueueService",
    "QueueServiceV2",
    "QueueBackend",
    "QueueStats",
    "create_queue_service",
]
