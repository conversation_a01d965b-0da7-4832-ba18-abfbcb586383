"""
Queue Service Factory

This module provides factory functions for creating queue services with different
backend configurations.
"""

from typing import Any, Optional

from app.configs import get_logger, settings
from app.queueing.backends.v2_redis_backend import RedisBackendV2
from app.queueing.interfaces import QueueBackend
from app.queueing.service import QueueServiceV2

logger = get_logger(__name__)


async def create_redis_backend(
    redis_url: Optional[str] = None,
    key_prefix: str = settings.REDIS_KEY_PREFIX,
    enhanced: bool = True,
) -> Any:
    """
    Create a Redis queue backend.

    Args:
        redis_url: Redis connection URL (defaults to settings.redis_connection_string)
        key_prefix: Prefix for Redis keys
        enhanced: Whether to use the enhanced backend with DLQ and atomic operations

    Returns:
        Configured Redis backend
    """
    if redis_url is None:
        redis_url = settings.redis_connection_string

    if enhanced:
        backend = RedisBackendV2(
            redis_url=redis_url,
            key_prefix=key_prefix,
            dlq_enabled=True,
            max_retries=3,
            retry_backoff_factor=2.0,
            retry_max_backoff=3600,
        )
    else:
        backend = RedisBackendV2(
            redis_url=redis_url,
            key_prefix=key_prefix,
            dlq_enabled=False,
            max_retries=3,
            retry_backoff_factor=2.0,
            retry_max_backoff=3600,
        )

    return backend


async def create_queue_service(
    backend: Optional[QueueBackend] = None, backend_type: str = "redis", **kwargs
) -> QueueServiceV2:
    """
    Create a queue service with the specified backend.

    Args:
        backend: Pre-configured backend instance
        backend_type: Type of backend to create ("redis")
        **kwargs: Additional arguments for backend creation

    Returns:
        Configured queue service
    """
    if backend is None:
        if backend_type == "redis":
            backend = await create_redis_backend(**kwargs)  # type: ignore
        else:
            raise ValueError(f"Unsupported backend type: {backend_type}")

    service = QueueServiceV2(backend)  # type: ignore
    logger.info(f"Created V2 queue service with {backend_type} backend")

    return service
