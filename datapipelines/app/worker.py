#!/usr/bin/env python3
"""
Worker Runner for TractionX Data Pipeline Service

This script starts the new round-robin queue worker with proper configuration,
monitoring, and graceful shutdown handling.
"""

import argparse
import asyncio
import os
import sys
from typing import Optional

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.configs import configure_logging, get_logger, settings
from app.queueing.factory import create_redis_backend
from app.queueing.service import QueueServiceV2

# Configure logging first
configure_logging()
logger = get_logger(__name__)


class SimpleRoundRobinWorker:
    """
    Simplified round-robin worker that works with the current codebase.
    """

    def __init__(
        self,
        worker_id: str = "worker-1",
        concurrency: int = 4,
        poll_interval: float = 1.0,
    ):
        self.worker_id = worker_id
        self.concurrency = concurrency
        self.poll_interval = poll_interval
        self.running = False
        self.queue_service: Optional[QueueServiceV2] = None

        # Priority queues in order (high to low)
        self.queue_priorities = ["high", "normal", "low"]

        # Load job handlers
        from app.tasks import get_job_handlers

        self.handlers = get_job_handlers()

        logger.info(
            f"Worker {self.worker_id} initialized",
        )

    async def initialize(self) -> None:
        """Initialize the worker."""
        # Create enhanced Redis backend using factory
        backend = await create_redis_backend(
            redis_url=settings.redis_connection_string,
            key_prefix=settings.REDIS_KEY_PREFIX,
            enhanced=True,
        )

        # Create V2 queue service
        from app.queueing.service import QueueServiceV2

        self.queue_service = QueueServiceV2(backend)  # type: ignore
        await self.queue_service.initialize()

        # Initialize job orchestrator for dependency management
        from app.services.jobs.orchestrator import get_job_orchestrator

        self._orchestrator = await get_job_orchestrator()

        logger.info(f"Worker {self.worker_id} queue service initialized")

    async def start(self) -> None:
        """Start the worker."""
        if not self.queue_service:
            await self.initialize()

        self.running = True
        logger.info(f"Starting worker {self.worker_id}")

        # Start worker tasks
        tasks = []
        for i in range(self.concurrency):
            task = asyncio.create_task(self._worker_loop(f"{self.worker_id}-{i}"))
            tasks.append(task)

        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.running = False
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _worker_loop(self, task_id: str) -> None:
        """Main worker loop."""
        logger.info(f"Worker task {task_id} started")

        while self.running:
            try:
                # Try to get a job using round-robin priority
                if self.queue_service:
                    # Use the service's dequeue method which handles dependency checking
                    job_data = await self.queue_service.dequeue_job(
                        queue_names=self.queue_priorities,
                        timeout=int(self.poll_interval),
                    )

                    if job_data:
                        await self._process_job(job_data, task_id)
                    else:
                        # No jobs available, brief sleep
                        await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info(f"Worker task {task_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker task {task_id} error: {e}", exc_info=True)
                await asyncio.sleep(1)

        logger.info(f"Worker task {task_id} stopped")

    async def _process_job(self, job_data: dict, task_id: str) -> None:
        """Process a single job."""
        job_id = job_data.get("id", "unknown")
        job_func = job_data.get("func")
        job_args = job_data.get("args", {})

        if not job_func:
            error_msg = f"Job {job_id} has no job_func specified"
            logger.error(error_msg)
            if self.queue_service:
                try:
                    await self.queue_service.fail_job(job_id, error_msg, retry=False)
                except Exception as queue_error:
                    logger.error(
                        f"Failed to mark job {job_id} as failed in queue: {queue_error}"
                    )
            else:
                logger.error(
                    f"No queue service available to mark job {job_id} as failed"
                )
            return

        logger.info(f"Processing job {job_id}: {job_func}")

        # Handle barrier-aware job processing
        meta = None
        logger.info("Job args structure: ", job_args=job_args)
        if isinstance(job_args, dict) and "meta" in job_args:
            meta = job_args.get("meta")
            job_args = job_args.get("args", {})
            logger.info("Extracted meta: ", meta=meta)
            logger.info("Extracted job_args: ", job_args=job_args)
        else:
            logger.info("No meta found in job_args, checking job_data structure")
            logger.info("Full job_data: ", job_data=job_data)

        try:
            # Convert job type from enum format to string format (same as DLQ conversion)
            job_func_normalized = job_func
            if job_func.startswith("JobType."):
                # Convert "JobType.COMPANY_RESOLVE_CRUNCHBASE" to "company.resolve.crunchbase"
                job_func_normalized = job_func.replace("JobType.", "").lower()
                # Convert from SNAKE_CASE to dot notation
                job_func_normalized = job_func_normalized.replace("_", ".")

            # Find handler
            handler = self.handlers.get(job_func_normalized)
            if not handler:
                raise ValueError(
                    f"No handler found for: {job_func} (normalized: {job_func_normalized})"
                )

            # Execute handler (now async)
            result = await handler(job_args)
            logger.info("Result: ", result=result)
            logger.info("job_data : ", job_data=job_data)

            # Handle job completion with dependency management
            if meta and "tracked_job_id" in meta:
                tracked_job_id = meta["tracked_job_id"]

                logger.info(f"Processing tracked job completion for {tracked_job_id}")

                try:
                    # Update tracked job status
                    from app.services.jobs.factory import get_job_service

                    job_service = await get_job_service()

                    await job_service.update_job_status(
                        tracked_job_id, "completed", output={"result": result}
                    )

                    # Handle job completion and trigger dependent jobs
                    # task_result = {
                    #     "job_id": job_id,
                    #     "result": result,
                    #     "metadata": meta,
                    #     "job_type": job_func,
                    # }

                    logger.info(
                        f"Tracked job {tracked_job_id} completed and dependencies checked"
                    )
                except Exception as e:
                    error_msg = (
                        f"Failed to handle job completion for {tracked_job_id}: {e}"
                    )
                    logger.error(error_msg)
                    # Mark the job as failed since completion handling failed
                    if self.queue_service:
                        try:
                            await self.queue_service.fail_job(
                                job_id, error_msg, retry=True
                            )
                        except Exception as queue_error:
                            logger.error(
                                f"Failed to mark job {job_id} as failed in queue: {queue_error}"
                            )
                    else:
                        logger.error(
                            f"No queue service available to mark job {job_id} as failed"
                        )
                    return
            else:
                logger.debug(f"Job {job_id} is not tracked (meta: {meta})")

            # Mark as completed
            if self.queue_service:
                try:
                    await self.queue_service.complete_job(job_id, result)
                    logger.info(f"Job {job_id} completed successfully")
                except Exception as queue_error:
                    logger.error(
                        f"Failed to mark job {job_id} as completed in queue: {queue_error}"
                    )
            else:
                logger.error(
                    f"No queue service available to mark job {job_id} as completed"
                )

        except Exception as e:
            error_msg = f"Job {job_id} failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Handle tracked job failure
            if meta and "tracked_job_id" in meta:
                tracked_job_id = meta["tracked_job_id"]

                try:
                    # Update tracked job status to failed
                    from app.services.jobs.factory import get_job_service

                    job_service = await get_job_service()

                    await job_service.update_job_status(
                        tracked_job_id, "failed", error=error_msg
                    )

                    logger.info(f"Tracked job {tracked_job_id} marked as failed")
                except Exception as e:
                    logger.error(
                        f"Failed to update tracked job {tracked_job_id} status: {e}"
                    )
                    # Continue with queue failure handling even if tracked job update fails

            # Mark job as failed in queue
            if self.queue_service:
                try:
                    await self.queue_service.fail_job(job_id, error_msg, retry=True)
                except Exception as queue_error:
                    logger.error(
                        f"Failed to mark job {job_id} as failed in queue: {queue_error}"
                    )
            else:
                logger.error(
                    f"No queue service available to mark job {job_id} as failed"
                )


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="TractionX Data Pipeline Worker")
    parser.add_argument("--worker-id", default="worker-1", help="Worker ID")
    parser.add_argument(
        "--concurrency", type=int, default=4, help="Number of concurrent workers"
    )
    parser.add_argument(
        "--poll-interval", type=float, default=1.0, help="Poll interval in seconds"
    )

    args = parser.parse_args()

    logger.info("Starting TractionX Data Pipeline Worker")
    logger.info(f"Worker ID: {args.worker_id}")
    logger.info(f"Concurrency: {args.concurrency}")
    logger.info(f"Poll Interval: {args.poll_interval}s")

    worker = SimpleRoundRobinWorker(
        worker_id=args.worker_id,
        concurrency=args.concurrency,
        poll_interval=args.poll_interval,
    )

    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
