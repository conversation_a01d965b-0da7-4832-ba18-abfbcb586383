"""
Founder Spider Chart ETL Processor for TractionX Data Pipeline Service.

This processor handles the generation of founder signals and spider chart data
by analyzing founder experiences, education, and skills using LLM.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.clients.openai_client import OpenAIClient
from app.configs import get_logger
from app.models.founder import (
    FounderSignalInput,
    FounderSignalOutput,
    SkillProfile,
    SpiderChartData,
)
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class SpiderChartProcessor:
    """Processor for generating founder spider chart data."""

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds_storage = rds_storage
        self.s3_storage = s3_storage
        self.ai_service = OpenAIClient()
        self.logger = get_logger(__name__)

    async def process_founder_signals(
        self, founder_id: str, org_id: str, company_id: str
    ) -> Dict[str, Any]:
        """
        Process founder signals and generate spider chart data.

        Args:
            founder_id: Founder ID from founder.basic table
            org_id: Organization ID
            company_id: Company ID

        Returns:
            Processed founder signals with spider chart data
        """
        try:
            # Fetch founder data from database
            founder_data = await self._fetch_founder_data(founder_id)
            if not founder_data:
                raise ValueError(f"Founder not found: {founder_id}")

            # Fetch related data (experiences, education, skills)
            experiences = await self._fetch_founder_experiences(founder_id)
            education = await self._fetch_founder_education(founder_id)
            skills = await self._fetch_founder_skills(founder_id)

            # Create input for LLM
            signal_input = self._create_signal_input(
                founder_data, experiences, education, skills
            )

            # Generate signals using LLM
            signal_output = await self._generate_signals(signal_input)

            # Store results
            stored_data = await self._store_spider_chart_data(
                founder_id, signal_output, org_id, company_id
            )

            return {
                "success": True,
                "founder_id": founder_id,
                "signal_output": signal_output.model_dump(),
                "stored_data": stored_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing founder signals: {e}")
            raise

    async def _fetch_founder_data(self, founder_id: str) -> Optional[Dict[str, Any]]:
        """Fetch founder basic data from database."""
        try:
            query = """
                SELECT id, founder_id, full_name, first_name, last_name, 
                       headline, summary, current_job_title, current_job_company,
                       location, social_profiles, source, confidence_score
                FROM founder.basic 
                WHERE id = $1
            """
            result = await self.rds_storage.execute_query(
                query, {"founder_id": founder_id}
            )
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"Error fetching founder data: {e}")
            return None

    async def _fetch_founder_experiences(self, founder_id: str) -> List[Dict[str, Any]]:
        """Fetch founder experiences from database."""
        try:
            query = """
                SELECT company_name, title, industry, company_size, 
                       start_date, end_date, is_primary, location, description
                FROM founder.experiences 
                WHERE founder_id = $1
                ORDER BY start_date DESC NULLS LAST
            """
            results = await self.rds_storage.execute_query(
                query, {"founder_id": founder_id}
            )
            return results
        except Exception as e:
            self.logger.error(f"Error fetching founder experiences: {e}")
            return []

    async def _fetch_founder_education(self, founder_id: str) -> List[Dict[str, Any]]:
        """Fetch founder education from database."""
        try:
            query = """
                SELECT school_name, degrees, majors, start_date, end_date, 
                       location, description
                FROM founder.education 
                WHERE founder_id = $1
                ORDER BY start_date DESC NULLS LAST
            """
            results = await self.rds_storage.execute_query(
                query, {"founder_id": founder_id}
            )
            return results
        except Exception as e:
            self.logger.error(f"Error fetching founder education: {e}")
            return []

    async def _fetch_founder_skills(self, founder_id: str) -> List[str]:
        """Fetch founder skills from database."""
        try:
            query = """
                SELECT skill 
                FROM founder.skills 
                WHERE founder_id = $1
                ORDER BY skill
            """
            results = await self.rds_storage.execute_query(
                query, {"founder_id": founder_id}
            )
            return [row["skill"] for row in results]
        except Exception as e:
            self.logger.error(f"Error fetching founder skills: {e}")
            return []

    def _create_signal_input(
        self,
        founder_data: Dict[str, Any],
        experiences: List[Dict[str, Any]],
        education: List[Dict[str, Any]],
        skills: List[str],
    ) -> FounderSignalInput:
        """Create signal input from founder data."""
        full_name = founder_data.get("full_name", "")
        current_job = ""
        if founder_data.get("current_job_title") and founder_data.get(
            "current_job_company"
        ):
            current_job = f"{founder_data['current_job_title']} at {founder_data['current_job_company']}"

        return FounderSignalInput(
            full_name=full_name,
            current_job=current_job,
            experiences=experiences,
            education=education,
            skills=skills,
            profiles=[],  # Will be populated if needed
        )

    async def _generate_signals(
        self, signal_input: FounderSignalInput
    ) -> FounderSignalOutput:
        """Generate founder signals using LLM."""
        try:
            # Create prompt for LLM
            prompt = self._create_llm_prompt(signal_input)

            # Call LLM service
            messages = [
                {
                    "role": "system",
                    "content": "You are an expert investor analyzing founder profiles.",
                },
                {"role": "user", "content": prompt},
            ]

            response = await self.ai_service.create_chat_completion(
                messages=messages,
                model="gpt-4o",
                max_tokens=1000,
                temperature=0.3,
            )

            # Parse LLM response
            signal_output = self._parse_llm_response(response)
            return signal_output

        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            # Return default output if LLM fails
            return self._create_default_signal_output(signal_input.full_name)

    def _create_llm_prompt(self, signal_input: FounderSignalInput) -> str:
        """Create LLM prompt for signal generation."""
        prompt = f"""
                You are an expert investor analyzing a founder's profile. Based on the following information, generate a comprehensive founder signal analysis.

                Founder Information:
                {signal_input.to_llm_prompt()}

                Please analyze this founder and provide:

                1. Overall Score (0-100): A comprehensive score based on experience, education, skills, and potential
                2. Key Strengths (3-5 items): Evidence-based strengths that would appeal to investors
                3. Key Risks (3-5 items): Investor-relevant risks or concerns
                4. High-Signal Tags (max 5): Punchy, scoped tags that capture the founder's profile
                5. Skill Profile (0-10 for each category):
                - Business: Business acumen, strategy, market understanding
                - Operations: Operational experience, management, execution
                - Fundraising: Fundraising experience, investor relations, financial acumen
                - Product: Product development, technical product skills, user understanding
                - Tech: Technical expertise, engineering background, technical skills

                Respond in the following JSON format:
                {{
                    "score": 85,
                    "strengths": ["Strong technical background", "Previous startup experience", "Relevant industry experience"],
                    "risks": ["Limited fundraising experience", "No previous exits", "Small network"],
                    "tags": ["Technical Founder", "B2B SaaS", "AI/ML"],
                    "skill_profile": {{
                        "business": 7,
                        "operations": 6,
                        "fundraising": 4,
                        "product": 8,
                        "tech": 9
                    }}
                }}"""
        return prompt

    def _parse_llm_response(self, response: Dict[str, Any]) -> FounderSignalOutput:
        """Parse LLM response into FounderSignalOutput."""
        try:
            # Extract content from response
            content = (
                response.get("choices", [{}])[0].get("message", {}).get("content", "")
            )
            if not content:
                raise ValueError("No content in LLM response")

            # Extract JSON from response
            import re

            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")

            # Create SkillProfile
            skill_profile = SkillProfile(
                business=data["skill_profile"]["business"],
                operations=data["skill_profile"]["operations"],
                fundraising=data["skill_profile"]["fundraising"],
                product=data["skill_profile"]["product"],
                tech=data["skill_profile"]["tech"],
            )

            # Create FounderSignalOutput
            return FounderSignalOutput(
                score=data["score"],
                strengths=data["strengths"],
                risks=data["risks"],
                tags=data["tags"],
                skill_profile=skill_profile,
            )

        except Exception as e:
            self.logger.error(f"Error parsing LLM response: {e}")
            raise ValueError(f"Invalid LLM response format: {e}")

    def _create_default_signal_output(self, full_name: str) -> FounderSignalOutput:
        """Create default signal output when LLM fails."""
        skill_profile = SkillProfile(
            business=5,
            operations=5,
            fundraising=5,
            product=5,
            tech=5,
        )

        return FounderSignalOutput(
            score=50,
            strengths=["Profile analysis pending"],
            risks=["Limited data available"],
            tags=["Analysis Pending"],
            skill_profile=skill_profile,
        )

    async def _store_spider_chart_data(
        self,
        founder_id: str,
        signal_output: FounderSignalOutput,
        org_id: str,
        company_id: str,
    ) -> Dict[str, Any]:
        """Store spider chart data in database."""
        try:
            # Prepare data for storage
            spider_chart_data = SpiderChartData(
                founder_id=founder_id,
                score=signal_output.score,
                tags=signal_output.tags,
                strengths=signal_output.strengths,
                risks=signal_output.risks,
                business_score=signal_output.skill_profile.business,
                operations_score=signal_output.skill_profile.operations,
                fundraising_score=signal_output.skill_profile.fundraising,
                product_score=signal_output.skill_profile.product,
                tech_score=signal_output.skill_profile.tech,
                updated_at=datetime.now(timezone.utc),
            )

            # Upsert spider chart data
            await self.rds_storage.upsert(
                "founder.spider_chart",
                spider_chart_data.model_dump(for_rds=True),
                key_fields=["founder_id"],
            )

            return spider_chart_data.model_dump()

        except Exception as e:
            self.logger.error(f"Error storing spider chart data: {e}")
            raise
