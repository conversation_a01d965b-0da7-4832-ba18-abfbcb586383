"""
This is a processor for LinkedIn enrichment data.
It is used to clean and structure the data for the database.
It is also used to extract skills using LLM.
It is used to create a founder record.
It is used to extract experiences and education.
It is used to extract skills.
It is used to store the data in the database.
"""

import json
from datetime import date, datetime, timezone
from typing import Any, Dict, Optional
from urllib.parse import urlparse
from uuid import NAMESPACE_DNS, uuid5

import aiohttp
from app.configs import get_logger
from app.ctx.job.linkedin.founder import LinkedInJobContext
from app.models.founder import (
    FounderEducation,
    FounderExperience,
    FounderRecord,
    FounderSkill,
)
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class BrightDataLinkedInDataProcessor:
    """
    Comprehensive processor for LinkedIn enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.LinkedInDataProcessor")

    async def process(
        self,
        payload: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: LinkedInJobContext,
    ) -> Dict[str, Any]:
        """
        Process a complete LinkedIn payload and store in normalized schema.

        Args:
            payload: Complete LinkedIn payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload
            metadata = job_context._asdict()

            # Validate required fields
            validation_result = self._validate_linkedin_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "founder_id": metadata.get("founder_id"),
                }

            # Generate founder UUID
            founder_uuid = self._generate_founder_uuid(
                enrichment_data.get("name", ""), enrichment_data.get("url", "")
            )

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                enrichment_data, metadata, founder_uuid, s3_raw_data_key
            )
            self.logger.info(f"Cleaned data: {cleaned_data}")

            # Store in database with transaction
            await self._store_founder_data(cleaned_data)

            self.logger.info(
                f"Successfully processed LinkedIn data for founder {founder_uuid}"
            )

            return {
                "success": True,
                "founder_uuid": founder_uuid,
                "founder_id": metadata.get("founder_id"),
                "records_created": {
                    "experiences": len(cleaned_data["experiences"]),
                    "education": len(cleaned_data["education"]),
                    "skills": len(cleaned_data["skills"]),
                },
            }

        except Exception as e:
            error_msg = f"LinkedIn processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "founder_id": metadata.get("founder_id"),
            }

    def _validate_linkedin_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate required fields in LinkedIn data."""
        errors = []

        # Check metadata
        required_metadata = ["founder_id", "company_id", "org_id"]
        for field in required_metadata:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check enrichment data
        full_name = enrichment_data.get("name", "").strip()
        linkedin_url = enrichment_data.get("url", "").strip()

        if not full_name:
            errors.append("Missing or empty full_name")

        if not linkedin_url:
            errors.append("Missing LinkedIn URL")

        return {"valid": len(errors) == 0, "errors": errors}

    def _generate_founder_uuid(self, name: str, linkedin_url: str) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL."""
        full_name = name.strip().lower()
        linkedin_url = linkedin_url.strip().lower()

        # Create deterministic string for UUID generation
        uuid_string = f"{full_name}|{linkedin_url}"

        # Generate UUIDv5 using DNS namespace
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    async def _clean_enrichment_data(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and structure enrichment data for database storage."""

        # Download avatar if available
        avatar_s3_url = None
        if enrichment_data.get("avatar"):
            avatar_s3_url = await self._extract_and_download_avatar(
                enrichment_data, metadata["founder_id"]
            )
            if avatar_s3_url:
                # Update enrichment_data with S3 URL
                enrichment_data["avatar"] = avatar_s3_url

        # Founder record is now created earlier for skill extraction

        # Extract and clean related data
        experiences = await self._extract_experiences(
            enrichment_data, metadata["founder_id"]
        )
        education = await self._extract_education(
            enrichment_data, metadata["founder_id"]
        )

        # Create founder record first for skill extraction
        founder_record = await self._create_founder_record(
            enrichment_data, metadata, founder_uuid, s3_raw_data_key
        )

        # Extract skills using LLM
        skills = await self._extract_skills(founder_record, experiences, education)

        return {
            "founder": founder_record,
            "experiences": experiences,
            "education": education,
            "skills": skills,
        }

    async def _create_founder_record(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> FounderRecord:
        """Create a clean FounderRecord from LinkedIn data."""

        # Clean name fields
        full_name = self._clean_string(enrichment_data.get("name"))
        first_name = self._clean_string(enrichment_data.get("first_name"))
        last_name = self._clean_string(enrichment_data.get("last_name"))

        # Clean job info
        current_job_title = self._clean_string(
            metadata.get("data", {}).get("current_job_title")
            or enrichment_data.get("experience")[0].get("title")  # type: ignore
        )
        current_job_company = self._clean_string(
            metadata.get("data", {}).get("current_company")
            or enrichment_data.get("experience")[0].get("company")  # type: ignore
        )

        # Clean URLs
        linkedin_url = self._clean_url(enrichment_data.get("url"))
        social_profiles = {
            "linkedin": linkedin_url,
            "github": "",
        }

        # Clean location
        location = self._clean_dict({
            "city": enrichment_data.get("city", ""),
            "country": enrichment_data.get("country_code", ""),
        })

        # Clean additional fields
        avatar_url = self._clean_url(enrichment_data.get("avatar"))
        headline = self._clean_string(enrichment_data.get("position"))
        summary = self._clean_string(enrichment_data.get("about"))

        # Parse enrichment date
        enrichment_date = None
        if enrichment_data.get("timestamp"):
            try:
                enrichment_date = datetime.fromisoformat(
                    enrichment_data["timestamp"].replace("Z", "+00:00")
                )
            except (ValueError, AttributeError):
                pass

        # Calculate confidence score
        confidence_score = metadata.get("confidence_score", 1.0)
        links = enrichment_data.get("bio_links", [])
        now = datetime.now(timezone.utc)
        honours = enrichment_data.get("honors_and_awards", [])

        return FounderRecord(
            id=founder_uuid,
            founder_id=metadata["founder_id"],
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            avatar_url=avatar_url,
            headline=headline,
            summary=summary,
            location=location,
            current_job_title=current_job_title,
            current_job_company=current_job_company,
            social_profiles=social_profiles,
            org_id=metadata["org_id"],
            company_id=metadata["company_id"],
            source="linkedin",
            confidence_score=confidence_score,
            enrichment_date=enrichment_date,
            s3_raw_data_key={"linkedin": s3_raw_data_key},
            created_at=now,
            updated_at=now,
            links={"from_linkedin": links},
            extra_data={"honors": honours},
        )

    async def _extract_experiences(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> list[FounderExperience]:
        """Extract and clean experience records from both flat and grouped entries."""
        experiences = []
        experience_list = enrichment_data.get("experience", [])

        for exp_data in experience_list:
            if not isinstance(exp_data, dict):
                continue

            # Handle grouped positions inside one company (LinkedIn format)
            if "positions" in exp_data:
                positions = exp_data.get("positions", [])
                for pos in positions:
                    experience = await self._build_experience_record(
                        founder_id=founder_id, exp_data=pos, parent_data=exp_data
                    )
                    if experience:
                        experiences.append(experience)
            else:
                experience = await self._build_experience_record(
                    founder_id=founder_id, exp_data=exp_data
                )
                if experience:
                    experiences.append(experience)

        return experiences

    async def _build_experience_record(
        self,
        founder_id: str,
        exp_data: Dict[str, Any],
        parent_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[FounderExperience]:
        """Build a single FounderExperience object from experience data."""
        company_info = (
            parent_data.get("company") if parent_data else exp_data.get("company")
        )
        title = exp_data.get("title")
        if isinstance(title, dict):
            title = title.get("name")

        company_name = self._clean_string(company_info)

        # URLs and IDs
        company_url = self._clean_url(
            exp_data.get("url") or (parent_data.get("url") if parent_data else None)
        )
        company_logo_url = self._clean_url(
            exp_data.get("company_logo_url")
            or (parent_data.get("company_logo_url") if parent_data else None)
        )
        c_id = str(
            uuid5(
                NAMESPACE_DNS,
                f"{exp_data.get('title', '')}-{company_name}-{exp_data.get('start_date', '')}",
            )
        )

        # Download logo if needed
        if company_logo_url and c_id:
            downloaded_logo_url = await self._download_company_logo(
                company_logo_url, c_id
            )
            if downloaded_logo_url:
                company_logo_url = downloaded_logo_url

        # Industry and size from company_info
        industry = None
        company_size = None
        industry = self._clean_string(exp_data.get("industry", ""))
        company_size = self._clean_string(exp_data.get("size", ""))

        return FounderExperience(
            id=str(
                uuid5(
                    NAMESPACE_DNS,
                    f"{exp_data.get('title', '')}-{company_name}-{exp_data.get('start_date', '')}",
                )
            ),
            founder_id=founder_id,
            company_name=company_name,
            title=self._clean_string(title),
            industry=industry,
            company_size=company_size,
            company_url=company_url,
            company_logo_url=company_logo_url,
            start_date=self._parse_date(exp_data.get("start_date")),
            end_date=self._parse_date(exp_data.get("end_date")),
            is_primary=exp_data.get("is_primary", False),
            location=self._extract_location_string(exp_data.get("location")),
            description=self._clean_string(
                exp_data.get("description") or exp_data.get("description_html")
            ),
        )

    async def _extract_education(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> list[FounderEducation]:
        """Extract and clean education records."""
        education_records = []
        education_list = enrichment_data.get("education", [])

        for edu_data in education_list:
            if not isinstance(edu_data, dict):
                continue

            # Extract school information
            school_name = self._clean_string(edu_data.get("title"))
            school_url = self._clean_url(edu_data.get("url"))
            institute_logo_url = self._clean_url(edu_data.get("institute_logo_url"))

            # Skip entries without school name
            if not school_name:
                continue

            # Download institute logo if available
            if institute_logo_url:
                # Generate a simple institute ID from school name
                institute_id = (
                    school_name.lower()
                    .replace(" ", "-")
                    .replace(",", "")
                    .replace(".", "")
                )
                downloaded_logo_url = await self._download_institute_logo(
                    institute_logo_url, institute_id
                )
                if downloaded_logo_url:
                    institute_logo_url = downloaded_logo_url

            # Clean degree and major lists - filter out None values
            degree = self._clean_string(edu_data.get("degree"))
            degrees = [degree] if degree else []

            majors = [
                m for m in edu_data.get("majors", []) if m and self._clean_string(m)
            ]

            # Parse dates from year format
            start_date = self._parse_year_to_date(edu_data.get("start_year"))
            end_date = self._parse_year_to_date(edu_data.get("end_year"))

            education = FounderEducation(
                id=str(uuid5(NAMESPACE_DNS, f"{edu_data.get('id', '')}")),
                founder_id="",  # Will be set during storage
                school_name=school_name,
                degrees=degrees,
                majors=majors,
                school_url=school_url,
                institute_logo_url=institute_logo_url,
                start_date=start_date,
                end_date=end_date,
                location=self._extract_location_string(edu_data.get("location")),
                description=self._clean_string(edu_data.get("description")),
            )
            education_records.append(education)

        return education_records

    async def _extract_and_download_avatar(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> Optional[str]:
        """
        Extract and download avatar from LinkedIn data.

        Args:
            enrichment_data: LinkedIn enrichment data containing avatar URL
            founder_id: Founder ID for S3 path generation

        Returns:
            S3 URL of the stored avatar, or None if failed
        """
        avatar_url = enrichment_data.get("avatar")
        if not avatar_url:
            self.logger.info("No avatar URL found in enrichment data")
            return None

        try:
            # Download the avatar
            avatar_data = await self._download_avatar_file(avatar_url)
            if not avatar_data:
                return None

            # Determine file extension
            file_extension = self._get_avatar_file_extension(
                avatar_url, avatar_data["content_type"]
            )
            if not file_extension:
                file_extension = "jpg"  # Default to jpg

            # Generate S3 key
            s3_key = f"assets/founders/{founder_id}/avatar.{file_extension}"

            # Upload to S3
            if self.s3:
                await self.s3.put_object(s3_key, avatar_data["content"])

                # Generate S3 URL
                s3_url = f"s3://{self.s3.bucket_name}/{s3_key}"
                self.logger.info(f"Avatar stored in S3: {s3_url}")
                return s3_url
            else:
                self.logger.warning("S3 storage not available, skipping avatar upload")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading/storing avatar: {e}")
            return None

    async def _download_avatar_file(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Download avatar file from URL.

        Args:
            url: URL to download from

        Returns:
            Dictionary with content and content_type, or None if failed
        """
        try:
            timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
            headers = {"User-Agent": "Mozilla/5.0 (compatible; TractionX-Bot/1.0)"}

            async with aiohttp.ClientSession(
                timeout=timeout, headers=headers
            ) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.read()
                        content_type = response.headers.get(
                            "content-type", "image/jpeg"
                        )

                        self.logger.info(
                            f"Downloaded avatar: {len(content)} bytes, content-type: {content_type}"
                        )

                        # Validate content type
                        if not content_type.startswith("image/"):
                            self.logger.warning(
                                f"Non-image content type: {content_type} for URL: {url}"
                            )
                            return None

                        # Validate file size (max 5MB)
                        if len(content) > 5 * 1024 * 1024:
                            self.logger.warning(
                                f"Avatar file too large: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        # Validate minimum file size (at least 1KB)
                        if len(content) < 1024:
                            self.logger.warning(
                                f"Avatar file too small: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        return {"content": content, "content_type": content_type}
                    else:
                        self.logger.error(
                            f"Failed to download avatar: HTTP {response.status} for URL: {url}"
                        )
                        return None

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error downloading avatar from {url}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error downloading avatar from {url}: {e}")
            return None

    async def _download_company_logo(
        self, logo_url: str, company_id: str
    ) -> Optional[str]:
        """
        Download company logo and store in S3.

        Args:
            logo_url: Company logo URL from LinkedIn
            company_id: Company ID for S3 path generation

        Returns:
            S3 URL of the stored logo, or None if failed
        """
        if not logo_url:
            return None

        try:
            # Download the logo
            logo_data = await self._download_avatar_file(logo_url)
            if not logo_data:
                return None

            # Determine file extension
            file_extension = self._get_avatar_file_extension(
                logo_url, logo_data["content_type"]
            )
            if not file_extension:
                file_extension = "jpg"  # Default to jpg

            # Generate S3 key
            s3_key = f"assets/companies/{company_id}/logo.{file_extension}"

            # Upload to S3
            if self.s3:
                await self.s3.put_object(s3_key, logo_data["content"])

                # Generate S3 URL
                s3_url = f"s3://{self.s3.bucket_name}/{s3_key}"
                self.logger.info(f"Company logo stored in S3: {s3_url}")
                return s3_url
            else:
                self.logger.warning("S3 storage not available, skipping logo upload")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading/storing company logo: {e}")
            return None

    async def _download_institute_logo(
        self, logo_url: str, institute_id: str
    ) -> Optional[str]:
        """
        Download institute logo and store in S3.

        Args:
            logo_url: Institute logo URL from LinkedIn
            institute_id: Institute ID for S3 path generation

        Returns:
            S3 URL of the stored logo, or None if failed
        """
        if not logo_url:
            return None

        try:
            # Download the logo
            logo_data = await self._download_avatar_file(logo_url)
            if not logo_data:
                return None

            # Determine file extension
            file_extension = self._get_avatar_file_extension(
                logo_url, logo_data["content_type"]
            )
            if not file_extension:
                file_extension = "jpg"  # Default to jpg

            # Generate S3 key
            s3_key = f"assets/institutes/{institute_id}/logo.{file_extension}"

            # Upload to S3
            if self.s3:
                await self.s3.put_object(s3_key, logo_data["content"])

                # Generate S3 URL
                s3_url = f"s3://{self.s3.bucket_name}/{s3_key}"
                self.logger.info(f"Institute logo stored in S3: {s3_url}")
                return s3_url
            else:
                self.logger.warning("S3 storage not available, skipping logo upload")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading/storing institute logo: {e}")
            return None

    def _parse_year_to_date(self, year_str: Any) -> Optional[date]:
        """
        Parse year string to date object (first day of year).

        Args:
            year_str: Year as string or number

        Returns:
            Date object for January 1st of the year, or None if invalid
        """
        if not year_str:
            return None

        try:
            year = int(str(year_str).strip())
            return date(year, 1, 1)
        except (ValueError, TypeError):
            return None

    def _get_avatar_file_extension(self, url: str, content_type: str) -> Optional[str]:
        """
        Determine file extension from URL or content type.

        Args:
            url: Original URL
            content_type: HTTP content type

        Returns:
            File extension (without dot), or None if cannot determine
        """
        try:
            # Try to get extension from URL
            parsed_url = urlparse(url)
            path = parsed_url.path.lower()

            # Common image extensions
            image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"]

            for ext in image_extensions:
                if path.endswith(ext):
                    return ext[1:]  # Remove the dot

            # Try to get extension from content type
            if content_type:
                # Map content types to extensions
                content_type_map = {
                    "image/jpeg": "jpg",
                    "image/jpg": "jpg",
                    "image/png": "png",
                    "image/gif": "gif",
                    "image/webp": "webp",
                    "image/svg+xml": "svg",
                }
                return content_type_map.get(content_type.lower())

            return None

        except Exception as e:
            self.logger.error(f"Error determining file extension: {e}")
            return None

    async def _extract_skills(
        self,
        founder_record: FounderRecord,
        experiences: list[FounderExperience],
        education: list[FounderEducation],
    ) -> list[FounderSkill]:
        """
        Extract skills using LLM from founder profile, experience, and education.

        Args:
            founder_record: Founder profile data
            experiences: List of work experiences
            education: List of education records

        Returns:
            List of FounderSkill objects
        """
        try:
            # Import here to avoid circular imports
            from app.clients.ai.together import get_together_client

            # Get Together AI client
            together_client = await get_together_client()

            # Create prompt for skill extraction
            prompt = self._create_skill_extraction_prompt(
                founder_record, experiences, education
            )

            # Create messages for chat completion
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                }
            ]

            # Get completion
            result = await together_client.create_chat_completion(
                messages=messages,
                max_tokens=1000,
                temperature=0.1,  # Lower temperature for more consistent output
            )

            if not result["success"]:
                self.logger.warning(f"Skill extraction failed: {result.get('error')}")
                return []

            # Parse the response
            content = result["content"].strip()

            # Try to extract JSON from markdown response
            json_content = self._extract_json_from_response(content)

            if not json_content:
                self.logger.warning(f"No JSON found in skills response: {content}")
                return []

            # Try to parse as JSON
            try:
                skills_data = json.loads(json_content)
                if isinstance(skills_data, dict) and "skills" in skills_data:
                    skills = skills_data["skills"]
                elif isinstance(skills_data, list):
                    skills = skills_data
                else:
                    self.logger.warning(
                        f"Unexpected skills response format: {json_content}"
                    )
                    return []

                # Clean and validate skills, then create FounderSkill objects
                founder_skills = []
                seen_skills = set()

                for skill in skills:
                    if isinstance(skill, str):
                        cleaned_skill = self._clean_string(skill)
                        if cleaned_skill and len(cleaned_skill) > 1:
                            # Normalize skill name (lowercase for deduplication)
                            normalized_skill = cleaned_skill.lower()

                            # Avoid duplicates
                            if normalized_skill not in seen_skills:
                                seen_skills.add(normalized_skill)

                                # Create FounderSkill object
                                founder_skill = FounderSkill(
                                    founder_id=founder_record.founder_id,
                                    skill=cleaned_skill,  # Use original cleaned skill (proper case)
                                )
                                founder_skills.append(founder_skill)

                self.logger.info(f"Extracted {len(founder_skills)} skills using LLM")
                return founder_skills

            except json.JSONDecodeError:
                self.logger.warning(f"Failed to parse extracted JSON: {json_content}")
                return []

        except Exception as e:
            self.logger.error(f"Error extracting skills with LLM: {e}")
            return []

    def _create_skill_extraction_prompt(
        self,
        founder_record: FounderRecord,
        experiences: list[FounderExperience],
        education: list[FounderEducation],
    ) -> str:
        """
        Create a prompt for skill extraction from founder data.

        Args:
            founder_record: Founder profile data
            experiences: List of work experiences
            education: List of education records

        Returns:
            Formatted prompt string
        """
        # Build experience text
        experience_text = ""
        for exp in experiences:
            experience_text += f"• {exp.title} at {exp.company_name}"
            if exp.description:
                experience_text += f": {exp.description}"
            if exp.start_date and exp.end_date:
                experience_text += f" ({exp.start_date.year}-{exp.end_date.year})"
            experience_text += "\n"

        # Build education text
        education_text = ""
        for edu in education:
            education_text += f"• {', '.join(edu.degrees)} from {edu.school_name}"
            if edu.start_date and edu.end_date:
                education_text += f" ({edu.start_date.year}-{edu.end_date.year})"
            education_text += "\n"

        prompt = f"""
        You are an expert at analyzing professional profiles and extracting relevant skills. Your task is to analyze a founder's profile and extract their key professional skills.

        Founder Profile:
        - Name: {founder_record.full_name or "N/A"}
        - Headline: {founder_record.headline or "N/A"}
        - Summary: {founder_record.summary or "N/A"}
        - Current Role: {founder_record.current_job_title or "N/A"} at {founder_record.current_job_company or "N/A"}

        Work Experience:
        {experience_text if experience_text else "No experience data available"}

        Education:
        {education_text if education_text else "No education data available"}

        Instructions:
        1. Analyze the founder's profile, experience, and education
        2. Extract relevant professional skills, technical skills, and domain expertise
        3. Focus on skills that would be valuable for investors evaluating a founder
        4. Include both technical skills (e.g., "Python", "Machine Learning", "Product Management") and soft skills (e.g., "Leadership", "Strategic Planning")
        5. Avoid generic or obvious skills like "Communication" unless strongly evidenced
        6. Prioritize skills that are specific and relevant to their industry/role
        7. Return exactly 10-20 most relevant skills

        Output Format:
        Return a JSON object with a "skills" array containing the extracted skills:
        {{
            "skills": [
                "skill1",
                "skill2",
                "skill3"
            ]
        }}

        Be specific, relevant, and focus on skills that demonstrate the founder's capabilities and expertise.
        """

        return prompt.strip()

    def _extract_json_from_response(self, content: str) -> Optional[str]:
        """
        Extract JSON from LLM response that may contain markdown formatting.

        Args:
            content: Raw response from LLM

        Returns:
            Extracted JSON string or None if not found
        """
        import re

        # Try to find JSON in markdown code blocks
        json_pattern = r"```(?:json)?\s*(\{.*?\})\s*```"
        match = re.search(json_pattern, content, re.DOTALL)
        if match:
            return match.group(1)

        # Try to find JSON object directly
        json_object_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
        match = re.search(json_object_pattern, content, re.DOTALL)
        if match:
            return match.group(0)

        # Try to find JSON array directly
        json_array_pattern = r"\[[^\[\]]*(?:\{[^{}]*\}[^\[\]]*)*\]"
        match = re.search(json_array_pattern, content, re.DOTALL)
        if match:
            return match.group(0)

        return None

    async def _store_founder_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store founder data in database with elegant upsert operations."""
        founder_record = cleaned_data["founder"]
        experiences = cleaned_data["experiences"]
        education = cleaned_data["education"]
        skills = cleaned_data["skills"]

        # Use the elegant upsert method for founder with relations
        # Use for_rds=True to preserve datetime objects for PostgreSQL
        founder_id = await self.rds.upsert_founder_with_relations(
            founder_data=founder_record.model_dump(for_rds=True),
            experiences=[exp.model_dump(for_rds=True) for exp in experiences],
            education=[edu.model_dump(for_rds=True) for edu in education],
            skills=[skill.model_dump(for_rds=True) for skill in skills],
        )

        self.logger.info(f"Successfully stored founder data with ID: {founder_id}")

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error using uniform error tracking."""
        try:
            metadata = payload.get("metadata", {})

            await self.rds.log_pipeline_error(
                pipeline_name="linkedin_founder",
                error_type="processing",
                error_message=error_msg,
                entity_id=metadata.get("founder_id"),
                entity_type="founder",
                org_id=metadata.get("org_id"),
                company_id=metadata.get("company_id"),
                raw_data_key=s3_key,
                payload_preview=str(payload)[:1000],  # First 1000 chars for debugging
            )

        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and normalize string values."""
        if not value or not isinstance(value, str):
            return None

        cleaned = value.strip()
        if not cleaned:
            return None

        # Remove excessive whitespace
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _clean_url(self, value: Any) -> Optional[str]:
        """Clean and normalize URL values."""
        if not value or not isinstance(value, str):
            return None

        url = value.strip().lower()
        if not url:
            return None

        # Add protocol if missing
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        return url

    def _clean_dict(self, value: Any) -> Optional[Dict[str, Any]]:
        """Clean and normalize dictionary values."""
        if not value or not isinstance(value, dict):
            return None

        return {k: self._clean_string(v) for k, v in value.items()}

    def _parse_date(self, date_str: Any) -> Optional[date]:
        """Parse date string to date object."""
        if not date_str:
            return None

        if isinstance(date_str, str):
            # Try different date formats
            for fmt in ["%Y-%m-%d", "%Y-%m", "%Y"]:
                try:
                    parsed = datetime.strptime(date_str, fmt)
                    return parsed.date()
                except ValueError:
                    continue

        return None

    def _extract_location_string(self, location_data: Any) -> Optional[str]:
        """Extract location string from various location formats."""
        if isinstance(location_data, str):
            return self._clean_string(location_data)
        elif isinstance(location_data, dict):
            parts = [
                location_data.get("locality"),
                location_data.get("region"),
                location_data.get("country"),
            ]
            # Filter out None values and clean strings
            location_parts = [self._clean_string(part) for part in parts if part]
            return ", ".join(location_parts) if location_parts else None  # type: ignore

        return None
