"""
PDL Founder Data Processor for TractionX Data Pipeline Service.

This module provides ETL processing for PDL founder enrichment data.
Handles cleaning, validation, UUID generation, and storage.
"""

from datetime import date, datetime, timezone
from typing import Any, Dict, Optional
from uuid import NAMESPACE_DNS, uuid5

from app.configs import get_logger
from app.ctx.job.pdl.founder import PDLJobContext
from app.models.founder import (
    FounderEducation,
    FounderExperience,
    FounderRecord,
    FounderSkill,
)
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class PDLDataProcessor:
    """
    Comprehensive processor for PDL enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.PDLDataProcessor")

    async def process(
        self,
        payload: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: PDLJobContext,
    ) -> Dict[str, Any]:
        """
        Process a complete PDL payload and store in normalized schema.

        Args:
            payload: Complete PDL payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup
            job_context: PDL job context

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload
            metadata = job_context._asdict()

            # Validate required fields
            validation_result = self._validate_pdl_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "founder_id": metadata.get("founder_id"),
                }

            # Generate founder UUID
            founder_uuid = job_context.founder_id

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                enrichment_data, metadata, founder_uuid, s3_raw_data_key
            )
            self.logger.info(f"Cleaned data: {cleaned_data}")
            # Store in database with transaction
            await self._store_founder_data(cleaned_data)

            self.logger.info(
                f"Successfully processed PDL data for founder {founder_uuid}"
            )

            return {
                "success": True,
                "founder_uuid": founder_uuid,
                "founder_id": metadata.get("founder_id"),
                "records_created": {
                    "experiences": len(cleaned_data["experiences"]),
                    "education": len(cleaned_data["education"]),
                    "skills": len(cleaned_data["skills"]),
                },
            }

        except Exception as e:
            error_msg = f"PDL processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "founder_id": metadata.get("founder_id"),
            }

    def _validate_pdl_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate required fields in PDL data."""
        errors = []

        # Check metadata
        required_metadata = ["founder_id", "company_id", "org_id"]
        for field in required_metadata:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check enrichment data
        pdl_id = enrichment_data.get("id", "").strip()
        linkedin_url = enrichment_data.get("linkedin_url", "").strip()

        if not pdl_id:
            errors.append("Missing or empty pdl_id")

        if not linkedin_url:
            # Try to get from profiles
            profiles = enrichment_data.get("profiles", [])
            linkedin_profiles = [p for p in profiles if p.get("network") == "linkedin"]
            if not linkedin_profiles:
                errors.append("Missing LinkedIn URL in both direct field and profiles")

        return {"valid": len(errors) == 0, "errors": errors}

    def _generate_founder_uuid(self, full_name: str, linkedin_url: str) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL."""
        full_name_clean = full_name.strip().lower()
        linkedin_url_clean = linkedin_url.strip().lower()

        # Create deterministic string for UUID generation
        uuid_string = f"{full_name_clean}|{linkedin_url_clean}"

        # Generate UUIDv5 using DNS namespace
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    async def _clean_enrichment_data(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and structure enrichment data for database storage."""

        # Clean basic founder info
        founder_record = await self._create_founder_record(
            enrichment_data, metadata, founder_uuid, s3_raw_data_key
        )

        # Extract and clean related data
        experiences = await self._extract_experiences(enrichment_data, founder_uuid)
        education = await self._extract_education(enrichment_data, founder_uuid)
        skills = await self._extract_skills(enrichment_data, founder_uuid)

        return {
            "founder": founder_record,
            "experiences": experiences,
            "education": education,
            "skills": skills,
        }

    async def _create_founder_record(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> FounderRecord:
        """Create a clean FounderRecord from PDL data."""

        # Clean name fields
        full_name = self._clean_string(enrichment_data.get("full_name"))
        first_name = self._clean_string(enrichment_data.get("first_name"))
        last_name = self._clean_string(enrichment_data.get("last_name"))

        # Clean job info
        metadata_data = metadata.get("data", {}) if metadata else {}
        current_job_title = self._clean_string(
            metadata_data.get("current_job_title") or enrichment_data.get("job_title")
        )
        current_job_company = self._clean_string(
            metadata_data.get("current_company")
            or enrichment_data.get("job_company_name")
        )

        # Clean URLs
        social_profiles = {
            "linkedin": self._clean_url(enrichment_data.get("linkedin_url")),
            "github": self._clean_url(enrichment_data.get("github_url")),
            "facebook": self._clean_url(enrichment_data.get("facebook_url")),
            "twitter": self._clean_url(enrichment_data.get("twitter_url")),
        }
        # Clean location
        location = self._clean_dict({
            "city": enrichment_data.get("location_locality"),
            "country": enrichment_data.get("location_country"),
        })

        avatar_url = ""
        headline = ""
        summary = ""
        links = {"from_pdl": []}
        extra_data = {
            "work_email": enrichment_data.get("work_email"),
            "mobile_phone": enrichment_data.get("mobile_phone"),
            "job_last_changed": enrichment_data.get("job_last_changed"),
            "profiles": enrichment_data.get("profiles"),
        }

        # Parse enrichment date
        enrichment_date = None
        if enrichment_data.get("enrichment_date"):
            try:
                enrichment_date = datetime.fromisoformat(
                    enrichment_data["enrichment_date"].replace("Z", "+00:00")
                )
            except Exception as e:
                self.logger.error(f"Error parsing enrichment date: {e}")
                enrichment_date = datetime.now(timezone.utc)

        # Calculate confidence score
        confidence_score = metadata.get("confidence_score", 0.5)

        now = datetime.now(timezone.utc)

        return FounderRecord(
            id=founder_uuid,
            founder_id=metadata["founder_id"],
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            avatar_url=avatar_url,
            headline=headline,
            summary=summary,
            location=location,
            current_job_title=current_job_title,
            current_job_company=current_job_company,
            social_profiles=social_profiles,
            links=links,
            extra_data=extra_data,
            org_id=metadata["org_id"],
            company_id=metadata["company_id"],
            source="pdl",
            confidence_score=confidence_score,
            enrichment_date=enrichment_date,
            s3_raw_data_key={"pdl": s3_raw_data_key},
            created_at=now,
            updated_at=now,
        )

    async def _extract_experiences(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> list[FounderExperience]:
        """Extract and clean experience records."""
        experiences = []
        experience_list = enrichment_data.get("experience", [])

        for exp_data in experience_list:
            if not isinstance(exp_data, dict):
                continue

            company_info = exp_data.get("company", {})
            title_info = exp_data.get("title", {})

            # Handle both string and dict title formats
            title = (
                title_info.get("name") if isinstance(title_info, dict) else title_info
            )

            # Skip if no company name
            if not company_info.get("name"):
                continue

            # Extract company URL from company info
            company_url = company_info.get("linkedin_url") or company_info.get(
                "website"
            )
            if company_url and not company_url.startswith(("http://", "https://")):
                company_url = f"https://{company_url}"

            # Extract location from location_names or company location
            location = None
            if exp_data.get("location_names"):
                location = ", ".join(exp_data["location_names"])
            else:
                # Safely handle location extraction from company_info
                company_location = company_info.get("location")
                if company_location and isinstance(company_location, dict):
                    if company_location.get("name"):
                        location = company_location["name"]
                    elif company_location.get("locality") and company_location.get(
                        "region"
                    ):
                        location = f"{company_location['locality']}, {company_location['region']}"

            # Generate unique ID for experience
            experience_id = str(
                uuid5(
                    NAMESPACE_DNS,
                    f"{exp_data.get('id', '')}|{founder_id}|{title}|{company_info.get('name', '')}",
                )
            )

            # Create experience record
            experience = FounderExperience(
                id=experience_id,
                founder_id=founder_id,
                company_name=self._clean_string(company_info.get("name")),
                title=self._clean_string(title),
                industry=self._clean_string(company_info.get("industry")),
                company_size=self._clean_string(company_info.get("size")),
                company_url=company_url,
                company_logo_url=None,  # PDL doesn't provide company logos
                start_date=self._parse_date(exp_data.get("start_date")),
                end_date=self._parse_date(exp_data.get("end_date")),
                is_primary=bool(exp_data.get("is_primary", False)),
                location=location,
                description=None,  # PDL doesn't provide job descriptions
            )
            experiences.append(experience)

        return experiences

    async def _extract_education(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> list[FounderEducation]:
        """Extract and clean education records."""
        education_records = []
        education_list = enrichment_data.get("education", [])

        for edu_data in education_list:
            if not isinstance(edu_data, dict):
                continue

            school_info = edu_data.get("school", {})
            school_name = self._clean_string(school_info.get("name"))

            # Skip entries without school name
            if not school_name:
                continue

            # Extract school URL from school info
            school_url = school_info.get("linkedin_url") or school_info.get("website")
            if school_url and not school_url.startswith(("http://", "https://")):
                school_url = f"https://{school_url}"

            # Extract location from school location
            location = None
            school_location = school_info.get("location")
            if school_location and isinstance(school_location, dict):
                if school_location.get("name"):
                    location = school_location["name"]
                elif school_location.get("locality") and school_location.get("region"):
                    location = (
                        f"{school_location['locality']}, {school_location['region']}"
                    )
                elif school_location.get("locality") and school_location.get("country"):
                    location = (
                        f"{school_location['locality']}, {school_location['country']}"
                    )

            # Clean degree and major lists - filter out None values and empty strings
            degrees = []
            for d in edu_data.get("degrees", []):
                cleaned_degree = self._clean_string(d)
                if cleaned_degree and cleaned_degree.strip():
                    degrees.append(cleaned_degree)

            majors = []
            for m in edu_data.get("majors", []):
                cleaned_major = self._clean_string(m)
                if cleaned_major and cleaned_major.strip():
                    majors.append(cleaned_major)

            # Generate unique ID for education
            education_id = str(
                uuid5(
                    NAMESPACE_DNS,
                    f"{edu_data.get('id', '')}|{founder_id}|{school_name}",
                )
            )

            education = FounderEducation(
                id=education_id,
                founder_id=founder_id,
                school_name=school_name,
                degrees=degrees,
                majors=majors,
                school_url=school_url,
                institute_logo_url=None,  # PDL doesn't provide institute logos
                start_date=self._parse_date(edu_data.get("start_date")),
                end_date=self._parse_date(edu_data.get("end_date")),
                location=location,
                description=None,  # PDL doesn't provide education descriptions
            )
            education_records.append(education)

        return education_records

    async def _extract_skills(
        self, enrichment_data: Dict[str, Any], founder_id: str
    ) -> list[FounderSkill]:
        """
        Extract and clean skills list using a smart hybrid approach.

        This method uses a two-tier approach:
        1. **Static filtering**: Fast, rule-based filtering using whitelist/blacklist
        2. **AI fallback**: If >80% of skills are filtered out, use Together AI for re-evaluation

        This balances cost, speed, and accuracy by only using AI when necessary.

        **Why this approach:**
        - Most PDL skills are already professional (low dropoff rate)
        - AI is expensive and slow - only use when needed
        - Static filtering catches 90%+ of cases efficiently
        - AI fallback prevents missing important skills when static filtering is too aggressive
        """
        skills = []
        skills_list = enrichment_data.get("skills", [])

        if not isinstance(skills_list, list):
            return skills

        # Step 1: Static filtering (fast, rule-based)
        static_filtered_skills = await self._extract_skills_static(
            skills_list, founder_id
        )

        # Step 2: Check if we need AI fallback
        original_count = len(skills_list)
        filtered_count = len(static_filtered_skills)
        dropoff_rate = (
            (original_count - filtered_count) / original_count
            if original_count > 0
            else 0
        )

        self.logger.info(
            f"Skills filtering: {original_count} original, {filtered_count} kept, "
            f"{dropoff_rate:.1%} dropoff rate"
        )

        # If dropoff rate > 80%, use AI to re-evaluate
        if dropoff_rate > 0.8 and original_count > 5:
            self.logger.info(
                f"High dropoff rate ({dropoff_rate:.1%}), using AI fallback"
            )
            ai_filtered_skills = await self._extract_skills_ai(skills_list, founder_id)

            # Use AI results if they provide more skills
            if len(ai_filtered_skills) > len(static_filtered_skills):
                self.logger.info(
                    f"AI found {len(ai_filtered_skills)} skills vs {len(static_filtered_skills)} static"
                )
                return ai_filtered_skills
            else:
                self.logger.info("AI didn't improve results, using static filtering")

        return static_filtered_skills

    async def _extract_skills_static(
        self, skills_list: list, founder_id: str
    ) -> list[FounderSkill]:
        """
        Static rule-based skills extraction (fast, no AI).

        This is the same as the previous implementation - efficient static filtering
        using whitelist, blacklist, and heuristics.
        """
        skills = []

        # High-confidence professional skills (whitelist)
        professional_whitelist = {
            # Programming languages
            "python",
            "java",
            "javascript",
            "typescript",
            "c++",
            "c#",
            "go",
            "rust",
            "php",
            "ruby",
            "swift",
            "kotlin",
            "scala",
            "r",
            "matlab",
            "sql",
            "html",
            "css",
            "react",
            "angular",
            "vue",
            "node.js",
            "django",
            "flask",
            "spring",
            "express",
            "laravel",
            "rails",
            "asp.net",
            "jquery",
            "bootstrap",
            "sass",
            "less",
            "webpack",
            "babel",
            "docker",
            "kubernetes",
            "terraform",
            "ansible",
            # Databases
            "postgresql",
            "mysql",
            "mongodb",
            "redis",
            "elasticsearch",
            "cassandra",
            "dynamodb",
            "sqlite",
            "oracle",
            "sql server",
            "mariadb",
            "neo4j",
            "influxdb",
            # Cloud platforms
            "aws",
            "azure",
            "gcp",
            "heroku",
            "digitalocean",
            "linode",
            "vultr",
            "cloudflare",
            "netlify",
            "vercel",
            "firebase",
            "supabase",
            # Data science
            "machine learning",
            "deep learning",
            "data analysis",
            "statistics",
            "pandas",
            "numpy",
            "scikit-learn",
            "tensorflow",
            "pytorch",
            "keras",
            "jupyter",
            "spark",
            "hadoop",
            "hive",
            "kafka",
            "airflow",
            "tableau",
            "power bi",
            "looker",
            "metabase",
            "dbt",
            "snowflake",
            # Design tools
            "adobe photoshop",
            "adobe illustrator",
            "adobe indesign",
            "figma",
            "sketch",
            "invision",
            "zeplin",
            "adobe xd",
            "canva",
            "procreate",
            # Product tools
            "product management",
            "agile",
            "scrum",
            "kanban",
            "jira",
            "confluence",
            "notion",
            "asana",
            "trello",
            "monday.com",
            "slack",
            "zoom",
            "teams",
            # Business skills
            "strategy",
            "marketing",
            "sales",
            "finance",
            "operations",
            "hr",
            "project management",
            "business development",
            "partnerships",
            "customer success",
            "growth hacking",
            "seo",
            "sem",
            "content marketing",
            # Common professional tools
            "excel",
            "powerpoint",
            "word",
            "outlook",
            "google docs",
            "google sheets",
            "google slides",
            "microsoft office",
            "adobe",
            "premiere",
            "after effects",
            "final cut pro",
            "logic pro",
            "protools",
            "autocad",
            "solidworks",
            "sketchup",
            "blender",
            "maya",
            "unity",
            "unreal engine",
            "git",
            "github",
            "bitbucket",
            "gitlab",
            "jenkins",
            "travis",
            "circleci",
            "github actions",
        }

        # High-confidence non-professional skills (blacklist)
        non_professional_blacklist = {
            "league of legends",
            "gaming",
            "video games",
            "sports",
            "hobbies",
            "brainstorming weird gift ideas for employees",
            "misusing linkedin",
            "providing linkedin kudos",
            "antique map valuation",
            "extreme ownership",
            "linkedin influencer",
            "social media",
            "networking",
            "communication",
            "leadership",
            "teamwork",
            "problem solving",
            "critical thinking",
            "time management",
            "organization",
            "attention to detail",
            "multitasking",
        }

        # Technical patterns that indicate professional skills
        technical_patterns = [
            "api",
            "sdk",
            "framework",
            "library",
            "tool",
            "platform",
            "database",
            "server",
            "cloud",
            "devops",
            "ci/cd",
            "testing",
            "algorithm",
            "architecture",
            "design pattern",
            "protocol",
            "interface",
            "integration",
            "deployment",
            "monitoring",
            "analytics",
            "automation",
            "optimization",
            "scalability",
        ]

        # Professional context indicators
        professional_indicators = [
            "js",
            "jsx",
            "ts",
            "tsx",
            "sql",
            "api",
            "sdk",
            "ml",
            "ai",
            "ui",
            "ux",
            "saas",
            "b2b",
            "b2c",
            "crm",
            "erp",
            "cms",
            "lms",
            "api",
            "rest",
            "graphql",
        ]

        # Clean and deduplicate skills
        seen_skills = set()
        for skill in skills_list:
            if isinstance(skill, str):
                cleaned_skill = self._clean_string(skill)
                if not cleaned_skill or len(cleaned_skill) < 2:
                    continue

                # Convert to lowercase for comparison
                skill_lower = cleaned_skill.lower().strip()

                # Skip if already seen
                if skill_lower in seen_skills:
                    continue

                # Skip if in blacklist
                if skill_lower in non_professional_blacklist:
                    continue

                # Check if it's a high-confidence professional skill
                is_professional = skill_lower in professional_whitelist

                # If not in whitelist, use heuristic analysis
                if not is_professional:
                    is_professional = self._is_likely_professional_skill(
                        skill_lower, technical_patterns, professional_indicators
                    )

                # Only add if determined to be professional
                if is_professional:
                    skill_record = FounderSkill(
                        id=str(uuid5(NAMESPACE_DNS, f"{founder_id}|{skill_lower}")),
                        founder_id=founder_id,
                        skill=skill_lower,
                    )
                    skills.append(skill_record)
                    seen_skills.add(skill_lower)

        return skills

    async def _extract_skills_ai(
        self, skills_list: list, founder_id: str
    ) -> list[FounderSkill]:
        """
        AI-powered skills extraction using Together AI.

        This is used as a fallback when static filtering removes too many skills.
        """
        try:
            # Import here to avoid circular imports
            from app.clients.ai.together import get_together_client

            # Get Together AI client
            together_client = await get_together_client()

            # Create prompt for skill extraction
            prompt = self._create_skills_ai_prompt(skills_list)

            # Create messages for chat completion
            messages = [
                {
                    "role": "system",
                    "content": """You are an expert at analyzing professional skills. Your task is to filter a list of skills and return only the professional, relevant ones that would be valuable for evaluating a founder or business professional.

                Focus on:
                - Technical skills (programming, tools, platforms, methodologies)
                - Business skills (strategy, management, operations, finance)
                - Domain expertise (industry knowledge, sector expertise)
                - Professional tools and platforms
                - Relevant certifications and methodologies

                Exclude:
                - Personal hobbies and interests
                - Generic soft skills (communication, leadership, teamwork)
                - Casual activities or non-professional interests
                - Gaming, sports, or entertainment activities

                Return a JSON array of professional skills only.""",
                },
                {"role": "user", "content": prompt},
            ]

            # Get completion
            result = await together_client.create_chat_completion(
                messages=messages,
                max_tokens=1000,
                temperature=0.1,  # Low temperature for consistent output
            )

            if not result.get("success"):
                self.logger.warning(
                    f"AI skills extraction failed: {result.get('error')}"
                )
                return []

            # Parse the response
            content = result.get("content", "").strip()

            # Try to extract JSON from the response
            import json
            import re

            # Look for JSON array in the response
            json_match = re.search(r"\[.*\]", content, re.DOTALL)
            if json_match:
                try:
                    ai_skills = json.loads(json_match.group())
                    if isinstance(ai_skills, list):
                        # Convert to FounderSkill objects
                        skills = []
                        seen_skills = set()
                        for skill in ai_skills:
                            if isinstance(skill, str):
                                skill_lower = skill.lower().strip()
                                if skill_lower and skill_lower not in seen_skills:
                                    skill_record = FounderSkill(
                                        id=str(
                                            uuid5(
                                                NAMESPACE_DNS,
                                                f"{founder_id}|{skill_lower}",
                                            )
                                        ),
                                        founder_id=founder_id,
                                        skill=skill_lower,
                                    )
                                    skills.append(skill_record)
                                    seen_skills.add(skill_lower)
                        return skills
                except json.JSONDecodeError:
                    self.logger.warning(
                        f"Failed to parse AI response as JSON: {content}"
                    )

            self.logger.warning(f"Could not extract skills from AI response: {content}")
            return []

        except Exception as e:
            self.logger.error(f"AI skills extraction failed: {str(e)}")
            return []

    def _create_skills_ai_prompt(self, skills_list: list) -> str:
        """Create prompt for AI skills extraction."""
        skills_text = "\n".join([
            f"- {skill}" for skill in skills_list if isinstance(skill, str)
        ])

        return f"""Please analyze the following list of skills and return only the professional, relevant ones that would be valuable for evaluating a founder or business professional.

Skills to analyze:
{skills_text}

Instructions:
1. Filter out non-professional skills (hobbies, casual activities, generic soft skills)
2. Keep only professional, technical, or business-relevant skills
3. Return the result as a JSON array of strings
4. Be conservative - if unsure, include the skill rather than exclude it

Example output format:
["python", "machine learning", "product management", "aws"]

Return only the JSON array, no additional text."""

    def _is_likely_professional_skill(
        self, skill: str, technical_patterns: list, professional_indicators: list
    ) -> bool:
        """
        Heuristic method to determine if a skill is likely professional.

        This method uses multiple heuristics to make an educated guess:
        1. Technical pattern matching
        2. Professional context indicators
        3. Length and format analysis
        4. Common professional terminology

        Returns True if the skill is likely professional, False otherwise.
        """
        skill_lower = skill.lower()

        # Check for technical patterns
        for pattern in technical_patterns:
            if pattern in skill_lower:
                return True

        # Check for professional indicators
        for indicator in professional_indicators:
            if indicator in skill_lower:
                return True

        # Check for common professional terminology
        professional_terms = [
            "management",
            "development",
            "engineering",
            "analysis",
            "design",
            "architecture",
            "strategy",
            "optimization",
            "automation",
            "integration",
            "deployment",
            "monitoring",
            "testing",
            "quality",
            "security",
            "compliance",
            "governance",
            "compliance",
            "audit",
            "risk",
            "compliance",
            "regulatory",
        ]

        for term in professional_terms:
            if term in skill_lower:
                return True

        # Check for tool/platform patterns (e.g., "tool name", "platform name")
        if any(
            word in skill_lower for word in ["tool", "platform", "software", "system"]
        ):
            return True

        # Check for version numbers or technical specifications
        import re

        if re.search(r"\d+\.\d+|\d+\.\d+\.\d+", skill_lower):  # Version numbers
            return True

        # Check for acronyms (likely professional)
        if re.match(r"^[A-Z]{2,}$", skill.upper()):  # All caps acronyms
            return True

        # Check for camelCase or kebab-case (likely technical)
        if re.search(r"[a-z][A-Z]|-[a-z]", skill):  # camelCase or kebab-case
            return True

        # Length-based heuristics (very short or very long skills are often not professional)
        if len(skill_lower) < 3 or len(skill_lower) > 50:
            return False

        # Check for common professional prefixes/suffixes
        professional_affixes = [
            "pro",
            "enterprise",
            "cloud",
            "web",
            "mobile",
            "api",
            "sdk",
            "framework",
            "library",
            "tool",
            "platform",
            "service",
            "system",
            "solution",
        ]

        for affix in professional_affixes:
            if skill_lower.startswith(affix) or skill_lower.endswith(affix):
                return True

        # If we can't determine with high confidence, be conservative and include it
        # This prevents false negatives (missing important skills)
        return True

    async def _store_founder_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store founder data in database with transaction."""
        founder_record = cleaned_data["founder"]
        experiences = cleaned_data["experiences"]
        education = cleaned_data["education"]
        skills = cleaned_data["skills"]

        # Use the new RDSV2 method for transactional upsert
        await self.rds.upsert_founder_with_relations(
            founder_data=founder_record.model_dump(for_rds=True),
            experiences=[exp.model_dump(for_rds=True) for exp in experiences],
            education=[edu.model_dump(for_rds=True) for edu in education],
            skills=[skill.model_dump(for_rds=True) for skill in skills],
        )
        self.logger.info(
            f"Successfully stored founder data with ID: {founder_record.id}"
        )

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error for debugging."""
        try:
            await self.rds.log_pipeline_error(
                pipeline_name="pdl_founder",
                error_type="processing",
                error_message=error_msg,
                entity_id=payload.get("founder_id"),
                entity_type="founder",
                org_id=payload.get("org_id"),
                company_id=payload.get("company_id"),
                raw_data_key=s3_key,
                payload_preview=str(payload)[:1000],
            )

        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and normalize string values."""
        if not value or not isinstance(value, str):
            return None

        cleaned = value.strip()
        if not cleaned:
            return None

        # Remove excessive whitespace
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _clean_url(self, value: Any) -> Optional[str]:
        """Clean and normalize URL values."""
        if not value or not isinstance(value, str):
            return None

        url = value.strip().lower()
        if not url:
            return None

        # Add protocol if missing
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        return url

    def _clean_dict(self, value: Any) -> Optional[Dict[str, Any]]:
        """Clean and normalize dictionary values."""
        if not value or not isinstance(value, dict):
            return None

        return {k: self._clean_string(v) for k, v in value.items()}

    def _parse_date(self, date_str: Any) -> Optional[date]:
        """Parse date string to date object."""
        if not date_str:
            return None

        if isinstance(date_str, str):
            # Try different date formats
            for fmt in ["%Y-%m-%d", "%Y-%m", "%Y"]:
                try:
                    parsed = datetime.strptime(date_str, fmt)
                    return parsed.date()
                except ValueError:
                    continue

        return None

    def _extract_location_string(self, location_data: Any) -> Optional[str]:
        """Extract location string from various location formats."""
        if isinstance(location_data, str):
            return self._clean_string(location_data)
        elif isinstance(location_data, dict):
            parts = [
                location_data.get("locality"),
                location_data.get("region"),
                location_data.get("country"),
            ]
            # Filter out None values and clean strings
            location_parts = [self._clean_string(part) for part in parts if part]
            filtered_parts = [part for part in location_parts if part is not None]
            return ", ".join(filtered_parts) if filtered_parts else None  # type: ignore

        return None
