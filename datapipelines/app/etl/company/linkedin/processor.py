"""
LinkedIn Company Data Processor for TractionX Data Pipeline Service.

This module provides ETL processing for LinkedIn company enrichment data.
Handles cleaning, validation, UUID generation, and storage.
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import aiohttp
from app.configs import get_logger
from app.ctx.job.linkedin.company import LinkedInCompanyJobContext
from app.models.company import CompanyDataSource, LinkedInCompanyData
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class LinkedInCompanyDataProcessor:
    """
    Comprehensive processor for LinkedIn company enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.LinkedInCompanyDataProcessor")

    async def process(
        self,
        payload: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: LinkedInCompanyJobContext,
    ) -> Dict[str, Any]:
        """
        Process a complete LinkedIn company payload and store in normalized schema.

        Args:
            payload: Complete LinkedIn payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup
            job_context: LinkedIn company job context

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload
            metadata = job_context._asdict()

            # Validate required fields
            validation_result = self._validate_linkedin_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "company_id": metadata.get("company_id"),
                }

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                enrichment_data, metadata, s3_raw_data_key
            )
            self.logger.info(f"Cleaned data: {cleaned_data}")

            # Store in database with transaction
            await self._store_company_data(cleaned_data)

            self.logger.info(
                f"Successfully processed LinkedIn company data for company {job_context.company_id}"
            )

            return {
                "success": True,
                "company_id": job_context.company_id,
                "records_created": {
                    "company_data_source": 1,
                },
            }

        except Exception as e:
            error_msg = f"LinkedIn company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    def _validate_linkedin_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate LinkedIn company enrichment data.

        Args:
            enrichment_data: Raw enrichment data
            metadata: Job metadata

        Returns:
            Validation result
        """
        errors = []

        # Check required metadata fields
        required_metadata_fields = ["company_id", "org_id", "company_domain"]
        for field in required_metadata_fields:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check if we have any enrichment data
        if not enrichment_data:
            errors.append("No enrichment data provided")

        # Check if we have basic company information
        if enrichment_data:
            if not enrichment_data.get("name") and not enrichment_data.get(
                "description"
            ):
                errors.append("No basic company information found")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
        }

    async def _clean_enrichment_data(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Clean and structure LinkedIn company enrichment data.

        This processor handles the real LinkedIn data structure from BrightData,
        which includes fields like:
        - id, name, description, about, website
        - industries, organization_type, company_size
        - headquarters, country_code, country_codes_array
        - locations, formatted_locations
        - followers, employees_in_linkedin
        - employees (with img, link, subtitle, title structure)
        - image, logo, slogan
        - similar, updates, crunchbase_url, funding, etc.

        Args:
            enrichment_data: Raw enrichment data
            metadata: Job metadata
            company_id: Company ID
            s3_raw_data_key: S3 key for raw data

        Returns:
            Cleaned data structure
        """
        self.logger.info(f"Processing LinkedIn data with {len(enrichment_data)} fields")

        # Get company ID for S3 path generation
        company_id = metadata.get("company_id", "")

        # Download company image and logo to S3
        image_s3_url = None
        logo_s3_url = None

        image_url = enrichment_data.get("image")
        if image_url and isinstance(image_url, str):
            image_s3_url = await self._download_company_image(image_url, company_id)
            if image_s3_url:
                self.logger.info(f"Downloaded company image to S3: {image_s3_url}")
            else:
                self.logger.warning(
                    f"Failed to download company image from: {image_url}"
                )

        logo_url = enrichment_data.get("logo")
        if logo_url and isinstance(logo_url, str):
            logo_s3_url = await self._download_company_logo(logo_url, company_id)
            if logo_s3_url:
                self.logger.info(f"Downloaded company logo to S3: {logo_s3_url}")
            else:
                self.logger.warning(f"Failed to download company logo from: {logo_url}")

        # Create LinkedIn company data model with proper field mapping
        linkedin_company_data = LinkedInCompanyData(
            linkedin_url=metadata.get("linkedin_url") or enrichment_data.get("url"),
            linkedin_id=enrichment_data.get("id") or enrichment_data.get("linkedin_id"),
            brightdata_snapshot_id=metadata.get("brightdata_snapshot_id"),
            name=self._clean_string(enrichment_data.get("name")),
            description=self._clean_string(enrichment_data.get("description")),
            about=self._clean_string(enrichment_data.get("about")),
            overview=self._clean_string(enrichment_data.get("overview")),
            website=self._clean_string(enrichment_data.get("website")),
            domain=metadata.get("company_domain"),
            industry=self._clean_string(enrichment_data.get("industry")),
            industries=self._clean_string(enrichment_data.get("industries")),
            company_type=self._clean_string(enrichment_data.get("company_type")),
            organization_type=self._clean_string(
                enrichment_data.get("organization_type")
            ),
            company_size=self._clean_string(enrichment_data.get("company_size")),
            founded_year=self._parse_int(enrichment_data.get("founded_year")),
            employee_count=self._parse_int(enrichment_data.get("employee_count")),
            employees_in_linkedin=self._parse_int(
                enrichment_data.get("employees_in_linkedin")
            ),
            employee_count_range=self._clean_string(
                enrichment_data.get("employee_count_range")
            ),
            headquarters=self._clean_string(enrichment_data.get("headquarters")),
            country=self._clean_string(enrichment_data.get("country")),
            country_code=self._clean_string(enrichment_data.get("country_code")),
            country_codes_array=self._clean_list(
                enrichment_data.get("country_codes_array", [])
            ),
            city=self._clean_string(enrichment_data.get("city")),
            state=self._clean_string(enrichment_data.get("state")),
            locations=self._clean_list(enrichment_data.get("locations", [])),
            formatted_locations=self._clean_list(
                enrichment_data.get("formatted_locations", [])
            ),
            followers_count=self._parse_int(
                enrichment_data.get("followers_count")
                or enrichment_data.get("followers")
            ),
            connections_count=self._parse_int(enrichment_data.get("connections_count")),
            specialties=self._clean_list(enrichment_data.get("specialties", [])),
            technologies=self._clean_list(enrichment_data.get("technologies", [])),
            keywords=self._clean_list(enrichment_data.get("keywords", [])),
            competitors=self._clean_list(enrichment_data.get("competitors", [])),
            email=self._clean_string(enrichment_data.get("email")),
            phone=self._clean_string(enrichment_data.get("phone")),
            employees=self._clean_list(enrichment_data.get("employees", [])),
            executives=self._clean_list(enrichment_data.get("executives", [])),
            image=image_s3_url or self._clean_string(enrichment_data.get("image")),
            logo=logo_s3_url or self._clean_string(enrichment_data.get("logo")),
            slogan=self._clean_string(enrichment_data.get("slogan")),
            similar=self._clean_list(enrichment_data.get("similar", [])),
            updates=self._clean_list(enrichment_data.get("updates", [])),
            crunchbase_url=self._clean_string(enrichment_data.get("crunchbase_url")),
            funding=enrichment_data.get("funding"),
            investors=self._clean_investors_list(enrichment_data.get("investors", [])),
            stock_info=enrichment_data.get("stock_info"),
            affiliated=self._clean_list(enrichment_data.get("affiliated", [])),
            linkedin_metadata=enrichment_data,
            enrichment_date=datetime.now(timezone.utc),
        )

        self.logger.info(
            f"Created LinkedIn company data for: {linkedin_company_data.name}"
        )

        # Create company data source record
        company_data_source = CompanyDataSource(
            company_id=metadata.get("company_id", ""),
            org_id=metadata["org_id"],
            source="linkedin",
            source_url=metadata.get("linkedin_url") or enrichment_data.get("url"),
            confidence_score=1.0,  # Default confidence for LinkedIn data
            enrichment_date=datetime.now(timezone.utc),
            s3_raw_data_key=s3_raw_data_key,
        )

        return {
            "linkedin_company_data": linkedin_company_data,
            "company_data_source": company_data_source,
        }

    async def _store_company_data(self, cleaned_data: Dict[str, Any]) -> None:
        """
        Store cleaned company data in the database.

        Args:
            cleaned_data: Cleaned data structure
        """
        try:
            # Store company data source record
            company_data_source = cleaned_data["company_data_source"]
            await self.rds.upsert(
                "company.datasources",
                company_data_source.model_dump(for_rds=True),
                key_fields=["company_id", "org_id", "source"],
            )

            # Store LinkedIn company data
            linkedin_company_data = cleaned_data["linkedin_company_data"]
            linkedin_data_dict = linkedin_company_data.model_dump(for_rds=True)

            # Add the required fields for the database
            linkedin_data_dict.update({
                "company_id": company_data_source.company_id,
                "org_id": company_data_source.org_id,
            })

            await self.rds.upsert(
                "company.linkedin",
                linkedin_data_dict,
                key_fields=["company_id", "org_id"],
            )

            self.logger.info(
                f"Successfully stored LinkedIn company data for company {company_data_source.company_id}"
            )

        except Exception as e:
            error_msg = f"Failed to store LinkedIn company data: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error for debugging."""
        try:
            await self.rds.log_pipeline_error(
                pipeline_name="linkedin_company",
                error_type="processing",
                error_message=error_msg,
                entity_id=payload.get("company_id"),
                entity_type="company",
                org_id=payload.get("org_id"),
                company_id=payload.get("company_id"),
                raw_data_key=s3_key,
                payload_preview=str(payload)[:1000],
            )

        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and normalize string values."""
        if not value or not isinstance(value, str):
            return None

        cleaned = value.strip()
        if not cleaned:
            return None

        # Remove excessive whitespace
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _clean_list(self, value: Any) -> list:
        """Clean and normalize list values, converting complex objects to JSON strings."""
        if not isinstance(value, list):
            return []

        cleaned_list = []
        for item in value:
            if isinstance(item, str):
                cleaned_item = self._clean_string(item)
                if cleaned_item:
                    cleaned_list.append(cleaned_item)
            elif isinstance(item, dict):
                # For complex objects, convert to JSON string for array storage
                cleaned_dict = {}
                for key, val in item.items():
                    if isinstance(val, str):
                        cleaned_val = self._clean_string(val)
                        if cleaned_val is not None:
                            cleaned_dict[key] = cleaned_val
                    else:
                        cleaned_dict[key] = val
                # Convert the cleaned dict to JSON string
                import json

                cleaned_list.append(json.dumps(cleaned_dict))
            else:
                # For other types (int, float, etc.), convert to string
                cleaned_list.append(str(item))

        return cleaned_list

    def _clean_investors_list(self, value: Any) -> list:
        """Clean and normalize investor list values, converting to JSON strings."""
        if not isinstance(value, list):
            return []

        cleaned_investors = []
        for investor in value:
            if isinstance(investor, str):
                # Convert string to dictionary format and then to JSON string
                cleaned_investor = self._clean_string(investor)
                if cleaned_investor:
                    import json

                    cleaned_investors.append(json.dumps({"name": cleaned_investor}))
            elif isinstance(investor, dict):
                # For complex objects like investors, clean them and convert to JSON string
                cleaned_investor_dict = {}
                for key, val in investor.items():
                    if isinstance(val, str):
                        cleaned_val = self._clean_string(val)
                        if cleaned_val is not None:
                            cleaned_investor_dict[key] = cleaned_val
                    else:
                        cleaned_investor_dict[key] = val
                import json

                cleaned_investors.append(json.dumps(cleaned_investor_dict))
            else:
                # For other types (int, float, etc.), convert to string and then to JSON
                if investor is not None:
                    import json

                    cleaned_investors.append(json.dumps({"name": str(investor)}))

        return cleaned_investors

    def _parse_int(self, value: Any) -> Optional[int]:
        """Parse integer values."""
        if value is None:
            return None

        if isinstance(value, int):
            return value

        if isinstance(value, str):
            try:
                # Remove any non-numeric characters except digits and minus
                import re

                numeric_str = re.sub(r"[^\d-]", "", value)
                if numeric_str:
                    return int(numeric_str)
            except (ValueError, TypeError):
                pass

        # Handle cases where the value might be a string representation of a number
        try:
            if isinstance(value, (float, str)):
                return int(float(value))
        except (ValueError, TypeError):
            pass

        return None

    async def _download_company_image(
        self, image_url: str, company_id: str
    ) -> Optional[str]:
        """
        Download company image and store in S3.

        Args:
            image_url: Company image URL from LinkedIn
            company_id: Company ID for S3 path generation

        Returns:
            S3 URL of the stored image, or None if failed
        """
        if not image_url:
            return None

        try:
            # Download the image
            image_data = await self._download_image_file(image_url)
            if not image_data:
                return None

            # Determine file extension
            file_extension = self._get_image_file_extension(
                image_url, image_data["content_type"]
            )
            if not file_extension:
                file_extension = "jpg"  # Default to jpg

            # Generate S3 key
            s3_key = f"assets/companies/{company_id}/image.{file_extension}"

            # Upload to S3
            if self.s3:
                await self.s3.put_object(s3_key, image_data["content"])

                # Generate S3 URL
                s3_url = f"s3://{self.s3.bucket_name}/{s3_key}"
                self.logger.info(f"Company image stored in S3: {s3_url}")
                return s3_url
            else:
                self.logger.warning("S3 storage not available, skipping image upload")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading/storing company image: {e}")
            return None

    async def _download_company_logo(
        self, logo_url: str, company_id: str
    ) -> Optional[str]:
        """
        Download company logo and store in S3.

        Args:
            logo_url: Company logo URL from LinkedIn
            company_id: Company ID for S3 path generation

        Returns:
            S3 URL of the stored logo, or None if failed
        """
        if not logo_url:
            return None

        try:
            # Download the logo
            logo_data = await self._download_image_file(logo_url)
            if not logo_data:
                return None

            # Determine file extension
            file_extension = self._get_image_file_extension(
                logo_url, logo_data["content_type"]
            )
            if not file_extension:
                file_extension = "jpg"  # Default to jpg

            # Generate S3 key
            s3_key = f"assets/companies/{company_id}/logo.{file_extension}"

            # Upload to S3
            if self.s3:
                await self.s3.put_object(s3_key, logo_data["content"])

                # Generate S3 URL
                s3_url = f"s3://{self.s3.bucket_name}/{s3_key}"
                self.logger.info(f"Company logo stored in S3: {s3_url}")
                return s3_url
            else:
                self.logger.warning("S3 storage not available, skipping logo upload")
                return None

        except Exception as e:
            self.logger.error(f"Error downloading/storing company logo: {e}")
            return None

    async def _download_image_file(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Download image file from URL.

        Args:
            url: URL to download from

        Returns:
            Dictionary with content and content_type, or None if failed
        """
        try:
            timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout
            headers = {"User-Agent": "Mozilla/5.0 (compatible; TractionX-Bot/1.0)"}

            async with aiohttp.ClientSession(
                timeout=timeout, headers=headers
            ) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.read()
                        content_type = response.headers.get(
                            "content-type", "image/jpeg"
                        )

                        self.logger.info(
                            f"Downloaded image: {len(content)} bytes, content-type: {content_type}"
                        )

                        # Validate content type
                        if not content_type.startswith("image/"):
                            self.logger.warning(
                                f"Non-image content type: {content_type} for URL: {url}"
                            )
                            return None

                        # Validate file size (max 10MB for company images)
                        if len(content) > 10 * 1024 * 1024:
                            self.logger.warning(
                                f"Image file too large: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        # Validate minimum file size (at least 1KB)
                        if len(content) < 1024:
                            self.logger.warning(
                                f"Image file too small: {len(content)} bytes for URL: {url}"
                            )
                            return None

                        return {"content": content, "content_type": content_type}
                    else:
                        self.logger.error(
                            f"Failed to download image: HTTP {response.status} for URL: {url}"
                        )
                        return None

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error downloading image from {url}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error downloading image from {url}: {e}")
            return None

    def _get_image_file_extension(self, url: str, content_type: str) -> Optional[str]:
        """
        Determine file extension from URL or content type.

        Args:
            url: Original URL
            content_type: HTTP content type

        Returns:
            File extension (without dot), or None if cannot determine
        """
        try:
            # Try to get extension from URL
            parsed_url = urlparse(url)
            path = parsed_url.path.lower()

            # Common image extensions
            image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"]

            for ext in image_extensions:
                if path.endswith(ext):
                    return ext[1:]  # Remove the dot

            # Try to get extension from content type
            if content_type:
                # Map content types to extensions
                content_type_map = {
                    "image/jpeg": "jpg",
                    "image/jpg": "jpg",
                    "image/png": "png",
                    "image/gif": "gif",
                    "image/webp": "webp",
                    "image/svg+xml": "svg",
                }
                return content_type_map.get(content_type.lower())

            return None

        except Exception as e:
            self.logger.error(f"Error determining file extension: {e}")
            return None
