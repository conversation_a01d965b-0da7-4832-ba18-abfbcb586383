"""
Crunchbase Company Data Processor for TractionX Data Pipeline Service.

This module handles processing and cleaning of Crunchbase company data from BrightData.
Focuses purely on data processing logic while delegating storage interactions.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.ctx.job.crunchbase.company import CrunchbaseCompanyJobContext
from app.models.company import CompanyDataSource, CrunchbaseCompanyData
from app.storage.interfaces import RelationalStorageInterface

logger = get_logger(__name__)


class CrunchbaseCompanyDataProcessor:
    """Processor for Crunchbase company data from BrightData."""

    def __init__(
        self,
        rds_storage: RelationalStorageInterface,
        s3_storage: Any,  # Using Any for now since S3StorageInterface might not be available
    ):
        self.rds_storage = rds_storage
        self.s3_storage = s3_storage
        self.logger = logger

    async def process(
        self,
        raw_data: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: CrunchbaseCompanyJobContext,
    ) -> Dict[str, Any]:
        """
        Process Crunchbase company data from BrightData.

        Args:
            raw_data: Raw data from BrightData
            s3_raw_data_key: S3 key for raw data backup
            job_context: Job context with metadata

        Returns:
            Processing result with cleaned data
        """
        start_time = time.time()

        try:
            self.logger.info(
                f"Processing Crunchbase data for {job_context.company_domain}"
            )

            # Step 1: Clean and validate enrichment data
            cleaned_data = await self._clean_enrichment_data(
                raw_data, job_context.data, s3_raw_data_key
            )

            # Step 2: Store data to database
            await self._store_company_data(cleaned_data)

            processing_time = time.time() - start_time
            self.logger.info(
                f"Crunchbase data processing completed in {processing_time:.2f}s"
            )

            return {
                "success": True,
                "data": cleaned_data,
                "processing_time": processing_time,
            }

        except Exception as e:
            self.logger.error(f"Crunchbase data processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time,
            }

    async def _clean_enrichment_data(
        self,
        enrichment_data: Dict[str, Any],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Clean and validate Crunchbase enrichment data.

        Args:
            enrichment_data: Raw enrichment data from BrightData
            metadata: Job metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Cleaned data with CrunchbaseCompanyData and CompanyDataSource
        """
        # Extract the first item if it's a list
        if isinstance(enrichment_data, list) and len(enrichment_data) > 0:
            company_data = enrichment_data[0]  # type: ignore
        elif isinstance(enrichment_data, dict):
            company_data = enrichment_data
        else:
            raise ValueError("Invalid enrichment data format")

        # Clean the data
        logger.info(f"Cleaning company data: {company_data}")
        cleaned_company_data = self._clean_company_data(company_data)

        # Create CrunchbaseCompanyData model
        crunchbase_company_data = CrunchbaseCompanyData(
            crunchbase_url=cleaned_company_data.get("url"),
            crunchbase_id=cleaned_company_data.get("id"),
            brightdata_snapshot_id=metadata.get("snapshot_id"),
            # Basic company info
            name=cleaned_company_data.get("name"),
            legal_name=cleaned_company_data.get("legal_name"),
            description=cleaned_company_data.get("about")
            or cleaned_company_data.get("description"),
            about=cleaned_company_data.get("about"),
            full_description=cleaned_company_data.get("full_description"),
            website=cleaned_company_data.get("website"),
            domain=cleaned_company_data.get("domain"),
            # Business details
            company_type=cleaned_company_data.get("company_type"),
            operating_status=cleaned_company_data.get("operating_status"),
            ipo_status=cleaned_company_data.get("ipo_status"),
            founded_date=cleaned_company_data.get("founded_date"),
            founded_year=self._parse_int(cleaned_company_data.get("founded_year")),
            employee_count=self._parse_int(cleaned_company_data.get("employee_count")),
            employee_count_range=cleaned_company_data.get("num_employees"),
            # Location
            headquarters=cleaned_company_data.get("headquarters"),
            country=cleaned_company_data.get("country"),
            country_code=cleaned_company_data.get("country_code"),
            city=cleaned_company_data.get("city"),
            state=cleaned_company_data.get("state"),
            region=cleaned_company_data.get("region"),
            location=self._convert_location_to_string_list(
                cleaned_company_data.get("location", [])
            ),
            address=cleaned_company_data.get("address"),
            # Industries and categories
            industries=self._convert_to_string_list(
                cleaned_company_data.get("industries", [])
            ),
            industry=self._extract_primary_industry(
                cleaned_company_data.get("industries", [])
            ),
            sub_industry=cleaned_company_data.get("sub_industry"),
            # Financial information
            funding_total=self._parse_float(cleaned_company_data.get("funding_total")),
            funding_rounds=cleaned_company_data.get("funding_rounds", {}),
            funding_rounds_list=cleaned_company_data.get("funding_rounds_list", []),
            last_funding_date=cleaned_company_data.get("last_funding_date"),
            last_funding_amount=self._parse_float(
                cleaned_company_data.get("last_funding_amount")
            ),
            valuation=self._parse_float(cleaned_company_data.get("valuation")),
            revenue=self._parse_float(cleaned_company_data.get("revenue")),
            # Social presence
            social_media_links=self._convert_to_string_list(
                cleaned_company_data.get("social_media_links", [])
            ),
            linkedin_url=self._extract_linkedin_url(
                cleaned_company_data.get("social_media_links", [])
            ),
            twitter_url=cleaned_company_data.get("twitter_url"),
            facebook_url=cleaned_company_data.get("facebook_url"),
            # Contact information
            email=cleaned_company_data.get("email")
            or cleaned_company_data.get("contact_email"),
            phone=cleaned_company_data.get("phone")
            or cleaned_company_data.get("contact_phone"),
            contact_email=cleaned_company_data.get("contact_email"),
            contact_phone=cleaned_company_data.get("contact_phone"),
            # Team and people
            founders=self._convert_to_string_list(
                cleaned_company_data.get("founders", [])
            ),
            executives=self._convert_to_string_list(
                cleaned_company_data.get("executives", [])
            ),
            current_employees=self._convert_to_string_list(
                cleaned_company_data.get("current_employees", [])
            ),
            alumni=self._convert_to_string_list(cleaned_company_data.get("alumni", [])),
            # Company details
            technologies=self._convert_to_string_list(
                cleaned_company_data.get("technologies", [])
            ),
            keywords=self._convert_to_string_list(
                cleaned_company_data.get("keywords", [])
            ),
            competitors=self._convert_to_string_list(
                cleaned_company_data.get("competitors", [])
            ),
            similar_companies=self._convert_to_string_list(
                cleaned_company_data.get("similar_companies", [])
            ),
            # Additional Crunchbase data
            image=cleaned_company_data.get("image"),
            cb_rank=self._parse_int(cleaned_company_data.get("cb_rank")),
            monthly_visits=self._parse_int(cleaned_company_data.get("monthly_visits")),
            semrush_visits_latest_month=self._parse_int(
                cleaned_company_data.get("semrush_visits_latest_month")
            ),
            monthly_visits_growth=self._parse_float(
                cleaned_company_data.get("monthly_visits_growth")
            ),
            num_contacts=self._parse_int(cleaned_company_data.get("num_contacts")),
            num_contacts_linkedin=self._parse_int(
                cleaned_company_data.get("num_contacts_linkedin")
            ),
            num_employee_profiles=self._parse_int(
                cleaned_company_data.get("num_employee_profiles")
            ),
            num_news=self._parse_int(cleaned_company_data.get("num_news")),
            num_investors=self._parse_int(cleaned_company_data.get("num_investors")),
            num_event_appearances=self._parse_int(
                cleaned_company_data.get("num_event_appearances")
            ),
            num_acquisitions=self._parse_int(
                cleaned_company_data.get("num_acquisitions")
            ),
            num_investments=self._parse_int(
                cleaned_company_data.get("num_investments")
            ),
            num_exits=self._parse_int(cleaned_company_data.get("num_exits")),
            # Featured lists and highlights
            featured_list=self._convert_to_string_list(
                cleaned_company_data.get("featured_list", [])
            ),
            overview_highlights=cleaned_company_data.get("overview_highlights", {}),
            people_highlights=cleaned_company_data.get("people_highlights", {}),
            technology_highlights=cleaned_company_data.get("technology_highlights", {}),
            # Additional data
            bombora=self._convert_to_string_list(
                cleaned_company_data.get("bombora", [])
            ),
            investors=self._convert_to_string_list(
                cleaned_company_data.get("investors", [])
            ),
            event_appearances=self._convert_to_string_list(
                cleaned_company_data.get("event_appearances", [])
            ),
            acquisitions=self._convert_to_string_list(
                cleaned_company_data.get("acquisitions", [])
            ),
            funds_raised=self._convert_to_string_list(
                cleaned_company_data.get("funds_raised", [])
            ),
            investments=self._convert_to_string_list(
                cleaned_company_data.get("investments", [])
            ),
            exits=self._convert_to_string_list(cleaned_company_data.get("exits", [])),
            news=self._convert_news_to_dict(cleaned_company_data.get("news")),
            # news={},
            # Additional Crunchbase data
            crunchbase_metadata=cleaned_company_data,
            enrichment_date=datetime.now(timezone.utc),
        )

        self.logger.info(
            f"Created Crunchbase company data for: {crunchbase_company_data.name}"
        )

        # Create company data source record
        company_data_source = CompanyDataSource(
            company_id=metadata.get("company_id", ""),
            org_id=metadata["org_id"],
            source="crunchbase",
            source_url=cleaned_company_data.get("url"),
            confidence_score=1.0,  # Default confidence for Crunchbase data
            enrichment_date=datetime.now(timezone.utc),
            s3_raw_data_key=s3_raw_data_key,
        )

        return {
            "crunchbase_company_data": crunchbase_company_data,
            "company_data_source": company_data_source,
        }

    def _clean_company_data(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean company data by removing None values and cleaning strings."""
        cleaned_data = {}

        for key, value in company_data.items():
            if value is not None:
                if isinstance(value, str):
                    cleaned_value = value.strip()
                    if cleaned_value:
                        cleaned_data[key] = cleaned_value
                elif isinstance(value, (list, dict)):
                    cleaned_data[key] = self._clean_list(value)
                else:
                    cleaned_data[key] = value

        return cleaned_data

    def _clean_list(self, data: Any) -> Any:
        """Clean list or dict data recursively."""
        if isinstance(data, list):
            return [self._clean_list(item) for item in data if item is not None]
        elif isinstance(data, dict):
            return {k: self._clean_list(v) for k, v in data.items() if v is not None}
        elif isinstance(data, str):
            return data.strip()
        else:
            return data

    def _convert_to_string_list(self, data: Any) -> List[str]:
        """Convert complex data structures to list of strings for TEXT[] fields."""
        if not data:
            return []

        def _extract_clean_string(obj: Any) -> str:
            """Extract a clean string representation from any object."""
            if isinstance(obj, str):
                return obj.strip()
            elif isinstance(obj, (int, float)):
                return str(obj)
            elif isinstance(obj, dict):
                # Try to extract meaningful string fields first
                for key in ["name", "title", "value", "id", "text", "label"]:
                    if key in obj and obj[key] is not None:
                        return str(obj[key]).strip()

                # If no obvious string fields, create a summary
                summary_parts = []
                for key, value in obj.items():
                    if isinstance(value, (str, int, float)) and value is not None:
                        summary_parts.append(f"{key}: {value}")
                    elif isinstance(value, dict) and "value" in value:
                        summary_parts.append(f"{key}: {value['value']}")

                if summary_parts:
                    # Limit to first 3 fields to avoid overly long strings
                    return " | ".join(summary_parts[:3])
                else:
                    # Last resort: create a simple representation without quotes or brackets
                    return f"Object with {len(obj)} fields"
            elif isinstance(obj, list):
                # For lists, join the first few items
                if not obj:
                    return "Empty list"
                items = [_extract_clean_string(item) for item in obj[:3]]
                return ", ".join(items)
            else:
                # For any other type, create a simple string representation
                return str(obj).strip()

        if isinstance(data, list):
            result = []
            for item in data:
                clean_string = _extract_clean_string(item)
                if clean_string and clean_string not in [
                    "Empty list",
                    "Object with 0 fields",
                ]:
                    result.append(clean_string)
            return result
        elif isinstance(data, dict):
            clean_string = _extract_clean_string(data)
            return (
                [clean_string]
                if clean_string and clean_string != "Object with 0 fields"
                else []
            )
        else:
            clean_string = _extract_clean_string(data)
            return [clean_string] if clean_string else []

    def _parse_int(self, value: Any) -> Optional[int]:
        """Parse integer value safely."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Remove any non-numeric characters except decimal point
                cleaned = "".join(c for c in value if c.isdigit() or c == ".")
                if cleaned:
                    return int(float(cleaned))
            elif isinstance(value, (int, float)):
                return int(value)
        except (ValueError, TypeError):
            pass
        return None

    def _parse_float(self, value: Any) -> Optional[float]:
        """Parse float value safely."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Remove any non-numeric characters except decimal point
                cleaned = "".join(
                    c for c in value if c.isdigit() or c == "." or c == "-"
                )
                if cleaned:
                    return float(cleaned)
            elif isinstance(value, (int, float)):
                return float(value)
        except (ValueError, TypeError):
            pass
        return None

    def _extract_primary_industry(self, industries: list) -> Optional[str]:
        """Extract primary industry from industries list."""
        if not industries:
            return None
        try:
            # Get the first industry's value
            first_industry = industries[0]
            if isinstance(first_industry, dict):
                return first_industry.get("value")
            elif isinstance(first_industry, str):
                return first_industry
        except (IndexError, AttributeError):
            pass
        return None

    def _extract_linkedin_url(self, social_media_links: list) -> Optional[str]:
        """Extract LinkedIn URL from social media links."""
        if not social_media_links:
            return None
        try:
            for link in social_media_links:
                if isinstance(link, str) and "linkedin.com" in link.lower():
                    return link
        except (AttributeError, TypeError):
            pass
        return None

    def _convert_news_to_dict(self, news: Any) -> Optional[Dict[str, Any]]:
        """Convert news field to a dictionary if it's a list."""
        if news is None:
            return None
        elif isinstance(news, dict):
            return news
        elif isinstance(news, list):
            return {"news_items": news}
        else:
            return {"news": str(news)}

    def _convert_location_to_string_list(self, location: Any) -> List[str]:
        """Convert location data to a string list for TEXT[] storage."""
        if not location:
            return []

        if isinstance(location, list):
            # If it's a list of dicts, extract the 'name' field
            result = []
            for loc in location:
                if isinstance(loc, dict):
                    # Extract name field from location object
                    name = loc.get("name")
                    if name:
                        result.append(str(name).strip())
                elif loc is not None:
                    result.append(str(loc).strip())
            return result
        elif isinstance(location, dict):
            # If it's a single dict, extract the 'name' field
            name = location.get("name")
            if name:
                return [str(name).strip()]
            else:
                return [str(location).strip()]
        else:
            return [str(location).strip()]

    async def _store_company_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store company data to database."""
        try:
            # Store company data source record
            company_data_source = cleaned_data["company_data_source"]
            await self.rds_storage.upsert(
                "company.datasources",
                company_data_source.model_dump(for_rds=True),
                key_fields=["company_id", "org_id", "source"],
            )

            # Store Crunchbase company data
            crunchbase_company_data = cleaned_data["crunchbase_company_data"]
            crunchbase_data_dict = crunchbase_company_data.model_dump(for_rds=True)

            # Add the required fields for the database
            crunchbase_data_dict.update({
                "company_id": company_data_source.company_id,
                "org_id": company_data_source.org_id,
            })

            await self.rds_storage.upsert(
                "company.crunchbase",
                crunchbase_data_dict,
                key_fields=["company_id", "org_id"],
            )

            self.logger.info(
                f"Stored Crunchbase data for company {company_data_source.company_id}"
            )

        except Exception as e:
            self.logger.error(f"Failed to store Crunchbase data: {e}")
            raise
