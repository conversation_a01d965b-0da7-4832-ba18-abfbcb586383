"""
Apollo Company Data Processor for TractionX Data Pipeline Service.

This module provides ETL processing for Apollo company enrichment data.
Handles cleaning, validation, UUID generation, and storage.
"""

import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from app.configs import get_logger
from app.ctx.job.apollo.company import ApolloCompanyJobContext
from app.models.company import ApolloCompanyData, CompanyDataSource
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class ApolloCompanyDataProcessor:
    """
    Comprehensive processor for Apollo company enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.ApolloCompanyDataProcessor")

    async def process(
        self,
        payload: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: ApolloCompanyJobContext,
    ) -> Dict[str, Any]:
        """
        Process a complete Apollo company payload and store in normalized schema.

        Args:
            payload: Complete Apollo payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup
            job_context: Apollo company job context

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload
            metadata = job_context._asdict()

            if "organization" in enrichment_data:
                enrichment_data = enrichment_data["organization"]

            # Validate required fields
            validation_result = self._validate_apollo_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "company_id": metadata.get("company_id"),
                }

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                enrichment_data, metadata, s3_raw_data_key
            )
            self.logger.info(f"Cleaned data: {cleaned_data}")

            # Store in database with transaction
            await self._store_company_data(cleaned_data)

            # Also write cleaned output to a JSON file for validation
            output_path = await self._store_local_output(
                cleaned_data=cleaned_data, job_context=job_context
            )

            self.logger.info(
                f"Successfully processed Apollo company data for company {job_context.company_id}"
            )

            return {
                "success": True,
                "company_id": job_context.company_id,
                "records_created": {
                    "company_data_source": 1,
                },
                "output_file": output_path,
            }

        except Exception as e:
            error_msg = f"Apollo company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    def _validate_apollo_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate Apollo company enrichment data.

        Args:
            enrichment_data: Raw enrichment data
            metadata: Job metadata

        Returns:
            Validation result with success status and errors
        """
        errors = []

        # Check required metadata fields
        required_metadata_fields = ["company_id", "org_id", "company_domain"]
        for field in required_metadata_fields:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check if we have basic company data
        if not enrichment_data:
            errors.append("No enrichment data provided")

        # Check for basic company information
        if enrichment_data:
            if not enrichment_data.get("name") and not enrichment_data.get(
                "organization", {}
            ).get("name"):
                errors.append("Missing company name")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
        }

    async def _clean_enrichment_data(
        self,
        enrichment_data: Union[Dict[str, Any], List[Dict[str, Any]]],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Clean and extract Apollo company enrichment data.

        Args:
            enrichment_data: Raw enrichment data from Apollo API
            metadata: Job metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Cleaned data ready for storage
        """
        try:
            # Extract organization data from Apollo response
            organization = {}
            if isinstance(enrichment_data, dict):
                # Handle raw API response format
                if "organization" in enrichment_data:
                    organization = enrichment_data["organization"]
                else:
                    # If no organization wrapper, assume the data is the organization
                    organization = enrichment_data
            elif isinstance(enrichment_data, list) and len(enrichment_data) > 0:
                # Handle list format (shouldn't happen with Apollo API)
                organization = enrichment_data[0].get(
                    "organization", enrichment_data[0]
                )
            else:
                organization = {}

            # Create ApolloCompanyData model from raw organization data
            apollo_company_data = ApolloCompanyData(
                # Apollo-specific fields
                company_id=metadata["company_id"],
                org_id=metadata["org_id"],
                apollo_id=organization.get("id"),
                # Basic info
                name=self._clean_string(organization.get("name")),
                domain=self._clean_string(organization.get("primary_domain")),
                website=self._clean_string(organization.get("website_url")),
                description=self._clean_string(organization.get("short_description")),
                # Business details
                industry=self._clean_string(organization.get("industry")),
                sub_industry=self._clean_string(organization.get("sub_industry")),
                employee_count=self._parse_int(
                    organization.get("estimated_num_employees")
                ),
                employee_count_range=self._clean_string(
                    organization.get("employee_count_range")
                ),
                founded_year=self._parse_int(organization.get("founded_year")),
                # Location
                headquarters=self._clean_string(organization.get("raw_address")),
                country=self._clean_string(organization.get("country")),
                city=self._clean_string(organization.get("city")),
                state=self._clean_string(organization.get("state")),
                # Financial
                funding_total=self._parse_float(organization.get("total_funding")),
                funding_rounds=self._parse_int(organization.get("funding_rounds")),
                # Support both keys from Apollo: last_funding_date and latest_funding_round_date
                last_funding_date=(
                    organization.get("last_funding_date")
                    or organization.get("latest_funding_round_date")
                ),
                last_funding_amount=self._parse_float(
                    organization.get("last_funding_amount")
                ),
                valuation=self._parse_float(organization.get("valuation")),
                revenue=self._parse_float(organization.get("annual_revenue")),
                # Social presence
                linkedin_url=self._clean_string(organization.get("linkedin_url")),
                twitter_url=self._clean_string(organization.get("twitter_url")),
                facebook_url=self._clean_string(organization.get("facebook_url")),
                # Contact info
                email=self._clean_string(organization.get("email")),
                phone=self._clean_string(organization.get("phone")),
                # Array fields - ensure they're properly cleaned
                technologies=self._clean_list(organization.get("technologies", [])),
                keywords=self._clean_list(organization.get("keywords", [])),
                # JSON fields
                tech_stack=organization.get("tech_stack", {}),
                departmental_head_count=organization.get("departmental_head_count", {}),
                # Additional Apollo data
                apollo_metadata={
                    "raw_response": enrichment_data,
                    "processed_at": datetime.now(timezone.utc).isoformat(),
                },
                enrichment_date=datetime.now(timezone.utc),
            )

            # Create company data source record
            company_data_source = CompanyDataSource(
                company_id=metadata["company_id"],
                org_id=metadata["org_id"],
                source="apollo",
                source_url=apollo_company_data.website,
                confidence_score=0.8,  # Default confidence for Apollo data
                enrichment_date=datetime.now(timezone.utc),
                s3_raw_data_key=s3_raw_data_key,
            )

            return {
                "apollo_company_data": apollo_company_data,
                "company_data_source": company_data_source,
            }

        except Exception as e:
            self.logger.error(
                f"Error cleaning Apollo enrichment data: {e}", exc_info=True
            )
            raise

    async def _store_local_output(
        self, cleaned_data: Dict[str, Any], job_context: ApolloCompanyJobContext
    ) -> str:
        """
        Persist the cleaned output to a local JSON file (for validation only).

        Datetimes will be serialized to strings.
        """
        try:
            # Prepare output directory
            # repo_root/datapipelines/output/apollo/
            repo_root = Path(__file__).resolve().parents[4]
            output_dir = repo_root / "datapipelines" / "output" / "apollo"
            output_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            safe_domain = (job_context.company_domain or "unknown").replace("/", "_")
            filename = f"apollo_company_cleaned_{safe_domain}_{timestamp}.json"
            output_path = output_dir / filename

            # Build a JSON-serializable payload
            apollo_company_data = cleaned_data["apollo_company_data"].model_dump(
                mode="json"
            )
            # Include identifiers to mirror intended DB schema
            apollo_company_data["company_id"] = job_context.company_id
            apollo_company_data["org_id"] = job_context.org_id
            company_data_source = cleaned_data["company_data_source"].model_dump(
                mode="json"
            )

            payload = {
                "job_context": job_context._asdict(),
                "apollo_company_data": apollo_company_data,
                "company_data_source": company_data_source,
                "saved_at": datetime.now(timezone.utc).isoformat(),
            }

            with output_path.open("w", encoding="utf-8") as f:
                json.dump(payload, f, indent=2, default=str)

            self.logger.info(f"Saved local ETL output to {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"Failed to save local ETL output: {e}", exc_info=True)
            raise

    async def _store_company_data(self, cleaned_data: Dict[str, Any]) -> None:
        """
        Store cleaned company data in the database using upsert.

        Args:
            cleaned_data: Cleaned data ready for storage
        """
        try:
            # Store company data source record (upsert on company_id, org_id, source)
            company_data_source = cleaned_data["company_data_source"]
            await self.rds.upsert(
                "company.datasources",
                company_data_source.model_dump(for_rds=True),
                key_fields=["company_id", "org_id", "source"],
            )

            # Store Apollo company data (upsert on company_id, org_id)
            apollo_company_data = cleaned_data["apollo_company_data"]
            await self.rds.upsert(
                "company.apollo",
                apollo_company_data.model_dump(for_rds=True),
                key_fields=["company_id", "org_id"],
            )

            self.logger.info(
                f"Successfully stored Apollo company data for company {company_data_source.company_id}"
            )

        except Exception as e:
            self.logger.error(f"Error storing Apollo company data: {e}", exc_info=True)
            raise

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """
        Store processing error for debugging.

        Args:
            payload: Original payload
            error_msg: Error message
            s3_key: S3 key for raw data
        """
        try:
            error_data = {
                "error": error_msg,
                "payload": payload,
                "s3_key": s3_key,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            error_key = f"errors/apollo_company/{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}_{hash(error_msg) % 10000}.json"
            await self.s3.put_object(error_key, error_data)

            self.logger.info(f"Stored processing error to S3: {error_key}")

        except Exception as e:
            self.logger.warning(f"Failed to store processing error: {e}")

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and validate string value."""
        if not value:
            return None
        if isinstance(value, str):
            cleaned = value.strip()
            return cleaned if cleaned else None
        return str(value).strip() if str(value).strip() else None

    def _clean_list(self, value: Any) -> list:
        """Clean and validate list value."""
        if not value:
            return []
        if isinstance(value, list):
            # Clean each item in the list
            cleaned_items = []
            for item in value:
                if item is not None:
                    if isinstance(item, str):
                        cleaned_item = item.strip()
                        if cleaned_item:
                            cleaned_items.append(cleaned_item)
                    elif isinstance(item, dict):
                        # Handle complex objects (like technology objects)
                        if "name" in item:
                            cleaned_items.append(item["name"])
                        else:
                            cleaned_items.append(str(item))
                    else:
                        cleaned_items.append(str(item))
            return cleaned_items
        return [str(value)] if value is not None else []

    def _parse_int(self, value: Any) -> Optional[int]:
        """Parse integer value."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Extract numbers from strings like "50-100 employees"
                import re

                numbers = re.findall(r"\d+", value)
                if numbers:
                    return int(numbers[0])
                return None
            return int(value)
        except (ValueError, TypeError):
            return None

    def _parse_float(self, value: Any) -> Optional[float]:
        """Parse float value."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Remove currency symbols and convert to float
                import re

                amount_str = re.sub(r"[^\d.]", "", value)
                if amount_str:
                    return float(amount_str)
                return None
            return float(value)
        except (ValueError, TypeError):
            return None
