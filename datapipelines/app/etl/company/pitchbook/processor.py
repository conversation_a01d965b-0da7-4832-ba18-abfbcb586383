"""
PitchBook Company Data Processor for TractionX Data Pipeline Service.

This module provides ETL processing for PitchBook company enrichment data.
Handles cleaning, validation, UUID generation, and storage.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from app.configs import get_logger
from app.ctx.job.pitchbook.company import PitchBookCompanyJobContext
from app.models.company import CompanyDataSource, PitchBookCompanyData
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class PitchBookCompanyDataProcessor:
    """
    Comprehensive processor for PitchBook company enrichment data.
    Handles cleaning, validation, UUID generation, and storage.
    """

    def __init__(self, rds_storage: RDSStorageV2, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.PitchBookCompanyDataProcessor")

    async def process(
        self,
        payload: Dict[str, Any],
        s3_raw_data_key: str,
        job_context: PitchBookCompanyJobContext,
    ) -> Dict[str, Any]:
        """
        Process a complete PitchBook company payload and store in normalized schema.

        Args:
            payload: Complete PitchBook payload with enrichment_data and metadata
            s3_raw_data_key: S3 key for raw data backup
            job_context: PitchBook company job context

        Returns:
            Processing result with success status and details
        """
        try:
            enrichment_data = payload
            # Convert NamedTuple to dict using the _asdict() method if available, otherwise use manual conversion
            if hasattr(job_context, "_asdict"):
                metadata = job_context._asdict()
            else:
                # Manual conversion for NamedTuple
                metadata = {
                    "job_id": job_context.job_id,
                    "company_id": job_context.company_id,
                    "org_id": job_context.org_id,
                    "company_domain": job_context.company_domain,
                    "data": job_context.data,
                    "pitchbook_url": job_context.pitchbook_url,
                    "brightdata_snapshot_id": job_context.brightdata_snapshot_id,
                }

            # Validate required fields
            validation_result = self._validate_pitchbook_data(enrichment_data, metadata)
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "company_id": metadata.get("company_id"),
                }

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                enrichment_data, metadata, s3_raw_data_key
            )
            self.logger.info(f"Cleaned data: {cleaned_data}")

            # Store in database with transaction
            await self._store_company_data(cleaned_data)

            self.logger.info(
                f"Successfully processed PitchBook company data for company {job_context.company_id}"
            )

            return {
                "success": True,
                "company_id": job_context.company_id,
                "records_created": {
                    "company_data_source": 1,
                },
            }

        except Exception as e:
            error_msg = f"PitchBook company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    def _validate_pitchbook_data(
        self, enrichment_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate PitchBook company enrichment data.

        Args:
            enrichment_data: Raw enrichment data
            metadata: Job metadata

        Returns:
            Validation result with success status and errors
        """
        errors = []

        # Check required metadata fields
        required_metadata_fields = ["company_id", "org_id", "company_domain"]
        for field in required_metadata_fields:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check if we have basic company data
        if not enrichment_data:
            errors.append("No enrichment data provided")

        # Check for basic company information
        if enrichment_data:
            if not enrichment_data.get("name") and not enrichment_data.get(
                "company_name"
            ):
                errors.append("Missing company name")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
        }

    async def _clean_enrichment_data(
        self,
        enrichment_data: Union[Dict[str, Any], List[Dict[str, Any]]],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Clean and prepare PitchBook enrichment data for storage.

        Args:
            enrichment_data: Raw enrichment data (dict or list)
            metadata: Job metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Cleaned data ready for storage
        """
        try:
            # Extract company data from raw response
            if isinstance(enrichment_data, list) and len(enrichment_data) > 0:
                company_data = enrichment_data[0]
            elif isinstance(enrichment_data, dict):
                company_data = enrichment_data
            else:
                raise ValueError("Invalid enrichment_data format")

            if not company_data:
                raise ValueError("No company data found in enrichment_data")

            # Clean and prepare PitchBook company data
            cleaned_company_data = PitchBookCompanyData(
                # PitchBook-specific fields
                pitchbook_url=company_data.get("url"),
                pitchbook_id=company_data.get("id"),
                brightdata_snapshot_id=metadata.get("brightdata_snapshot_id"),
                company_id=metadata.get("company_id"),
                org_id=metadata["org_id"],
                # Basic company info
                name=company_data.get("company_name"),
                description=company_data.get("description"),
                website=self._extract_website_from_contact_info(
                    company_data.get("contact_information", [])
                ),
                domain=metadata.get("company_domain"),
                # Business details
                company_type=None,  # Extract from contact_information if available
                status=company_data.get("status"),
                founded_year=self._parse_int(company_data.get("year_founded")),
                employee_count=self._parse_int(company_data.get("employees")),
                # Location
                headquarters=self._extract_headquarters_from_contact_info(
                    company_data.get("contact_information", [])
                ),
                country=company_data.get("country"),
                city=company_data.get("city"),
                state=company_data.get("state"),
                # Financial information
                funding_total=None,  # Extract from funding data if available
                funding_rounds=company_data.get("financing_rounds"),
                last_funding_date=None,  # Extract from funding data if available
                last_funding_amount=self._parse_float(
                    company_data.get("last_funding_amount")
                ),
                latest_deal_amount=self._parse_latest_deal_amount(
                    company_data.get("latest_deal_amount"),
                    company_data.get("latest_deal_amount_value"),
                ),
                latest_deal_type=company_data.get("latest_deal_type"),
                valuation=None,  # Extract from financial data if available
                revenue=None,  # Extract from financial data if available
                # Investment information
                investments_count=company_data.get("investments"),
                investments=company_data.get("all_investments", []),
                investment_relationships=self._extract_investment_relationships(
                    company_data.get("all_investments", [])
                ),
                # Competitor information - convert dict list to string list
                competitors=self._extract_competitor_names(
                    company_data.get("competitors", [])
                ),
                # Contact information
                contact_information=company_data.get("contact_information", []),
                email=None,  # Extract from contact_information if available
                phone=None,  # Extract from contact_information if available
                # Social presence
                linkedin_url=self._extract_linkedin_url(
                    company_data.get("company_socials", [])
                ),
                twitter_url=None,  # Extract from social data if available
                facebook_url=None,  # Extract from social data if available
                # Company details
                technologies=[],  # Extract from company data if available
                keywords=[],  # Extract from company data if available
                patents=company_data.get("patents"),
                patent_activity=self._convert_patent_activity_to_dict(
                    company_data.get("patent_activity", [])
                ),
                # Research and analysis - convert list to dict
                research_analysis=self._convert_research_analysis_to_dict(
                    company_data.get("research_analysis", [])
                ),
                faq=company_data.get("faq", []),
                # Additional PitchBook data
                pitchbook_metadata={
                    "raw_data_keys": list(company_data.keys())
                    if isinstance(company_data, dict)
                    else [],
                    "processing_timestamp": datetime.now(timezone.utc).isoformat(),
                },
                enrichment_date=datetime.now(timezone.utc),
            )

            # Create company data source record
            company_data_source = CompanyDataSource(
                company_id=metadata["company_id"],
                org_id=metadata["org_id"],
                source="pitchbook",
                source_url=cleaned_company_data.pitchbook_url,
                confidence_score=0.8,
                enrichment_date=cleaned_company_data.enrichment_date,
                s3_raw_data_key=s3_raw_data_key,
            )

            return {
                "company_data_source": company_data_source,
                "pitchbook_company_data": cleaned_company_data,
            }

        except Exception as e:
            self.logger.error(
                f"Error cleaning PitchBook enrichment data: {e}", exc_info=True
            )
            raise

    async def _store_company_data(self, cleaned_data: Dict[str, Any]) -> None:
        """
        Store cleaned company data in the database.

        Args:
            cleaned_data: Cleaned data ready for storage
        """
        try:
            # Store company data source record - use upsert to handle duplicates
            company_data_source = cleaned_data["company_data_source"]
            await self.rds.upsert(
                "company.datasources",
                company_data_source.model_dump(for_rds=True),
                key_fields=["company_id", "org_id", "source"],
            )

            # Store PitchBook company data - use upsert to handle duplicates
            pitchbook_company_data = cleaned_data["pitchbook_company_data"]

            # Convert data for storage (serialize_value_for_rds handles TEXT[] conversion automatically)
            data_for_storage = pitchbook_company_data.model_dump(for_rds=True)

            self.logger.info(
                f"PitchBook company data for company_id: {data_for_storage}"
            )

            await self.rds.upsert(
                "company.pitchbook",
                data_for_storage,
                key_fields=["company_id", "org_id"],
            )

            self.logger.info(
                f"Successfully stored PitchBook company data for company_id: {company_data_source.company_id}"
            )

        except Exception as e:
            self.logger.error(
                f"Error storing PitchBook company data: {e}", exc_info=True
            )
            raise

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """
        Store processing error for debugging.

        Args:
            payload: Original payload
            error_msg: Error message
            s3_key: S3 key for raw data
        """
        try:
            error_data = {
                "error": error_msg,
                "payload": payload,
                "s3_key": s3_key,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            error_key = f"errors/pitchbook_company/{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}_{hash(error_msg) % 10000}.json"
            await self.s3.put_object(error_key, error_data)

            self.logger.info(f"Stored processing error to S3: {error_key}")

        except Exception as e:
            self.logger.warning(f"Failed to store processing error: {e}")

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and validate string value."""
        if not value:
            return None
        if isinstance(value, str):
            cleaned = value.strip()
            return cleaned if cleaned else None
        return str(value).strip() if str(value).strip() else None

    def _clean_list(self, value: Any) -> list:
        """Clean and validate list value."""
        if not value:
            return []
        if isinstance(value, list):
            return [item for item in value if item is not None]
        return [value] if value is not None else []

    def _parse_int(self, value: Any) -> Optional[int]:
        """Parse integer value."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Extract numbers from strings like "50-100 employees"
                import re

                numbers = re.findall(r"\d+", value)
                if numbers:
                    return int(numbers[0])
                return None
            return int(value)
        except (ValueError, TypeError):
            return None

    def _parse_float(self, value: Any) -> Optional[float]:
        """Parse float value."""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                # Remove currency symbols and convert to float
                import re

                amount_str = re.sub(r"[^\d.]", "", value)
                if amount_str:
                    return float(amount_str)
                return None
            return float(value)
        except (ValueError, TypeError):
            return None

    def _parse_latest_deal_amount(
        self, amount_str: Optional[str], amount_value: Optional[Dict[str, Any]]
    ) -> Optional[float]:
        """Parse the latest deal amount from a string or a value object."""
        # First try to use the amount_value object if available
        if amount_value and isinstance(amount_value, dict):
            value = amount_value.get("value")
            if value is not None:
                try:
                    return float(value)
                except (ValueError, TypeError):
                    pass

        # Fall back to parsing the amount string
        if amount_str:
            return self._parse_float(amount_str)

        return None

    def _extract_website_from_contact_info(self, contact_info: list) -> Optional[str]:
        """Extract website from contact information."""
        for contact in contact_info:
            if isinstance(contact, dict) and contact.get("Type") == "Website":
                return contact.get("value")
        return None

    def _extract_headquarters_from_contact_info(
        self, contact_info: list
    ) -> Optional[str]:
        """Extract headquarters from contact information."""
        for contact in contact_info:
            if isinstance(contact, dict) and contact.get("Type") == "Corporate Office":
                return contact.get("value")
        return None

    def _extract_linkedin_url(self, socials: list) -> Optional[str]:
        """Extract LinkedIn URL from social links."""
        for social in socials:
            if isinstance(social, dict) and "linkedin.com" in social.get("domain", ""):
                return social.get("link")
        return None

    def _extract_investment_relationships(self, investments: list) -> list:
        """Extract investment relationships from investments data."""
        relationships = []
        for investment in investments:
            if isinstance(investment, dict):
                # Extract deal size if available
                deal_size = None
                if investment.get("deal_size"):
                    try:
                        deal_size = float(investment["deal_size"])
                    except (ValueError, TypeError):
                        pass

                # Determine investment stage from deal type
                investment_stage = None
                deal_type = investment.get("deal_type")
                if deal_type:
                    deal_type_lower = deal_type.lower()
                    if "seed" in deal_type_lower:
                        investment_stage = "seed"
                    elif "series a" in deal_type_lower:
                        investment_stage = "series_a"
                    elif "series b" in deal_type_lower:
                        investment_stage = "series_b"
                    elif "series c" in deal_type_lower:
                        investment_stage = "series_c"
                    elif "ipo" in deal_type_lower:
                        investment_stage = "ipo"
                    elif "acquisition" in deal_type_lower:
                        investment_stage = "acquisition"
                    elif (
                        "accelerator" in deal_type_lower
                        or "incubator" in deal_type_lower
                    ):
                        investment_stage = "accelerator_incubator"

                investment_relationship = {
                    "company_name": investment.get("company_name"),
                    "industry": investment.get("industry"),
                    "deal_date": investment.get("deal_date"),
                    "deal_size": deal_size,
                    "deal_type": deal_type,
                    "investment_stage": investment_stage,
                    "confidence_score": 0.9,
                }
                relationships.append(investment_relationship)

        return relationships

    def _extract_competitor_names(self, competitors: list) -> list:
        """Extract competitor names from competitors data."""
        names = []
        for competitor in competitors:
            if isinstance(competitor, dict):
                name = competitor.get("company_name") or competitor.get("name")
                if name:
                    names.append(name)
        return names

    def _extract_competitor_relationships(self, competitors: list) -> list:
        """Extract competitor relationships from competitors data."""
        relationships = []
        for competitor in competitors:
            if isinstance(competitor, dict):
                competitor_relationship = {
                    "name": competitor.get("name") or competitor.get("company_name"),
                    "industry": competitor.get("industry"),
                    "url": competitor.get("url"),
                    "confidence_score": 0.8,
                }
                relationships.append(competitor_relationship)

        return relationships

    def _convert_patent_activity_to_dict(
        self, patent_activity: list
    ) -> Optional[Dict[str, Any]]:
        """Convert patent activity list to a dictionary."""
        if not patent_activity:
            return None

        # Convert the list to a dictionary with publication_id as key
        activity_dict = {}
        for item in patent_activity:
            if isinstance(item, dict):
                publication_id = item.get("publication_id")
                if publication_id:
                    activity_dict[publication_id] = {
                        "patent_title": item.get("patent_title"),
                        "status": item.get("status"),
                        "first_filing_date": item.get("first_filing_date"),
                    }
        return activity_dict

    def _convert_research_analysis_to_dict(
        self, research_analysis: list
    ) -> Optional[Dict[str, Any]]:
        """Convert research analysis list to a dictionary."""
        if not research_analysis:
            return None

        # Convert the list to a dictionary with title as key
        analysis_dict = {}
        for item in research_analysis:
            if isinstance(item, dict):
                title = item.get("title")
                if title:
                    analysis_dict[title] = {
                        "link": item.get("link"),
                        "date": item.get("date"),
                    }
        return analysis_dict
