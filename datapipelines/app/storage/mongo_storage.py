"""
MongoDB storage implementation for TractionX Data Pipeline Service.
"""

import asyncio
from typing import Any, Dict, List, Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from app.configs import get_logger, settings


class MongoStorage:
    """MongoDB storage implementation for barrier tracking."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.client: Optional[AsyncIOMotorClient] = None
        self.db: Optional[AsyncIOMotorDatabase] = None
        self.connection_string = settings.mongo_connection_string

    async def initialize(self) -> None:
        """Initialize the MongoDB connection."""
        try:
            self.client = AsyncIOMotorClient(self.connection_string)
            self.db = self.client[settings.MONGODB_DB_NAME]
            self.logger.info("MongoDB storage initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize MongoDB storage: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up MongoDB resources."""
        if self.client:
            self.client.close()
            self.logger.info("MongoDB storage cleaned up")


class DealsMongoStorage:
    """MongoDB storage implementation specifically for deals collection operations."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.client: Optional[AsyncIOMotorClient] = None
        self.db: Optional[AsyncIOMotorDatabase] = None
        self.connection_string = (
            f"{settings.MONGODB_BACKEND_URL}/{settings.MONGODB_BACKEND_DB_NAME}"
        )

    async def initialize(self) -> None:
        """Initialize the MongoDB connection for deals."""
        try:
            self.client = AsyncIOMotorClient(self.connection_string)
            self.db = self.client[settings.MONGODB_BACKEND_DB_NAME]
            self.logger.info("Deals MongoDB storage initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Deals MongoDB storage: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up MongoDB resources."""
        if self.client:
            self.client.close()
            self.logger.info("Deals MongoDB storage cleaned up")

    async def get_deals_by_company_domain(
        self, company_domain: str
    ) -> List[Dict[str, Any]]:
        """
        Get all deals for a specific company domain.

        Args:
            company_domain: Company domain to search for

        Returns:
            List of deal documents
        """
        try:
            if self.db is None:
                raise ValueError("Database not initialized")

            # Query deals collection for the company domain
            # Assuming company_website field contains the domain
            cursor = self.db.deals.find({
                "company_website": {"$regex": company_domain, "$options": "i"}
            })

            deals = await cursor.to_list(length=None)
            self.logger.info(
                f"Found {len(deals)} deals for company domain: {company_domain}"
            )
            return deals

        except Exception as e:
            self.logger.error(
                f"Error getting deals for company domain {company_domain}: {e}"
            )
            return []

    async def update_deal_fields(self, deal_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update specific fields in a deal document.

        Args:
            deal_id: Deal document ID (string)
            updates: Dictionary of fields to update

        Returns:
            Success status
        """
        try:
            if self.db is None:
                raise ValueError("Database not initialized")

            # Convert string deal_id to ObjectId
            try:
                object_id = ObjectId(deal_id)
            except Exception as e:
                self.logger.error(f"Invalid ObjectId format for deal_id {deal_id}: {e}")
                return False

            # Add updated_at timestamp
            updates["updated_at"] = int(asyncio.get_event_loop().time())

            result = await self.db.deals.update_one(
                {"_id": object_id}, {"$set": updates}
            )

            success = result.modified_count > 0
            if success:
                self.logger.info(
                    f"Updated deal {deal_id} with fields: {list(updates.keys())}"
                )
            else:
                self.logger.warning(
                    f"No changes made to deal {deal_id}, result: {result}, updates: {updates}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Error updating deal {deal_id}: {e}")
            return False
