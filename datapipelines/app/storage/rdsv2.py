"""
RDS Storage V2 for TractionX Data Pipeline Service.

This module provides a modern, unified storage interface that automatically
selects the appropriate PostgreSQL provider (Neon for dev/staging, AWS RDS for production)
through the RDS manager.
"""

import json
from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.clients.postgres.rds_manager import get_rds_manager
from app.configs import get_logger
from app.models.base import BaseModel
from app.storage.interfaces import RelationalStorageInterface


class RDSStorageV2(RelationalStorageInterface):
    """
    Modern PostgreSQL storage implementation with automatic provider selection.

    Features:
    - Automatic provider selection (Neon for dev/staging, AWS RDS for production)
    - Unified interface for all database operations
    - Professional error handling and logging
    - Transaction support
    - Health monitoring
    - Connection pooling and optimization
    """

    def __init__(self):
        """Initialize the RDS storage with automatic provider selection."""
        self.logger = get_logger(f"{__name__}.RDSStorageV2")
        self.rds_manager = get_rds_manager()
        self.client = None

        self.logger.info("RDS Storage V2 initialized")

    async def initialize(self) -> None:
        """Initialize the database connection."""
        try:
            await self.rds_manager.initialize()
            self.client = self.rds_manager.get_client()

            provider_info = self.rds_manager.get_provider_info()
            self.logger.info(
                "RDS Storage V2 initialized successfully",
                provider=provider_info["provider"],
                environment=provider_info["environment"],
            )

        except Exception as e:
            self.logger.error(f"Failed to initialize RDS Storage V2: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up database resources."""
        try:
            await self.rds_manager.cleanup()
            self.client = None
            self.logger.info("RDS Storage V2 cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    async def health_check(self) -> bool:
        """Check if database is healthy."""
        try:
            health_status = await self.rds_manager.health_check()
            return health_status.get("healthy", False)
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False

    async def create_table(self, table_name: str, schema: Dict[str, Any]) -> bool:
        """Create a table with the given schema."""
        try:
            # Convert schema to SQL DDL
            columns = []
            for column_name, column_def in schema.items():
                columns.append(f"{column_name} {column_def}")

            ddl = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"

            await self.rds_manager.execute_query(ddl)
            self.logger.info(f"Created table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create table {table_name}: {e}")
            return False

    async def insert(
        self, table_name: str, data: Union[Dict[str, Any], BaseModel]
    ) -> str:
        """Insert data and return the ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump(mode="python")
            else:
                data_dict = data.copy()

            # Add timestamp if not present
            if "created_at" not in data_dict:
                data_dict["created_at"] = datetime.now(timezone.utc)

            # Prepare SQL with proper quoting
            columns = [self._quote_column_name(col) for col in data_dict.keys()]
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in data_dict.keys()]

            sql = f"""
                INSERT INTO {table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                RETURNING id
            """

            record_id = await self.rds_manager.execute_scalar(sql, values)
            self.logger.debug(f"Inserted record into {table_name} with ID {record_id}")
            return str(record_id)

        except Exception as e:
            self.logger.error(f"Failed to insert into {table_name}: {e}")
            raise

    async def update(
        self, table_name: str, id: str, data: Union[Dict[str, Any], BaseModel]
    ) -> bool:
        """Update data by ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump(mode="python")
            else:
                data_dict = data.copy()

            # Add updated timestamp
            data_dict["updated_at"] = datetime.now(timezone.utc)

            # Prepare SQL with proper quoting
            set_clauses = []
            values = []
            for i, (column, value) in enumerate(data_dict.items()):
                quoted_column = self._quote_column_name(column)
                set_clauses.append(f"{quoted_column} = ${i + 1}")
                values.append(self._serialize_value(value))

            values.append(id)  # For WHERE clause

            sql = f"""
                UPDATE {table_name}
                SET {", ".join(set_clauses)}
                WHERE id = ${len(values)}
            """

            result = await self.rds_manager.execute_query(sql, values)
            success = len(result) > 0

            if success:
                self.logger.debug(f"Updated record in {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to update in {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to update {table_name} record {id}: {e}")
            return False

    def _quote_column_name(self, column_name: str) -> str:
        """Quote column names that are reserved keywords or contain special characters."""
        # List of PostgreSQL reserved keywords that we commonly use
        reserved_keywords = {
            "similar",
            "order",
            "group",
            "user",
            "comment",
            "key",
            "index",
            "table",
            "schema",
            "database",
            "type",
            "view",
            "function",
            "procedure",
            "trigger",
            "sequence",
            "constraint",
            "foreign",
            "primary",
            "unique",
            "check",
            "default",
            "null",
            "not",
            "and",
            "or",
            "in",
            "like",
            "between",
            "case",
            "when",
            "then",
            "else",
            "end",
            "as",
            "on",
            "join",
            "left",
            "right",
            "inner",
            "outer",
            "full",
            "cross",
            "union",
            "intersect",
            "except",
            "all",
            "distinct",
            "where",
            "having",
            "group",
            "order",
            "limit",
            "offset",
            "fetch",
            "for",
            "update",
            "share",
            "nowait",
            "skip",
            "locked",
            "of",
            "only",
            "with",
            "recursive",
            "window",
            "over",
            "partition",
            "rows",
            "range",
            "preceding",
            "following",
            "unbounded",
            "current",
            "row",
            "first",
            "last",
            "value",
            "values",
            "default",
            "returning",
            "into",
            "strict",
            "volatile",
            "stable",
            "immutable",
            "leakproof",
            "cost",
            "rows",
            "security",
            "invoker",
            "definer",
            "language",
            "replace",
            "create",
            "drop",
            "alter",
            "add",
            "set",
            "reset",
            "rename",
            "to",
            "from",
            "using",
            "references",
            "cascade",
            "restrict",
            "no",
            "action",
            "deferrable",
            "initially",
            "deferred",
            "immediate",
            "match",
            "full",
            "partial",
            "simple",
            "generated",
            "always",
            "stored",
            "virtual",
            "identity",
            "system",
            "user",
            "session",
            "local",
            "global",
            "temporary",
            "unlogged",
            "inherits",
            "like",
            "including",
            "excluding",
            "with",
            "without",
            "oids",
            "on",
            "commit",
            "preserve",
            "delete",
            "drop",
            "truncate",
            "analyze",
            "vacuum",
            "cluster",
            "reindex",
            "refresh",
            "materialized",
            "concurrently",
            "verbose",
            "freeze",
            "full",
            "fast",
            "skip",
            "locked",
            "nowait",
            "skip",
            "locked",
            "nowait",
            "skip",
            "locked",
        }

        # Quote if it's a reserved keyword or contains special characters
        if (
            column_name.lower() in reserved_keywords
            or not column_name.replace("_", "").replace("-", "").isalnum()
        ):
            return f'"{column_name}"'
        return column_name

    async def upsert(
        self,
        table_name: str,
        data: Union[Dict[str, Any], BaseModel],
        key_fields: List[str],
    ) -> str:
        """Insert or update data based on key fields."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump(mode="python")
            else:
                data_dict = data.copy()

            # Add timestamps
            now = datetime.now(timezone.utc)
            if "created_at" not in data_dict:
                data_dict["created_at"] = now
            data_dict["updated_at"] = now

            # Build conflict resolution with proper quoting
            conflict_columns = ", ".join([
                self._quote_column_name(field) for field in key_fields
            ])
            update_clauses = []
            for column in data_dict.keys():
                if column not in key_fields:
                    quoted_column = self._quote_column_name(column)
                    update_clauses.append(f"{quoted_column} = EXCLUDED.{quoted_column}")

            # Prepare SQL with proper quoting
            columns = [self._quote_column_name(col) for col in data_dict.keys()]
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in data_dict.keys()]

            sql = f"""
                INSERT INTO {table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                ON CONFLICT ({conflict_columns})
                DO UPDATE SET {", ".join(update_clauses)}
                RETURNING id
            """

            record_id = await self.rds_manager.execute_scalar(sql, values)
            self.logger.debug(f"Upserted record in {table_name} with ID {record_id}")
            return str(record_id)

        except Exception as e:
            self.logger.error(f"Failed to upsert into {table_name}: {e}")
            raise

    async def get_by_id(self, table_name: str, id: str) -> Optional[Dict[str, Any]]:
        """Get data by ID."""
        try:
            sql = f"SELECT * FROM {table_name} WHERE id = $1"
            result = await self.rds_manager.execute_query(sql, [id])

            if result:
                return result[0]
            return None

        except Exception as e:
            self.logger.error(
                f"Failed to get record from {table_name} with ID {id}: {e}"
            )
            return None

    async def get_by_fields(
        self, table_name: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get data by field filters."""
        try:
            # Build WHERE clause with proper quoting
            where_clauses = []
            values = []
            for i, (column, value) in enumerate(filters.items()):
                quoted_column = self._quote_column_name(column)
                where_clauses.append(f"{quoted_column} = ${i + 1}")
                values.append(self._serialize_value(value))

            where_clause = " AND ".join(where_clauses) if where_clauses else "TRUE"
            sql = f"SELECT * FROM {table_name} WHERE {where_clause}"

            return await self.rds_manager.execute_query(sql, values)

        except Exception as e:
            self.logger.error(f"Failed to get records from {table_name}: {e}")
            return []

    async def delete(self, table_name: str, id: str) -> bool:
        """Delete data by ID."""
        try:
            sql = f"DELETE FROM {table_name} WHERE id = $1"
            result = await self.rds_manager.execute_query(sql, [id])

            success = len(result) > 0

            if success:
                self.logger.debug(f"Deleted record from {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to delete from {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to delete from {table_name} record {id}: {e}")
            return False

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a custom query."""
        try:
            if params:
                param_list = list(params.values())
                return await self.rds_manager.execute_query(query, param_list)
            else:
                return await self.rds_manager.execute_query(query)

        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            return []

    def _serialize_value(self, value: Any) -> Any:
        """Serialize value for database storage."""
        if value is None:
            return None
        elif isinstance(value, UUID):
            return str(value)
        elif isinstance(value, dict):
            return json.dumps(value, default=self._json_serializer)
        elif isinstance(value, list):
            # For PostgreSQL arrays, pass the list directly
            # PostgreSQL will handle the conversion to array type
            # Note: This assumes the field is a PostgreSQL array, not JSONB
            return value
        elif isinstance(value, (datetime, date)):
            # Pass datetime/date objects directly - PostgreSQL can handle them
            return value
        elif isinstance(value, str):
            # Don't try to parse strings as dates - let PostgreSQL handle it
            return value
        else:
            return value

    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for complex objects."""
        if isinstance(obj, UUID):
            return str(obj)
        elif hasattr(obj, "model_dump"):  # Pydantic models
            return obj.model_dump(mode="python")
        elif hasattr(obj, "__dict__"):
            return obj.__dict__
        else:
            raise TypeError(
                f"Object of type {type(obj).__name__} is not JSON serializable"
            )

    # Advanced methods for complex operations

    async def insert_with_relations(
        self,
        table_name: str,
        main_data: Dict[str, Any],
        related_data: Dict[str, List[Dict[str, Any]]],
        foreign_key_field: str = "id",
    ) -> str:
        """
        Insert main record with related records in a transaction.

        Args:
            table_name: Main table name
            main_data: Main record data
            related_data: Dict of {table_name: [records]} for related tables
            foreign_key_field: Field name to use as foreign key

        Returns:
            Main record ID
        """
        try:
            # Prepare transaction queries
            queries = []

            # Main insert query
            main_columns = [self._quote_column_name(col) for col in main_data.keys()]
            main_placeholders = [f"${i + 1}" for i in range(len(main_columns))]
            main_values = [
                self._serialize_value(main_data[col]) for col in main_data.keys()
            ]

            main_sql = f"""
                INSERT INTO {table_name} ({", ".join(main_columns)})
                VALUES ({", ".join(main_placeholders)})
                RETURNING id
            """
            queries.append({"query": main_sql, "params": main_values})

            # Related table queries
            for related_table, records in related_data.items():
                for record in records:
                    record[foreign_key_field] = (
                        "${len(queries) + 1}"  # Reference main record ID
                    )
                    columns = [self._quote_column_name(col) for col in record.keys()]
                    placeholders = [f"${i + 1}" for i in range(len(columns))]
                    values = [
                        self._serialize_value(record[col]) for col in record.keys()
                    ]

                    sql = f"""
                        INSERT INTO {related_table} ({", ".join(columns)})
                        VALUES ({", ".join(placeholders)})
                    """
                    queries.append({"query": sql, "params": values})

            # Execute transaction
            results = await self.rds_manager.execute_transaction(queries)
            main_id = results[0][0]["id"] if results and results[0] else None

            self.logger.info(f"Inserted {table_name} with relations, ID: {main_id}")
            return str(main_id)

        except Exception as e:
            self.logger.error(f"Failed to insert with relations: {e}")
            raise

    async def get_with_relations(
        self,
        table_name: str,
        record_id: str,
        related_tables: List[str],
        foreign_key_field: str = "id",
    ) -> Optional[Dict[str, Any]]:
        """
        Get a record with all related data.

        Args:
            table_name: Main table name
            record_id: Record ID to retrieve
            related_tables: List of related table names
            foreign_key_field: Foreign key field name

        Returns:
            Dictionary with main record and related data
        """
        try:
            # Get main record
            main_record = await self.get_by_id(table_name, record_id)
            if not main_record:
                return None

            result = main_record.copy()

            # Get related records
            for related_table in related_tables:
                quoted_foreign_key = self._quote_column_name(foreign_key_field)
                sql = f"SELECT * FROM {related_table} WHERE {quoted_foreign_key} = $1"
                related_records = await self.rds_manager.execute_query(sql, [record_id])
                result[related_table] = related_records

            return result

        except Exception as e:
            self.logger.error(f"Failed to get with relations: {e}")
            return None

    async def bulk_insert(
        self,
        table_name: str,
        records: List[Dict[str, Any]],
        batch_size: int = 1000,
    ) -> List[str]:
        """
        Insert multiple records efficiently.

        Args:
            table_name: Table name
            records: List of record dictionaries
            batch_size: Number of records per batch

        Returns:
            List of inserted record IDs
        """
        try:
            if not records:
                return []

            all_ids = []

            # Process in batches
            for i in range(0, len(records), batch_size):
                batch = records[i : i + batch_size]

                # Prepare batch insert
                if len(batch) == 1:
                    # Single record insert
                    record_id = await self.insert(table_name, batch[0])
                    all_ids.append(record_id)
                else:
                    # Multi-record insert
                    queries = []
                    for record in batch:
                        # Add timestamp if not present
                        if "created_at" not in record:
                            record["created_at"] = datetime.now(timezone.utc)

                        columns = [
                            self._quote_column_name(col) for col in record.keys()
                        ]
                        placeholders = [f"${i + 1}" for i in range(len(columns))]
                        values = [
                            self._serialize_value(record[col]) for col in record.keys()
                        ]

                        sql = f"""
                            INSERT INTO {table_name} ({", ".join(columns)})
                            VALUES ({", ".join(placeholders)})
                            RETURNING id
                        """
                        queries.append({"query": sql, "params": values})

                    # Execute batch
                    results = await self.rds_manager.execute_transaction(queries)
                    batch_ids = [str(result[0]["id"]) for result in results if result]
                    all_ids.extend(batch_ids)

            self.logger.info(f"Bulk inserted {len(all_ids)} records into {table_name}")
            return all_ids

        except Exception as e:
            self.logger.error(f"Failed to bulk insert into {table_name}: {e}")
            raise

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current database provider."""
        return self.rds_manager.get_provider_info()

    async def get_health_status(self) -> Dict[str, Any]:
        """Get detailed health status."""
        return await self.rds_manager.health_check()

    # Founder-specific methods for elegant operations
    async def upsert_founder_with_relations(
        self,
        founder_data: Dict[str, Any],
        experiences: List[Dict[str, Any]],
        education: List[Dict[str, Any]],
        skills: List[Dict[str, Any]],
    ) -> str:
        """
        Upsert founder data with all related records in a single transaction.

        Args:
            founder_data: Founder record data
            experiences: List of experience records
            education: List of education records
            skills: List of skill records

        Returns:
            Founder record ID
        """
        try:
            # Get the original founder_id for foreign key relationships
            original_founder_id = founder_data.get("founder_id")
            if not original_founder_id:
                raise ValueError("founder_id is required in founder_data")

            # Upsert founder record first
            founder_uuid = await self.upsert(
                table_name="founder.basic", data=founder_data, key_fields=["founder_id"]
            )

            # Upsert experiences - use simpler unique key
            for experience in experiences:
                exp_data = experience.copy()
                exp_data["founder_id"] = founder_uuid  # Use UUID for foreign key
                # Use just founder_id + company_name + title for uniqueness
                # This avoids NULL start_date issues
                await self.upsert(
                    table_name="founder.experiences",
                    data=exp_data,
                    key_fields=["founder_id", "company_name", "title"],
                )

            # Upsert education - use simpler unique key
            for edu in education:
                edu_data = edu.copy()
                edu_data["founder_id"] = founder_uuid  # Use UUID for foreign key
                # Use just founder_id + school_name for uniqueness
                # This avoids NULL start_date issues
                await self.upsert(
                    table_name="founder.education",
                    data=edu_data,
                    key_fields=["founder_id", "school_name"],
                )

            # Upsert skills - use founder_id + skill (this one is fine)
            for skill in skills:
                skill_data = skill.copy()
                skill_data["founder_id"] = founder_uuid  # Use UUID for foreign key
                await self.upsert(
                    table_name="founder.skills",
                    data=skill_data,
                    key_fields=["founder_id", "skill"],
                )

            self.logger.info(
                f"Successfully upserted founder with relations, UUID: {founder_uuid}, founder_id: {original_founder_id}"
            )
            return founder_uuid

        except Exception as e:
            self.logger.error(f"Failed to upsert founder with relations: {e}")
            raise

    async def get_founder_with_relations(
        self, founder_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get founder record with all related data.

        Args:
            founder_id: Founder record ID

        Returns:
            Dictionary with founder and related data
        """
        try:
            return await self.get_with_relations(
                table_name="founder.basic",
                record_id=founder_id,
                related_tables=[
                    "founder.experiences",
                    "founder.education",
                    "founder.skills",
                ],
                foreign_key_field="founder_id",
            )
        except Exception as e:
            self.logger.error(f"Failed to get founder with relations: {e}")
            return None

    async def log_pipeline_error(
        self,
        pipeline_name: str,
        error_type: str,
        error_message: str,
        entity_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        org_id: Optional[str] = None,
        company_id: Optional[str] = None,
        raw_data_key: Optional[str] = None,
        payload_preview: Optional[str] = None,
        stack_trace: Optional[str] = None,
    ) -> str:
        """
        Log a pipeline error to the uniform error tracking table.

        Args:
            pipeline_name: Name of the pipeline (e.g., 'linkedin_founder', 'apollo_company')
            error_type: Type of error (e.g., 'validation', 'processing', 'storage')
            error_message: Error message
            entity_id: ID of the entity being processed
            entity_type: Type of entity (e.g., 'founder', 'company')
            org_id: Organization ID
            company_id: Company ID
            raw_data_key: S3 key for raw data
            payload_preview: Preview of the payload that caused the error
            stack_trace: Stack trace if available

        Returns:
            Error record ID
        """
        try:
            error_data = {
                "pipeline_name": pipeline_name,
                "error_type": error_type,
                "error_message": error_message,
                "entity_id": entity_id,
                "entity_type": entity_type,
                "org_id": org_id,
                "company_id": company_id,
                "raw_data_key": raw_data_key,
                "payload_preview": payload_preview,
                "stack_trace": stack_trace,
            }

            # Remove None values
            error_data = {k: v for k, v in error_data.items() if v is not None}

            error_id = await self.upsert(
                table_name="error.pipeline_errors",
                data=error_data,
                key_fields=["pipeline_name", "entity_id", "error_type"],
            )

            self.logger.info(f"Logged pipeline error: {error_id}")
            return error_id

        except Exception as e:
            self.logger.error(f"Failed to log pipeline error: {e}")
            raise

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


# Factory function for creating RDS Storage V2 instances
def create_rds_storage_v2() -> RDSStorageV2:
    """
    Factory function to create an RDS Storage V2 instance.

    Returns:
        Configured RDSStorageV2 instance
    """
    return RDSStorageV2()


# Global instance for convenience
_rds_storage_v2: Optional[RDSStorageV2] = None


def get_rds_storage_v2() -> RDSStorageV2:
    """
    Get the global RDS Storage V2 instance.

    Returns:
        RDSStorageV2 instance
    """
    global _rds_storage_v2

    if _rds_storage_v2 is None:
        _rds_storage_v2 = create_rds_storage_v2()

    return _rds_storage_v2


async def initialize_rds_storage_v2() -> None:
    """Initialize the global RDS Storage V2 instance."""
    storage = get_rds_storage_v2()
    await storage.initialize()


async def cleanup_rds_storage_v2() -> None:
    """Clean up the global RDS Storage V2 instance."""
    global _rds_storage_v2

    if _rds_storage_v2:
        await _rds_storage_v2.cleanup()
        _rds_storage_v2 = None
