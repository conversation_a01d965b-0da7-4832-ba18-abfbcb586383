"""
Status and monitoring API endpoints for TractionX Data Pipeline Service.
"""

from typing import Any, Dict

import redis.asyncio as redis  # type: ignore
from fastapi import APIRouter, HTTPException

from app.configs import get_logger, settings

logger = get_logger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Comprehensive health check for all pipeline components.
    """
    try:
        health_status = {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "components": {},
        }

        # Check Redis connection
        try:
            redis_conn = redis.from_url(
                settings.redis_connection_string, decode_responses=True
            )
            await redis_conn.ping()
            await redis_conn.close()
            health_status["components"]["redis"] = {"status": "healthy"}
        except Exception as e:
            health_status["components"]["redis"] = {
                "status": "unhealthy",
                "error": str(e),
            }
            health_status["status"] = "degraded"

        # Check external API configurations
        api_configs = {
            "clay": bool(settings.CLAY_API_KEY),
            "pdl": bool(settings.PDL_API_KEY),
            "serpapi": bool(settings.SERPAPI_KEY),
            "openai": bool(settings.OPENAI_API_KEY),
        }
        health_status["components"]["external_apis"] = {
            "status": "configured" if any(api_configs.values()) else "not_configured",
            "configured_apis": [
                api for api, configured in api_configs.items() if configured
            ],
        }

        return health_status

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@router.get("/metrics")
async def get_metrics() -> Dict[str, Any]:
    """
    Get pipeline metrics and statistics.
    """
    try:
        metrics = {
            "service_info": {
                "name": settings.SERVICE_NAME,
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
            },
            "configuration": {
                "worker_concurrency": settings.WORKER_CONCURRENCY,
                "pipeline_timeout": settings.PIPELINE_TIMEOUT,
                "enabled_pipelines": {
                    "company_enrichment": settings.ENABLE_COMPANY_ENRICHMENT,
                    "founder_enrichment": settings.ENABLE_FOUNDER_ENRICHMENT,
                    "news_aggregation": settings.ENABLE_NEWS_AGGREGATION,
                    "embedding_generation": settings.ENABLE_EMBEDDING_GENERATION,
                },
            },
        }

        # Add queue metrics if Redis is available
        try:
            from app.queueing.factory import create_redis_backend
            from app.queueing.service import QueueServiceV2

            # Create V2 queue service
            backend = await create_redis_backend(enhanced=True)
            queue_service = QueueServiceV2(backend)
            await queue_service.initialize()

            queues = ["high", "normal", "low"]
            queue_metrics = {}

            for queue_name in queues:
                stats = await queue_service.get_queue_stats(queue_name)
                queue_stats = stats.get("queue_stats", {})
                queue_metrics[queue_name] = {
                    "pending": getattr(queue_stats, "pending_count", 0),
                    "running": getattr(queue_stats, "running_count", 0),
                    "completed": getattr(queue_stats, "completed_count", 0),
                    "failed": getattr(queue_stats, "failed_count", 0),
                }

            await queue_service.cleanup()
            metrics["queues"] = queue_metrics

        except Exception as e:
            logger.warning(f"Could not get queue metrics: {e}")
            metrics["queues"] = {"error": "Queue metrics unavailable"}

        return metrics

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get metrics")


@router.get("/config")
async def get_configuration() -> Dict[str, Any]:
    """
    Get current pipeline configuration (non-sensitive values only).
    """
    try:
        config = {
            "service": {
                "name": settings.SERVICE_NAME,
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
                "debug": settings.DEBUG,
            },
            "api": {
                "host": settings.API_HOST,
                "port": settings.API_PORT,
                "prefix": settings.API_PREFIX,
            },
            "worker": {
                "concurrency": settings.WORKER_CONCURRENCY,
                "poll_interval": settings.WORKER_POLL_INTERVAL,
                "timeout": settings.WORKER_TIMEOUT,
            },
            "pipelines": {
                "timeout": settings.PIPELINE_TIMEOUT,
                "company_enrichment": settings.ENABLE_COMPANY_ENRICHMENT,
                "founder_enrichment": settings.ENABLE_FOUNDER_ENRICHMENT,
                "news_aggregation": settings.ENABLE_NEWS_AGGREGATION,
                "embedding_generation": settings.ENABLE_EMBEDDING_GENERATION,
            },
            "external_apis": {
                "clay_configured": bool(settings.CLAY_API_KEY),
                "pdl_configured": bool(settings.PDL_API_KEY),
                "serpapi_configured": bool(settings.SERPAPI_KEY),
                "openai_configured": bool(settings.OPENAI_API_KEY),
            },
        }

        return config

    except Exception as e:
        logger.error(f"Failed to get configuration: {e}")
        raise HTTPException(status_code=500, detail="Failed to get configuration")
