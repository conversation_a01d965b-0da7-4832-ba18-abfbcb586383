"""
API endpoints for TractionX Data Pipeline Service.
"""

from app.api.v1.pipelines import router as pipelines_router
from app.api.v1.status import router as status_router
from fastapi import APIRouter

# Create main API router
router = APIRouter()

# Include sub-routers
router.include_router(pipelines_router, prefix="/pipelines", tags=["pipelines"])
router.include_router(status_router, prefix="/status", tags=["status"])

__all__ = ["router"]
