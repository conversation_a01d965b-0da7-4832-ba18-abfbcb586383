"""
Pipeline API for TractionX Data Pipeline Service

This module provides a clean API for triggering data pipelines with simple
job type mappings and straightforward job creation.
"""

import time
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import uuid4

from app.configs import get_logger
from app.models.base import TractionXModel
from app.models.job_tracking import JOB_TYPE_REGISTRY, JobPriority, JobType, RetryConfig
from app.queueing.service import QueueServiceV2
from app.services.jobs.orchestrator import get_job_orchestrator
from app.utils.api import (
    calculate_estimated_duration,
    convert_legacy_payload_to_entity_data,
    get_v2_queue_service,
    validate_pipeline_request,
)
from app.utils.common import get_root_domain
from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import Field, validator

logger = get_logger(__name__)
router = APIRouter(tags=["pipelines"])


class EntityData(TractionXModel):
    """Flexible entity data for pipeline processing."""

    entity_type: str = Field(..., description="Type of entity (company, founder, etc.)")
    entity_id: Optional[str] = Field(None, description="Unique entity identifier")
    data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Flexible entity data - can contain any fields",
        alias="entity_data",  # Allow both 'data' and 'entity_data' as field names
    )

    def __getitem__(self, key: str) -> Any:
        """Allow dict-like access to data."""
        return self.data.get(key)

    def get(self, key: str, default: Any = None) -> Any:
        """Get value from data with default."""
        return self.data.get(key, default)

    def dict(self, **kwargs):
        """Return dict representation including data."""
        base_dict = super().dict(**kwargs)
        base_dict.update(self.data)
        return base_dict


class JobConfiguration(TractionXModel):
    """Configuration for individual jobs in a pipeline."""

    job_type: JobType = Field(..., description="Type of job to execute")
    priority: JobPriority = Field(
        default=JobPriority.NORMAL, description="Job priority"
    )
    retry_config: Optional[RetryConfig] = Field(None, description="Retry configuration")
    timeout_seconds: Optional[int] = Field(None, description="Job timeout in seconds")
    delay_seconds: int = Field(default=0, description="Delay before job execution")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Job-specific metadata"
    )
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Job configuration"
    )


class OrchestrationPattern(str, Enum):
    """Orchestration patterns for pipeline execution."""

    SEQUENTIAL = "sequential"  # Jobs run one after another
    PARALLEL = "parallel"  # Jobs run simultaneously
    FAN_OUT_FAN_IN = "fan_out_fan_in"  # Multiple jobs run in parallel, then merge
    PIPELINE = "pipeline"  # Jobs form a processing pipeline
    BARRIER = "barrier"  # Jobs wait for barrier conditions
    CONDITIONAL = "conditional"  # Jobs run based on conditions
    COMPREHENSIVE = "comprehensive"  # Special comprehensive enrichment pattern


class DependencyRule(TractionXModel):
    """Rule for job dependencies."""

    job_type: JobType = Field(..., description="Job type that depends on others")
    depends_on: List[JobType] = Field(..., description="Job types this depends on")
    condition: Optional[str] = Field(None, description="Condition for dependency")
    timeout_seconds: Optional[int] = Field(None, description="Dependency timeout")


class PipelineConfiguration(TractionXModel):
    """Configuration for pipeline execution."""

    orchestration_pattern: OrchestrationPattern = Field(
        default=OrchestrationPattern.COMPREHENSIVE,
        description="Orchestration pattern to use",
    )
    priority: JobPriority = Field(
        default=JobPriority.NORMAL, description="Overall pipeline priority"
    )
    timeout_seconds: Optional[int] = Field(None, description="Overall pipeline timeout")
    retry_config: Optional[RetryConfig] = Field(
        None, description="Default retry configuration"
    )
    dependencies: List[DependencyRule] = Field(
        default_factory=list, description="Job dependencies"
    )
    job_configs: Dict[JobType, JobConfiguration] = Field(
        default_factory=dict, description="Per-job configurations"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Pipeline metadata"
    )


class PipelineTriggerRequest(TractionXModel):
    """Comprehensive request model for triggering pipelines."""

    # Core identification
    org_id: str = Field(..., description="Organization identifier")
    pipeline_id: str = Field(
        default_factory=lambda: f"pipeline_{int(time.time())}_{uuid4().hex[:8]}",
        description="Unique pipeline identifier",
    )

    payload: Optional[Dict[str, Any]] = Field(
        None, description="Legacy payload for backward compatibility"
    )  # Legacy fields for backward compatibility

    # Entity data
    entities: List[EntityData] = Field(..., description="Entities to process")

    # Pipeline configuration
    configuration: PipelineConfiguration = Field(
        default_factory=lambda: PipelineConfiguration(
            orchestration_pattern=OrchestrationPattern.COMPREHENSIVE,
            priority=JobPriority.NORMAL,
            timeout_seconds=None,
            retry_config=None,
        ),
        description="Pipeline configuration",
    )

    # Job selection
    job_types: List[JobType] = Field(
        default=[
            JobType.COMPANY_ENRICH_COMPREHENSIVE,
            JobType.FOUNDER_ENRICH_PDL,
            JobType.UTILITY_NEWS_AGGREGATE,
            JobType.UTILITY_EMBEDDINGS_GENERATE,
        ],
        description="Types of jobs to execute",
    )

    # Advanced features
    enable_job_tracking: bool = Field(default=True, description="Enable job tracking")
    enable_dependency_management: bool = Field(
        default=True, description="Enable dependency management"
    )
    enable_progress_tracking: bool = Field(
        default=True, description="Enable progress tracking"
    )
    enable_rollback: bool = Field(
        default=False, description="Enable rollback on failure"
    )

    # Monitoring and notifications
    webhook_url: Optional[str] = Field(
        None, description="Webhook URL for notifications"
    )
    callback_data: Dict[str, Any] = Field(
        default_factory=dict, description="Data to include in callbacks"
    )

    @validator("entities")
    def validate_entities(cls, v, values):
        # Only validate if we have both entities and job_types
        job_types = values.get("job_types", [])
        payload = values.get("payload")
        if v and job_types:
            validate_pipeline_request(v, job_types, payload)
        return v

    @validator("job_types")
    def validate_job_types(cls, v, values):
        # Only validate if we have both entities and job_types
        entities = values.get("entities", [])
        payload = values.get("payload")
        if v and entities:
            validate_pipeline_request(entities, v, payload)
        return v


class PipelineResponse(TractionXModel):
    """Response model for pipeline trigger."""

    success: bool = Field(
        ..., description="Whether pipeline was triggered successfully"
    )
    pipeline_id: str = Field(..., description="Unique pipeline identifier")
    message: str = Field(..., description="Response message")

    # Job tracking information
    tracked_jobs: List[str] = Field(
        default_factory=list, description="List of tracked job IDs"
    )
    queue_jobs: List[str] = Field(
        default_factory=list, description="List of queue job IDs"
    )

    # Pipeline metadata
    orchestration_pattern: OrchestrationPattern = Field(
        ..., description="Orchestration pattern used"
    )
    total_jobs: int = Field(..., description="Total number of jobs created")
    estimated_duration: Optional[int] = Field(
        None, description="Estimated duration in seconds"
    )

    # Status endpoints
    status_url: Optional[str] = Field(None, description="URL to check pipeline status")
    cancel_url: Optional[str] = Field(None, description="URL to cancel pipeline")


# Use centralized job type registry


JOB_TYPE_MAPPINGS = JOB_TYPE_REGISTRY


def create_job_payload(
    job_type: JobType, entity: EntityData, request: PipelineTriggerRequest
) -> Dict[str, Any]:
    """Create job payload based on job type and entity."""

    mapping = JOB_TYPE_MAPPINGS.get(job_type)
    if not mapping:
        raise ValueError(f"Unknown job type: {job_type}")

    # Check if entity type is supported
    if entity.entity_type not in mapping["entity_types"]:
        raise ValueError(
            f"Job type {job_type} does not support entity type {entity.entity_type}"
        )

    # Base payload
    payload = {
        "entity_id": entity.entity_id,
        "entity_type": entity.entity_type,
        "org_id": request.org_id,
        "entity_data": entity.dict(),
        "pipeline_id": request.pipeline_id,
        "webhook_url": request.webhook_url,
        "callback_data": request.callback_data,
    }

    # Add job-specific fields
    if job_type in [
        JobType.COMPANY_ENRICH_PDL,
        JobType.COMPANY_ENRICH_LINKEDIN,
        JobType.COMPANY_ENRICH_CRUNCHBASE,
        JobType.COMPANY_ENRICH_PITCHBOOK,
        JobType.COMPANY_ENRICH_APOLLO,
        JobType.COMPANY_ENRICH_COMPREHENSIVE,
        JobType.COMPANY_RESOLVE_CRUNCHBASE,
        JobType.COMPANY_RESOLVE_LINKEDIN,
        JobType.COMPANY_RESOLVE_PITCHBOOK,
    ]:
        domain = get_root_domain(entity.data.get("domain"))  # type: ignore
        if not domain:
            raise ValueError(f"Company entity {entity.entity_id} requires a domain")
        payload["company_domain"] = domain
        payload["company_description"] = entity.data.get(
            "description"
        ) or entity.data.get("name")

    elif job_type in [JobType.FOUNDER_ENRICH_PDL, JobType.FOUNDER_ENRICH_LINKEDIN]:
        payload["founder_id"] = entity.entity_id
        payload["founder_name"] = entity.data.get("name")

    elif job_type in [
        JobType.COMPANY_WEBSITE_INSIGHTS,
        JobType.COMPANY_WEBSITE_SITEMAP,
    ]:
        domain = get_root_domain(entity.data.get("domain"))  # type: ignore
        if not domain:
            raise ValueError(
                f"Company entity {entity.entity_id} requires a domain for website jobs"
            )
        payload["company_domain"] = domain

    return payload


def get_supported_job_types(entity_type: str) -> List[JobType]:
    """Get job types supported by the given entity type."""
    supported = []
    for job_type, mapping in JOB_TYPE_MAPPINGS.items():
        if entity_type in mapping["entity_types"]:
            supported.append(job_type)
    return supported


@router.post("/trigger", response_model=PipelineResponse)
async def trigger_pipeline(
    request: PipelineTriggerRequest, background_tasks: BackgroundTasks
) -> PipelineResponse:
    """
    Trigger a sophisticated data pipeline with full v2 architecture support.

    This endpoint leverages our complete v2 architecture including:
    - Job tracking with MongoDB
    - Dependency management and orchestration
    - Barrier patterns for coordinated execution
    - Progress tracking and monitoring
    - Retry logic and dead letter queue
    - Comprehensive error handling
    """
    try:
        logger.info(
            "Triggering pipeline for",
            pipeline_id=request.pipeline_id,
            org_id=request.org_id,
            entities_count=len(request.entities),
            job_types=request.job_types,
            orchestration_pattern=request.configuration.orchestration_pattern,
        )

        # Get orchestrator and queue service
        orchestrator = await get_job_orchestrator()
        queue_service = await get_v2_queue_service()

        # Initialize tracking
        tracked_jobs = []
        total_jobs = 0

        # Process each entity
        for entity in request.entities:
            entity_jobs = await _process_entity_with_orchestrator(
                entity=entity,
                request=request,
                orchestrator=orchestrator,
                queue_service=queue_service,
            )
            logger.info(f"Entity jobs: {entity_jobs}")
            tracked_jobs.extend(entity_jobs["tracked_jobs"])
            total_jobs += entity_jobs["total_jobs"]

        # Calculate estimated duration
        estimated_duration = calculate_estimated_duration(
            total_jobs, request.configuration.orchestration_pattern.value
        )

        return PipelineResponse(
            success=True,
            pipeline_id=request.pipeline_id,
            message=f"Pipeline triggered successfully with {total_jobs} jobs",
            tracked_jobs=tracked_jobs,
            queue_jobs=[],  # Orchestrator handles queue management internally
            orchestration_pattern=request.configuration.orchestration_pattern,
            total_jobs=total_jobs,
            estimated_duration=estimated_duration,
            status_url=f"/pipelines/status/{request.pipeline_id}",
            cancel_url=f"/pipelines/cancel/{request.pipeline_id}",
        )

    except Exception as e:
        logger.error(f"Failed to trigger pipeline: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


async def _process_entity_with_orchestrator(
    entity: EntityData,
    request: PipelineTriggerRequest,
    orchestrator,
    queue_service: QueueServiceV2,
) -> Dict[str, Any]:
    """Process pipeline for a single entity using the orchestrator."""

    tracked_jobs = []
    total_jobs = 0

    # Create job configurations based on orchestration pattern
    if (
        request.configuration.orchestration_pattern
        == OrchestrationPattern.COMPREHENSIVE
    ):
        # Use comprehensive enrichment pattern - use requested job types
        job_configs = []
        for job_type in request.job_types:
            # Skip company-specific jobs for non-company entities
            if entity.entity_type != "company" and job_type in [
                JobType.COMPANY_ENRICH_COMPREHENSIVE,
                JobType.COMPANY_ENRICH_PDL,
                JobType.COMPANY_ENRICH_LINKEDIN,
                JobType.COMPANY_ENRICH_CRUNCHBASE,
                JobType.COMPANY_ENRICH_PITCHBOOK,
                JobType.COMPANY_ENRICH_APOLLO,
                JobType.COMPANY_WEBSITE_INSIGHTS,
                JobType.COMPANY_WEBSITE_SITEMAP,
                JobType.COMPANY_RESOLVE_CRUNCHBASE,
                JobType.COMPANY_RESOLVE_LINKEDIN,
                JobType.COMPANY_RESOLVE_PITCHBOOK,
            ]:
                continue

            # Skip founder-specific jobs for non-founder entities
            if entity.entity_type != "founder" and job_type in [
                JobType.FOUNDER_ENRICH_PDL,
                JobType.FOUNDER_ENRICH_LINKEDIN,
            ]:
                continue

            # Build payload based on entity type and job type
            payload = {
                "entity_id": entity.entity_id,
                "entity_type": entity.entity_type,
                "org_id": request.org_id,
                "entity_data": entity.dict(),
                "pipeline_id": request.pipeline_id,
                "webhook_url": request.webhook_url,
                "callback_data": request.callback_data,
            }

            # Add entity-specific fields
            if entity.entity_type == "company" and job_type in [
                JobType.COMPANY_ENRICH_COMPREHENSIVE,
                JobType.COMPANY_ENRICH_PDL,
                JobType.COMPANY_ENRICH_LINKEDIN,
                JobType.COMPANY_ENRICH_CRUNCHBASE,
                JobType.COMPANY_ENRICH_PITCHBOOK,
                JobType.COMPANY_ENRICH_APOLLO,
                JobType.COMPANY_RESOLVE_CRUNCHBASE,
                JobType.COMPANY_RESOLVE_LINKEDIN,
                JobType.COMPANY_RESOLVE_PITCHBOOK,
            ]:
                domain = get_root_domain(
                    entity.data.get("domain")  # type: ignore
                )
                if domain:
                    payload["company_domain"] = domain
                    payload["company_description"] = entity.data.get(
                        "description"
                    ) or entity.data.get("name")

            elif entity.entity_type == "founder" and job_type in [
                JobType.FOUNDER_ENRICH_PDL,
                JobType.FOUNDER_ENRICH_LINKEDIN,
            ]:
                payload = entity.model_dump(mode="python")

                # add org_id to payload
                payload["org_id"] = request.org_id

            job_configs.append({
                "job_type": job_type,
                "payload": payload,
                "metadata": {
                    "pipeline_id": request.pipeline_id,
                    "orchestration_pattern": request.configuration.orchestration_pattern.value,
                    "entity_data": entity.dict(),
                    **request.configuration.metadata,
                },
                "config": {},
                "priority": request.configuration.priority,
            })
    else:
        # Use standard orchestration pattern - filter jobs based on entity type
        job_configs = []
        for job_type in request.job_types:
            # Skip company-specific jobs for non-company entities
            if entity.entity_type != "company" and job_type in [
                JobType.COMPANY_ENRICH_COMPREHENSIVE,
                JobType.COMPANY_ENRICH_PDL,
                JobType.COMPANY_ENRICH_LINKEDIN,
                JobType.COMPANY_ENRICH_CRUNCHBASE,
                JobType.COMPANY_ENRICH_PITCHBOOK,
                JobType.COMPANY_ENRICH_APOLLO,
                JobType.COMPANY_WEBSITE_INSIGHTS,
                JobType.COMPANY_WEBSITE_SITEMAP,
                JobType.COMPANY_RESOLVE_CRUNCHBASE,
                JobType.COMPANY_RESOLVE_LINKEDIN,
                JobType.COMPANY_RESOLVE_PITCHBOOK,
            ]:
                continue

            # Skip founder-specific jobs for non-founder entities
            if entity.entity_type != "founder" and job_type in [
                JobType.FOUNDER_ENRICH_PDL,
                JobType.FOUNDER_ENRICH_LINKEDIN,
            ]:
                continue

            # Build payload based on entity type
            payload = {
                "entity_id": entity.entity_id,
                "entity_type": entity.entity_type,
                "org_id": request.org_id,
                "entity_data": entity.dict(),
                "pipeline_id": request.pipeline_id,
                "webhook_url": request.webhook_url,
                "callback_data": request.callback_data,
            }

            # Add entity-specific fields
            if entity.entity_type == "company" and job_type in [
                JobType.COMPANY_ENRICH_COMPREHENSIVE,
                JobType.COMPANY_ENRICH_PDL,
                JobType.COMPANY_ENRICH_LINKEDIN,
                JobType.COMPANY_ENRICH_CRUNCHBASE,
                JobType.COMPANY_ENRICH_PITCHBOOK,
                JobType.COMPANY_ENRICH_APOLLO,
                JobType.COMPANY_RESOLVE_CRUNCHBASE,
                JobType.COMPANY_RESOLVE_LINKEDIN,
                JobType.COMPANY_RESOLVE_PITCHBOOK,
            ]:
                domain = get_root_domain(
                    entity.data.get("domain")  # type: ignore
                )
                if domain:
                    payload["company_domain"] = domain
                    payload["company_description"] = entity.data.get(
                        "description"
                    ) or entity.data.get("name")

            elif entity.entity_type == "founder" and job_type in [
                JobType.FOUNDER_ENRICH_PDL,
                JobType.FOUNDER_ENRICH_LINKEDIN,
            ]:
                payload = entity.model_dump(mode="python")
                payload["org_id"] = request.org_id

            job_configs.append({
                "job_type": job_type,
                "payload": payload,
                "metadata": {
                    "pipeline_id": request.pipeline_id,
                    "orchestration_pattern": request.configuration.orchestration_pattern.value,
                    "entity_data": entity.dict(),
                    **request.configuration.metadata,
                },
                "config": {},
                "priority": request.configuration.priority,
            })

    # Create jobs using orchestrator with queue service
    if request.configuration.orchestration_pattern == OrchestrationPattern.SEQUENTIAL:
        # Sequential execution
        jobs = await orchestrator.create_job_chain(
            entity_type=entity.entity_type,
            entity_id=entity.entity_id,
            job_configs=job_configs,
            job_group=f"pipeline_{request.pipeline_id}_{entity.entity_id}",
            orchestration_pattern=request.configuration.orchestration_pattern.value,
            queue_service=queue_service,
        )
    else:
        # Parallel execution
        jobs = await orchestrator.create_parallel_jobs(
            entity_type=entity.entity_type,
            entity_id=entity.entity_id,
            job_configs=job_configs,
            job_group=f"pipeline_{request.pipeline_id}_{entity.entity_id}",
            queue_service=queue_service,
        )

    logger.info(f"Jobs returned from orchestrator: {jobs}")

    tracked_jobs.extend(jobs)
    total_jobs += len(jobs)

    return {
        "tracked_jobs": tracked_jobs,
        "total_jobs": total_jobs,
    }


# Legacy endpoints for backward compatibility
@router.post("/trigger/legacy")
async def trigger_legacy_pipeline(
    request: PipelineTriggerRequest, background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """Legacy pipeline trigger endpoint for backward compatibility."""

    # Convert legacy request to new format
    legacy_request = PipelineTriggerRequest(
        org_id=request.org_id,
        payload=request.payload,
        entities=[
            EntityData(
                **convert_legacy_payload_to_entity_data(
                    request.payload or {},
                    entity_id=request.payload.get("company_id", "unknown")
                    if request.payload
                    else "unknown",
                )
            )
        ],
        job_types=[JobType(job_type) for job_type in request.job_types],
        configuration=PipelineConfiguration(
            orchestration_pattern=OrchestrationPattern.COMPREHENSIVE,
            priority=JobPriority.NORMAL,
            timeout_seconds=None,
            retry_config=None,
        ),
        webhook_url=request.webhook_url,
        callback_data=request.callback_data,
    )

    # Use new implementation
    response = await trigger_pipeline(legacy_request, background_tasks)

    # Convert response to legacy format
    return {
        "success": response.success,
        "message": response.message,
        "pipeline_id": response.pipeline_id,
        "job_ids": response.queue_jobs,
        "total_jobs": response.total_jobs,
    }


@router.post("/retry/force/{job_id}")
async def force_retry_job(job_id: str) -> Dict[str, Any]:
    """
    Force retry a job, bypassing max retries limit.

    Args:
        job_id: The job ID to force retry

    Returns:
        Dictionary with retry status
    """
    try:
        logger.info(f"Force retrying job: {job_id}")

        # Get queue service
        queue_service = await get_v2_queue_service()

        # Force retry the job
        success = await queue_service.force_retry_job(job_id)

        if success:
            return {
                "success": True,
                "message": f"Job {job_id} force retried successfully",
                "job_id": job_id,
            }
        else:
            return {
                "success": False,
                "message": f"Failed to force retry job {job_id}",
                "job_id": job_id,
            }

    except Exception as e:
        logger.error(f"Error force retrying job {job_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
