"""
Together AI Client Module for TractionX Data Pipeline Service.

This module provides a unified wrapper for Together AI LLM generation,
centralizing all LLM interactions across the platform.
"""

import time
from typing import Any, Dict, List, Optional

from app.clients.base_client import BaseClient
from app.configs import get_logger, settings

logger = get_logger(__name__)


class TogetherClient(BaseClient):
    """
    Unified Together AI client for LLM generation.

    Handles:
    - LLM chat completions with Together AI
    - Automatic retries and error handling
    - Response parsing and validation
    - Rate limiting and timeout management
    """

    def __init__(self):
        """Initialize the Together AI client."""
        super().__init__(
            base_url="https://api.together.xyz/v1",
            api_key=settings.TOGETHER_API_KEY,
            timeout=120.0,  # Longer timeout for LLM generation
            max_retries=5,  # More retries for rate limiting
            retry_delay=5.0,  # Longer base delay for rate limiting
            headers={
                "Content-Type": "application/json",
            },
        )

        # Default model
        self.default_model = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"

        self.logger.info(f"Together client config: model={self.default_model}")

    async def initialize(self) -> None:
        """Initialize the Together AI client."""
        if not self.api_key:
            raise ValueError("TOGETHER_API_KEY not configured")

        # Initialize base client
        await super().initialize()

        self.logger.info("Together AI client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()

        self.logger.info("Together AI client cleaned up")

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        api_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a chat completion using Together AI.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (defaults to self.default_model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            stream: Whether to stream the response
            api_key: Optional API key to use (overrides the default key)

        Returns:
            Dictionary with success status and completion data
        """
        try:
            start_time = time.time()
            model = model or self.default_model

            self.logger.info(
                "Creating chat completion",
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                message_count=len(messages),
            )

            # Prepare request data
            data = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            # Use dynamic API key if provided
            headers = None
            if api_key:
                headers = {"Authorization": f"Bearer {api_key}"}

            # Use base client's post method
            result = await self.post("/chat/completions", data=data, headers=headers)

            if result["success"]:
                # Extract content from response
                response_data = result["data"]
                if "choices" in response_data and response_data["choices"]:
                    content = (
                        response_data["choices"][0]
                        .get("message", {})
                        .get("content", "")
                    )
                else:
                    content = ""

                response_time_ms = int((time.time() - start_time) * 1000)

                self.logger.info(
                    "Chat completion successful",
                    model=model,
                    response_time_ms=response_time_ms,
                    content_length=len(content),
                    tokens_used=response_data.get("usage", {}).get("total_tokens", 0),
                )

                return {
                    "success": True,
                    "content": content,
                    "full_response": response_data,
                    "response_time_ms": response_time_ms,
                    "model": model,
                    "attempts": result.get("attempts", 1),
                }

            return result

        except Exception as e:
            error_msg = f"Chat completion failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
            }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Together AI service.

        Returns:
            Dictionary with health status
        """
        try:
            # Try a simple request to check if the service is accessible
            result = await self.get("/models")

            return {
                "success": result["success"],
                "service": "together_ai",
                "status": "healthy" if result["success"] else "unhealthy",
                "error": result.get("error") if not result["success"] else None,
            }
        except Exception as e:
            return {
                "success": False,
                "service": "together_ai",
                "status": "unhealthy",
                "error": str(e),
            }


# Global client instance
_together_client: Optional[TogetherClient] = None


async def get_together_client() -> TogetherClient:
    """Get the global Together AI client instance."""
    global _together_client

    if _together_client is None:
        _together_client = TogetherClient()
        await _together_client.initialize()

    return _together_client


async def cleanup_together_client():
    """Clean up the global Together AI client instance."""
    global _together_client

    if _together_client:
        await _together_client.cleanup()
        _together_client = None


# AI Manager compatibility layer
class AIManagerWrapper:
    """Wrapper class that provides TogetherClient-compatible interface using AI Manager."""

    def __init__(self, ai_manager):
        self.ai_manager = ai_manager

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        api_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Create a chat completion using the AI Manager."""
        return await self.ai_manager.create_chat_completion(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=stream,
            pii=False,  # Default to non-PII for backward compatibility
        )

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check using the AI Manager."""
        return await self.ai_manager.health_check()

    async def cleanup(self) -> None:
        """Clean up the AI Manager."""
        await self.ai_manager.cleanup()


async def get_ai_manager_wrapper():
    """Get the AI Manager wrapper for backward compatibility."""
    from datapipelines.app.clients.ai.manager import get_ai_manager

    ai_manager = await get_ai_manager()
    return AIManagerWrapper(ai_manager)
