"""
AI Manager Module for TractionX Data Pipeline Service.

This module provides a unified AI manager that routes LLM requests between multiple
providers (Together AI, OpenRouter, OpenAI, etc.) based on policies, cost, and availability.
"""

import hashlib
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Protocol

from app.clients.ai.together import TogetherClient
from app.configs import get_logger, settings

from datapipelines.app.clients.ai.openrouter import OpenRouterClient

logger = get_logger(__name__)


class ProviderType(Enum):
    """Supported AI providers."""

    TOGETHER = "together"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    PERPLEXITY = "perplexity"


class SensitivityLevel(Enum):
    """Sensitivity levels for routing decisions."""

    NON_PII = "non_pii"
    PII = "pii"


@dataclass
class ProviderConfig:
    """Configuration for an AI provider."""

    provider_type: ProviderType
    api_keys: List[str] = field(default_factory=list)
    base_url: str = ""
    default_model: str = ""
    rate_limit: Optional[int] = None
    cost_per_1k_tokens: float = 0.0
    is_secure: bool = False
    is_free: bool = False
    enabled: bool = True


@dataclass
class KeyState:
    """State tracking for API key rotation."""

    key_hash: str
    last_used: float = 0.0
    requests_in_window: int = 0
    window_start: float = 0.0
    total_requests: int = 0  # Track total requests for this key


class AIClientProtocol(Protocol):
    """Protocol for AI clients that support chat completion."""

    async def initialize(self) -> None:
        """Initialize the client."""
        ...

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        api_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Create a chat completion."""
        ...

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        ...

    async def cleanup(self) -> None:
        """Clean up client resources."""
        ...


class AIManager:
    """
    AI Manager for multi-provider LLM routing.

    Handles:
    - Dynamic provider selection based on policies
    - Simple API key rotation (try next key on failure)
    - Automatic failover between providers
    - Cost optimization and security compliance
    - Key usage statistics tracking

    Key Rotation Features:
    - Simple rotation: try next key when current one fails
    - Rate limit awareness: skip keys that have hit rate limits
    - Usage statistics tracking for monitoring
    """

    def __init__(self):
        """Initialize the AI Manager."""
        self.logger = get_logger(f"{__name__}.AIManager")

        # Provider configurations
        self.providers: Dict[ProviderType, ProviderConfig] = {}
        self.key_states: Dict[str, KeyState] = {}

        # Routing policies
        self.routing_config = {
            "non_pii_order": [ProviderType.TOGETHER, ProviderType.OPENROUTER],
            "pii_order": [ProviderType.OPENAI, ProviderType.PERPLEXITY],
            "free_models": {
                ProviderType.TOGETHER: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                ProviderType.OPENROUTER: "openai/gpt-oss-20b:free",
            },
        }

        # Provider clients
        self.clients: Dict[ProviderType, AIClientProtocol] = {}

        # Initialize providers
        self._initialize_providers()

        self.logger.info("AI Manager initialized")

    def _initialize_providers(self) -> None:
        """Initialize provider configurations."""
        # Together AI
        together_keys = self._get_api_keys("TOGETHER_API_KEY")
        self.providers[ProviderType.TOGETHER] = ProviderConfig(
            provider_type=ProviderType.TOGETHER,
            api_keys=together_keys,
            base_url="https://api.together.xyz/v1",
            default_model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            rate_limit=1000,  # requests per minute
            cost_per_1k_tokens=0.0,  # Free tier
            is_secure=False,
            is_free=True,
            enabled=len(together_keys) > 0,
        )

        # OpenRouter
        openrouter_keys = self._get_api_keys("OPENROUTER_API_KEY")
        self.providers[ProviderType.OPENROUTER] = ProviderConfig(
            provider_type=ProviderType.OPENROUTER,
            api_keys=openrouter_keys,
            base_url="https://openrouter.ai/api/v1",
            default_model="openai/gpt-oss-20b:free",
            rate_limit=500,
            cost_per_1k_tokens=0.00024,  # $0.24 per 1M tokens
            is_secure=False,
            is_free=True,
            enabled=len(openrouter_keys) > 0,
        )

        # OpenAI
        openai_keys = self._get_api_keys("OPENAI_API_KEY")
        self.providers[ProviderType.OPENAI] = ProviderConfig(
            provider_type=ProviderType.OPENAI,
            api_keys=openai_keys,
            base_url="https://api.openai.com/v1",
            default_model="gpt-4",
            rate_limit=3000,
            cost_per_1k_tokens=0.03,  # $0.03 per 1K tokens
            is_secure=True,
            is_free=False,
            enabled=len(openai_keys) > 0,
        )

        # Perplexity
        perplexity_keys = self._get_api_keys("PERPLEXITY_API_KEY")
        self.providers[ProviderType.PERPLEXITY] = ProviderConfig(
            provider_type=ProviderType.PERPLEXITY,
            api_keys=perplexity_keys,
            base_url="https://api.perplexity.ai",
            default_model="llama-3.1-8b-instant",
            rate_limit=1000,
            cost_per_1k_tokens=0.0002,  # $0.20 per 1M tokens
            is_secure=True,
            is_free=False,
            enabled=len(perplexity_keys) > 0,
        )

    def _get_api_keys(self, key_name: str) -> List[str]:
        """Get API keys from settings, supporting multiple keys."""
        # Check for single key first
        single_key = getattr(settings, key_name, None)
        if single_key:
            return [single_key]

        # Check for multiple keys (e.g., TOGETHER_API_KEYS)
        multiple_keys_name = f"{key_name}S"
        multiple_keys = getattr(settings, multiple_keys_name, None)
        if multiple_keys:
            if isinstance(multiple_keys, str):
                return [key.strip() for key in multiple_keys.split(",") if key.strip()]
            elif isinstance(multiple_keys, list):
                return multiple_keys

        return []

    async def initialize(self) -> None:
        """Initialize all provider clients."""
        try:
            # Initialize Together AI client
            if self.providers[ProviderType.TOGETHER].enabled:
                self.clients[ProviderType.TOGETHER] = TogetherClient()
                await self.clients[ProviderType.TOGETHER].initialize()
                self.logger.info("Together AI client initialized")

            # Initialize OpenRouter client
            if self.providers[ProviderType.OPENROUTER].enabled:
                self.clients[ProviderType.OPENROUTER] = OpenRouterClient()
                await self.clients[ProviderType.OPENROUTER].initialize()
                self.logger.info("OpenRouter client initialized")

            # Initialize other clients as needed
            # TODO: Add OpenAI, Perplexity clients when implemented

            self.logger.info("AI Manager initialization complete")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Manager: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """Clean up all provider clients."""
        for provider_type, client in self.clients.items():
            try:
                await client.cleanup()
                self.logger.info(f"Cleaned up {provider_type.value} client")
            except Exception as e:
                self.logger.error(
                    f"Failed to cleanup {provider_type.value} client: {str(e)}"
                )

        self.clients.clear()

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        pii: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a chat completion using the best available provider.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (defaults to provider's default model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            stream: Whether to stream the response
            pii: Whether the request contains PII (affects provider selection)

        Returns:
            Dictionary with success status and completion data
        """
        start_time = time.time()

        # Determine sensitivity level
        sensitivity = SensitivityLevel.PII if pii else SensitivityLevel.NON_PII

        # Get provider order based on sensitivity
        provider_order = self.routing_config["pii_order" if pii else "non_pii_order"]

        self.logger.info(
            "Creating chat completion",
            sensitivity=sensitivity.value,
            provider_order=[p.value for p in provider_order],
            message_count=len(messages),
            max_tokens=max_tokens,
            temperature=temperature,
        )

        # Try providers in order
        for provider_type in provider_order:
            if not self.providers[provider_type].enabled:
                self.logger.debug(
                    f"Provider {provider_type.value} is disabled, skipping"
                )
                continue

            try:
                result = await self._try_provider(
                    provider_type=provider_type,
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=stream,
                )

                if result["success"]:
                    response_time_ms = int((time.time() - start_time) * 1000)

                    self.logger.info(
                        "Chat completion successful",
                        provider=provider_type.value,
                        model=result.get("model", "unknown"),
                        response_time_ms=response_time_ms,
                        content_length=len(result.get("content", "")),
                    )

                    return result
                else:
                    self.logger.warning(
                        f"Provider {provider_type.value} failed: {result.get('error', 'Unknown error')}"
                    )

            except Exception as e:
                self.logger.error(f"Provider {provider_type.value} error: {str(e)}")
                continue

        # All providers failed
        error_msg = "All AI providers failed"
        self.logger.error(error_msg)

        return {
            "success": False,
            "error": error_msg,
        }

    async def _try_provider(
        self,
        provider_type: ProviderType,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """Try a specific provider for chat completion."""
        provider_config = self.providers[provider_type]

        # Try each available key until one works
        for key in provider_config.api_keys:
            key_hash = self._hash_key(key)
            key_state = self.key_states.get(key_hash)

            if not key_state:
                # Initialize key state
                key_state = KeyState(key_hash=key_hash)
                self.key_states[key_hash] = key_state

            # Check rate limiting
            current_time = time.time()
            if current_time - key_state.window_start > 60:  # Reset window every minute
                key_state.window_start = current_time
                key_state.requests_in_window = 0

            # Skip if rate limit exceeded
            if key_state.requests_in_window >= (provider_config.rate_limit or 1000):
                continue

            # Use provider-specific client
            client = self.clients.get(provider_type)
            if not client:
                return {
                    "success": False,
                    "error": f"Provider {provider_type.value} client not initialized",
                }

            try:
                # Update key usage
                key_state.requests_in_window += 1
                key_state.total_requests += 1
                key_state.last_used = current_time

                # Use the client's create_chat_completion method with the selected key
                result = await client.create_chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=stream,
                    api_key=key,  # Pass the selected API key
                )

                if result["success"]:
                    self.logger.debug(
                        f"Success with key for {provider_type.value}",
                        key_hash=key_hash,
                        requests_in_window=key_state.requests_in_window,
                    )
                    return result
                else:
                    self.logger.debug(
                        f"Key failed for {provider_type.value}, trying next key",
                        key_hash=key_hash,
                        error=result.get("error", "Unknown error"),
                    )
                    continue

            except Exception as e:
                self.logger.debug(
                    f"Key exception for {provider_type.value}, trying next key",
                    key_hash=key_hash,
                    error=str(e),
                )
                continue

        # All keys failed
        return {
            "success": False,
            "error": f"All keys for provider {provider_type.value} failed",
        }

    def _hash_key(self, key: str) -> str:
        """Hash API key for storage (don't store actual keys)."""
        return hashlib.sha256(key.encode()).hexdigest()[:16]

    def get_key_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about key usage across all providers.

        Returns:
            Dictionary with key usage statistics
        """
        stats = {}

        for provider_type, provider_config in self.providers.items():
            provider_stats = {
                "total_keys": len(provider_config.api_keys),
                "enabled": provider_config.enabled,
                "keys": [],
            }

            for key in provider_config.api_keys:
                key_hash = self._hash_key(key)
                key_state = self.key_states.get(key_hash)

                if key_state:
                    key_info = {
                        "key_hash": key_hash,
                        "total_requests": key_state.total_requests,
                        "requests_in_window": key_state.requests_in_window,
                        "last_used": key_state.last_used,
                    }
                    provider_stats["keys"].append(key_info)

            stats[provider_type.value] = provider_stats

        return stats

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all providers."""
        results = {}

        for provider_type, client in self.clients.items():
            try:
                health_result = await client.health_check()
                results[provider_type.value] = health_result
            except Exception as e:
                results[provider_type.value] = {
                    "success": False,
                    "error": str(e),
                }

        return {
            "success": any(r.get("success", False) for r in results.values()),
            "providers": results,
        }

    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the AI Manager connection with a simple request.

        Returns:
            Dictionary with test results
        """
        try:
            test_messages = [
                {
                    "role": "user",
                    "content": "Hello! Please respond with 'AI Manager is working correctly.'",
                }
            ]

            result = await self.create_chat_completion(
                messages=test_messages,
                max_tokens=50,
                temperature=0.1,
                pii=False,
            )

            if result["success"]:
                return {
                    "success": True,
                    "message": "AI Manager test successful",
                    "response": result.get("content", ""),
                    "provider": result.get("model", "unknown"),
                }
            else:
                return {
                    "success": False,
                    "message": "AI Manager test failed",
                    "error": result.get("error", "Unknown error"),
                }

        except Exception as e:
            return {
                "success": False,
                "message": "AI Manager test failed",
                "error": str(e),
            }


# Global AI Manager instance
_ai_manager: Optional[AIManager] = None


async def get_ai_manager() -> AIManager:
    """Get the global AI Manager instance."""
    global _ai_manager

    if _ai_manager is None:
        _ai_manager = AIManager()
        await _ai_manager.initialize()

    return _ai_manager


async def cleanup_ai_manager():
    """Clean up the global AI Manager instance."""
    global _ai_manager

    if _ai_manager:
        await _ai_manager.cleanup()
        _ai_manager = None
