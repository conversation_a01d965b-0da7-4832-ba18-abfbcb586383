"""
AI Manager Module for TractionX Data Pipeline Service.

This module provides a unified AI manager that routes LLM requests between multiple
providers (Together AI, OpenRouter, OpenAI, etc.) based on policies, cost, and availability.
"""

import hashlib
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Protocol

from app.clients.ai.openrouter import OpenRouterClient
from app.clients.ai.together import TogetherClient
from app.configs import get_logger, settings

logger = get_logger(__name__)


class ProviderType(Enum):
    """Supported AI providers."""

    TOGETHER = "together"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    PERPLEXITY = "perplexity"


class SensitivityLevel(Enum):
    """Sensitivity levels for routing decisions."""

    NON_PII = "non_pii"
    PII = "pii"


@dataclass
class ProviderConfig:
    """Configuration for an AI provider."""

    provider_type: ProviderType
    api_keys: List[str] = field(default_factory=list)
    base_url: str = ""
    default_model: str = ""
    rate_limit: Optional[int] = None
    cost_per_1k_tokens: float = 0.0
    is_secure: bool = False
    is_free: bool = False
    enabled: bool = True
    consecutive_failures: int = 0  # Track consecutive provider failures
    last_failure_time: float = 0.0  # Time of last provider failure
    is_disabled: bool = False  # Whether provider is temporarily disabled
    disable_until: float = 0.0  # When to re-enable the provider
    max_failures_before_disable: int = 5  # Max failures before disabling provider
    disable_duration: int = 300  # Disable duration in seconds (5 minutes)


@dataclass
class KeyState:
    """State tracking for API key rotation."""

    key_hash: str
    last_used: float = 0.0
    requests_in_window: int = 0
    window_start: float = 0.0
    total_requests: int = 0  # Track total requests for this key
    consecutive_failures: int = 0  # Track consecutive failures
    last_failure_time: float = 0.0  # Time of last failure
    is_rate_limited: bool = False  # Whether key is currently rate limited
    rate_limit_reset_time: float = 0.0  # When rate limit resets
    is_disabled: bool = False  # Whether key is temporarily disabled
    disable_until: float = 0.0  # When to re-enable the key


class AIClientProtocol(Protocol):
    """Protocol for AI clients that support chat completion."""

    async def initialize(self) -> None:
        """Initialize the client."""
        ...

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        api_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Create a chat completion."""
        ...

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check."""
        ...

    async def cleanup(self) -> None:
        """Clean up client resources."""
        ...


class AIManager:
    """
    AI Manager for multi-provider LLM routing.

    Handles:
    - Dynamic provider selection based on policies
    - Simple API key rotation (try next key on failure)
    - Automatic failover between providers
    - Cost optimization and security compliance
    - Key usage statistics tracking

    Key Rotation Features:
    - Simple rotation: try next key when current one fails
    - Rate limit awareness: skip keys that have hit rate limits
    - Usage statistics tracking for monitoring
    """

    def __init__(self):
        """Initialize the AI Manager."""
        self.logger = get_logger(f"{__name__}.AIManager")

        # Provider configurations
        self.providers: Dict[ProviderType, ProviderConfig] = {}
        self.key_states: Dict[str, KeyState] = {}

        # Routing policies
        self.routing_config = {
            "non_pii_order": [ProviderType.TOGETHER, ProviderType.OPENROUTER],
            "pii_order": [ProviderType.OPENAI, ProviderType.PERPLEXITY],
            "free_models": {
                ProviderType.TOGETHER: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                ProviderType.OPENROUTER: "openai/gpt-oss-20b:free",
            },
        }

        # Provider clients
        self.clients: Dict[ProviderType, AIClientProtocol] = {}

        # Initialize providers
        self._initialize_providers()

        self.logger.info("AI Manager initialized")

    def _initialize_providers(self) -> None:
        """Initialize provider configurations."""
        # Together AI
        together_keys = self._get_api_keys("TOGETHER_API_KEY")
        self.providers[ProviderType.TOGETHER] = ProviderConfig(
            provider_type=ProviderType.TOGETHER,
            api_keys=together_keys,
            base_url="https://api.together.xyz/v1",
            default_model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            rate_limit=1000,  # requests per minute
            cost_per_1k_tokens=0.0,  # Free tier
            is_secure=False,
            is_free=True,
            enabled=len(together_keys) > 0,
        )

        # OpenRouter
        openrouter_keys = self._get_api_keys("OPENROUTER_API_KEY")
        self.providers[ProviderType.OPENROUTER] = ProviderConfig(
            provider_type=ProviderType.OPENROUTER,
            api_keys=openrouter_keys,
            base_url="https://openrouter.ai/api/v1",
            default_model="openai/gpt-oss-20b:free",
            rate_limit=500,
            cost_per_1k_tokens=0.00024,  # $0.24 per 1M tokens
            is_secure=False,
            is_free=True,
            enabled=len(openrouter_keys) > 0,
        )

        # OpenAI
        openai_keys = self._get_api_keys("OPENAI_API_KEY")
        self.providers[ProviderType.OPENAI] = ProviderConfig(
            provider_type=ProviderType.OPENAI,
            api_keys=openai_keys,
            base_url="https://api.openai.com/v1",
            default_model="gpt-4",
            rate_limit=3000,
            cost_per_1k_tokens=0.03,  # $0.03 per 1K tokens
            is_secure=True,
            is_free=False,
            enabled=len(openai_keys) > 0,
        )

        # Perplexity
        perplexity_keys = self._get_api_keys("PERPLEXITY_API_KEY")
        self.providers[ProviderType.PERPLEXITY] = ProviderConfig(
            provider_type=ProviderType.PERPLEXITY,
            api_keys=perplexity_keys,
            base_url="https://api.perplexity.ai",
            default_model="llama-3.1-8b-instant",
            rate_limit=1000,
            cost_per_1k_tokens=0.0002,  # $0.20 per 1M tokens
            is_secure=True,
            is_free=False,
            enabled=len(perplexity_keys) > 0,
        )

    def _get_api_keys(self, key_name: str) -> List[str]:
        """Get API keys from settings, supporting multiple keys."""
        # Check for single key first
        single_key = getattr(settings, key_name, None)
        if single_key:
            return [single_key]

        # Check for multiple keys (e.g., TOGETHER_API_KEYS)
        multiple_keys_name = f"{key_name}S"
        multiple_keys = getattr(settings, multiple_keys_name, None)
        if multiple_keys:
            if isinstance(multiple_keys, str):
                return [key.strip() for key in multiple_keys.split(",") if key.strip()]
            elif isinstance(multiple_keys, list):
                return multiple_keys

        return []

    async def initialize(self) -> None:
        """Initialize all provider clients."""
        try:
            # Initialize Together AI client
            if self.providers[ProviderType.TOGETHER].enabled:
                self.clients[ProviderType.TOGETHER] = TogetherClient()
                await self.clients[ProviderType.TOGETHER].initialize()
                self.logger.info("Together AI client initialized")

            # Initialize OpenRouter client
            if self.providers[ProviderType.OPENROUTER].enabled:
                self.clients[ProviderType.OPENROUTER] = OpenRouterClient()
                await self.clients[ProviderType.OPENROUTER].initialize()
                self.logger.info("OpenRouter client initialized")

            # Initialize other clients as needed
            # TODO: Add OpenAI, Perplexity clients when implemented

            self.logger.info("AI Manager initialization complete")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Manager: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """Clean up all provider clients."""
        for provider_type, client in self.clients.items():
            try:
                await client.cleanup()
                self.logger.info(f"Cleaned up {provider_type.value} client")
            except Exception as e:
                self.logger.error(
                    f"Failed to cleanup {provider_type.value} client: {str(e)}"
                )

        self.clients.clear()

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        pii: bool = False,
        provider_type: Optional[ProviderType] = None,
    ) -> Dict[str, Any]:
        """
        Create a chat completion using the best available provider.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (defaults to provider's default model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            stream: Whether to stream the response
            pii: Whether the request contains PII (affects provider selection)

        Returns:
            Dictionary with success status and completion data
        """
        start_time = time.time()

        if provider_type is None:
            # Determine sensitivity level
            sensitivity = SensitivityLevel.PII if pii else SensitivityLevel.NON_PII

            # Get provider order based on sensitivity
            provider_order = self.routing_config[
                "pii_order" if pii else "non_pii_order"
            ]
        else:
            provider_order = [provider_type]

        self.logger.info(
            "Creating chat completion",
            sensitivity=sensitivity.value,
            provider_order=[p.value for p in provider_order],
            message_count=len(messages),
            max_tokens=max_tokens,
            temperature=temperature,
        )

        # Try providers in order
        for provider_type in provider_order:
            # Check if provider is available (enabled and not disabled)
            if not self._is_provider_available(provider_type):
                provider_config = self.providers.get(provider_type)
                if provider_config:
                    self.logger.debug(
                        f"Provider {provider_type.value} is unavailable, skipping",
                        enabled=provider_config.enabled,
                        is_disabled=provider_config.is_disabled,
                        consecutive_failures=provider_config.consecutive_failures,
                    )
                continue

            try:
                result = await self._try_provider(
                    provider_type=provider_type,
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=stream,
                )

                if result["success"]:
                    response_time_ms = int((time.time() - start_time) * 1000)

                    self.logger.info(
                        "Chat completion successful",
                        provider=provider_type.value,
                        model=result.get("model", "unknown"),
                        response_time_ms=response_time_ms,
                        content_length=len(result.get("content", "")),
                    )

                    return result
                else:
                    # Update provider failure state
                    self._update_provider_failure(provider_type, result)

                    self.logger.warning(
                        f"Provider {provider_type.value} failed: {result.get('error', 'Unknown error')}",
                        status_code=result.get("status_code"),
                    )

            except Exception as e:
                # Handle unexpected exceptions
                error_response = {"error": str(e), "status_code": 0}
                self._update_provider_failure(provider_type, error_response)

                self.logger.error(f"Provider {provider_type.value} error: {str(e)}")
                continue

        # All providers failed
        error_msg = "All AI providers failed"
        self.logger.error(error_msg)

        return {
            "success": False,
            "error": error_msg,
        }

    async def _try_provider(
        self,
        provider_type: ProviderType,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """Try a specific provider for chat completion."""
        provider_config = self.providers[provider_type]

        # Use provider-specific client
        client = self.clients.get(provider_type)
        if not client:
            return {
                "success": False,
                "error": f"Provider {provider_type.value} client not initialized",
            }

        # Try each available key until one works
        for key in provider_config.api_keys:
            key_hash = self._hash_key(key)
            key_state = self.key_states.get(key_hash)

            if not key_state:
                # Initialize key state
                key_state = KeyState(key_hash=key_hash)
                self.key_states[key_hash] = key_state

            # Check if key is available (not disabled, not rate limited)
            if not self._is_key_available(key_hash):
                self.logger.debug(
                    f"Key unavailable for {provider_type.value}, skipping",
                    key_hash=key_hash,
                    is_disabled=key_state.is_disabled,
                    is_rate_limited=key_state.is_rate_limited,
                )
                continue

            # Check rate limiting window
            current_time = time.time()
            if current_time - key_state.window_start > 60:  # Reset window every minute
                key_state.window_start = current_time
                key_state.requests_in_window = 0

            # Skip if rate limit exceeded for this window
            if key_state.requests_in_window >= (provider_config.rate_limit or 1000):
                self.logger.debug(
                    f"Key rate limit exceeded for {provider_type.value}, skipping",
                    key_hash=key_hash,
                    requests_in_window=key_state.requests_in_window,
                )
                continue

            try:
                # Update key usage
                key_state.requests_in_window += 1
                key_state.total_requests += 1
                key_state.last_used = current_time

                # Use the client's create_chat_completion method with the selected key
                result = await client.create_chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=stream,
                    api_key=key,  # Pass the selected API key
                )

                if result["success"]:
                    # Update success state
                    self._update_key_success(key_hash)
                    self._update_provider_success(provider_type)

                    self.logger.debug(
                        f"Success with key for {provider_type.value}",
                        key_hash=key_hash,
                        requests_in_window=key_state.requests_in_window,
                    )
                    return result
                else:
                    # Update failure state
                    self._update_key_failure(key_hash, result, provider_type)

                    self.logger.debug(
                        f"Key failed for {provider_type.value}, trying next key",
                        key_hash=key_hash,
                        error=result.get("error", "Unknown error"),
                        status_code=result.get("status_code"),
                    )
                    continue

            except Exception as e:
                # Handle unexpected exceptions
                error_response = {"error": str(e), "status_code": 0}
                self._update_key_failure(key_hash, error_response, provider_type)

                self.logger.debug(
                    f"Key exception for {provider_type.value}, trying next key",
                    key_hash=key_hash,
                    error=str(e),
                )
                continue

        # All keys failed
        return {
            "success": False,
            "error": f"All keys for provider {provider_type.value} failed",
        }

    def _is_rate_limit_error(
        self, error_response: Dict[str, Any], provider_type: ProviderType
    ) -> bool:
        """Detect if an error response indicates rate limiting."""
        error_msg = str(error_response.get("error", "")).lower()
        status_code = error_response.get("status_code", 0)

        # Common rate limit indicators
        if status_code == 429:
            return True

        # Provider-specific rate limit detection
        rate_limit_indicators = [
            "rate limit",
            "rate_limit",
            "ratelimit",
            "too many requests",
            "quota exceeded",
            "requests per minute",
            "requests per second",
            "throttled",
            "throttling",
        ]

        # Together AI specific
        if provider_type == ProviderType.TOGETHER:
            together_indicators = [
                "rate limit exceeded",
                "too many requests",
                "quota exceeded",
                "requests per minute exceeded",
            ]
            rate_limit_indicators.extend(together_indicators)

        # OpenRouter specific
        elif provider_type == ProviderType.OPENROUTER:
            openrouter_indicators = [
                "rate limit exceeded",
                "credits exhausted",
                "monthly limit exceeded",
                "daily limit exceeded",
            ]
            rate_limit_indicators.extend(openrouter_indicators)

        return any(indicator in error_msg for indicator in rate_limit_indicators)

    def _is_auth_error(self, error_response: Dict[str, Any]) -> bool:
        """Detect if an error response indicates authentication failure."""
        error_msg = str(error_response.get("error", "")).lower()
        status_code = error_response.get("status_code", 0)

        if status_code in [401, 403]:
            return True

        auth_indicators = [
            "unauthorized",
            "invalid api key",
            "invalid_api_key",
            "authentication failed",
            "forbidden",
            "access denied",
            "invalid token",
            "expired token",
            "api key not found",
        ]

        return any(indicator in error_msg for indicator in auth_indicators)

    def _is_server_error(self, error_response: Dict[str, Any]) -> bool:
        """Detect if an error response indicates a server error."""
        status_code = error_response.get("status_code", 0)
        return 500 <= status_code < 600

    def _update_key_failure(
        self, key_hash: str, error_response: Dict[str, Any], provider_type: ProviderType
    ) -> None:
        """Update key state after a failure."""
        key_state = self.key_states.get(key_hash)
        if not key_state:
            return

        current_time = time.time()
        key_state.consecutive_failures += 1
        key_state.last_failure_time = current_time

        # Handle rate limiting
        if self._is_rate_limit_error(error_response, provider_type):
            key_state.is_rate_limited = True
            # Set rate limit reset time (default 60 seconds, can be extracted from headers)
            key_state.rate_limit_reset_time = current_time + 60
            self.logger.warning(
                f"Key rate limited for {provider_type.value}",
                key_hash=key_hash,
                reset_time=key_state.rate_limit_reset_time,
            )

        # Handle authentication errors (disable key)
        elif self._is_auth_error(error_response):
            key_state.is_disabled = True
            key_state.disable_until = current_time + 3600  # Disable for 1 hour
            self.logger.error(
                f"Key authentication failed for {provider_type.value}, disabling for 1 hour",
                key_hash=key_hash,
            )

        # Handle too many consecutive failures
        elif key_state.consecutive_failures >= 3:
            key_state.is_disabled = True
            key_state.disable_until = current_time + 300  # Disable for 5 minutes
            self.logger.warning(
                f"Key disabled due to consecutive failures for {provider_type.value}",
                key_hash=key_hash,
                consecutive_failures=key_state.consecutive_failures,
            )

    def _update_key_success(self, key_hash: str) -> None:
        """Update key state after a successful request."""
        key_state = self.key_states.get(key_hash)
        if not key_state:
            return

        # Reset failure counters on success
        key_state.consecutive_failures = 0
        key_state.is_rate_limited = False
        key_state.is_disabled = False

    def _is_key_available(self, key_hash: str) -> bool:
        """Check if a key is available for use."""
        key_state = self.key_states.get(key_hash)
        if not key_state:
            return True

        current_time = time.time()

        # Check if key is disabled
        if key_state.is_disabled and current_time < key_state.disable_until:
            return False

        # Check if key is rate limited
        if key_state.is_rate_limited and current_time < key_state.rate_limit_reset_time:
            return False

        # Re-enable key if disable period has passed
        if key_state.is_disabled and current_time >= key_state.disable_until:
            key_state.is_disabled = False

        # Re-enable key if rate limit period has passed
        if (
            key_state.is_rate_limited
            and current_time >= key_state.rate_limit_reset_time
        ):
            key_state.is_rate_limited = False

        return True

    def _hash_key(self, key: str) -> str:
        """Hash API key for storage (don't store actual keys)."""
        return hashlib.sha256(key.encode()).hexdigest()[:16]

    def _update_provider_failure(
        self, provider_type: ProviderType, error_response: Dict[str, Any]
    ) -> None:
        """Update provider state after a failure."""
        provider_config = self.providers.get(provider_type)
        if not provider_config:
            return

        current_time = time.time()
        provider_config.consecutive_failures += 1
        provider_config.last_failure_time = current_time

        # Disable provider if too many consecutive failures
        if (
            provider_config.consecutive_failures
            >= provider_config.max_failures_before_disable
        ):
            provider_config.is_disabled = True
            provider_config.disable_until = (
                current_time + provider_config.disable_duration
            )
            self.logger.warning(
                f"Provider {provider_type.value} disabled due to consecutive failures",
                consecutive_failures=provider_config.consecutive_failures,
                disable_until=provider_config.disable_until,
            )

    def _update_provider_success(self, provider_type: ProviderType) -> None:
        """Update provider state after a successful request."""
        provider_config = self.providers.get(provider_type)
        if not provider_config:
            return

        # Reset failure counters on success
        provider_config.consecutive_failures = 0
        provider_config.is_disabled = False

    def _is_provider_available(self, provider_type: ProviderType) -> bool:
        """Check if a provider is available for use."""
        provider_config = self.providers.get(provider_type)
        if not provider_config or not provider_config.enabled:
            return False

        current_time = time.time()

        # Check if provider is disabled
        if provider_config.is_disabled and current_time < provider_config.disable_until:
            return False

        # Re-enable provider if disable period has passed
        if (
            provider_config.is_disabled
            and current_time >= provider_config.disable_until
        ):
            provider_config.is_disabled = False
            provider_config.consecutive_failures = 0
            self.logger.info(
                f"Provider {provider_type.value} re-enabled after disable period"
            )

        return True

    def get_key_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about key usage across all providers.

        Returns:
            Dictionary with key usage statistics
        """
        stats = {}

        for provider_type, provider_config in self.providers.items():
            provider_stats = {
                "total_keys": len(provider_config.api_keys),
                "enabled": provider_config.enabled,
                "keys": [],
            }

            for key in provider_config.api_keys:
                key_hash = self._hash_key(key)
                key_state = self.key_states.get(key_hash)

                if key_state:
                    key_info = {
                        "key_hash": key_hash,
                        "total_requests": key_state.total_requests,
                        "requests_in_window": key_state.requests_in_window,
                        "last_used": key_state.last_used,
                        "consecutive_failures": key_state.consecutive_failures,
                        "is_rate_limited": key_state.is_rate_limited,
                        "is_disabled": key_state.is_disabled,
                        "is_available": self._is_key_available(key_hash),
                    }
                    provider_stats["keys"].append(key_info)

            stats[provider_type.value] = provider_stats

        return stats

    def get_provider_health(self) -> Dict[str, Any]:
        """Get health status of all providers."""
        health = {}
        current_time = time.time()

        for provider_type, provider_config in self.providers.items():
            # Count available keys
            available_keys = 0
            total_keys = len(provider_config.api_keys)

            for key in provider_config.api_keys:
                key_hash = self._hash_key(key)
                if self._is_key_available(key_hash):
                    available_keys += 1

            health[provider_type.value] = {
                "enabled": provider_config.enabled,
                "is_available": self._is_provider_available(provider_type),
                "is_disabled": provider_config.is_disabled,
                "consecutive_failures": provider_config.consecutive_failures,
                "last_failure_time": provider_config.last_failure_time,
                "disable_until": provider_config.disable_until,
                "total_keys": total_keys,
                "available_keys": available_keys,
                "disabled_keys": total_keys - available_keys,
                "time_until_reenable": max(
                    0, provider_config.disable_until - current_time
                )
                if provider_config.is_disabled
                else 0,
            }

        return health

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        # Convert routing config to JSON-serializable format
        routing_config_serializable = {}
        for key, value in self.routing_config.items():
            if isinstance(value, list):
                routing_config_serializable[key] = [
                    item.value if hasattr(item, "value") else item for item in value
                ]
            elif isinstance(value, dict):
                routing_config_serializable[key] = {
                    k.value if hasattr(k, "value") else k: v for k, v in value.items()
                }
            else:
                routing_config_serializable[key] = value

        return {
            "providers": self.get_provider_health(),
            "keys": self.get_key_statistics(),
            "routing_config": routing_config_serializable,
            "timestamp": time.time(),
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all providers."""
        results = {}

        for provider_type, client in self.clients.items():
            try:
                health_result = await client.health_check()
                results[provider_type.value] = health_result
            except Exception as e:
                results[provider_type.value] = {
                    "success": False,
                    "error": str(e),
                }

        return {
            "success": any(r.get("success", False) for r in results.values()),
            "providers": results,
        }

    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the AI Manager connection with a simple request.

        Returns:
            Dictionary with test results
        """
        try:
            test_messages = [
                {
                    "role": "user",
                    "content": "Hello! Please respond with 'AI Manager is working correctly.'",
                }
            ]

            result = await self.create_chat_completion(
                messages=test_messages,
                max_tokens=50,
                temperature=0.1,
                pii=False,
            )

            if result["success"]:
                return {
                    "success": True,
                    "message": "AI Manager test successful",
                    "response": result.get("content", ""),
                    "provider": result.get("model", "unknown"),
                }
            else:
                return {
                    "success": False,
                    "message": "AI Manager test failed",
                    "error": result.get("error", "Unknown error"),
                }

        except Exception as e:
            return {
                "success": False,
                "message": "AI Manager test failed",
                "error": str(e),
            }


# Global AI Manager instance
_ai_manager: Optional[AIManager] = None


async def get_ai_manager() -> AIManager:
    """Get the global AI Manager instance."""
    global _ai_manager

    if _ai_manager is None:
        _ai_manager = AIManager()
        await _ai_manager.initialize()

    return _ai_manager


async def cleanup_ai_manager():
    """Clean up the global AI Manager instance."""
    global _ai_manager

    if _ai_manager:
        await _ai_manager.cleanup()
        _ai_manager = None
