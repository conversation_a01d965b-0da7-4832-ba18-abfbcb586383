"""
OpenRouter Client Module for TractionX Data Pipeline Service.

This module provides a unified wrapper for OpenRouter LLM generation,
centralizing all LLM interactions across the platform.
"""

import time
from typing import Any, Dict, List, Optional

from app.clients.base_client import BaseClient
from app.configs import get_logger, settings

logger = get_logger(__name__)


class OpenRouterClient(BaseClient):
    """
    Unified OpenRouter client for LLM generation.

    Handles:
    - LLM chat completions with OpenRouter
    - Automatic retries and error handling
    - Response parsing and validation
    - Rate limiting and timeout management
    """

    def __init__(self):
        """Initialize the OpenRouter client."""
        super().__init__(
            base_url="https://openrouter.ai/api/v1",
            api_key=settings.OPENROUTER_API_KEY,
            timeout=120.0,  # Longer timeout for LLM generation
            max_retries=5,  # More retries for rate limiting
            retry_delay=5.0,  # Longer base delay for rate limiting
            headers={
                "Content-Type": "application/json",
            },
        )

        # Default model
        self.default_model = "openai/gpt-oss-20b:free"

        self.logger.info(f"OpenRouter client config: model={self.default_model}")

    async def initialize(self) -> None:
        """Initialize the OpenRouter client."""
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY not configured")

        # Initialize base client
        await super().initialize()

        self.logger.info("OpenRouter client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()

        self.logger.info("OpenRouter client cleaned up")

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
        api_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a chat completion using OpenRouter.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (defaults to self.default_model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            stream: Whether to stream the response
            api_key: Optional API key to use (overrides the default key)

        Returns:
            Dictionary with success status and completion data
        """
        try:
            start_time = time.time()
            model = model or self.default_model

            self.logger.info(
                "Creating chat completion",
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                message_count=len(messages),
            )

            # Prepare request data
            data = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            # Use dynamic API key if provided
            headers = None
            if api_key:
                headers = {"Authorization": f"Bearer {api_key}"}

            # Use base client's post method
            result = await self.post("/chat/completions", data=data, headers=headers)

            if result["success"]:
                # Extract content from response
                response_data = result["data"]
                if "choices" in response_data and response_data["choices"]:
                    content = (
                        response_data["choices"][0]
                        .get("message", {})
                        .get("content", "")
                    )
                else:
                    content = ""

                response_time_ms = int((time.time() - start_time) * 1000)

                self.logger.info(
                    "Chat completion successful",
                    model=model,
                    response_time_ms=response_time_ms,
                    content_length=len(content),
                    tokens_used=response_data.get("usage", {}).get("total_tokens", 0),
                )

                return {
                    "success": True,
                    "content": content,
                    "full_response": response_data,
                    "response_time_ms": response_time_ms,
                    "model": model,
                    "attempts": result.get("attempts", 1),
                }

            return result

        except Exception as e:
            error_msg = f"Chat completion failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
            }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the OpenRouter service.

        Returns:
            Dictionary with health status
        """
        try:
            # Try a simple request to check if the service is accessible
            result = await self.get("/models")

            return {
                "success": result["success"],
                "service": "openrouter",
                "status": "healthy" if result["success"] else "unhealthy",
                "error": result.get("error") if not result["success"] else None,
            }
        except Exception as e:
            return {
                "success": False,
                "service": "openrouter",
                "status": "unhealthy",
                "error": str(e),
            }


# Global client instance
_openrouter_client: Optional[OpenRouterClient] = None


async def get_openrouter_client() -> OpenRouterClient:
    """Get the global OpenRouter client instance."""
    global _openrouter_client

    if _openrouter_client is None:
        _openrouter_client = OpenRouterClient()
        await _openrouter_client.initialize()

    return _openrouter_client


async def cleanup_openrouter_client():
    """Clean up the global OpenRouter client instance."""
    global _openrouter_client

    if _openrouter_client:
        await _openrouter_client.cleanup()
        _openrouter_client = None
