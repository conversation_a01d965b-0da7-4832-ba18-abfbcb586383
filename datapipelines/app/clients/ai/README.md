# Enhanced AI Manager with Failover and API Key Rotation

## Overview

The enhanced AI Manager provides robust failover capabilities and intelligent API key rotation for multiple AI providers. It ensures high availability and reliability by automatically handling rate limits, provider failures, and API key issues.

## Key Features

### 1. **Provider Failover**
- Automatic failover between AI providers (Together AI, OpenRouter, OpenAI, etc.)
- Circuit breaker pattern to temporarily disable failing providers
- Configurable failure thresholds and recovery times
- Provider health monitoring and statistics

### 2. **API Key Rotation**
- Support for multiple API keys per provider
- Intelligent key rotation when rate limits are hit
- Automatic key disabling for authentication failures
- Key health tracking and availability monitoring

### 3. **Enhanced Error Detection**
- Provider-specific rate limit detection
- Authentication error handling
- Server error retry logic
- Comprehensive error classification

### 4. **Monitoring and Statistics**
- Real-time provider health status
- API key usage statistics
- System-wide status reporting
- Failure tracking and recovery metrics

## Configuration

### Environment Variables

The manager supports multiple API keys per provider using comma-separated values:

```bash
# Single key (existing format)
TOGETHER_API_KEY=your_together_api_key

# Multiple keys (new format)
TOGETHER_API_KEYS=key1,key2,key3
OPENROUTER_API_KEYS=key1,key2,key3
```

### Provider Configuration

Each provider can be configured with:
- `max_failures_before_disable`: Number of consecutive failures before disabling (default: 5)
- `disable_duration`: How long to disable provider in seconds (default: 300)
- `rate_limit`: Requests per minute limit
- `cost_per_1k_tokens`: Cost optimization parameter

## Usage Examples

### Basic Usage

```python
from app.clients.ai.manager import AIManager

# Initialize manager
manager = AIManager()
await manager.initialize()

# Create chat completion
result = await manager.create_chat_completion(
    messages=[{"role": "user", "content": "Hello!"}],
    max_tokens=100,
    temperature=0.7
)

if result["success"]:
    print(result["content"])
else:
    print(f"Error: {result['error']}")
```

### PII-Aware Routing

```python
# Non-PII request (uses cheaper providers first)
result = await manager.create_chat_completion(
    messages=messages,
    pii=False  # Routes to Together AI, OpenRouter
)

# PII request (uses secure providers)
result = await manager.create_chat_completion(
    messages=messages,
    pii=True  # Routes to OpenAI, Perplexity
)
```

### Health Monitoring

```python
# Get provider health
health = manager.get_provider_health()
print(f"Together AI available: {health['together']['is_available']}")
print(f"Available keys: {health['together']['available_keys']}")

# Get key statistics
stats = manager.get_key_statistics()
for provider, provider_stats in stats.items():
    print(f"{provider}: {provider_stats['total_keys']} keys")

# Get comprehensive system status
status = await manager.get_system_status()
```

## Error Handling and Recovery

### Rate Limiting
- Automatically detects rate limit errors from HTTP 429 status codes
- Recognizes rate limit messages in response content
- Temporarily disables rate-limited keys with automatic recovery
- Rotates to next available key immediately

### Authentication Errors
- Detects invalid API key errors (401, 403 status codes)
- Automatically disables invalid keys for 1 hour
- Continues with remaining valid keys
- Logs authentication failures for monitoring

### Provider Failures
- Tracks consecutive failures per provider
- Implements circuit breaker pattern
- Temporarily disables failing providers
- Automatic re-enabling after cooldown period

### Key Management
- Tracks key health and availability
- Automatic key rotation on failures
- Intelligent key selection based on recent performance
- Graceful degradation when keys are exhausted

## Monitoring and Alerting

### Health Metrics
- Provider availability status
- Key availability counts
- Failure rates and patterns
- Response times and success rates

### Statistics Tracking
- Total requests per key
- Consecutive failure counts
- Rate limit hit frequencies
- Provider usage distribution

### System Status
- Real-time health dashboard data
- Historical performance metrics
- Failure analysis and trends
- Capacity planning information

## Best Practices

### API Key Management
1. **Use multiple keys per provider** for better reliability
2. **Rotate keys regularly** to maintain security
3. **Monitor key usage** to avoid unexpected rate limits
4. **Set up alerts** for key failures and rate limits

### Provider Configuration
1. **Configure appropriate failure thresholds** based on provider reliability
2. **Set reasonable disable durations** to balance availability and stability
3. **Monitor provider health** regularly
4. **Test failover scenarios** periodically

### Error Handling
1. **Implement proper retry logic** in your application
2. **Handle both success and failure cases** gracefully
3. **Log errors appropriately** for debugging
4. **Monitor error rates** and patterns

## Testing

Run the test script to verify functionality:

```bash
cd datapipelines
python test_ai_manager.py
```

The test script covers:
- Basic chat completion
- Provider failover
- API key rotation
- Error handling
- Health monitoring
- Statistics collection

## Troubleshooting

### Common Issues

1. **All providers failing**
   - Check API keys are valid
   - Verify network connectivity
   - Check provider service status

2. **Rate limits being hit frequently**
   - Add more API keys
   - Implement request throttling
   - Monitor usage patterns

3. **Keys being disabled unexpectedly**
   - Check for authentication errors
   - Verify key permissions
   - Monitor key usage limits

### Debug Information

Enable debug logging to see detailed information:

```python
import logging
logging.getLogger("app.clients.ai.manager").setLevel(logging.DEBUG)
```

This will show:
- Key selection decisions
- Provider failover events
- Rate limit detections
- Error classifications
