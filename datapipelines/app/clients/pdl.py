"""
PDL Client Module for TractionX Data Pipeline Service.

This module provides a unified wrapper for PDL (People Data Labs) API requests,
centralizing all PDL interactions across the platform.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional

from app.clients.base_client import BaseClient
from app.configs import get_logger, settings

logger = get_logger(__name__)


@dataclass
class PDLResponse:
    success: bool
    request_id: str
    raw_response: Any
    data: Optional[Any]


class PDLClient(BaseClient):
    """
    Unified PDL client for People Data Labs API.

    Handles:
    - Person enrichment requests
    - Company enrichment requests
    - Automatic retries and error handling
    - Rate limiting and timeout management
    """

    def __init__(self):
        """Initialize the PDL client."""
        # PDL API uses X-Api-Key header, not Authorization Bearer
        custom_headers = {
            "Content-Type": "application/json",
        }

        # Add API key to custom headers for PDL
        if settings.PDL_API_KEY:
            custom_headers["X-Api-Key"] = settings.PDL_API_KEY

        super().__init__(
            base_url="https://api.peopledatalabs.com",
            api_key=None,  # Don't use the base client's Authorization header
            timeout=30.0,
            max_retries=3,
            retry_delay=2.0,
            headers=custom_headers,
        )

        self.logger.info("PDL client initialized")

    async def initialize(self) -> None:
        """Initialize the PDL client."""
        if not settings.PDL_API_KEY:
            raise ValueError("PDL_API_KEY not configured")

        # Initialize base client
        await super().initialize()

        self.logger.info("PDL client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()

        self.logger.info("PDL client cleaned up")

    async def enrich_person(
        self,
        profile: str,
        min_likelihood: int = 2,
        include_if_matched: bool = False,
        titlecase: bool = False,
        pretty: bool = False,
    ) -> PDLResponse:
        """
        Enrich person data using PDL API.

        Args:
            profile: LinkedIn URL or email to enrich
            min_likelihood: Minimum likelihood score (1-10)
            include_if_matched: Include data even if not matched
            titlecase: Convert names to title case
            pretty: Pretty print response

        Returns:
            Dictionary with success status and response data
        """
        try:
            self.logger.info(
                "Enriching person with PDL",
                profile=profile,
                min_likelihood=min_likelihood,
            )

            # Prepare API parameters
            params = {
                "profile": profile,
                "min_likelihood": str(min_likelihood),
                "include_if_matched": str(include_if_matched).lower(),
                "titlecase": str(titlecase).lower(),
                "pretty": str(pretty).lower(),
            }

            # Make API call to PDL
            response = await self.get("/v5/person/enrich", params=params)

            # Check if the base client request was successful
            if not response.get("success"):
                return PDLResponse(
                    success=False,
                    request_id=profile,
                    raw_response=response,
                    data=None,
                )

            # Get the actual PDL response from the base client's data field
            pdl_response = response.get("data", {})

            # Validate PDL response
            if not self.validate_response(pdl_response):
                return PDLResponse(
                    success=False,
                    request_id=profile,
                    raw_response=pdl_response,
                    data=None,
                )

            # Check PDL API status
            if pdl_response.get("status") != 200:
                return PDLResponse(
                    success=False,
                    request_id=profile,
                    raw_response=pdl_response,
                    data=None,
                )

            # Extract person data
            person_data = pdl_response.get("data")
            if not person_data:
                return PDLResponse(
                    success=False,
                    request_id=profile,
                    raw_response=pdl_response,
                    data=None,
                )

            self.logger.info(
                "Successfully enriched person with PDL",
                profile=profile,
                likelihood=person_data.get("likelihood"),
            )

            return PDLResponse(
                success=True,
                request_id=profile,
                raw_response=pdl_response,
                data=person_data,
            )

        except Exception as e:
            error_msg = f"PDL person enrichment failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return PDLResponse(
                success=False,
                request_id=profile,
                raw_response=None,
                data=None,
            )

    async def enrich_company(
        self,
        domain: str,
        pretty: bool = False,
    ) -> Dict[str, Any]:
        """
        Enrich company data using PDL API.

        Args:
            domain: Company domain to enrich
            pretty: Pretty print response

        Returns:
            Dictionary with success status and response data
        """
        try:
            self.logger.info("Enriching company with PDL", domain=domain)

            # Prepare API parameters
            params = {
                "domain": domain,
                "pretty": str(pretty).lower(),
            }

            # Make API call to PDL
            response = await self.get("/v5/company/enrich", params=params)

            # Check if the base client request was successful
            if not response.get("success"):
                return {
                    "success": False,
                    "error": "Base client request failed",
                    "data": response,
                }

            # Get the actual PDL response from the base client's data field
            pdl_response = response.get("data", {})

            # Validate PDL response
            if not self.validate_response(pdl_response):
                return {
                    "success": False,
                    "error": "Invalid PDL response format",
                    "data": pdl_response,
                }

            # Check PDL API status
            if pdl_response.get("status") != 200:
                return {
                    "success": False,
                    "error": f"PDL API error: {pdl_response.get('error', 'Unknown error')}",
                    "data": pdl_response,
                }

            # Extract company data
            company_data = pdl_response.get("data")
            if not company_data:
                return {
                    "success": False,
                    "error": "No company data found in PDL response",
                    "data": pdl_response,
                }

            self.logger.info(
                "Successfully enriched company with PDL",
                domain=domain,
                company_name=company_data.get("name"),
            )

            return {
                "success": True,
                "data": company_data,
                "raw_response": pdl_response,
            }

        except Exception as e:
            error_msg = f"PDL company enrichment failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "data": None,
            }

    async def health_check(self) -> Dict[str, Any]:
        """
        Check PDL API health.

        Returns:
            Health status dictionary
        """
        try:
            # Make a simple API call to check health
            response = await self.get(
                "/v5/person/enrich", params={"profile": "<EMAIL>"}
            )

            # Check if the base client request was successful
            if not response.get("success"):
                return {
                    "healthy": False,
                    "status": "error",
                    "message": f"Base client request failed: {response.get('error', 'Unknown error')}",
                }

            # Get the actual PDL response from the base client's data field
            pdl_response = response.get("data", {})

            # PDL will return an error for invalid requests, but the API is working
            if "status" in pdl_response:
                return {
                    "healthy": True,
                    "status": "operational",
                    "message": "PDL API is responding",
                }
            else:
                return {
                    "healthy": False,
                    "status": "error",
                    "message": "PDL API not responding properly",
                }

        except Exception as e:
            return {
                "healthy": False,
                "status": "error",
                "message": f"PDL health check failed: {str(e)}",
            }

    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        Validate PDL API response format.

        Args:
            response: Response from PDL API

        Returns:
            True if response is valid, False otherwise
        """
        if not isinstance(response, dict):
            return False

        # Check for required fields
        required_fields = ["status"]
        for field in required_fields:
            if field not in response:
                return False

        return True


# Global PDL client instance
_pdl_client: Optional[PDLClient] = None


async def get_pdl_client() -> PDLClient:
    """
    Get the global PDL client instance.

    Returns:
        PDLClient instance
    """
    global _pdl_client

    if _pdl_client is None:
        _pdl_client = PDLClient()
        await _pdl_client.initialize()

    return _pdl_client


async def cleanup_pdl_client():
    """Clean up the global PDL client instance."""
    global _pdl_client

    if _pdl_client:
        await _pdl_client.cleanup()
        _pdl_client = None
