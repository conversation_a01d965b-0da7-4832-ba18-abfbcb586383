"""
Base Client Module for TractionX Data Pipeline Service.

This module provides a base client class that all API clients can inherit from,
providing common functionality like retries, error handling, logging, and configuration.
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union

import httpx
from app.configs import get_logger

logger = get_logger(__name__)


class BaseClient(ABC):
    """
    Base client class for all API clients in the TractionX Data Pipeline Service.

    Provides common functionality:
    - Async HTTP client management
    - Automatic retries with exponential backoff
    - Error handling and logging
    - Request/response validation
    - Rate limiting support
    - Configuration management
    """

    def __init__(
        self,
        base_url: str,
        api_key: Optional[str] = None,
        timeout: float = 60.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        rate_limit: Optional[int] = None,
        headers: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize the base client.

        Args:
            base_url: Base URL for the API
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Base delay between retries (will be exponential)
            rate_limit: Requests per minute (optional)
            headers: Additional headers to include in requests
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.rate_limit = rate_limit

        # HTTP client
        self.client: Optional[httpx.AsyncClient] = None

        # Rate limiting
        self._last_request_time = 0.0
        self._request_count = 0
        self._rate_limit_window_start = time.time()

        # Default headers
        self.default_headers = {
            "Content-Type": "application/json",
            "User-Agent": "TractionX-DataPipeline/1.0",
        }

        # Add API key to headers if provided
        if api_key:
            self.default_headers["Authorization"] = f"Bearer {api_key}"

        # Add custom headers
        if headers:
            self.default_headers.update(headers)

        # Setup logger
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        self.logger.info(
            "Base client initialized",
            base_url=self.base_url,
            timeout=self.timeout,
            max_retries=self.max_retries,
            rate_limit=self.rate_limit,
        )

    async def initialize(self) -> None:
        """Initialize the HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                headers=self.default_headers,
                timeout=self.timeout,
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
            )
            self.logger.info("HTTP client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        if self.client:
            await self.client.aclose()
            self.client = None
            self.logger.info("HTTP client cleaned up")

    async def _rate_limit_check(self) -> None:
        """Check and enforce rate limiting."""
        if not self.rate_limit:
            return

        current_time = time.time()

        # Reset counter if window has passed
        if current_time - self._rate_limit_window_start >= 60:
            self._request_count = 0
            self._rate_limit_window_start = current_time

        # Check if we're at the limit
        if self._request_count >= self.rate_limit:
            wait_time = 60 - (current_time - self._rate_limit_window_start)
            if wait_time > 0:
                self.logger.warning(f"Rate limit reached, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
                self._request_count = 0
                self._rate_limit_window_start = time.time()

        self._request_count += 1

    async def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with retries and error handling.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL (relative to base_url)
            data: Request data (dict for JSON, str for raw)
            headers: Additional headers
            params: Query parameters
            **kwargs: Additional arguments for httpx

        Returns:
            Dictionary with success status and response data
        """
        if not self.client:
            return {
                "success": False,
                "error": "Client not initialized",
            }

        # Check rate limiting
        await self._rate_limit_check()

        # Prepare request
        request_headers = self.default_headers.copy()
        if headers:
            request_headers.update(headers)

        # Prepare request data
        request_data = None
        if data is not None:
            if isinstance(data, dict):
                request_data = json.dumps(data)
            else:
                request_data = str(data)

        start_time = time.time()

        # Make request with retries
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(
                    f"Making {method} request",
                    url=url,
                    attempt=attempt + 1,
                    data_size=len(request_data) if request_data else 0,
                )

                response = await self.client.request(
                    method=method,
                    url=url,
                    content=request_data,
                    headers=request_headers,
                    params=params,
                    **kwargs,
                )

                response_time_ms = int((time.time() - start_time) * 1000)

                # Handle different response status codes
                if response.status_code < 400:
                    # Success
                    try:
                        result = response.json()
                    except json.JSONDecodeError:
                        result = response.text

                    self.logger.info(
                        f"{method} request successful",
                        url=url,
                        status_code=response.status_code,
                        response_time_ms=response_time_ms,
                        response_size=len(str(result)),
                        attempts=attempt + 1,
                    )

                    return {
                        "success": True,
                        "data": result,
                        "status_code": response.status_code,
                        "response_time_ms": response_time_ms,
                        "attempts": attempt + 1,
                    }

                elif response.status_code == 429:
                    # Rate limit error - retry with exponential backoff
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2**attempt)
                        self.logger.warning(
                            f"Rate limit error (429), retrying in {wait_time}s (attempt {attempt + 1})",
                            url=url,
                            status_code=response.status_code,
                        )
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        # Final attempt failed
                        error_msg = f"Rate limit error (429): {response.text}"
                        self.logger.error(
                            error_msg, url=url, status_code=response.status_code
                        )

                        return {
                            "success": False,
                            "error": error_msg,
                            "status_code": response.status_code,
                            "response_time_ms": response_time_ms,
                        }

                elif response.status_code < 500:
                    # Other client error (4xx) - check if it's a rate limit error in disguise
                    response_text = response.text.lower()
                    rate_limit_indicators = [
                        "rate limit", "rate_limit", "ratelimit",
                        "too many requests", "quota exceeded",
                        "requests per minute", "requests per second",
                        "throttled", "throttling", "credits exhausted"
                    ]

                    is_rate_limit = any(indicator in response_text for indicator in rate_limit_indicators)

                    if is_rate_limit and attempt < self.max_retries - 1:
                        # Treat as rate limit and retry
                        wait_time = self.retry_delay * (2**attempt)
                        self.logger.warning(
                            f"Rate limit detected in {response.status_code} response, retrying in {wait_time}s (attempt {attempt + 1})",
                            url=url,
                            status_code=response.status_code,
                        )
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        # Other client error - don't retry
                        error_msg = f"Client error {response.status_code}: {response.text}"
                        self.logger.error(
                            error_msg, url=url, status_code=response.status_code
                        )

                        return {
                            "success": False,
                            "error": error_msg,
                            "status_code": response.status_code,
                            "response_time_ms": response_time_ms,
                        }

                else:
                    # Server error (5xx) - retry if attempts remain
                    if attempt < self.max_retries - 1:
                        wait_time = self.retry_delay * (2**attempt)
                        self.logger.warning(
                            f"Server error {response.status_code}, retrying in {wait_time}s (attempt {attempt + 1})",
                            url=url,
                            status_code=response.status_code,
                        )
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        # Final attempt failed
                        error_msg = (
                            f"Server error {response.status_code}: {response.text}"
                        )
                        self.logger.error(
                            error_msg, url=url, status_code=response.status_code
                        )

                        return {
                            "success": False,
                            "error": error_msg,
                            "status_code": response.status_code,
                            "response_time_ms": response_time_ms,
                        }

            except httpx.TimeoutException:
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2**attempt)
                    self.logger.warning(
                        f"Request timeout, retrying in {wait_time}s (attempt {attempt + 1})",
                        url=url,
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    error_msg = f"Request timeout after {self.max_retries} attempts"
                    self.logger.error(error_msg, url=url)

                    return {
                        "success": False,
                        "error": error_msg,
                        "response_time_ms": int((time.time() - start_time) * 1000),
                    }

            except httpx.RequestError as e:
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2**attempt)
                    self.logger.warning(
                        f"Request error: {str(e)}, retrying in {wait_time}s (attempt {attempt + 1})",
                        url=url,
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    error_msg = (
                        f"Request error after {self.max_retries} attempts: {str(e)}"
                    )
                    self.logger.error(error_msg, url=url)

                    return {
                        "success": False,
                        "error": error_msg,
                        "response_time_ms": int((time.time() - start_time) * 1000),
                    }

        # If we get here, all retries failed
        return {
            "success": False,
            "error": "All retry attempts failed",
            "response_time_ms": int((time.time() - start_time) * 1000),
        }

    async def get(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Make a GET request."""
        return await self._make_request(
            "GET", url, params=params, headers=headers, **kwargs
        )

    async def post(
        self,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Make a POST request."""
        return await self._make_request(
            "POST", url, data=data, headers=headers, params=params, **kwargs
        )

    async def put(
        self,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Make a PUT request."""
        return await self._make_request(
            "PUT", url, data=data, headers=headers, params=params, **kwargs
        )

    async def delete(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Make a DELETE request."""
        return await self._make_request(
            "DELETE", url, headers=headers, params=params, **kwargs
        )

    async def patch(
        self,
        url: str,
        data: Optional[Union[Dict[str, Any], str]] = None,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Make a PATCH request."""
        return await self._make_request(
            "PATCH", url, data=data, headers=headers, params=params, **kwargs
        )

    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the service.

        Returns:
            Dictionary with health status
        """
        pass

    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        Validate a response from the service.

        Args:
            response: Response dictionary

        Returns:
            True if response is valid
        """
        if not response.get("success"):
            return False

        # Add custom validation logic in subclasses
        return True

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


class ClientManager:
    """
    Manager for multiple client instances.

    Provides centralized management of client lifecycle and cleanup.
    """

    def __init__(self):
        self.clients: Dict[str, BaseClient] = {}
        self.logger = get_logger(f"{__name__}.ClientManager")

    def register_client(self, name: str, client: BaseClient) -> None:
        """Register a client with the manager."""
        self.clients[name] = client
        self.logger.info(f"Registered client: {name}")

    async def initialize_all(self) -> None:
        """Initialize all registered clients."""
        for name, client in self.clients.items():
            try:
                await client.initialize()
                self.logger.info(f"Initialized client: {name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize client {name}: {str(e)}")
                raise

    async def cleanup_all(self) -> None:
        """Clean up all registered clients."""
        for name, client in self.clients.items():
            try:
                await client.cleanup()
                self.logger.info(f"Cleaned up client: {name}")
            except Exception as e:
                self.logger.error(f"Failed to cleanup client {name}: {str(e)}")

    def get_client(self, name: str) -> Optional[BaseClient]:
        """Get a client by name."""
        return self.clients.get(name)

    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Perform health checks on all clients."""
        results = {}
        for name, client in self.clients.items():
            try:
                results[name] = await client.health_check()
            except Exception as e:
                results[name] = {
                    "success": False,
                    "error": str(e),
                }
        return results


# Global client manager instance
_client_manager: Optional[ClientManager] = None


def get_client_manager() -> ClientManager:
    """Get the global client manager instance."""
    global _client_manager

    if _client_manager is None:
        _client_manager = ClientManager()

    return _client_manager


async def cleanup_all_clients():
    """Clean up all clients managed by the global manager."""
    global _client_manager

    if _client_manager:
        await _client_manager.cleanup_all()
        _client_manager = None
