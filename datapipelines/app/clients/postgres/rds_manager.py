"""
RDS Manager for TractionX Data Pipeline Service.

This module provides a unified interface for managing PostgreSQL connections
across different environments and providers (Neon, AWS RDS).
"""

from typing import Optional, Union

from app.clients.postgres.aws_rds import AWSRDSPostgresClient, create_aws_rds_client
from app.clients.postgres.neon import NeonPostgresClient, create_neon_client
from app.configs import get_logger, settings


class RDSManager:
    """
    Manager for PostgreSQL database connections across different environments.

    Automatically selects the appropriate client based on environment:
    - Production: AWS RDS
    - Development/Staging/Local: Neon
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.RDSManager")
        self.client: Optional[Union[NeonPostgresClient, AWSRDSPostgresClient]] = None
        self.provider = self._determine_provider()

        self.logger.info(f"RDS Manager initialized with provider: {self.provider}")

    def _determine_provider(self) -> str:
        """
        Determine the appropriate database provider based on environment.

        Returns:
            Provider name ('neon' or 'aws_rds')
        """
        environment = settings.ENVIRONMENT.lower()

        if environment == "production":
            self.logger.info("Production environment detected - using AWS RDS")
            return "aws_rds"
        else:
            # Development, staging, local all use Neon
            self.logger.info(
                f"{environment.capitalize()} environment detected - using Neon"
            )
            return "neon"

    def get_client(self) -> Union[NeonPostgresClient, AWSRDSPostgresClient]:
        """
        Get the appropriate database client for the current environment.

        Returns:
            Configured PostgreSQL client instance
        """
        if self.client is None:
            self.client = self._create_client()

        return self.client

    def _create_client(self) -> Union[NeonPostgresClient, AWSRDSPostgresClient]:
        """
        Create the appropriate database client based on the determined provider.

        Returns:
            Configured PostgreSQL client instance
        """
        if self.provider == "neon":
            return create_neon_client(
                connection_string=settings.NEON_CONNECTION_STRING,
                min_size=1,
                max_size=settings.DB_POOL_SIZE,
                max_inactive_connection_lifetime=300,
                command_timeout=60.0,
                statement_timeout=300.0,
                enable_ssl=True,
                retry_attempts=3,
                retry_delay=1.0,
            )
        elif self.provider == "aws_rds":
            return create_aws_rds_client(
                connection_string=settings.database_connection_string,
                min_size=1,
                max_size=settings.DB_POOL_SIZE,
                max_inactive_connection_lifetime=300,
                command_timeout=60.0,
                statement_timeout=300.0,
                enable_ssl=True,
                retry_attempts=3,
                retry_delay=1.0,
                enable_connection_health_check=True,
                connection_health_check_interval=30.0,
            )
        else:
            raise ValueError(f"Unsupported database provider: {self.provider}")

    async def initialize(self) -> None:
        """Initialize the database client."""
        try:
            client = self.get_client()
            await client.initialize()
            self.logger.info(
                f"Database client initialized successfully with {self.provider}"
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize database client: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up database resources."""
        if self.client:
            await self.client.cleanup()
            self.client = None
            self.logger.info("Database client cleaned up")

    async def health_check(self) -> dict:
        """
        Perform a health check on the database.

        Returns:
            Health status dictionary
        """
        if not self.client:
            return {
                "healthy": False,
                "error": "Client not initialized",
                "provider": self.provider,
            }

        try:
            health_status = await self.client.health_check()
            health_status["provider"] = self.provider
            return health_status
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "provider": self.provider,
            }

    async def execute_query(
        self,
        query: str,
        params: Optional[Union[list, dict]] = None,
        timeout: Optional[float] = None,
    ) -> list:
        """
        Execute a query using the current database client.

        Args:
            query: SQL query to execute
            params: Query parameters
            timeout: Query timeout in seconds

        Returns:
            Query results as list of dictionaries
        """
        client = self.get_client()
        return await client.execute_query(query, params, timeout)

    async def execute_scalar(
        self,
        query: str,
        params: Optional[Union[list, dict]] = None,
        timeout: Optional[float] = None,
    ):
        """
        Execute a query and return a single scalar value.

        Args:
            query: SQL query to execute
            params: Query parameters
            timeout: Query timeout in seconds

        Returns:
            Scalar value from the query
        """
        client = self.get_client()
        return await client.execute_scalar(query, params, timeout)

    async def execute_transaction(
        self,
        queries: list,
        timeout: Optional[float] = None,
    ) -> list:
        """
        Execute multiple queries in a single transaction.

        Args:
            queries: List of query dictionaries with 'query' and 'params' keys
            timeout: Transaction timeout in seconds

        Returns:
            List of results for each query
        """
        client = self.get_client()
        return await client.execute_transaction(queries, timeout)

    def get_provider_info(self) -> dict:
        """
        Get information about the current database provider.

        Returns:
            Provider information dictionary
        """
        return {
            "provider": self.provider,
            "environment": settings.ENVIRONMENT,
            "client_initialized": self.client is not None,
        }

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


# Global RDS manager instance
_rds_manager: Optional[RDSManager] = None


def get_rds_manager() -> RDSManager:
    """
    Get the global RDS manager instance.

    Returns:
        RDSManager instance
    """
    global _rds_manager

    if _rds_manager is None:
        _rds_manager = RDSManager()

    return _rds_manager


async def initialize_rds_manager() -> None:
    """Initialize the global RDS manager."""
    manager = get_rds_manager()
    await manager.initialize()


async def cleanup_rds_manager() -> None:
    """Clean up the global RDS manager."""
    global _rds_manager

    if _rds_manager:
        await _rds_manager.cleanup()
        _rds_manager = None
