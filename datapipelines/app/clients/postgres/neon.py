"""
Neon PostgreSQL Client for TractionX Data Pipeline Service.

This module provides a specialized PostgreSQL client for Neon database,
offering optimized connection management, connection pooling, and
professional error handling for the TractionX data pipeline service.
"""

import asyncio
import json
import ssl
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

import asyncpg
from app.configs import get_logger, settings
from asyncpg import Connection, Pool
from asyncpg.exceptions import (
    CheckViolationError,
    ConnectionDoesNotExistError,
    ForeignKeyViolationError,
    InvalidAuthorizationSpecificationError,
    InvalidPasswordError,
    NotNullViolationError,
    PostgresError,
    QueryCanceledError,
    UniqueViolationError,
)


class NeonPostgresClient:
    """
    Professional PostgreSQL client for Neon database.

    Features:
    - Connection pooling with configurable pool sizes
    - Automatic connection retry with exponential backoff
    - Comprehensive error handling and logging
    - Transaction management
    - Query optimization and monitoring
    - SSL/TLS support for secure connections
    - Health monitoring and diagnostics
    """

    def __init__(
        self,
        connection_string: Optional[str] = None,
        min_size: int = 1,
        max_size: int = 10,
        max_inactive_connection_lifetime: float = 300.0,
        command_timeout: float = 60.0,
        statement_timeout: float = 300.0,
        enable_ssl: bool = True,
        ssl_context: Optional[ssl.SSLContext] = None,
        retry_attempts: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        Initialize the Neon PostgreSQL client.

        Args:
            connection_string: Database connection string (uses settings if not provided)
            min_size: Minimum number of connections in the pool
            max_size: Maximum number of connections in the pool
            max_inactive_connection_lifetime: Maximum lifetime of inactive connections
            command_timeout: Command timeout in seconds
            statement_timeout: Statement timeout in seconds
            enable_ssl: Whether to enable SSL/TLS connections
            ssl_context: Custom SSL context (optional)
            retry_attempts: Number of retry attempts for failed operations
            retry_delay: Base delay between retries (exponential backoff)
        """
        self.connection_string = connection_string or settings.NEON_CONNECTION_STRING
        self.min_size = min_size
        self.max_size = max_size
        self.max_inactive_connection_lifetime = max_inactive_connection_lifetime
        self.command_timeout = command_timeout
        self.statement_timeout = statement_timeout
        self.enable_ssl = enable_ssl
        self.ssl_context = ssl_context
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay

        # Connection pool
        self.pool: Optional[Pool] = None

        # Statistics and monitoring
        self._connection_count = 0
        self._query_count = 0
        self._error_count = 0
        self._last_health_check = None

        # Setup logger
        self.logger = get_logger(f"{__name__}.NeonPostgresClient")

        self.logger.info(
            "Neon PostgreSQL client initialized",
            min_size=self.min_size,
            max_size=self.max_size,
            command_timeout=self.command_timeout,
            statement_timeout=self.statement_timeout,
            enable_ssl=self.enable_ssl,
            retry_attempts=self.retry_attempts,
        )

    async def initialize(self) -> None:
        """Initialize the database connection pool."""
        try:
            # Configure SSL if enabled
            ssl_config = None
            if self.enable_ssl:
                if self.ssl_context:
                    ssl_config = self.ssl_context
                else:
                    # Create default SSL context for Neon
                    ssl_config = ssl.create_default_context()
                    ssl_config.check_hostname = False
                    ssl_config.verify_mode = ssl.CERT_NONE

            # Create connection pool
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=self.min_size,
                max_size=self.max_size,
                max_inactive_connection_lifetime=self.max_inactive_connection_lifetime,
                command_timeout=self.command_timeout,
                ssl=ssl_config,
                setup=self._setup_connection,
            )

            # Verify pool is working
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
                self._connection_count += 1

            self.logger.info(
                "Neon PostgreSQL connection pool initialized successfully",
                pool_size=f"{self.min_size}-{self.max_size}",
                ssl_enabled=self.enable_ssl,
            )

        except Exception as e:
            self.logger.error(f"Failed to initialize Neon PostgreSQL client: {e}")
            self._error_count += 1
            raise

    async def cleanup(self) -> None:
        """Clean up database resources."""
        if self.pool:
            await self.pool.close()
            self.pool = None
            self.logger.info("Neon PostgreSQL connection pool closed")

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a comprehensive health check on the database.

        Returns:
            Dictionary with health status and diagnostics
        """
        try:
            if not self.pool:
                return {
                    "healthy": False,
                    "error": "Connection pool not initialized",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            start_time = datetime.now(timezone.utc)

            # Test basic connectivity
            async with self.pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                if result != 1:
                    raise ValueError("Health check query returned unexpected result")

            # Get pool statistics
            pool_size = self.pool.get_size()
            pool_stats = {
                "size": pool_size,
                "free_size": pool_size,  # asyncpg doesn't expose free_size directly
                "active_connections": 0,  # asyncpg doesn't expose active connections directly
            }

            # Test query performance
            async with self.pool.acquire() as conn:
                start_query = datetime.now(timezone.utc)
                await conn.fetchval("SELECT NOW()")
                query_time_ms = (
                    datetime.now(timezone.utc) - start_query
                ).total_seconds() * 1000

            health_status = {
                "healthy": True,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "response_time_ms": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds()
                * 1000,
                "query_time_ms": query_time_ms,
                "pool_stats": pool_stats,
                "statistics": {
                    "total_connections": self._connection_count,
                    "total_queries": self._query_count,
                    "total_errors": self._error_count,
                },
            }

            self._last_health_check = health_status
            return health_status

        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            self._error_count += 1

            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "statistics": {
                    "total_connections": self._connection_count,
                    "total_queries": self._query_count,
                    "total_errors": self._error_count,
                },
            }

    async def _setup_connection(self, connection: Connection) -> None:
        """Setup connection with custom settings and extensions."""
        try:
            # Enable UUID extension
            await connection.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')

            # Set application name for monitoring
            await connection.execute("SET application_name = 'tractionx-dev'")

            # Set timezone
            await connection.execute("SET timezone = 'UTC'")

            # Set statement timeout
            await connection.execute(
                f"SET statement_timeout = {self.statement_timeout * 1000}"
            )

            self.logger.debug("Connection setup completed")

        except Exception as e:
            self.logger.error(f"Failed to setup connection: {e}")
            raise

    async def execute_query(
        self,
        query: str,
        params: Optional[Union[List[Any], Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Execute a query with retry logic and error handling.

        Args:
            query: SQL query to execute
            params: Query parameters (list or dict)
            timeout: Query timeout in seconds (overrides default)

        Returns:
            List of dictionaries representing query results
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        for attempt in range(self.retry_attempts):
            try:
                async with self.pool.acquire() as conn:
                    # Set custom timeout if provided
                    if timeout:
                        await conn.execute(f"SET statement_timeout = {timeout * 1000}")

                    if params:
                        if isinstance(params, dict):
                            # Convert dict params to list for asyncpg
                            param_list = list(params.values())
                            records = await conn.fetch(query, *param_list)
                        else:
                            records = await conn.fetch(query, *params)
                    else:
                        records = await conn.fetch(query)

                    self._query_count += 1

                    # Convert records to dictionaries
                    result = [dict(record) for record in records]

                    self.logger.debug(
                        "Query executed successfully",
                        query_length=len(query),
                        result_count=len(result),
                        attempt=attempt + 1,
                    )

                    return result

            except QueryCanceledError as e:
                if "statement timeout" in str(e).lower():
                    error_msg = (
                        f"Query timeout after {timeout or self.statement_timeout}s"
                    )
                    self.logger.warning(error_msg, query_length=len(query))
                    raise TimeoutError(error_msg) from e
                else:
                    raise

            except (
                UniqueViolationError,
                ForeignKeyViolationError,
                CheckViolationError,
                NotNullViolationError,
            ) as e:
                # Data integrity errors - don't retry
                self.logger.error(f"Data integrity error: {e}")
                self._error_count += 1
                raise

            except (
                ConnectionDoesNotExistError,
                InvalidPasswordError,
                InvalidAuthorizationSpecificationError,
            ) as e:
                # Authentication/connection errors - don't retry
                self.logger.error(f"Authentication/connection error: {e}")
                self._error_count += 1
                raise

            except PostgresError as e:
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2**attempt)
                    self.logger.warning(
                        f"PostgreSQL error, retrying in {wait_time}s (attempt {attempt + 1}): {e}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(
                        f"PostgreSQL error after {self.retry_attempts} attempts: {e}"
                    )
                    self._error_count += 1
                    raise

            except Exception as e:
                self.logger.error(f"Unexpected error executing query: {e}")
                self._error_count += 1
                raise

        # This should never be reached due to the raise in the loop
        raise RuntimeError("Query execution failed after all retry attempts")

    async def execute_transaction(
        self,
        queries: List[Dict[str, Any]],
        timeout: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Execute multiple queries in a single transaction.

        Args:
            queries: List of query dictionaries with 'query' and 'params' keys
            timeout: Transaction timeout in seconds

        Returns:
            List of results for each query
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        for attempt in range(self.retry_attempts):
            try:
                async with self.pool.acquire() as conn:
                    async with conn.transaction():
                        # Set custom timeout if provided
                        if timeout:
                            await conn.execute(
                                f"SET statement_timeout = {timeout * 1000}"
                            )

                        results = []
                        for query_info in queries:
                            query = query_info["query"]
                            params = query_info.get("params")

                            if params:
                                if isinstance(params, dict):
                                    param_list = list(params.values())
                                    records = await conn.fetch(query, *param_list)
                                else:
                                    records = await conn.fetch(query, *params)
                            else:
                                records = await conn.fetch(query)

                            results.append([dict(record) for record in records])

                        self._query_count += len(queries)

                        self.logger.debug(
                            "Transaction executed successfully",
                            query_count=len(queries),
                            attempt=attempt + 1,
                        )

                        return results

            except PostgresError as e:
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2**attempt)
                    self.logger.warning(
                        f"Transaction error, retrying in {wait_time}s (attempt {attempt + 1}): {e}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(
                        f"Transaction error after {self.retry_attempts} attempts: {e}"
                    )
                    self._error_count += 1
                    raise

            except Exception as e:
                self.logger.error(f"Unexpected error executing transaction: {e}")
                self._error_count += 1
                raise

        raise RuntimeError("Transaction execution failed after all retry attempts")

    async def execute_scalar(
        self,
        query: str,
        params: Optional[Union[List[Any], Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> Any:
        """
        Execute a query and return a single scalar value.

        Args:
            query: SQL query to execute
            params: Query parameters
            timeout: Query timeout in seconds

        Returns:
            Scalar value from the query
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        for attempt in range(self.retry_attempts):
            try:
                async with self.pool.acquire() as conn:
                    # Set custom timeout if provided
                    if timeout:
                        await conn.execute(f"SET statement_timeout = {timeout * 1000}")

                    if params:
                        if isinstance(params, dict):
                            param_list = list(params.values())
                            result = await conn.fetchval(query, *param_list)
                        else:
                            result = await conn.fetchval(query, *params)
                    else:
                        result = await conn.fetchval(query)

                    self._query_count += 1

                    self.logger.debug(
                        "Scalar query executed successfully",
                        query_length=len(query),
                        attempt=attempt + 1,
                    )

                    return result

            except PostgresError as e:
                if attempt < self.retry_attempts - 1:
                    wait_time = self.retry_delay * (2**attempt)
                    self.logger.warning(
                        f"Scalar query error, retrying in {wait_time}s (attempt {attempt + 1}): {e}"
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.logger.error(
                        f"Scalar query error after {self.retry_attempts} attempts: {e}"
                    )
                    self._error_count += 1
                    raise

            except Exception as e:
                self.logger.error(f"Unexpected error executing scalar query: {e}")
                self._error_count += 1
                raise

        raise RuntimeError("Scalar query execution failed after all retry attempts")

    def _serialize_value(self, value: Any) -> Any:
        """Serialize value for database storage."""
        if isinstance(value, UUID):
            return str(value)
        elif isinstance(value, dict):
            return json.dumps(value, default=self._json_serializer)
        elif isinstance(value, list):
            return value
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            # Try to parse ISO datetime strings back to datetime objects
            try:
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                return value
        else:
            return value

    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for complex objects."""
        if isinstance(obj, UUID):
            return str(obj)
        elif hasattr(obj, "model_dump"):  # Pydantic models
            return obj.model_dump()
        elif hasattr(obj, "__dict__"):
            return obj.__dict__
        else:
            raise TypeError(
                f"Object of type {type(obj).__name__} is not JSON serializable"
            )

    async def get_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        if not self.pool:
            return {"error": "Pool not initialized"}

        pool_size = self.pool.get_size()
        return {
            "size": pool_size,
            "free_size": pool_size,  # asyncpg doesn't expose free_size directly
            "active_connections": 0,  # asyncpg doesn't expose active connections directly
            "utilization_percent": 0,  # Cannot calculate without free_size
        }

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


# Factory function for creating Neon client instances
def create_neon_client(
    connection_string: Optional[str] = None,
    **kwargs: Any,
) -> NeonPostgresClient:
    """
    Factory function to create a Neon PostgreSQL client.

    Args:
        connection_string: Database connection string
        **kwargs: Additional client configuration options

    Returns:
        Configured NeonPostgresClient instance
    """
    return NeonPostgresClient(connection_string=connection_string, **kwargs)
