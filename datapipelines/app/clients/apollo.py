"""
Apollo Client for TractionX Data Pipeline Service.

This client handles all Apollo API interactions including:
- Organization enrichment
- Data fetching
- Error handling and rate limiting
"""

from typing import Any, Dict, Optional

from app.clients.base_client import BaseClient
from app.configs import settings


class ApolloClient(BaseClient):
    """
    Apollo API client for organization enrichment and data fetching.

    Handles:
    - Organization enrichment by domain
    - Rate limiting and error handling
    - Raw API response handling (no data processing)
    """

    def __init__(self):
        """Initialize Apollo client with configuration."""
        custom_headers = {
            "Content-Type": "application/json",
            "accept": "application/json",
            "Cache-Control": "no-cache",
        }

        # Apollo uses x-api-key, not Authorization Bearer
        if settings.APOLLO_API_KEY:
            custom_headers["x-api-key"] = settings.APOLLO_API_KEY

        super().__init__(
            base_url=settings.APOLLO_BASE_URL or "https://api.apollo.io",
            timeout=30.0,
            max_retries=3,
            retry_delay=2.0,
            rate_limit=60,  # Apollo rate limit
            headers=custom_headers,
        )

    async def health_check(self) -> Dict[str, Any]:
        """
        Check Apollo API health.

        Returns:
            Health check result
        """
        try:
            # Simple health check - try to get API status
            response = await self.get("/api/v1/health")
            return {
                "status": "healthy",
                "response": response,
                "timestamp": self._get_timestamp(),
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": self._get_timestamp(),
            }

    async def enrich_organization(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Enrich organization data using Apollo API.

        Args:
            domain: Company domain to enrich

        Returns:
            Raw API response dictionary or None if not found
        """
        try:
            self.logger.info(f"Enriching organization data for domain: {domain}")

            # Make API call to Apollo enrichment endpoint (GET with query params)
            response = await self.get(
                "/api/v1/organizations/enrich", params={"domain": domain}
            )
            self.logger.info("Apollo response", response=response)
            self.logger.info(
                "Apollo response received",
                domain=domain,
                has_organization=bool(response.get("organization")),
            )

            # Return raw response - let ETL processor handle data conversion
            if response.get("data", {}).get("organization"):
                self.logger.info(f"Successfully fetched organization data for {domain}")
                return response

            self.logger.info(f"No organization data found for domain: {domain}")
            return None

        except Exception as e:
            self.logger.error(f"Apollo enrichment failed for domain {domain}: {e}")
            raise

    async def search_organizations(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for organizations using Apollo API.

        Args:
            query: Search query
            **kwargs: Additional search parameters

        Returns:
            Raw search results
        """
        try:
            self.logger.info(f"Searching organizations with query: {query}")

            # Prepare search parameters
            search_params = {"q": query}
            search_params.update(kwargs)

            # Make API call to Apollo search endpoint
            response = await self.get(
                "/api/v1/organizations/search", params=search_params
            )

            self.logger.info(
                "Apollo search response received",
                query=query,
                results_count=len(response.get("organizations", [])),
            )

            return response

        except Exception as e:
            self.logger.error(f"Apollo search failed for query {query}: {e}")
            raise

    def _get_timestamp(self) -> str:
        """Get current timestamp string."""
        from datetime import datetime, timezone

        return datetime.now(timezone.utc).isoformat()


# Global Apollo client instance
_apollo_client: Optional[ApolloClient] = None


async def get_apollo_client() -> ApolloClient:
    """
    Get or create the global Apollo client instance.

    Returns:
        ApolloClient instance
    """
    global _apollo_client

    if _apollo_client is None:
        _apollo_client = ApolloClient()
        await _apollo_client.initialize()

    return _apollo_client


async def cleanup_apollo_client() -> None:
    """Clean up the global Apollo client instance."""
    global _apollo_client

    if _apollo_client:
        await _apollo_client.cleanup()
        _apollo_client = None
