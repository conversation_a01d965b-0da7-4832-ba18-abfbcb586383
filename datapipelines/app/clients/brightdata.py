"""
BrightData Client Module for TractionX Data Pipeline Service.

This module provides a unified wrapper for BrightData Web Unlocker API requests,
centralizing all BrightData interactions across the platform.
"""

import time
from typing import Any, Dict, Optional

from app.clients.base_client import BaseClient
from app.configs import get_logger, settings
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class BrightDataClient(BaseClient):
    """
    Unified BrightData client for Web Unlocker and other BrightData services.

    Handles:
    - Web Unlocker requests with JS rendering
    - Automatic retries and error handling
    - Response caching and storage
    - Rate limiting and timeout management
    """

    def __init__(self):
        """Initialize the BrightData client."""
        super().__init__(
            base_url="https://api.brightdata.com",
            api_key=settings.BRIGHTDATA_API_KEY,
            timeout=60.0,  # Increased timeout for JS rendering
            max_retries=3,
            retry_delay=2.0,
            headers={
                "Content-Type": "application/json",
            },
        )

        self.s3_storage: Optional[S3Storage] = None
        self.web_unlocker_zone = settings.BRIGHTDATA_WEB_UNLOCKER_ZONE

        self.logger.info(f"BrightData client config: zone={self.web_unlocker_zone}")

    async def initialize(self) -> None:
        """Initialize the BrightData client."""
        if not self.api_key:
            raise ValueError("BRIGHTDATA_API_KEY not configured")

        if not self.web_unlocker_zone:
            raise ValueError("BRIGHTDATA_WEB_UNLOCKER_ZONE not configured")

        # Initialize base client
        await super().initialize()

        # Initialize S3 storage
        self.s3_storage = S3Storage()
        await self.s3_storage.initialize()

        self.logger.info("BrightData client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()

        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("BrightData client cleaned up")

    async def fetch_webpage(
        self, url: str, render_js: bool = True, format_type: str = "raw"
    ) -> Dict[str, Any]:
        """
        Fetch a webpage using BrightData Web Unlocker.

        Args:
            url: Target URL to fetch
            render_js: Whether to render JavaScript
            format_type: Response format ('raw', 'html', 'json')

        Returns:
            Dictionary with success status and response data
        """
        try:
            self.logger.info(
                "Fetching webpage with BrightData Web Unlocker",
                url=url,
                render_js=render_js,
                format_type=format_type,
            )

            # Prepare request data
            data = {
                "zone": self.web_unlocker_zone,
                "url": url,
                "render": render_js,
                "format": format_type,
            }

            # First attempt
            result = await self.post("/request", data=data)

            # Check if response is empty and retry once
            if result["success"] and (
                not result.get("data") or str(result["data"]).strip() == ""
            ):
                self.logger.warning(
                    f"Empty response received for {url}, retrying once..."
                )

                # Wait a moment before retry
                import asyncio

                await asyncio.sleep(2)

                # Retry attempt
                result = await self.post("/request", data=data)

                if result["success"] and (
                    not result.get("data") or str(result["data"]).strip() == ""
                ):
                    self.logger.warning(f"Still empty response after retry for {url}")
                else:
                    self.logger.info(f"Retry successful for {url}")

            if result["success"]:
                self.logger.info(
                    "Webpage fetched successfully",
                    url=url,
                    status_code=result.get("status_code"),
                    response_size=len(str(result["data"])),
                )

            return result

        except Exception as e:
            error_msg = f"Failed to fetch webpage: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "url": url,
            }

    async def store_webpage_response(
        self, url: str, response_data: Any, org_id: str = "unknown"
    ) -> str:
        """
        Store webpage response to S3 for audit and debugging.

        Args:
            url: Original URL that was fetched
            response_data: Response data from BrightData
            org_id: Organization identifier

        Returns:
            S3 key where data was stored
        """
        try:
            if not self.s3_storage:
                raise RuntimeError("S3 storage not initialized")

            # Generate S3 key
            from urllib.parse import urlparse

            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            path = parsed_url.path.strip("/")

            if not path:
                slug = "homepage"
            else:
                # Replace any remaining slashes with underscores for S3 compatibility
                slug = path.replace("/", "_")

            s3_key = f"brightdata_webpages/{domain}/{slug}.json"

            # Prepare data for storage
            storage_data = {
                "url": url,
                "org_id": org_id,
                "timestamp": time.time(),
                "response_data": response_data,
            }

            success = await self.s3_storage.put_object(s3_key, storage_data)
            if success:
                self.logger.info(f"Webpage response stored to S3: {s3_key}")
            else:
                self.logger.error(f"Failed to store webpage response to S3: {s3_key}")

            return s3_key

        except Exception as e:
            self.logger.error(f"Failed to store webpage response: {str(e)}")
            return ""

    async def fetch_and_store_webpage(
        self, url: str, org_id: str = "unknown", render_js: bool = True
    ) -> Dict[str, Any]:
        """
        Fetch webpage and store response to S3.

        Args:
            url: Target URL to fetch
            org_id: Organization identifier
            render_js: Whether to render JavaScript

        Returns:
            Dictionary with success status, response data, and S3 key
        """
        # Fetch webpage
        fetch_result = await self.fetch_webpage(url, render_js)

        if not fetch_result["success"]:
            return fetch_result

        # Store response to S3
        s3_key = await self.store_webpage_response(url, fetch_result["data"], org_id)

        return {
            **fetch_result,
            "s3_key": s3_key,
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the BrightData service.

        Returns:
            Dictionary with health status
        """
        try:
            # Try a simple request to check if the service is accessible
            result = await self.get("/")

            return {
                "success": result["success"],
                "service": "brightdata",
                "status": "healthy" if result["success"] else "unhealthy",
                "error": result.get("error") if not result["success"] else None,
            }
        except Exception as e:
            return {
                "success": False,
                "service": "brightdata",
                "status": "unhealthy",
                "error": str(e),
            }


# Global client instance
_brightdata_client: Optional[BrightDataClient] = None


async def get_brightdata_client() -> BrightDataClient:
    """Get the global BrightData client instance."""
    global _brightdata_client

    if _brightdata_client is None:
        _brightdata_client = BrightDataClient()
        await _brightdata_client.initialize()

    return _brightdata_client


async def cleanup_brightdata_client():
    """Clean up the global BrightData client instance."""
    global _brightdata_client

    if _brightdata_client:
        await _brightdata_client.cleanup()
        _brightdata_client = None
