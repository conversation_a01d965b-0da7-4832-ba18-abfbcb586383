"""
Company data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import Field, HttpUrl, field_validator

from app.models.base import EnrichmentSource, TractionXModel


class CompanyData(TractionXModel):
    """Core company data structure."""

    # Core identifiers
    company_id: str = Field(..., description="Unique company identifier")
    name: str = Field(..., description="Company name")

    # Basic information
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[HttpUrl] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    industry: Optional[str] = Field(None, description="Industry/sector")
    stage: Optional[str] = Field(
        None, description="Company stage (seed, series A, etc.)"
    )
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Number of employees")

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")

    # Financial
    funding_total: Optional[float] = Field(None, description="Total funding raised")
    valuation: Optional[float] = Field(None, description="Company valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Social/Contact
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[HttpUrl] = Field(None, description="Twitter URL")
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Contact phone")

    # Metadata
    source: EnrichmentSource = Field(..., description="Data source")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence"
    )

    # Additional data
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional company data"
    )


class CompanyDataSource(TractionXModel):
    """
    Company data source record for storing data from different sources.
    This replaces the legacy barrier_sync architecture with a clean, normalized approach.
    """

    # Primary key - using UUID for better scalability
    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Core identifiers
    company_id: str = Field(..., description="Company identifier")
    org_id: str = Field(..., description="Organization ID")
    source: str = Field(
        ..., description="Data source (linkedin, crunchbase, apollo, etc.)"
    )

    # Metadata
    source_url: Optional[str] = Field(None, description="Source URL")
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence score"
    )
    enrichment_date: Optional[datetime] = Field(
        None, description="When data was enriched"
    )

    # Raw data backup
    s3_raw_data_key: Optional[str] = Field(
        None, description="S3 key for raw data backup"
    )

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class LinkedInCompanyData(TractionXModel):
    """LinkedIn company enrichment data model."""

    # LinkedIn-specific fields
    linkedin_url: Optional[str] = Field(None, description="LinkedIn company URL")
    linkedin_id: Optional[str] = Field(None, description="LinkedIn company ID")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )

    # Basic company info
    name: Optional[str] = Field(None, description="Company name")
    description: Optional[str] = Field(None, description="Company description")
    about: Optional[str] = Field(None, description="About section")
    overview: Optional[str] = Field(None, description="Company overview")
    website: Optional[str] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    industry: Optional[str] = Field(None, description="Industry")
    industries: Optional[str] = Field(None, description="Industries (comma-separated)")
    company_type: Optional[str] = Field(None, description="Company type")
    organization_type: Optional[str] = Field(None, description="Organization type")
    company_size: Optional[str] = Field(None, description="Company size")
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Employee count")
    employees_in_linkedin: Optional[int] = Field(
        None, description="Employees on LinkedIn"
    )
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    country_code: Optional[str] = Field(None, description="Country code")
    country_codes_array: List[str] = Field(
        default_factory=list, description="Country codes array"
    )
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")
    locations: List[str] = Field(default_factory=list, description="Company locations")
    formatted_locations: List[str] = Field(
        default_factory=list, description="Formatted locations"
    )

    # Social metrics
    followers_count: Optional[int] = Field(None, description="Number of followers")
    connections_count: Optional[int] = Field(None, description="Number of connections")

    # Company details
    specialties: List[str] = Field(
        default_factory=list, description="Company specialties"
    )
    technologies: List[str] = Field(
        default_factory=list, description="Technologies used"
    )
    keywords: List[str] = Field(default_factory=list, description="Keywords")
    competitors: List[str] = Field(default_factory=list, description="Competitors")

    # Contact information
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")

    # Employee and executive data
    employees: List[str] = Field(
        default_factory=list, description="Employee data as JSON strings"
    )
    executives: List[str] = Field(
        default_factory=list, description="Executive data as JSON strings"
    )

    # Additional LinkedIn data
    image: Optional[str] = Field(None, description="Company image URL")
    logo: Optional[str] = Field(None, description="Company logo URL")
    slogan: Optional[str] = Field(None, description="Company slogan")
    similar: List[str] = Field(
        default_factory=list, description="Similar companies as JSON strings"
    )
    updates: List[str] = Field(
        default_factory=list, description="Company updates as JSON strings"
    )
    crunchbase_url: Optional[str] = Field(None, description="Crunchbase URL")
    funding: Optional[Dict[str, Any]] = Field(None, description="Funding information")
    investors: List[str] = Field(
        default_factory=list, description="Investors as JSON strings"
    )
    stock_info: Optional[Dict[str, Any]] = Field(None, description="Stock information")
    affiliated: List[str] = Field(
        default_factory=list, description="Affiliated companies as JSON strings"
    )

    # Additional LinkedIn data
    linkedin_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="LinkedIn-specific metadata"
    )
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When data was enriched",
    )

    def to_company_data(self, company_id: str) -> CompanyData:
        """Convert to core company data structure."""
        # Convert URLs to HttpUrl format if they exist
        website_url = None
        if self.website:
            try:
                website_url = HttpUrl(self.website)
            except Exception:
                website_url = None

        linkedin_url_obj = None
        if self.linkedin_url:
            try:
                linkedin_url_obj = HttpUrl(self.linkedin_url)
            except Exception:
                linkedin_url_obj = None

        return CompanyData(
            company_id=company_id,
            name=self.name or "",
            description=self.description or self.about or self.overview,
            website=website_url,
            domain=self.domain,
            industry=self.industry or self.industries,
            founded_year=self.founded_year,
            employee_count=self.employee_count or self.employees_in_linkedin,
            headquarters=self.headquarters,
            country=self.country,
            city=self.city,
            linkedin_url=linkedin_url_obj,
            email=self.email,
            phone=self.phone,
            source=EnrichmentSource.LINKEDIN,
            funding_total=None,
            valuation=None,
            revenue=None,
            twitter_url=None,
            additional_data={
                "linkedin_id": self.linkedin_id,
                "brightdata_snapshot_id": self.brightdata_snapshot_id,
                "company_type": self.company_type or self.organization_type,
                "company_size": self.company_size,
                "employee_count_range": self.employee_count_range,
                "state": self.state,
                "followers_count": self.followers_count,
                "connections_count": self.connections_count,
                "specialties": self.specialties,
                "technologies": self.technologies,
                "keywords": self.keywords,
                "competitors": self.competitors,
                "linkedin_metadata": self.linkedin_metadata,
                "enrichment_date": self.enrichment_date.isoformat(),
                "country_code": self.country_code,
                "country_codes_array": self.country_codes_array,
                "locations": self.locations,
                "formatted_locations": self.formatted_locations,
                "employees_in_linkedin": self.employees_in_linkedin,
                "image": self.image,
                "logo": self.logo,
                "slogan": self.slogan,
            },
            stage=None,
            confidence_score=None,
        )


class CrunchbaseCompanyData(TractionXModel):
    """Crunchbase company enrichment data model."""

    # Crunchbase-specific fields
    crunchbase_url: Optional[str] = Field(None, description="Crunchbase company URL")
    crunchbase_id: Optional[str] = Field(None, description="Crunchbase company ID")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )

    # Basic company info
    name: Optional[str] = Field(None, description="Company name")
    legal_name: Optional[str] = Field(None, description="Legal company name")
    description: Optional[str] = Field(None, description="Company description")
    about: Optional[str] = Field(None, description="About section")
    full_description: Optional[str] = Field(None, description="Full description")
    website: Optional[str] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    company_type: Optional[str] = Field(None, description="Company type")
    operating_status: Optional[str] = Field(None, description="Operating status")
    ipo_status: Optional[str] = Field(None, description="IPO status")
    founded_date: Optional[str] = Field(None, description="Founded date")
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Employee count")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    country_code: Optional[str] = Field(None, description="Country code")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")
    region: Optional[str] = Field(None, description="Region")
    location: List[str] = Field(
        default_factory=list, description="Location data as TEXT[]"
    )
    address: Optional[str] = Field(None, description="Address")

    # Industries and categories
    industries: List[str] = Field(default_factory=list, description="Industries")
    industry: Optional[str] = Field(None, description="Primary industry")
    sub_industry: Optional[str] = Field(None, description="Sub-industry")

    # Financial information
    funding_total: Optional[float] = Field(None, description="Total funding")
    funding_rounds: Dict[str, Any] = Field(
        default_factory=dict, description="Funding rounds"
    )
    funding_rounds_list: List[Dict[str, Any]] = Field(
        default_factory=list, description="Funding rounds list"
    )
    last_funding_date: Optional[str] = Field(None, description="Last funding date")
    last_funding_amount: Optional[float] = Field(
        None, description="Last funding amount"
    )
    valuation: Optional[float] = Field(None, description="Company valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Social presence
    social_media_links: List[str] = Field(
        default_factory=list, description="Social media links"
    )
    linkedin_url: Optional[str] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[str] = Field(None, description="Twitter URL")
    facebook_url: Optional[str] = Field(None, description="Facebook URL")

    # Contact information
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")
    contact_email: Optional[str] = Field(None, description="Contact email")
    contact_phone: Optional[str] = Field(None, description="Contact phone")

    # Team and people
    founders: List[str] = Field(default_factory=list, description="Company founders")
    executives: List[str] = Field(
        default_factory=list, description="Company executives"
    )
    current_employees: List[str] = Field(
        default_factory=list, description="Current employees"
    )
    alumni: List[str] = Field(default_factory=list, description="Alumni")

    # Company details
    technologies: List[str] = Field(
        default_factory=list, description="Technologies used"
    )
    keywords: List[str] = Field(default_factory=list, description="Keywords")
    competitors: List[str] = Field(default_factory=list, description="Competitors")
    similar_companies: List[str] = Field(
        default_factory=list, description="Similar companies"
    )

    # Additional Crunchbase data
    image: Optional[str] = Field(None, description="Company image URL")
    cb_rank: Optional[int] = Field(None, description="Crunchbase rank")
    monthly_visits: Optional[int] = Field(None, description="Monthly visits")
    semrush_visits_latest_month: Optional[int] = Field(
        None, description="SEMrush visits latest month"
    )
    monthly_visits_growth: Optional[float] = Field(
        None, description="Monthly visits growth"
    )
    num_contacts: Optional[int] = Field(None, description="Number of contacts")
    num_contacts_linkedin: Optional[int] = Field(
        None, description="Number of LinkedIn contacts"
    )
    num_employee_profiles: Optional[int] = Field(
        None, description="Number of employee profiles"
    )
    num_news: Optional[int] = Field(None, description="Number of news articles")
    num_investors: Optional[int] = Field(None, description="Number of investors")
    num_event_appearances: Optional[int] = Field(
        None, description="Number of event appearances"
    )
    num_acquisitions: Optional[int] = Field(None, description="Number of acquisitions")
    num_investments: Optional[int] = Field(None, description="Number of investments")
    num_exits: Optional[int] = Field(None, description="Number of exits")

    # Featured lists and highlights
    featured_list: List[str] = Field(default_factory=list, description="Featured lists")
    overview_highlights: Dict[str, Any] = Field(
        default_factory=dict, description="Overview highlights"
    )
    people_highlights: Dict[str, Any] = Field(
        default_factory=dict, description="People highlights"
    )
    technology_highlights: Dict[str, Any] = Field(
        default_factory=dict, description="Technology highlights"
    )

    # Additional data
    bombora: List[str] = Field(default_factory=list, description="Bombora data")
    investors: List[str] = Field(default_factory=list, description="Investors")
    event_appearances: List[str] = Field(
        default_factory=list, description="Event appearances"
    )
    acquisitions: List[str] = Field(default_factory=list, description="Acquisitions")
    funds_raised: List[str] = Field(default_factory=list, description="Funds raised")
    investments: List[str] = Field(default_factory=list, description="Investments")
    exits: List[str] = Field(default_factory=list, description="Exits")
    news: Optional[Dict[str, Any]] = Field(None, description="News data")

    # Additional Crunchbase data
    crunchbase_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Crunchbase-specific metadata"
    )
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When data was enriched",
    )

    def to_company_data(self, company_id: str) -> CompanyData:
        """Convert to core company data structure."""
        # Convert URLs to HttpUrl format if they exist
        website_url = None
        if self.website:
            try:
                website_url = HttpUrl(self.website)
            except Exception:
                website_url = None

        linkedin_url_obj = None
        if self.linkedin_url:
            try:
                linkedin_url_obj = HttpUrl(self.linkedin_url)
            except Exception:
                linkedin_url_obj = None

        twitter_url_obj = None
        if self.twitter_url:
            try:
                twitter_url_obj = HttpUrl(self.twitter_url)
            except Exception:
                twitter_url_obj = None

        # Extract founded year from founded_date if available
        founded_year = self.founded_year
        if not founded_year and self.founded_date:
            try:
                founded_year = int(self.founded_date.split("-")[0])
            except (ValueError, AttributeError):
                founded_year = None

        # Extract primary industry from industries list
        primary_industry = self.industry
        if not primary_industry and self.industries:
            try:
                primary_industry = self.industries[0]
            except (IndexError, AttributeError):
                primary_industry = None

        return CompanyData(
            company_id=company_id,
            name=self.name or self.legal_name or "",
            description=self.description or self.about or self.full_description,
            website=website_url,
            domain=self.domain,
            industry=primary_industry,
            founded_year=founded_year,
            employee_count=self.employee_count,
            headquarters=self.headquarters,
            country=self.country,
            city=self.city,
            funding_total=self.funding_total,
            valuation=self.valuation,
            revenue=self.revenue,
            linkedin_url=linkedin_url_obj,
            twitter_url=twitter_url_obj,
            email=self.email or self.contact_email,
            phone=self.phone or self.contact_phone,
            source=EnrichmentSource.CRUNCHBASE,
            additional_data={
                "crunchbase_id": self.crunchbase_id,
                "crunchbase_url": self.crunchbase_url,
                "brightdata_snapshot_id": self.brightdata_snapshot_id,
                "company_type": self.company_type,
                "operating_status": self.operating_status,
                "ipo_status": self.ipo_status,
                "founded_date": self.founded_date,
                "employee_count_range": self.employee_count_range,
                "state": self.state,
                "region": self.region,
                "country_code": self.country_code,
                "address": self.address,
                "industries": self.industries,
                "sub_industry": self.sub_industry,
                "funding_rounds": self.funding_rounds,
                "funding_rounds_list": self.funding_rounds_list,
                "last_funding_date": self.last_funding_date,
                "last_funding_amount": self.last_funding_amount,
                "social_media_links": self.social_media_links,
                "facebook_url": self.facebook_url,
                "founders": self.founders,
                "executives": self.executives,
                "current_employees": self.current_employees,
                "alumni": self.alumni,
                "technologies": self.technologies,
                "keywords": self.keywords,
                "competitors": self.competitors,
                "similar_companies": self.similar_companies,
                "image": self.image,
                "cb_rank": self.cb_rank,
                "monthly_visits": self.monthly_visits,
                "semrush_visits_latest_month": self.semrush_visits_latest_month,
                "monthly_visits_growth": self.monthly_visits_growth,
                "num_contacts": self.num_contacts,
                "num_contacts_linkedin": self.num_contacts_linkedin,
                "num_employee_profiles": self.num_employee_profiles,
                "num_news": self.num_news,
                "num_investors": self.num_investors,
                "num_event_appearances": self.num_event_appearances,
                "num_acquisitions": self.num_acquisitions,
                "num_investments": self.num_investments,
                "num_exits": self.num_exits,
                "featured_list": self.featured_list,
                "overview_highlights": self.overview_highlights,
                "people_highlights": self.people_highlights,
                "technology_highlights": self.technology_highlights,
                "bombora": self.bombora,
                "investors": self.investors,
                "event_appearances": self.event_appearances,
                "acquisitions": self.acquisitions,
                "funds_raised": self.funds_raised,
                "investments": self.investments,
                "exits": self.exits,
                "news": self.news,
                "crunchbase_metadata": self.crunchbase_metadata,
                "enrichment_date": self.enrichment_date.isoformat(),
            },
            stage=None,
            confidence_score=None,
        )


class ApolloCompanyData(TractionXModel):
    """Company data from Apollo enrichment service."""

    # Apollo-specific fields
    apollo_id: Optional[str] = Field(None, description="Apollo company ID")
    company_id: Optional[str] = Field(None, description="Company ID")
    org_id: Optional[str] = Field(None, description="Organization ID")
    # Basic info
    name: Optional[str] = Field(None, description="Company name")
    domain: Optional[str] = Field(None, description="Company domain")
    website: Optional[str] = Field(None, description="Company website")
    description: Optional[str] = Field(None, description="Company description")

    # Business details
    industry: Optional[str] = Field(None, description="Industry")
    sub_industry: Optional[str] = Field(None, description="Sub-industry")
    employee_count: Optional[int] = Field(None, description="Employee count")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )
    founded_year: Optional[int] = Field(None, description="Year founded")

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")

    # Financial
    funding_total: Optional[float] = Field(None, description="Total funding")
    funding_rounds: Optional[int] = Field(None, description="Number of funding rounds")
    last_funding_date: Optional[datetime] = Field(None, description="Last funding date")
    last_funding_amount: Optional[float] = Field(
        None, description="Last funding amount"
    )
    valuation: Optional[float] = Field(None, description="Valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Note: Technologies, tech_stack, keywords, and departmental_head_count
    # are stored in separate tables and extracted separately by the transformer
    technologies: Optional[List[str]] = Field(None, description="Technologies")
    tech_stack: Optional[Dict[str, Any]] = Field(None, description="Tech stack")
    keywords: Optional[List[str]] = Field(None, description="Keywords")
    departmental_head_count: Optional[Dict[str, Any]] = Field(
        None, description="Departmental head count"
    )

    # Social presence
    linkedin_url: Optional[str] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[str] = Field(None, description="Twitter URL")
    facebook_url: Optional[str] = Field(None, description="Facebook URL")

    # Contact info
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")

    # Additional Apollo data
    apollo_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Apollo-specific metadata"
    )
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When data was enriched",
    )

    def to_company_data(self, company_id: str) -> CompanyData:
        """Convert to core company data structure."""
        # Convert URLs to HttpUrl format if they exist
        website_url = None
        if self.website:
            try:
                website_url = HttpUrl(self.website)
            except Exception:
                website_url = None

        linkedin_url_obj = None
        if self.linkedin_url:
            try:
                linkedin_url_obj = HttpUrl(self.linkedin_url)
            except Exception:
                linkedin_url_obj = None

        twitter_url_obj = None
        if self.twitter_url:
            try:
                twitter_url_obj = HttpUrl(self.twitter_url)
            except Exception:
                twitter_url_obj = None

        return CompanyData(
            company_id=company_id,
            name=self.name or "",
            description=self.description,
            website=website_url,
            domain=self.domain,
            industry=self.industry,
            founded_year=self.founded_year,
            employee_count=self.employee_count,
            headquarters=self.headquarters,
            country=self.country,
            city=self.city,
            funding_total=self.funding_total,
            valuation=self.valuation,
            revenue=self.revenue,
            linkedin_url=linkedin_url_obj,
            twitter_url=twitter_url_obj,
            email=self.email,
            stage=None,
            confidence_score=None,
            phone=self.phone,
            source=EnrichmentSource.APOLLO,
            additional_data={
                "apollo_id": self.apollo_id,
                "sub_industry": self.sub_industry,
                "employee_count_range": self.employee_count_range,
                "state": self.state,
                "funding_rounds": self.funding_rounds,
                "last_funding_date": self.last_funding_date.isoformat()
                if self.last_funding_date
                else None,
                "last_funding_amount": self.last_funding_amount,
                "facebook_url": self.facebook_url,
                "apollo_metadata": self.apollo_metadata,
            },
        )


class PitchBookCompanyData(TractionXModel):
    """PitchBook company enrichment data model."""

    # PitchBook-specific fields
    pitchbook_url: Optional[str] = Field(None, description="PitchBook company URL")
    pitchbook_id: Optional[str] = Field(None, description="PitchBook company ID")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )
    company_id: Optional[str] = Field(None, description="Company ID")
    org_id: Optional[str] = Field(None, description="Organization ID")
    # Basic company info
    name: Optional[str] = Field(None, description="Company name")
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[str] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    company_type: Optional[str] = Field(None, description="Company type")
    status: Optional[str] = Field(None, description="Company status")
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Employee count")

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")

    # Financial information
    funding_total: Optional[float] = Field(None, description="Total funding")
    funding_rounds: Optional[int] = Field(None, description="Number of funding rounds")
    last_funding_date: Optional[str] = Field(None, description="Last funding date")
    last_funding_amount: Optional[float] = Field(
        None, description="Last funding amount"
    )
    latest_deal_amount: Optional[float] = Field(None, description="Latest deal amount")
    latest_deal_type: Optional[str] = Field(None, description="Latest deal type")
    valuation: Optional[float] = Field(None, description="Company valuation")
    revenue: Optional[float] = Field(None, description="Annual revenue")

    # Investment information
    investments_count: Optional[int] = Field(None, description="Number of investments")
    investments: List[Dict[str, Any]] = Field(
        default_factory=list, description="Investment data"
    )
    investment_relationships: List[Dict[str, Any]] = Field(
        default_factory=list, description="Investment relationships"
    )

    # Competitor information
    competitors: List[str] = Field(default_factory=list, description="Competitors")

    # Contact information
    contact_information: List[Dict[str, Any]] = Field(
        default_factory=list, description="Contact information"
    )
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")

    # Social presence
    linkedin_url: Optional[str] = Field(None, description="LinkedIn URL")
    twitter_url: Optional[str] = Field(None, description="Twitter URL")
    facebook_url: Optional[str] = Field(None, description="Facebook URL")

    # Company details
    technologies: List[str] = Field(
        default_factory=list, description="Technologies used"
    )
    keywords: List[str] = Field(default_factory=list, description="Keywords")
    patents: Optional[List[Dict[str, Any]]] = Field(
        None, description="Patent information"
    )
    patent_activity: Optional[Dict[str, Any]] = Field(
        None, description="Patent activity"
    )

    # Research and analysis
    research_analysis: Optional[Dict[str, Any]] = Field(
        None, description="Research analysis"
    )
    faq: List[Dict[str, Any]] = Field(default_factory=list, description="FAQ data")

    # Additional PitchBook data
    pitchbook_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="PitchBook-specific metadata"
    )
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When data was enriched",
    )

    def to_company_data(self, company_id: str) -> CompanyData:
        """Convert to core company data structure."""
        # Convert URLs to HttpUrl format if they exist
        website_url = None
        if self.website:
            try:
                website_url = HttpUrl(self.website)
            except Exception:
                website_url = None

        linkedin_url_obj = None
        if self.linkedin_url:
            try:
                linkedin_url_obj = HttpUrl(self.linkedin_url)
            except Exception:
                linkedin_url_obj = None

        twitter_url_obj = None
        if self.twitter_url:
            try:
                twitter_url_obj = HttpUrl(self.twitter_url)
            except Exception:
                twitter_url_obj = None

        return CompanyData(
            company_id=company_id,
            name=self.name or "",
            description=self.description,
            website=website_url,
            domain=self.domain,
            industry=None,  # Extract from contact_information if available
            stage=self._extract_stage_from_deal_type(),
            founded_year=self.founded_year,
            employee_count=self.employee_count,
            headquarters=self.headquarters,
            country=self.country,
            city=self.city,
            funding_total=self.funding_total,
            valuation=self.valuation,
            revenue=self.revenue,
            linkedin_url=linkedin_url_obj,
            twitter_url=twitter_url_obj,
            email=self.email,
            phone=self.phone,
            source=EnrichmentSource.PITCHBOOK,
            confidence_score=0.8,
            additional_data={
                "pitchbook_id": self.pitchbook_id,
                "pitchbook_url": self.pitchbook_url,
                "brightdata_snapshot_id": self.brightdata_snapshot_id,
                "company_type": self.company_type,
                "status": self.status,
                "state": self.state,
                "funding_rounds": self.funding_rounds,
                "last_funding_date": self.last_funding_date,
                "last_funding_amount": self.last_funding_amount,
                "latest_deal_amount": self.latest_deal_amount,
                "latest_deal_type": self.latest_deal_type,
                "investments_count": self.investments_count,
                "investments": self.investments,
                "investment_relationships": self.investment_relationships,
                "competitors": self.competitors,
                "contact_information": self.contact_information,
                "facebook_url": self.facebook_url,
                "technologies": self.technologies,
                "keywords": self.keywords,
                "patents": self.patents,
                "patent_activity": self.patent_activity,
                "research_analysis": self.research_analysis,
                "faq": self.faq,
                "pitchbook_metadata": self.pitchbook_metadata,
            },
        )

    def _extract_stage_from_deal_type(self) -> Optional[str]:
        """Extract company stage from latest deal type."""
        if not self.latest_deal_type:
            return None

        deal_type = self.latest_deal_type.lower()
        if "seed" in deal_type:
            return "seed"
        elif "series a" in deal_type:
            return "series_a"
        elif "series b" in deal_type:
            return "series_b"
        elif "series c" in deal_type:
            return "series_c"
        elif "ipo" in deal_type:
            return "ipo"
        elif "acquisition" in deal_type:
            return "acquisition"
        elif "accelerator" in deal_type or "incubator" in deal_type:
            return "accelerator_incubator"
        else:
            return None


class CompanyEnrichmentData(TractionXModel):
    """Enriched company data from multiple sources."""

    # Core company info
    company_id: str = Field(..., description="Unique company identifier")
    org_id: str = Field(..., description="Organization ID")

    # Form submission data (highest priority)
    form_data: Optional[CompanyData] = Field(
        None, description="Data from form submission"
    )

    # Enrichment data sources
    linkedin_data: Optional[LinkedInCompanyData] = Field(
        None, description="LinkedIn enrichment data"
    )
    apollo_data: Optional[ApolloCompanyData] = Field(
        None, description="Apollo enrichment data"
    )

    # Processing metadata
    enrichment_status: Dict[str, str] = Field(
        default_factory=dict, description="Status of each enrichment"
    )
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Quality metrics
    completeness_score: Optional[float] = Field(
        None, description="Data completeness score"
    )
    confidence_score: Optional[float] = Field(
        None, description="Overall confidence score"
    )

    def get_canonical_data(self) -> Optional[CompanyData]:
        """Get canonical company data using merge rules (form > linkedin > apollo)."""
        # Start with form data if available
        if self.form_data:
            canonical = self.form_data.model_copy()
        else:
            canonical = None

        # Merge LinkedIn data for missing fields
        if self.linkedin_data:
            linkedin_company = self.linkedin_data.to_company_data(self.company_id)

            if canonical is None:
                canonical = linkedin_company
            else:
                # Fill missing fields with LinkedIn data
                for field_name, field_value in linkedin_company.model_dump().items():
                    if field_name in ["company_id", "source", "last_updated"]:
                        continue

                    current_value = getattr(canonical, field_name, None)
                    if current_value is None and field_value is not None:
                        setattr(canonical, field_name, field_value)

        # Merge Apollo data for missing fields
        if self.apollo_data:
            apollo_company = self.apollo_data.to_company_data(self.company_id)

            if canonical is None:
                canonical = apollo_company
            else:
                # Fill missing fields with Apollo data
                for field_name, field_value in apollo_company.model_dump().items():
                    if field_name in ["company_id", "source", "last_updated"]:
                        continue

                    current_value = getattr(canonical, field_name, None)
                    if current_value is None and field_value is not None:
                        setattr(canonical, field_name, field_value)

        return canonical


class CompanyKeyword(TractionXModel):
    """Company keyword model for normalized storage."""

    id: str = Field(..., description="Unique keyword record ID")
    company_id: str = Field(..., description="Company identifier")
    keyword: str = Field(..., description="Keyword text")


class CompanyTechnology(TractionXModel):
    """Company technology model for normalized storage."""

    id: str = Field(..., description="Unique technology record ID")
    company_id: str = Field(..., description="Company identifier")
    technology: str = Field(..., description="Technology name")
    category: Optional[str] = Field(None, description="Technology category")


class CompanyDepartmentCount(TractionXModel):
    """Company department count model for normalized storage."""

    id: str = Field(..., description="Unique department record ID")
    company_id: str = Field(..., description="Company identifier")
    department: str = Field(..., description="Department name")
    head_count: int = Field(..., description="Number of employees in department")


class CompanyResolverData(TractionXModel):
    """
    Model for storing resolved URL data for all companies and tasks.

    This table stores resolver outputs for LinkedIn, Crunchbase, Pitchbook, etc.
    """

    # Core identifiers
    company_id: str = Field(..., description="Company identifier")
    org_id: str = Field(..., description="Organization identifier")
    company_domain: str = Field(..., description="Company domain")
    resolver_type: str = Field(
        ..., description="Type of resolver (linkedin, crunchbase, pitchbook, etc.)"
    )

    # Resolution data
    resolved_url: Optional[str] = Field(
        None, description="Resolved URL (if successful)"
    )
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Confidence score of the resolution"
    )
    status: str = Field(
        default="pending",
        description="Resolution status (pending, resolved, failed, no_match)",
    )

    s3_raw_data_key: Optional[str] = Field(
        None, description="S3 key for raw data backup"
    )

    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_stage: Optional[str] = Field(None, description="Stage where error occurred")

    # Timestamps
    resolved_at: Optional[datetime] = Field(
        None, description="When resolution completed"
    )
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
