"""
Founder data models for TractionX Data Pipeline Service.
"""

from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import Field, HttpUrl, field_validator

from app.models.base import EnrichmentSource, TractionXModel

"""
Founder signal generation models for LLM-based scoring and analysis.
"""


class FounderData(TractionXModel):
    """Core founder data structure."""

    # Core identifiers
    founder_id: str = Field(..., description="Unique founder identifier")
    company_id: str = Field(..., description="Associated company ID")

    # Personal information
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    full_name: Optional[str] = Field(None, description="Full name")
    email: Optional[str] = Field(None, description="Email address")

    # Professional information
    title: Optional[str] = Field(None, description="Current title/position")
    bio: Optional[str] = Field(None, description="Professional biography")

    # Location
    location: Optional[str] = Field(None, description="Current location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")

    # Social profiles
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn profile URL")
    twitter_url: Optional[HttpUrl] = Field(None, description="Twitter profile URL")
    github_url: Optional[HttpUrl] = Field(None, description="GitHub profile URL")

    # Professional background
    previous_companies: List[str] = Field(
        default_factory=list, description="Previous companies"
    )
    education: List[Dict[str, Any]] = Field(
        default_factory=list, description="Education background"
    )
    skills: List[str] = Field(default_factory=list, description="Professional skills")

    # Metadata
    source: EnrichmentSource = Field(..., description="Data source")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence"
    )

    # Additional data
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional founder data"
    )


class PDLFounderData(TractionXModel):
    """PDL founder enrichment data model."""

    # Core identification
    pdl_id: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    middle_name: Optional[str] = None
    middle_initial: Optional[str] = None
    last_initial: Optional[str] = None
    sex: Optional[str] = None
    birth_year: Optional[bool] = None
    birth_date: Optional[bool] = None

    # Contact information (PDL enrich endpoint only returns availability flags)
    emails: List[str] = Field(
        default_factory=list
    )  # "work_email_available", "personal_email_available", etc.
    phone_numbers: List[str] = Field(
        default_factory=list
    )  # "phone_numbers_available", "mobile_phone_available", etc.
    work_email: Optional[bool] = None
    personal_emails: Optional[bool] = None
    recommended_personal_email: Optional[bool] = None
    mobile_phone: Optional[bool] = None

    # Location
    location_name: Optional[str] = None
    location_country: Optional[str] = None
    location_region: Optional[str] = None
    location_locality: Optional[str] = None
    location_metro: Optional[str] = None
    location_geo: Optional[str] = None
    location_street_address: Optional[bool] = None
    location_address_line_2: Optional[str] = None
    location_postal_code: Optional[bool] = None
    location_continent: Optional[str] = None
    location_last_updated: Optional[str] = None

    # Current job
    job_title: Optional[str] = None
    job_title_role: Optional[str] = None
    job_title_sub_role: Optional[str] = None
    job_title_class: Optional[str] = None
    job_title_levels: List[str] = Field(default_factory=list)
    job_company_id: Optional[str] = None
    job_company_name: Optional[str] = None
    job_company_website: Optional[str] = None
    job_company_size: Optional[str] = None
    job_company_founded: Optional[int] = None
    job_company_industry: Optional[str] = None
    job_company_linkedin_url: Optional[str] = None
    job_company_linkedin_id: Optional[str] = None
    job_company_facebook_url: Optional[str] = None
    job_company_twitter_url: Optional[str] = None
    job_company_location_name: Optional[str] = None
    job_company_location_locality: Optional[str] = None
    job_company_location_metro: Optional[str] = None
    job_company_location_region: Optional[str] = None
    job_company_location_geo: Optional[str] = None
    job_company_location_street_address: Optional[str] = None
    job_company_location_address_line_2: Optional[str] = None
    job_company_location_postal_code: Optional[str] = None
    job_company_location_country: Optional[str] = None
    job_company_location_continent: Optional[str] = None
    job_last_changed: Optional[str] = None
    job_last_verified: Optional[str] = None
    job_start_date: Optional[datetime] = None

    # Professional details
    industry: Optional[str] = None
    experience: List[Dict[str, Any]] = Field(default_factory=list)
    education: List[Dict[str, Any]] = Field(default_factory=list)

    # Social profiles
    linkedin_url: Optional[str] = None
    linkedin_username: Optional[str] = None
    linkedin_id: Optional[str] = None
    twitter_url: Optional[str] = None
    twitter_username: Optional[str] = None
    github_url: Optional[str] = None
    github_username: Optional[str] = None
    facebook_url: Optional[str] = None
    facebook_username: Optional[str] = None
    facebook_id: Optional[str] = None

    # Skills and interests
    skills: List[str] = Field(default_factory=list)
    interests: List[str] = Field(default_factory=list)

    # Additional location data
    location_names: Optional[bool] = None
    regions: Optional[bool] = None
    countries: List[str] = Field(default_factory=list)
    street_addresses: Optional[bool] = None

    # Profiles array
    profiles: List[Dict[str, Any]] = Field(default_factory=list)

    # PDL metadata
    pdl_metadata: Dict[str, Any] = Field(default_factory=dict)
    likelihood: Optional[int] = None
    dataset_version: Optional[str] = None
    enrichment_date: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc)
    )

    def to_founder_data(self, founder_id: str, company_id: str) -> "FounderData":
        """Convert PDL data to standard FounderData format."""
        # Get primary email (PDL enrich endpoint doesn't return actual emails)
        primary_email = None
        if self.work_email:
            primary_email = "work_email_available"
        elif self.personal_emails:
            primary_email = "personal_email_available"

        # Build location string
        location_parts = [
            self.location_locality,
            self.location_region,
            self.location_country,
        ]
        location = ", ".join([part for part in location_parts if part])

        # Convert URLs to HttpUrl format if they exist
        linkedin_url = None
        twitter_url = None
        github_url = None

        if self.linkedin_url:
            try:
                linkedin_url = HttpUrl(self.linkedin_url)
            except ValueError:
                pass

        if self.twitter_url:
            try:
                twitter_url = HttpUrl(self.twitter_url)
            except ValueError:
                pass

        if self.github_url:
            try:
                github_url = HttpUrl(self.github_url)
            except ValueError:
                pass

        return FounderData(
            founder_id=founder_id,
            company_id=company_id,
            first_name=self.first_name,
            last_name=self.last_name,
            full_name=self.full_name,
            email=primary_email,
            title=self.job_title,
            bio=None,  # PDL doesn't provide bio
            location=location or None,
            country=self.location_country,
            city=self.location_locality,
            linkedin_url=linkedin_url,
            twitter_url=twitter_url,
            github_url=github_url,
            confidence_score=float(self.likelihood) / 10.0
            if self.likelihood
            else None,  # Convert 1-10 scale to 0-1
            previous_companies=[
                exp.get("company", {}).get("name", "")
                for exp in self.experience
                if exp.get("company", {}).get("name")
            ],
            education=self.education,
            skills=self.skills,
            source=EnrichmentSource.PDL,
            additional_data={
                "pdl_id": self.pdl_id,
                "middle_name": self.middle_name,
                "emails": self.emails,
                "phone_numbers": self.phone_numbers,
                "location_region": self.location_region,
                "job_company_name": self.job_company_name,
                "job_start_date": self.job_start_date.isoformat()
                if self.job_start_date
                else None,
                "experience": self.experience,
                "facebook_url": self.facebook_url,
                "interests": self.interests,
                "pdl_metadata": self.pdl_metadata,
                "enrichment_date": self.enrichment_date.isoformat(),
            },
        )


class FounderEnrichmentData(TractionXModel):
    """Enriched founder data from multiple sources."""

    # Core founder info
    founder_id: str = Field(..., description="Unique founder identifier")
    company_id: str = Field(..., description="Associated company ID")
    org_id: str = Field(..., description="Organization ID")

    # Form submission data (highest priority)
    form_data: Optional[FounderData] = Field(
        None, description="Data from form submission"
    )

    # Enrichment data
    pdl_data: Optional[PDLFounderData] = Field(None, description="PDL enrichment data")

    # Processing metadata
    enrichment_status: Dict[str, str] = Field(
        default_factory=dict, description="Status of each enrichment"
    )
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Quality metrics
    completeness_score: Optional[float] = Field(
        None, description="Data completeness score"
    )
    confidence_score: Optional[float] = Field(
        None, description="Overall confidence score"
    )

    def get_canonical_data(self) -> FounderData:
        """Get canonical founder data using merge rules (form > pdl)."""
        # Start with form data if available
        if self.form_data:
            canonical = self.form_data.model_copy()
        else:
            # Create base structure with all required fields
            canonical = FounderData(
                founder_id=self.founder_id,
                company_id=self.company_id,
                first_name=None,
                last_name=None,
                full_name=None,
                email=None,
                title=None,
                bio=None,
                location=None,
                country=None,
                city=None,
                linkedin_url=None,
                twitter_url=None,
                github_url=None,
                confidence_score=None,
                source=EnrichmentSource.FORM_SUBMISSION,
            )

        # Merge PDL data for missing fields
        if self.pdl_data:
            pdl_founder = self.pdl_data.to_founder_data(
                self.founder_id, self.company_id
            )

            # Fill missing fields with PDL data
            for field_name, field_value in pdl_founder.model_dump().items():
                if field_name in ["founder_id", "company_id", "source", "last_updated"]:
                    continue

                current_value = getattr(canonical, field_name, None)
                if current_value is None and field_value is not None:
                    setattr(canonical, field_name, field_value)

        return canonical


# ============================================================================
# NEW NORMALIZED FOUNDER SCHEMA MODELS
# ============================================================================


class FounderRecord(TractionXModel):
    """
    Core founder record with basic identity and current job info.
    Replaces the legacy founders table with normalized structure.
    """

    # Primary key - using UUID for better scalability
    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Core identifiers
    founder_id: str = Field(
        ..., description="Unique founder identifier (legacy compatibility)"
    )

    # Personal information
    full_name: Optional[str] = Field(None, max_length=500, description="Full name")
    first_name: Optional[str] = Field(None, max_length=255, description="First name")
    last_name: Optional[str] = Field(None, max_length=255, description="Last name")
    avatar_url: Optional[str] = Field(None, description="Avatar URL")
    headline: Optional[str] = Field(None, description="Headline")
    summary: Optional[str] = Field(None, description="Summary")
    location: Optional[Dict[str, Any]] = Field(
        None,
        description="Location",
        examples=[{"city": "San Francisco", "country": "US"}],
    )
    # Current job information
    current_job_title: Optional[str] = Field(
        None, max_length=255, description="Current job title"
    )
    current_job_company: Optional[str] = Field(
        None, max_length=255, description="Current company"
    )

    # Social profiles
    social_profiles: Optional[Dict[str, Any]] = Field(
        None,
        description="Social profiles",
        examples=[{"linkedin": "https://linkedin.com/in/john-doe"}],
    )
    links: Optional[Dict[str, Any]] = Field(
        None,
        description="Links",
        examples=[{"from_linkedin": ["https://linkedin.com/in/john-doe"]}],
    )
    extra_data: Optional[Dict[str, Any]] = Field(
        None,
        description="Extra data",
        examples=[{"honors": ["Best Founder 2024", "Best CEO 2023"]}],
    )

    # Organizational context
    org_id: str = Field(..., max_length=255, description="Organization ID")
    company_id: str = Field(..., max_length=255, description="Company ID")

    # Data quality and source tracking
    source: str = Field(..., max_length=100, description="Data source")
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence score"
    )
    enrichment_date: Optional[datetime] = Field(
        None, description="When data was enriched"
    )

    # Raw data backup
    s3_raw_data_key: Dict[str, Any] = Field(
        default_factory=dict,
        description="S3 key for raw enrichment data",
        examples=[
            {"linkedin": "brightdata/linkedin/123.json"},
            {"pdl": "brightdata/pdl/123.json"},
        ],
    )

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class FounderExperience(TractionXModel):
    """Professional experience record for a founder."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Company information
    company_name: Optional[str] = Field(
        None, max_length=255, description="Company name"
    )
    title: Optional[str] = Field(None, max_length=255, description="Job title")
    industry: Optional[str] = Field(None, max_length=255, description="Industry")
    company_size: Optional[str] = Field(None, max_length=50, description="Company size")
    company_url: Optional[str] = Field(None, description="Company LinkedIn URL")
    company_logo_url: Optional[str] = Field(None, description="Company logo URL")

    # Dates
    start_date: Optional[date] = Field(None, description="Start date")
    end_date: Optional[date] = Field(None, description="End date")

    # Flags
    is_primary: Optional[bool] = Field(
        None, description="Is this the primary/current role"
    )

    # Location
    location: Optional[str] = Field(None, description="Job location")

    # Description
    description: Optional[str] = Field(None, description="Job description")
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class FounderEducation(TractionXModel):
    """Education record for a founder."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # School information
    school_name: Optional[str] = Field(None, max_length=255, description="School name")
    degrees: List[str] = Field(default_factory=list, description="List of degrees")
    majors: List[str] = Field(default_factory=list, description="List of majors")
    school_url: Optional[str] = Field(None, description="School LinkedIn URL")
    institute_logo_url: Optional[str] = Field(None, description="Institute logo URL")

    # Dates
    start_date: Optional[date] = Field(None, description="Start date")
    end_date: Optional[date] = Field(None, description="End date")

    # Location
    location: Optional[str] = Field(None, description="School location")

    # Description
    description: Optional[str] = Field(None, description="Education description")
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class FounderSkill(TractionXModel):
    """Skill record for a founder."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")
    skill: str = Field(..., description="Skill name")
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v


class FounderProfile(TractionXModel):
    """Social profile record for a founder."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")
    network: str = Field(..., max_length=50, description="Social network name")
    url: str = Field(..., description="Profile URL")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v


class FounderSignal(TractionXModel):
    """LLM-generated signals and scoring for a founder."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Scoring
    score: Optional[int] = Field(
        None, ge=0, le=100, description="Overall founder score (0-100)"
    )
    tags: List[str] = Field(default_factory=list, description="Generated tags")

    # Analysis
    strengths: Dict[str, Any] = Field(
        default_factory=dict, description="Identified strengths"
    )
    risks: Dict[str, Any] = Field(default_factory=dict, description="Identified risks")

    # Metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class TeamSignal(TractionXModel):
    """Team-level signals across founders of a company."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    company_id: str = Field(..., max_length=255, description="Company ID")
    org_id: str = Field(..., max_length=255, description="Organization ID")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            # Check if it's a valid UUID format
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Team analysis
    complementarity_score: Optional[int] = Field(
        None, ge=0, le=100, description="Team complementarity score"
    )
    coverage: Dict[str, Any] = Field(
        default_factory=dict, description="Skill/experience coverage analysis"
    )
    narrative: Optional[str] = Field(None, description="Generated team narrative")

    # Metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class SkillProfile(TractionXModel):
    """Skill profile for spider chart visualization."""

    business: int = Field(..., ge=0, le=10, description="Business acumen score (0-10)")
    operations: int = Field(
        ..., ge=0, le=10, description="Operations management score (0-10)"
    )
    fundraising: int = Field(
        ..., ge=0, le=10, description="Fundraising ability score (0-10)"
    )
    product: int = Field(
        ..., ge=0, le=10, description="Product development score (0-10)"
    )
    tech: int = Field(..., ge=0, le=10, description="Technical expertise score (0-10)")


class FounderSignalOutput(TractionXModel):
    """LLM output structure for founder signal generation."""

    score: int = Field(..., ge=0, le=100, description="Overall founder score (0-100)")
    strengths: List[str] = Field(..., description="Key strengths (evidence-based)")
    risks: List[str] = Field(..., description="Key risks (investor-relevant)")
    tags: List[str] = Field(..., description="High-signal tags (max 5)")
    skill_profile: SkillProfile = Field(
        ..., description="Skill profile for spider chart"
    )

    @field_validator("strengths", "risks")
    def validate_list_length(cls, v):
        """Ensure lists are not empty and have reasonable length."""
        if not v:
            raise ValueError("List cannot be empty")
        if len(v) > 10:
            raise ValueError("List too long (max 10 items)")
        return v

    @field_validator("tags")
    def validate_tags(cls, v):
        """Ensure tags are punchy and scoped."""
        if len(v) > 5:
            raise ValueError("Too many tags (max 5)")
        for tag in v:
            if len(tag) > 50:
                raise ValueError("Tag too long (max 50 chars)")
        return v


class FounderSignalInput(TractionXModel):
    """Input structure for LLM signal generation."""

    full_name: str = Field(..., description="Founder's full name")
    current_job: Optional[str] = Field(
        None, description="Current job title and company"
    )
    experiences: List[Dict[str, Any]] = Field(
        default_factory=list, description="Work experience"
    )
    education: List[Dict[str, Any]] = Field(
        default_factory=list, description="Education background"
    )
    skills: List[str] = Field(default_factory=list, description="Professional skills")
    profiles: List[Dict[str, Any]] = Field(
        default_factory=list, description="Social profiles"
    )

    def to_llm_prompt(self) -> str:
        """Convert to LLM prompt format."""
        prompt_data = {
            "full_name": self.full_name,
            "current_job": self.current_job,
            "experiences": self.experiences,
            "education": self.education,
            "skills": self.skills,
        }

        import json

        return json.dumps(prompt_data, indent=2, default=str)


class FounderSignalRecord(TractionXModel):
    """Database record for stored founder signals."""

    id: str = Field(
        default_factory=lambda: str(uuid4()), description="Primary key UUID"
    )
    founder_id: str = Field(..., description="Reference to founder record")

    # Override the base model's ObjectId validator to accept UUID strings
    @field_validator("id", mode="before")
    def validate_uuid_id(cls, v):
        """Validate that id is a valid UUID string."""
        if isinstance(v, str):
            import re

            uuid_pattern = re.compile(
                r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
                re.IGNORECASE,
            )
            if uuid_pattern.match(v):
                return v
            else:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    # Scoring
    score: int = Field(..., ge=0, le=100, description="Overall founder score (0-100)")
    tags: List[str] = Field(default_factory=list, description="Generated tags")

    # Analysis
    strengths: Dict[str, Any] = Field(
        default_factory=dict, description="Identified strengths"
    )
    risks: Dict[str, Any] = Field(default_factory=dict, description="Identified risks")

    # Skill profile
    skill_profile: Dict[str, int] = Field(
        default_factory=dict, description="Skill scores"
    )

    # Metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    llm_model: str = Field(..., description="LLM model used for generation")
    prompt_version: str = Field(default="v1", description="Prompt version used")

    # Processing metadata
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )


class SpiderChartData(TractionXModel):
    """Spider chart data for founder visualization."""

    founder_id: str = Field(..., description="Founder ID")
    score: int = Field(..., ge=0, le=100, description="Overall founder score (0-100)")
    tags: List[str] = Field(default_factory=list, description="Generated tags")
    strengths: List[str] = Field(
        default_factory=list, description="Identified strengths"
    )
    risks: List[str] = Field(default_factory=list, description="Identified risks")

    # Skill scores for spider chart
    business_score: int = Field(
        ..., ge=0, le=10, description="Business acumen score (0-10)"
    )
    operations_score: int = Field(
        ..., ge=0, le=10, description="Operations management score (0-10)"
    )
    fundraising_score: int = Field(
        ..., ge=0, le=10, description="Fundraising ability score (0-10)"
    )
    product_score: int = Field(
        ..., ge=0, le=10, description="Product development score (0-10)"
    )
    tech_score: int = Field(
        ..., ge=0, le=10, description="Technical expertise score (0-10)"
    )

    # Metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    llm_model: str = Field(
        default="gpt-4o", description="LLM model used for generation"
    )
    prompt_version: str = Field(default="v1", description="Prompt version used")

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class BrightDataFounderData(TractionXModel):
    """BrightData founder enrichment data model."""

    # BrightData specific fields
    snapshot_id: str = Field(..., description="BrightData snapshot ID")
    url: str = Field(..., description="LinkedIn URL that was scraped")

    # Profile data
    full_name: Optional[str] = Field(None, description="Full name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    headline: Optional[str] = Field(None, description="Professional headline")
    summary: Optional[str] = Field(None, description="Profile summary")
    location: Optional[str] = Field(None, description="Location")

    # Experience data
    experience: List[Dict[str, Any]] = Field(
        default_factory=list, description="Work experience"
    )

    # Education data
    education: List[Dict[str, Any]] = Field(
        default_factory=list, description="Education history"
    )

    # Skills and endorsements
    skills: List[Dict[str, Any]] = Field(
        default_factory=list, description="Skills and endorsements"
    )

    # Social profiles
    profiles: List[Dict[str, Any]] = Field(
        default_factory=list, description="Social profiles"
    )

    # Metadata
    profile_picture_url: Optional[str] = Field(None, description="Profile picture URL")
    connection_count: Optional[int] = Field(None, description="Number of connections")
    follower_count: Optional[int] = Field(None, description="Number of followers")

    # Processing metadata
    scraped_at: Optional[datetime] = Field(None, description="When data was scraped")
    confidence_score: Optional[float] = Field(None, description="Data confidence score")


class BrightDataSnapshotRecord(TractionXModel):
    """Raw BrightData snapshot record from LinkedIn scraping."""

    # Basic profile info
    id: Optional[str] = Field(None, description="LinkedIn profile ID")
    name: Optional[str] = Field(None, description="Full name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")

    # Location
    city: Optional[str] = Field(None, description="City")
    country_code: Optional[str] = Field(None, description="Country code")
    location: Optional[str] = Field(None, description="Full location")

    # Professional info
    position: Optional[str] = Field(None, description="Current position")
    about: Optional[str] = Field(None, description="About section")

    # Current company
    current_company: Optional[Dict[str, Any]] = Field(
        None, description="Current company info"
    )
    current_company_name: Optional[str] = Field(
        None, description="Current company name"
    )
    current_company_company_id: Optional[str] = Field(
        None, description="Current company ID"
    )

    # Experience
    experience: List[Dict[str, Any]] = Field(
        default_factory=list, description="Work experience"
    )

    # Education
    education: List[Dict[str, Any]] = Field(
        default_factory=list, description="Education history"
    )
    educations_details: Optional[str] = Field(None, description="Education details")

    # Skills (will be extracted via LLM)
    skills: List[str] = Field(
        default_factory=list, description="Skills extracted via LLM"
    )

    # Social and connections
    url: Optional[str] = Field(None, description="LinkedIn URL")
    input_url: Optional[str] = Field(None, description="Original input URL")
    linkedin_id: Optional[str] = Field(None, description="LinkedIn ID")
    linkedin_num_id: Optional[str] = Field(None, description="LinkedIn numeric ID")

    # Profile metadata
    avatar: Optional[str] = Field(None, description="Profile picture URL")
    banner_image: Optional[str] = Field(None, description="Banner image URL")
    followers: Optional[int] = Field(None, description="Number of followers")
    connections: Optional[int] = Field(None, description="Number of connections")

    # Additional data
    languages: List[Dict[str, Any]] = Field(
        default_factory=list, description="Languages"
    )
    certifications: List[Dict[str, Any]] = Field(
        default_factory=list, description="Certifications"
    )
    recommendations: List[str] = Field(
        default_factory=list, description="Recommendations"
    )
    volunteer_experience: List[Dict[str, Any]] = Field(
        default_factory=list, description="Volunteer experience"
    )
    projects: List[Dict[str, Any]] = Field(default_factory=list, description="Projects")
    activity: List[Dict[str, Any]] = Field(
        default_factory=list, description="Recent activity"
    )

    # Processing metadata
    timestamp: Optional[str] = Field(None, description="Scraping timestamp")
    input: Optional[Dict[str, Any]] = Field(None, description="Input data")


# Import UUID at the top level
