"""
LinkedIn data models for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic import Field

from app.models.base import EnrichmentSource, TractionXModel





class LinkedInResolverInput(TractionXModel):
    """Input schema for LinkedIn URL resolver."""

    company_domain: str = Field(..., description="Company domain to resolve")
    company_description: Optional[str] = Field(
        None, description="Optional company description for better matching"
    )
    org_id: Optional[str] = Field(
        None, description="Organization ID for multi-tenant support"
    )
    job_id: Optional[str] = Field(None, description="Job identifier for tracking")


class LinkedInResolverOutput(TractionXModel):
    """Output schema for LinkedIn URL resolver."""

    status: str = Field(..., description="Processing status")
    company_domain: str = Field(..., description="Input company domain")
    linkedin_url: Optional[str] = Field(None, description="Resolved LinkedIn URL")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )
    log: str = Field(..., description="Processing log message")

    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_stage: Optional[str] = Field(None, description="Stage where error occurred")

    # Processing metadata
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )
    llm_decision: Optional[str] = Field(None, description="LLM decision for debugging")
    serper_results_count: Optional[int] = Field(
        None, description="Number of Serper results"
    )

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    completed_at: Optional[datetime] = Field(
        None, description="When processing completed"
    )


class SerperSearchResult(TractionXModel):
    """Individual search result from Serper API."""

    title: str = Field(..., description="Search result title")
    link: str = Field(..., description="Search result URL")
    snippet: str = Field(..., description="Search result snippet")
    position: int = Field(..., description="Position in search results")


class SerperSearchResponse(TractionXModel):
    """Response from Serper API."""

    organic: List[SerperSearchResult] = Field(
        default_factory=list, description="Organic search results"
    )
    total_results: Optional[int] = Field(None, description="Total number of results")
    search_time: Optional[float] = Field(None, description="Search time in seconds")


class BrightDataLinkedInData(TractionXModel):
    """LinkedIn company data structure from BrightData scraping."""

    # Core identifiers
    company_id: str = Field(..., description="Unique company identifier")
    linkedin_url: str = Field(..., description="Source LinkedIn URL")
    brightdata_snapshot_id: str = Field(..., description="BrightData snapshot ID")

    # Basic information
    name: Optional[str] = Field(None, description="Company name")
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[str] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    industry: Optional[str] = Field(None, description="Industry/sector")
    company_type: Optional[str] = Field(None, description="Company type")
    company_size: Optional[str] = Field(None, description="Company size")
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Number of employees")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters location")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")

    # LinkedIn specific
    followers_count: Optional[int] = Field(None, description="LinkedIn followers")
    specialties: List[str] = Field(
        default_factory=list, description="Company specialties"
    )
    about: Optional[str] = Field(None, description="About section")
    overview: Optional[str] = Field(None, description="Company overview")

    # Contact information
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Contact phone")

    # Team information
    employees: List[Dict[str, Any]] = Field(
        default_factory=list, description="Company employees"
    )
    executives: List[Dict[str, Any]] = Field(
        default_factory=list, description="Company executives"
    )

    # Additional data
    technologies: List[str] = Field(
        default_factory=list, description="Technologies used"
    )
    keywords: List[str] = Field(default_factory=list, description="Company keywords")
    competitors: List[str] = Field(default_factory=list, description="Competitors")

    # Metadata
    source: EnrichmentSource = Field(
        default=EnrichmentSource.MANUAL, description="Data source"
    )
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    confidence_score: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Data confidence"
    )

    # Raw data reference
    s3_raw_data_key: Optional[str] = Field(
        None, description="S3 key for raw data backup"
    )
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional company data"
    )


class BrightDataSnapshotRecord(TractionXModel):
    """Raw snapshot record from BrightData API."""

    # Basic info
    name: Optional[str] = Field(None, description="Company name")
    description: Optional[str] = Field(None, description="Company description")
    website: Optional[str] = Field(None, description="Company website")
    domain: Optional[str] = Field(None, description="Company domain")

    # Business details
    industry: Optional[str] = Field(None, description="Industry")
    company_type: Optional[str] = Field(None, description="Company type")
    company_size: Optional[str] = Field(None, description="Company size")
    founded_year: Optional[int] = Field(None, description="Year founded")
    employee_count: Optional[int] = Field(None, description="Employee count")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )

    # Location
    headquarters: Optional[str] = Field(None, description="Headquarters")
    country: Optional[str] = Field(None, description="Country")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")

    # LinkedIn specific
    followers_count: Optional[int] = Field(None, description="LinkedIn followers")
    specialties: List[str] = Field(default_factory=list, description="Specialties")
    about: Optional[str] = Field(None, description="About section")
    overview: Optional[str] = Field(None, description="Company overview")

    # Contact info
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Phone number")

    # Team
    employees: List[Dict[str, Any]] = Field(
        default_factory=list, description="Employees"
    )
    executives: List[Dict[str, Any]] = Field(
        default_factory=list, description="Executives"
    )

    # Additional
    technologies: List[str] = Field(default_factory=list, description="Technologies")
    keywords: List[str] = Field(default_factory=list, description="Keywords")
    competitors: List[str] = Field(default_factory=list, description="Competitors")

    # Raw data
    raw_data: Dict[str, Any] = Field(
        default_factory=dict, description="Raw data from BrightData"
    )


class LinkedInEnrichmentData(TractionXModel):
    """Enriched company data from LinkedIn via BrightData."""

    # Core company info
    company_id: str = Field(..., description="Unique company identifier")
    org_id: str = Field(..., description="Organization ID")

    # LinkedIn data
    linkedin_data: Optional[BrightDataLinkedInData] = Field(
        None, description="LinkedIn enrichment data"
    )

    # Processing metadata
    enrichment_status: Dict[str, str] = Field(
        default_factory=dict, description="Status of each enrichment"
    )
    last_enriched: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Quality metrics
    completeness_score: Optional[float] = Field(
        None, description="Data completeness score"
    )
    confidence_score: Optional[float] = Field(
        None, description="Overall confidence score"
    )

    # Resolver metadata
    resolver_job_id: Optional[str] = Field(None, description="Resolver job ID")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )
    s3_raw_data_key: Optional[str] = Field(None, description="S3 key for raw data")
