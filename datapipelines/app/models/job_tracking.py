"""
Job tracking models for TractionX Data Pipeline Service.

This module provides centralized job type definitions and tracking models.
"""

from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from app.models.base import TractionXModel


class JobType(str, Enum):
    """
    Industry-standard hierarchical job types.

    Format: {entity}.{action}.{source}
    Examples: company.enrich.pdl, founder.enrich.linkedin
    """

    # Company Jobs
    COMPANY_ENRICH_PDL = "company.enrich.pdl"
    COMPANY_LINKEDIN_PROCESSOR = "company.linkedin.processor"
    COMPANY_CRUNCHBASE_PROCESSOR = "company.crunchbase.processor"
    COMPANY_PITCHBOOK_PROCESSOR = "company.pitchbook.processor"
    COMPANY_ENRICH_APOLLO = "company.enrich.apollo"
    COMPANY_ENRICH_COMPREHENSIVE = "company.enrich.comprehensive"

    # Company Resolve Jobs
    COMPANY_RESOLVE_CRUNCHBASE = "company.resolve.crunchbase"
    COMPANY_RESOLVE_LINKEDIN = "company.resolve.linkedin"
    COMPANY_RESOLVE_PITCHBOOK = "company.resolve.pitchbook"

    # Company Website Jobs
    COMPANY_WEBSITE_INSIGHTS = "company.website.insights"
    COMPANY_WEBSITE_SITEMAP = "company.website.sitemap"

    # Founder Jobs
    FOUNDER_ENRICH_PDL = "founder.enrich.pdl"
    FOUNDER_ENRICH_LINKEDIN = "founder.enrich.linkedin"
    FOUNDER_SPIDER_CHART = "founder.spider.chart"

    # Processing Jobs
    PROCESSING_MERGE_DATA = "processing.merge.data"
    PROCESSING_MERGE_COMPREHENSIVE = "processing.merge.comprehensive"
    PROCESSING_COMPREHENSIVE_DATA = "processing.comprehensive.data"

    # Utility Jobs
    UTILITY_NEWS_AGGREGATE = "utility.news.aggregate"
    UTILITY_EMBEDDINGS_GENERATE = "utility.embeddings.generate"
    UTILITY_DEALS_UPDATE = "utility.deals.update"

    # Management Jobs
    MANAGEMENT_STATUS_CHECK = "management.status.check"
    MANAGEMENT_RETRY_FAILED = "management.retry.failed"
    MANAGEMENT_CLEANUP = "management.cleanup"


class JobPriority(str, Enum):
    """Job priority levels."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class JobStatus(str, Enum):
    """Job status values."""

    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class RetryConfig(TractionXModel):
    """Configuration for job retries."""

    max_retries: int = Field(default=3, description="Maximum number of retries")
    retry_delay: int = Field(default=60, description="Delay between retries in seconds")
    backoff_factor: float = Field(default=2.0, description="Exponential backoff factor")
    max_delay: int = Field(default=3600, description="Maximum delay between retries")


class JobMetadata(TractionXModel):
    """Metadata for job tracking."""

    pipeline_id: Optional[str] = Field(None, description="Pipeline identifier")
    entity_id: Optional[str] = Field(None, description="Entity identifier")
    entity_type: Optional[str] = Field(None, description="Entity type")
    orchestration_pattern: Optional[str] = Field(
        None, description="Orchestration pattern"
    )
    webhook_url: Optional[str] = Field(
        None, description="Webhook URL for notifications"
    )
    callback_data: Dict[str, Any] = Field(
        default_factory=dict, description="Callback data"
    )
    created_by: Optional[str] = Field(None, description="User who created the job")
    tags: List[str] = Field(default_factory=list, description="Job tags")


class JobRecord(TractionXModel):
    """Job record for tracking."""

    id: str = Field(default_factory=lambda: str(uuid4()), description="Unique job ID")
    job_type: JobType = Field(..., description="Type of job")
    status: JobStatus = Field(default=JobStatus.PENDING, description="Job status")
    priority: JobPriority = Field(
        default=JobPriority.NORMAL, description="Job priority"
    )

    # Payload and configuration
    payload: Dict[str, Any] = Field(..., description="Job payload")
    metadata: JobMetadata = Field(
        default_factory=lambda: JobMetadata(
            pipeline_id=None,
            entity_id=None,
            entity_type=None,
            orchestration_pattern=None,
            webhook_url=None,
            callback_data={},
            created_by=None,
            tags=[],
        ),
        description="Job metadata",
    )
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Job configuration"
    )

    # Timing
    created_at: Optional[str] = Field(None, description="Job creation timestamp")
    started_at: Optional[str] = Field(None, description="Job start timestamp")
    completed_at: Optional[str] = Field(None, description="Job completion timestamp")

    # Retry and error handling
    retry_count: int = Field(default=0, description="Number of retries attempted")
    retry_config: Optional[RetryConfig] = Field(None, description="Retry configuration")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_details: Optional[Dict[str, Any]] = Field(
        None, description="Detailed error info"
    )

    # Dependencies
    depends_on: List[str] = Field(default_factory=list, description="Job dependencies")
    dependent_jobs: List[str] = Field(
        default_factory=list, description="Jobs that depend on this"
    )

    # Progress tracking
    progress: float = Field(default=0.0, description="Job progress (0.0 to 1.0)")
    progress_message: Optional[str] = Field(None, description="Progress message")

    # Results
    result: Optional[Dict[str, Any]] = Field(None, description="Job result data")
    result_size: Optional[int] = Field(None, description="Size of result data")


# Centralized Job Type Registry
JOB_TYPE_REGISTRY = {
    # Company Jobs
    JobType.COMPANY_ENRICH_PDL: {
        "function": "process_pdl_enrichment_task",
        "required_fields": ["founder_id", "org_id"],
        "entity_types": ["founder"],
        "description": "Enrich founder data using PDL",
        "estimated_duration": 30,
        "category": "enrichment",
    },
    JobType.COMPANY_LINKEDIN_PROCESSOR: {
        "function": "scrape_linkedin_company_data_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Enrich company data using LinkedIn",
        "estimated_duration": 45,
        "category": "enrichment",
    },
    JobType.COMPANY_CRUNCHBASE_PROCESSOR: {
        "function": "scrape_crunchbase_company_data_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Enrich company data using Crunchbase",
        "estimated_duration": 60,
        "category": "enrichment",
    },
    JobType.FOUNDER_SPIDER_CHART: {
        "function": "generate_founder_spider_chart_task",
        "required_fields": ["founder_id", "org_id"],
        "entity_types": ["founder"],
        "description": "Generate founder spider chart",
        "estimated_duration": 30,
        "category": "enrichment",
    },
    JobType.COMPANY_PITCHBOOK_PROCESSOR: {
        "function": "scrape_pitchbook_company_data_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Enrich company data using PitchBook",
        "estimated_duration": 90,
        "category": "enrichment",
    },
    JobType.COMPANY_ENRICH_APOLLO: {
        "function": "process_apollo_company_data_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Enrich company data using Apollo",
        "estimated_duration": 30,
        "category": "enrichment",
    },
    JobType.COMPANY_ENRICH_COMPREHENSIVE: {
        "function": "enrich_company_data_comprehensive",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Comprehensive company enrichment",
        "estimated_duration": 300,
        "category": "enrichment",
    },
    # Company Resolve Jobs
    JobType.COMPANY_RESOLVE_CRUNCHBASE: {
        "function": "resolve_crunchbase_url_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Resolve Crunchbase URL for company",
        "estimated_duration": 15,
        "category": "resolve",
    },
    JobType.COMPANY_RESOLVE_LINKEDIN: {
        "function": "resolve_linkedin_url_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Resolve LinkedIn URL for company",
        "estimated_duration": 15,
        "category": "resolve",
    },
    JobType.COMPANY_RESOLVE_PITCHBOOK: {
        "function": "resolve_pitchbook_company_url_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Resolve PitchBook URL for company",
        "estimated_duration": 15,
        "category": "resolve",
    },
    # Company Website Jobs
    JobType.COMPANY_WEBSITE_INSIGHTS: {
        "function": "generate_website_insights_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Generate website insights",
        "estimated_duration": 120,
        "category": "website",
    },
    JobType.COMPANY_WEBSITE_SITEMAP: {
        "function": "generate_sitemap_task",
        "required_fields": ["company_domain", "org_id"],
        "entity_types": ["company"],
        "description": "Generate website sitemap",
        "estimated_duration": 60,
        "category": "website",
    },
    # Founder Jobs
    JobType.FOUNDER_ENRICH_PDL: {
        "function": "process_pdl_enrichment_task",
        "required_fields": ["founder_id", "org_id"],
        "entity_types": ["founder"],
        "description": "Enrich founder data using PDL",
        "estimated_duration": 30,
        "category": "enrichment",
    },
    JobType.FOUNDER_ENRICH_LINKEDIN: {
        "function": "enrich_founder_linkedin_data",
        "required_fields": ["founder_name", "founder_linkedin", "org_id"],
        "entity_types": ["founder"],
        "description": "Enrich founder data using LinkedIn",
        "estimated_duration": 45,
        "category": "enrichment",
    },
    # Processing Jobs
    JobType.PROCESSING_MERGE_DATA: {
        "function": "merge_enrichment_data",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Merge enrichment data",
        "estimated_duration": 30,
        "category": "processing",
    },
    JobType.PROCESSING_MERGE_COMPREHENSIVE: {
        "function": "merge_comprehensive_enrichment_results",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Merge comprehensive results",
        "estimated_duration": 60,
        "category": "processing",
    },
    JobType.PROCESSING_COMPREHENSIVE_DATA: {
        "function": "process_comprehensive_enrichment_data_task",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Process comprehensive data",
        "estimated_duration": 120,
        "category": "processing",
    },
    # Utility Jobs
    JobType.UTILITY_NEWS_AGGREGATE: {
        "function": "aggregate_news_data",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Aggregate news data",
        "estimated_duration": 90,
        "category": "utility",
    },
    JobType.UTILITY_EMBEDDINGS_GENERATE: {
        "function": "generate_embeddings",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Generate embeddings",
        "estimated_duration": 60,
        "category": "utility",
    },
    JobType.UTILITY_DEALS_UPDATE: {
        "function": "update_deals_with_company_data_task",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Update deals with company data",
        "estimated_duration": 30,
        "category": "utility",
    },
    # Management Jobs
    JobType.MANAGEMENT_STATUS_CHECK: {
        "function": "get_enrichment_status",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Check enrichment status",
        "estimated_duration": 5,
        "category": "management",
    },
    JobType.MANAGEMENT_RETRY_FAILED: {
        "function": "retry_failed_enrichment",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Retry failed enrichment",
        "estimated_duration": 30,
        "category": "management",
    },
    JobType.MANAGEMENT_CLEANUP: {
        "function": "cleanup_enrichment",
        "required_fields": ["entity_id", "org_id"],
        "entity_types": ["company", "founder"],
        "description": "Cleanup enrichment data",
        "estimated_duration": 15,
        "category": "management",
    },
}


def get_job_type_info(job_type: JobType) -> Dict[str, Any]:
    """Get information about a job type."""
    return JOB_TYPE_REGISTRY.get(job_type, {})


def get_job_types_by_category(category: str) -> List[JobType]:
    """Get job types by category."""
    return [
        job_type
        for job_type, info in JOB_TYPE_REGISTRY.items()
        if info.get("category") == category
    ]


def get_job_types_by_entity_type(entity_type: str) -> List[JobType]:
    """Get job types that support a specific entity type."""
    return [
        job_type
        for job_type, info in JOB_TYPE_REGISTRY.items()
        if entity_type in info.get("entity_types", [])
    ]


def validate_job_type_for_entity(job_type: JobType, entity_type: str) -> bool:
    """Validate if a job type supports a specific entity type."""
    info = get_job_type_info(job_type)
    return entity_type in info.get("entity_types", [])


def get_required_fields_for_job_type(job_type: JobType) -> List[str]:
    """Get required fields for a job type."""
    info = get_job_type_info(job_type)
    return info.get("required_fields", [])


def get_function_name_for_job_type(job_type: JobType) -> str:
    """Get the function name for a job type."""
    info = get_job_type_info(job_type)
    return info.get("function", "")


def get_estimated_duration_for_job_type(job_type: JobType) -> int:
    """Get estimated duration for a job type in seconds."""
    info = get_job_type_info(job_type)
    return info.get("estimated_duration", 60)


class JobProgress(TractionXModel):
    """Progress information for long-running jobs."""

    current_step: str = Field(..., description="Current step name")
    total_steps: int = Field(..., description="Total number of steps")
    current_step_number: int = Field(..., description="Current step number")
    step_progress: float = Field(
        ..., ge=0.0, le=1.0, description="Progress within current step (0.0-1.0)"
    )
    overall_progress: float = Field(
        ..., ge=0.0, le=1.0, description="Overall progress (0.0-1.0)"
    )
    estimated_time_remaining: Optional[float] = Field(
        None, description="Estimated time remaining in seconds"
    )
    step_details: Optional[Dict[str, Any]] = Field(
        None, description="Additional step details"
    )


class JobError(TractionXModel):
    """Error information for failed jobs."""

    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    error_traceback: Optional[str] = Field(None, description="Full error traceback")
    occurred_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    retry_count: int = Field(default=0, description="Number of retries attempted")


class JobDependency(TractionXModel):
    """Detailed dependency information for a job."""

    job_id: UUID = Field(..., description="Dependent job ID")
    dependency_type: str = Field(default="required", description="Type of dependency")
    condition: Optional[str] = Field(None, description="Condition for dependency")
    timeout_seconds: Optional[int] = Field(None, description="Dependency timeout")


class TrackedJob(TractionXModel):
    """Model for tracking job execution in the datapipelines system."""

    # Core identification
    id: UUID = Field(default_factory=uuid4, description="Unique job ID")
    job_id: str = Field(..., description="Queue job ID")
    job_type: JobType = Field(..., description="Type of job")

    # Entity association
    entity_type: str = Field(
        ..., description="Type of entity (e.g., 'company', 'founder')"
    )
    entity_id: Optional[str] = Field(None, description="Entity ID")

    # Job configuration
    payload: Dict[str, Any] = Field(
        default_factory=dict, description="Job payload data"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Job configuration"
    )

    # Priority and scheduling
    priority: int = Field(default=0, description="Job priority (0-100)")
    queue_priority: JobPriority = Field(
        default=JobPriority.NORMAL, description="Queue priority"
    )

    # Status and lifecycle
    status: JobStatus = Field(
        default=JobStatus.PENDING, description="Current job status"
    )
    progress: Optional[JobProgress] = Field(
        None, description="Job progress information"
    )

    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(
        None, description="When job started execution"
    )
    completed_at: Optional[datetime] = Field(None, description="When job completed")

    # Retry and error handling
    retry_config: RetryConfig = Field(
        default_factory=RetryConfig, description="Retry configuration"
    )
    retry_count: int = Field(default=0, description="Number of retries attempted")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    errors: List[JobError] = Field(
        default_factory=list, description="List of errors encountered"
    )

    # Results and output
    result: Optional[Dict[str, Any]] = Field(None, description="Job result data")
    output: Optional[Dict[str, Any]] = Field(None, description="Job output data")

    # Enhanced dependency management
    parent_job_id: Optional[UUID] = Field(
        None, description="Parent job ID if this is a child job"
    )
    depends_on: List[UUID] = Field(
        default_factory=list, description="List of job IDs this job depends on"
    )

    # New: Enhanced orchestration fields
    job_group: Optional[str] = Field(
        None, description="Job group for coordination and monitoring"
    )
    triggers: List[str] = Field(
        default_factory=list, description="Jobs to trigger when this completes"
    )
    wait_for: List[str] = Field(
        default_factory=list, description="Jobs to wait for before starting"
    )

    # Dependency tracking
    total_dependencies: int = Field(
        default=0, description="Total number of dependencies"
    )
    completed_dependencies: int = Field(
        default=0, description="Number of completed dependencies"
    )
    dependency_details: List[JobDependency] = Field(
        default_factory=list, description="Detailed dependency information"
    )

    # Orchestration metadata
    orchestration_pattern: Optional[str] = Field(
        None, description="Orchestration pattern used (fan_out_fan_in, pipeline, etc.)"
    )
    execution_conditions: Dict[str, Any] = Field(
        default_factory=dict, description="Conditions for job execution"
    )
    rollback_job_id: Optional[UUID] = Field(
        None, description="Job to execute if this job fails"
    )

    # Queue information
    queue_name: str = Field(default="default", description="Queue name")
    worker_id: Optional[str] = Field(
        None, description="ID of worker processing this job"
    )

    # Performance metrics
    processing_time: Optional[float] = Field(
        None, description="Total processing time in seconds"
    )
    queue_wait_time: Optional[float] = Field(
        None, description="Time spent waiting in queue"
    )

    # Dead letter queue
    dlq_reason: Optional[str] = Field(
        None, description="Reason for moving to dead letter queue"
    )
    dlq_at: Optional[datetime] = Field(None, description="When job was moved to DLQ")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }

    def is_ready_to_execute(self) -> bool:
        """Check if job is ready to execute (all dependencies met)."""
        return (
            self.status == JobStatus.PENDING
            and self.completed_dependencies >= self.total_dependencies
            and not self.depends_on  # Legacy check for backward compatibility
        )

    def add_dependency(
        self,
        job_id: UUID,
        dependency_type: str = "required",
        condition: Optional[str] = None,
        timeout_seconds: Optional[int] = None,
    ) -> None:
        """Add a dependency to this job."""
        dependency = JobDependency(
            job_id=job_id,
            dependency_type=dependency_type,
            condition=condition,
            timeout_seconds=timeout_seconds,
        )
        self.dependency_details.append(dependency)
        self.total_dependencies += 1
        self.depends_on.append(job_id)  # Maintain backward compatibility

    def mark_dependency_completed(self, job_id: UUID) -> bool:
        """Mark a dependency as completed."""
        for dep in self.dependency_details:
            if dep.job_id == job_id:
                self.completed_dependencies += 1
                return True
        return False

    def get_dependency_progress(self) -> float:
        """Get dependency completion progress (0.0 to 1.0)."""
        if self.total_dependencies == 0:
            return 1.0
        return self.completed_dependencies / self.total_dependencies


class JobStats(BaseModel):
    """Statistics for job tracking."""

    total_jobs: int = Field(default=0, description="Total number of jobs")
    pending_jobs: int = Field(default=0, description="Number of pending jobs")
    running_jobs: int = Field(default=0, description="Number of running jobs")
    completed_jobs: int = Field(default=0, description="Number of completed jobs")
    failed_jobs: int = Field(default=0, description="Number of failed jobs")
    retrying_jobs: int = Field(default=0, description="Number of retrying jobs")
    blocked_jobs: int = Field(default=0, description="Number of blocked jobs")
    dead_letter_jobs: int = Field(
        default=0, description="Number of jobs in dead letter queue"
    )

    avg_processing_time: Optional[float] = Field(
        None, description="Average processing time"
    )
    avg_queue_wait_time: Optional[float] = Field(
        None, description="Average queue wait time"
    )
    success_rate: Optional[float] = Field(
        None, description="Job success rate (0.0-1.0)"
    )

    # Per job type statistics
    job_type_stats: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="Statistics per job type"
    )


class DeadLetterJob(TractionXModel):
    """Model for jobs in the dead letter queue."""

    job_id: str = Field(..., description="Original job ID")
    tracked_job_id: UUID = Field(..., description="Tracked job ID")
    job_type: JobType = Field(..., description="Type of job")
    entity_type: str = Field(..., description="Entity type")
    entity_id: Optional[str] = Field(None, description="Entity ID")

    reason: str = Field(..., description="Reason for DLQ placement")
    error_count: int = Field(..., description="Number of errors encountered")
    last_error: JobError = Field(..., description="Last error encountered")

    original_payload: Dict[str, Any] = Field(..., description="Original job payload")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Job metadata")

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    moved_to_dlq_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc)
    )

    # Recovery information
    can_retry: bool = Field(default=True, description="Whether job can be retried")
    retry_after: Optional[datetime] = Field(None, description="When job can be retried")
    manual_intervention_required: bool = Field(
        default=False, description="Whether manual intervention is required"
    )


@dataclass
class QueueStats:
    """Statistics for a queue."""

    queue_name: str
    pending_count: int
    running_count: int
    completed_count: int
    failed_count: int
    total_processed: int
    avg_processing_time: Optional[float] = None
    last_job_processed: Optional[datetime] = None


@dataclass
class JobResult:
    """Result of a job execution."""

    job_id: str
    status: JobStatus
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    retry_count: int = 0
    progress: Optional[JobProgress] = None
