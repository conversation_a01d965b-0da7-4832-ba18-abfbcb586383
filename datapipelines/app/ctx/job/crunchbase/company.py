"""
Crunchbase Company Job Context for TractionX Data Pipeline Service.

This module provides job context classes for Crunchbase company-related tasks.
"""

from dataclasses import asdict, dataclass
from typing import Any, Dict, Optional


@dataclass
class CrunchbaseCompanyJobContext:
    """Job context for Crunchbase company tasks."""

    job_id: str
    company_id: str
    org_id: str
    company_domain: str
    data: Dict[str, Any]
    brightdata_snapshot_id: Optional[str] = None

    def _asdict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
