"""
Job Tracking Service Interfaces for TractionX Data Pipeline Service

This module defines the interfaces for job tracking services, following the same
elegant patterns as the backend system with proper abstraction and type safety.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.models.job_tracking import (
    DeadLetterJob,
    JobError,
    JobProgress,
    JobStats,
    JobStatus,
    JobType,
    TrackedJob,
)


class JobServiceInterface(ABC):
    """Interface for job tracking services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the service."""
        pass

    @abstractmethod
    async def create_job(
        self,
        job_id: str,
        entity_type: str,
        entity_id: Optional[str],
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 0,
        parent_job_id: Optional[Union[str, UUID]] = None,
        depends_on: Optional[List[Union[str, UUID]]] = None,
        scheduled_at: Optional[int] = None,
        queue_priority: Optional[str] = None,
        queue_type: Optional[str] = None,
        queue_job_type: Optional[str] = None,
    ) -> TrackedJob:
        """
        Create a new tracked job.

        Args:
            entity_type: Type of entity this job is associated with
            entity_id: ID of the entity this job is associated with
            job_type: Type of job being performed
            payload: Job payload to be passed to the queue
            metadata: Additional metadata about the job
            config: Configuration for the job
            priority: Priority of the job (higher values = higher priority)
            parent_job_id: ID of the parent job if this is a child job
            depends_on: IDs of jobs this job depends on
            scheduled_at: When the job is scheduled to run
            queue_priority: Priority in the queue system
            queue_type: Type of queue to use
            queue_job_type: Type of job in the queue system

        Returns:
            The created job
        """
        pass

    @abstractmethod
    async def update_job_status(
        self,
        job_id: Union[str, UUID],
        status: Union[str, JobStatus],
        error: Optional[str] = None,
        progress: Optional[float] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> Optional[TrackedJob]:
        """
        Update the status of a tracked job.

        Args:
            job_id: ID of the job to update
            status: New status of the job
            error: Error message if the job failed
            progress: Progress of the job (0.0 to 1.0)
            output: Output of the job (results, data, etc.)
            metadata: Additional metadata about the job
            config: Configuration for the job

        Returns:
            The updated job, or None if the job was not found
        """
        pass

    @abstractmethod
    async def get_job(self, job_id: Union[str, UUID]) -> Optional[TrackedJob]:
        """
        Get a tracked job by ID.

        Args:
            job_id: ID of the job to get

        Returns:
            The job, or None if not found
        """
        pass

    @abstractmethod
    async def get_entity_jobs(
        self,
        entity_type: str,
        entity_id: str,
        status: Optional[Union[str, JobStatus]] = None,
        job_type: Optional[Union[str, JobType]] = None,
    ) -> List[TrackedJob]:
        """
        Get all jobs for an entity.

        Args:
            entity_type: Type of entity
            entity_id: ID of the entity
            status: Optional filter by job status
            job_type: Optional filter by job type

        Returns:
            List of jobs for the entity
        """
        pass

    @abstractmethod
    async def link_job_to_entity(
        self,
        job_id: Union[str, UUID],
        entity_type: str,
        entity_id: str,
    ) -> bool:
        """
        Link a job to an entity by adding it to the entity's active_jobs list.

        Args:
            job_id: ID of the job to link
            entity_type: Type of entity
            entity_id: ID of the entity

        Returns:
            True if the job was linked, False otherwise
        """
        pass

    @abstractmethod
    async def unlink_job_from_entity(
        self,
        job_id: Union[str, UUID],
        entity_type: str,
        entity_id: str,
    ) -> bool:
        """
        Unlink a job from an entity by removing it from the entity's active_jobs list.

        Args:
            job_id: ID of the job to unlink
            entity_type: Type of entity
            entity_id: ID of the entity

        Returns:
            True if the job was unlinked, False otherwise
        """
        pass

    @abstractmethod
    async def update_job_progress(
        self,
        job_id: Union[str, UUID],
        progress: JobProgress,
    ) -> bool:
        """
        Update job progress information.

        Args:
            job_id: ID of the job to update
            progress: Progress information

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def add_job_error(
        self,
        job_id: Union[str, UUID],
        error: JobError,
    ) -> bool:
        """
        Add an error to a job's error list.

        Args:
            job_id: ID of the job
            error: Error information

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def retry_job(
        self,
        job_id: Union[str, UUID],
        delay_seconds: int = 0,
    ) -> bool:
        """
        Retry a failed job.

        Args:
            job_id: ID of the job to retry
            delay_seconds: Delay before retry

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def cancel_job(
        self,
        job_id: Union[str, UUID],
    ) -> bool:
        """
        Cancel a job.

        Args:
            job_id: ID of the job to cancel

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def move_to_dlq(
        self,
        job_id: Union[str, UUID],
        reason: str,
    ) -> bool:
        """
        Move a job to the dead letter queue.

        Args:
            job_id: ID of the job to move
            reason: Reason for moving to DLQ

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def get_dlq_jobs(
        self,
        limit: int = 100,
        offset: int = 0,
    ) -> List[DeadLetterJob]:
        """
        Get jobs from the dead letter queue.

        Args:
            limit: Maximum number of jobs to return
            offset: Number of jobs to skip

        Returns:
            List of dead letter jobs
        """
        pass

    @abstractmethod
    async def retry_dlq_job(
        self,
        job_id: Union[str, UUID],
    ) -> bool:
        """
        Retry a job from the dead letter queue.

        Args:
            job_id: ID of the job to retry

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def get_job_stats(
        self,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        job_type: Optional[Union[str, JobType]] = None,
        status: Optional[Union[str, JobStatus]] = None,
    ) -> JobStats:
        """
        Get job statistics.

        Args:
            entity_type: Optional filter by entity type
            entity_id: Optional filter by entity ID
            job_type: Optional filter by job type
            status: Optional filter by job status

        Returns:
            Job statistics
        """
        pass

    @abstractmethod
    async def cleanup_old_jobs(
        self,
        days_old: int = 30,
        status: Optional[Union[str, JobStatus]] = None,
    ) -> int:
        """
        Clean up old jobs.

        Args:
            days_old: Minimum age of jobs to clean up
            status: Optional filter by job status

        Returns:
            Number of jobs cleaned up
        """
        pass

    @abstractmethod
    async def get_jobs_by_dependencies(
        self,
        dependency_job_ids: List[Union[str, UUID]],
    ) -> List[TrackedJob]:
        """
        Get jobs that depend on the specified job IDs.

        Args:
            dependency_job_ids: List of job IDs to check dependencies for

        Returns:
            List of jobs that depend on the specified jobs
        """
        pass

    @abstractmethod
    async def get_jobs_by_group(
        self,
        job_group: str,
    ) -> List[TrackedJob]:
        """
        Get all jobs in a specific job group.

        Args:
            job_group: Job group name

        Returns:
            List of jobs in the group
        """
        pass

    @abstractmethod
    async def get_jobs_by_filters(
        self,
        filters: Dict[str, Any],
    ) -> List[TrackedJob]:
        """
        Get jobs based on multiple filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            List of jobs matching the filters
        """
        pass
