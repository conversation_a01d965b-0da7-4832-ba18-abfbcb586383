"""
MongoDB Implementation of Job Tracking Service

This module provides a MongoDB implementation of the job tracking service,
following the same elegant patterns as the backend system with TTL support.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.configs import get_logger, settings
from app.models.job_tracking import (
    DeadLetterJob,
    JobError,
    JobPriority,
    JobProgress,
    JobStats,
    JobStatus,
    JobType,
    TrackedJob,
)
from app.services.jobs.interfaces import JobServiceInterface
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection
from pymongo import ASCENDING, DESCENDING
from pymongo.errors import DuplicateKeyError

logger = get_logger(__name__)


class MongoJobService(JobServiceInterface):
    """MongoDB implementation of the job tracking service."""

    def __init__(
        self,
        mongo_url: Optional[str] = None,
        database_name: str = "tx_datapipelines",
        collection_prefix: str = "jobs",
        ttl_days: int = 30,
    ):
        """Initialize the MongoDB job service."""
        self.mongo_url = mongo_url or settings.mongo_connection_string
        self.database_name = database_name
        self.collection_prefix = collection_prefix
        self.ttl_days = ttl_days
        self._client: Optional[AsyncIOMotorClient] = None
        self._database = None

    async def get_client(self) -> AsyncIOMotorClient:
        """Get MongoDB client (lazy initialization)."""
        if not self._client:
            self._client = AsyncIOMotorClient(self.mongo_url)
            self._database = self._client[self.database_name]
        return self._client

    async def get_collection(self, collection_name: str) -> AsyncIOMotorCollection:
        """Get MongoDB collection."""
        await self.get_client()
        if self._database is not None:
            return self._database[f"{self.collection_prefix}_{collection_name}"]
        raise ValueError("Database not initialized")

    async def initialize(self) -> None:
        """Initialize the service."""
        client = await self.get_client()

        # Test connection
        await client.admin.command("ping")

        # Create indexes for efficient querying
        await self._create_indexes()

        # Create TTL indexes
        await self._create_ttl_indexes()

        logger.info("MongoDB job service initialized")

    async def cleanup(self) -> None:
        """Clean up resources used by the service."""
        if self._client:
            self._client.close()
            self._client = None

    async def create_job(
        self,
        job_id: str,
        entity_type: str,
        entity_id: str,
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 0,
        parent_job_id: Optional[Union[str, UUID]] = None,
        depends_on: Optional[List[Union[str, UUID]]] = None,
        scheduled_at: Optional[int] = None,
        queue_priority: Optional[str] = None,
        queue_type: Optional[str] = None,
        queue_job_type: Optional[str] = None,
    ) -> TrackedJob:
        """Create a new tracked job."""
        # Convert string job_type to enum if needed
        if isinstance(job_type, str):
            job_type = JobType(job_type)

        # Convert JobPriority enum to integer if needed
        if isinstance(priority, JobPriority):
            priority_int = {
                JobPriority.LOW: 20,
                JobPriority.NORMAL: 50,
                JobPriority.HIGH: 80,
                JobPriority.CRITICAL: 100,
            }.get(priority, 50)
        else:
            priority_int = priority

        # Create tracked job (scheduled_at is handled by queue system, not stored in tracked job)
        tracked_job = TrackedJob(
            job_id=job_id,
            job_type=job_type,
            entity_type=entity_type,
            entity_id=entity_id,
            payload=payload,
            metadata=metadata or {},
            config=config or {},
            priority=priority_int,  # This is an integer (0-100)
            parent_job_id=parent_job_id,  # type: ignore
            depends_on=depends_on or [],  # type: ignore
            queue_name=queue_type or "default",
            status=JobStatus.PENDING,
            queue_priority=queue_priority or JobPriority.NORMAL,  # type: ignore
            progress=None,
            started_at=None,
            completed_at=None,
            result=None,
            output=None,
            job_group=None,
            orchestration_pattern=None,
            rollback_job_id=None,
            worker_id=None,
            processing_time=None,
            queue_wait_time=None,
            dlq_reason=None,
            dlq_at=None,
        )

        # Save to MongoDB
        await self._save_tracked_job(tracked_job)

        logger.info(
            f"Created tracked job {tracked_job.id} for {entity_type}:{entity_id}"
        )
        return tracked_job

    async def update_job_status(
        self,
        job_id: Union[str, UUID],
        status: Union[str, JobStatus],
        error: Optional[str] = None,
        progress: Optional[float] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> Optional[TrackedJob]:
        """Update the status of a tracked job."""
        # Convert string status to enum if needed
        if isinstance(status, str):
            status = JobStatus(status)

        # Get existing job
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return None

        # Update fields
        old_status = tracked_job.status
        tracked_job.status = status
        tracked_job.updated_at = datetime.now(timezone.utc)

        if error:
            error_obj = JobError(
                error_type="JobExecutionError",
                error_message=error,
                error_traceback=f"old_status: {old_status}, new_status: {status}",
                retry_count=tracked_job.retry_count,
            )
            tracked_job.errors.append(error_obj)

        if progress is not None:
            if not tracked_job.progress:
                tracked_job.progress = JobProgress(
                    current_step="Processing",
                    total_steps=1,
                    current_step_number=1,
                    step_progress=progress,
                    overall_progress=progress,
                    estimated_time_remaining=None,
                    step_details=None,
                )
            else:
                tracked_job.progress.overall_progress = progress

        if output is not None:
            tracked_job.output = output

        if metadata is not None:
            tracked_job.metadata.update(metadata)

        if config is not None:
            tracked_job.config.update(config)

        # Update timing fields
        if status == JobStatus.RUNNING and not tracked_job.started_at:
            tracked_job.started_at = datetime.now(timezone.utc)
        elif status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            tracked_job.completed_at = datetime.now(timezone.utc)
            if tracked_job.started_at:
                tracked_job.processing_time = (
                    tracked_job.completed_at - tracked_job.started_at
                ).total_seconds()

        # Save updated job
        logger.info(f"Tracked job before save: {tracked_job}")
        await self._save_tracked_job(tracked_job)

        logger.info(f"Updated job {tracked_job.id} status to {status}")
        return tracked_job

    async def get_job(self, job_id: Union[str, UUID]) -> Optional[TrackedJob]:
        """Get a tracked job by ID."""
        collection = await self.get_collection("trackedjobs")

        job_data = await collection.find_one({"job_id": str(job_id)})
        logger.info(f"Job data from MongoDB: {job_data}")
        if not job_data:
            return None

        try:
            # Remove MongoDB-specific fields
            job_data.pop("_id", None)
            job_data.pop("created_at_ttl", None)
            logger.info(f"Job data after pop: {job_data}")
            return TrackedJob(**job_data)
        except Exception as e:
            logger.error(f"Error parsing job {job_id}: {e}")
            return None

    async def get_entity_jobs(
        self,
        entity_type: str,
        entity_id: str,
        status: Optional[Union[str, JobStatus]] = None,
        job_type: Optional[Union[str, JobType]] = None,
    ) -> List[TrackedJob]:
        """Get all jobs for an entity."""
        collection = await self.get_collection("trackedjobs")

        # Build query
        query = {
            "entity_type": entity_type,
            "entity_id": entity_id,
        }

        if status:
            if isinstance(status, str):
                status = JobStatus(status)
            query["status"] = status.value

        if job_type:
            if isinstance(job_type, str):
                job_type = JobType(job_type)
            query["job_type"] = job_type.value

        # Execute query
        cursor = collection.find(query).sort("created_at", DESCENDING)
        jobs = []

        async for job_data in cursor:
            try:
                # Remove MongoDB-specific fields
                job_data.pop("_id", None)
                job_data.pop("created_at_ttl", None)
                job = TrackedJob.parse_obj(job_data)
                jobs.append(job)
            except Exception as e:
                logger.error(f"Error parsing job: {e}")

        return jobs

    async def link_job_to_entity(
        self,
        job_id: Union[str, UUID],
        entity_type: str,
        entity_id: str,
    ) -> bool:
        """Link a job to an entity."""
        # This is handled automatically in MongoDB through the entity_type and entity_id fields
        return True

    async def unlink_job_from_entity(
        self,
        job_id: Union[str, UUID],
    ) -> bool:
        """Unlink a job from an entity."""
        # This would require updating the job document
        collection = await self.get_collection("trackedjobs")
        result = await collection.update_one(
            {"_id": str(job_id)}, {"$unset": {"entity_type": "", "entity_id": ""}}
        )
        return result.modified_count > 0

    async def update_job_progress(
        self,
        job_id: Union[str, UUID],
        progress: JobProgress,
    ) -> bool:
        """Update job progress information."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return False

        tracked_job.progress = progress
        tracked_job.updated_at = datetime.now(timezone.utc)

        await self._save_tracked_job(tracked_job)
        return True

    async def add_job_error(
        self,
        job_id: Union[str, UUID],
        error: JobError,
    ) -> bool:
        """Add an error to a job's error list."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return False

        tracked_job.errors.append(error)
        tracked_job.updated_at = datetime.now(timezone.utc)

        await self._save_tracked_job(tracked_job)
        return True

    async def retry_job(
        self,
        job_id: Union[str, UUID],
        delay_seconds: int = 0,
    ) -> bool:
        """Retry a failed job."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return False

        # Check if we can retry
        if tracked_job.retry_count >= tracked_job.max_retries:
            await self.move_to_dlq(tracked_job.id, "Max retries exceeded")
            return False

        # Update retry count and status
        tracked_job.retry_count += 1
        tracked_job.status = JobStatus.RETRYING
        tracked_job.updated_at = datetime.now(timezone.utc)

        await self._save_tracked_job(tracked_job)

        logger.info(
            f"Retrying job {tracked_job.id} (attempt {tracked_job.retry_count})"
        )
        return True

    async def cancel_job(self, job_id: Union[str, UUID]) -> bool:
        """Cancel a job."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return False

        tracked_job.status = JobStatus.CANCELLED
        tracked_job.updated_at = datetime.now(timezone.utc)

        await self._save_tracked_job(tracked_job)
        return True

    async def move_to_dlq(
        self,
        job_id: Union[str, UUID],
        reason: str,
    ) -> bool:
        """Move a job to the dead letter queue."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job:
            return False

        # Create DLQ entry
        dlq_job = DeadLetterJob(
            job_id=tracked_job.job_id,
            tracked_job_id=tracked_job.id,
            job_type=tracked_job.job_type,
            entity_type=tracked_job.entity_type,
            entity_id=tracked_job.entity_id,
            reason=reason,
            error_count=tracked_job.retry_count,
            last_error=tracked_job.errors[-1]
            if tracked_job.errors
            else JobError(
                error_type="Unknown",
                error_message=reason,
                error_traceback=None,
            ),
            retry_after=None,
            original_payload=tracked_job.payload,
            metadata=tracked_job.metadata,
        )

        # Save DLQ job
        await self._save_dlq_job(dlq_job)

        # Update tracked job
        tracked_job.status = JobStatus.FAILED
        tracked_job.dlq_reason = reason
        tracked_job.dlq_at = datetime.now(timezone.utc)
        tracked_job.updated_at = datetime.now(timezone.utc)

        await self._save_tracked_job(tracked_job)

        logger.warning(f"Job {tracked_job.id} moved to DLQ: {reason}")
        return True

    async def get_dlq_jobs(
        self,
        limit: int = 100,
        offset: int = 0,
    ) -> List[DeadLetterJob]:
        """Get jobs from the dead letter queue."""
        collection = await self.get_collection("dlqjobs")

        cursor = (
            collection.find()
            .skip(offset)
            .limit(limit)
            .sort("moved_to_dlq_at", DESCENDING)
        )
        dlq_jobs = []

        async for job_data in cursor:
            try:
                # Remove MongoDB-specific fields
                job_data.pop("_id", None)
                job_data.pop("moved_to_dlq_at_ttl", None)
                dlq_job = DeadLetterJob.parse_obj(job_data)
                dlq_jobs.append(dlq_job)
            except Exception as e:
                logger.error(f"Error parsing DLQ job: {e}")

        return dlq_jobs

    async def retry_dlq_job(self, job_id: Union[str, UUID]) -> bool:
        """Retry a job from the dead letter queue."""
        tracked_job = await self.get_job(job_id)
        if not tracked_job or tracked_job.status != JobStatus.FAILED:
            return False

        # Remove from DLQ
        collection = await self.get_collection("dlqjobs")
        await collection.delete_one({"tracked_job_id": str(job_id)})

        # Reset job for retry
        tracked_job.status = JobStatus.PENDING
        tracked_job.retry_count = 0
        tracked_job.dlq_reason = None
        tracked_job.dlq_at = None
        tracked_job.updated_at = datetime.now(timezone.utc)

        logger.info(f"Tracked job before save: {tracked_job}")

        await self._save_tracked_job(tracked_job)

        logger.info(f"DLQ job {tracked_job.id} retried")
        return True

    async def get_job_stats(
        self,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        job_type: Optional[Union[str, JobType]] = None,
        status: Optional[Union[str, JobStatus]] = None,
    ) -> Optional[JobStats]:
        """Get job statistics."""
        collection = await self.get_collection("trackedjobs")

        # Build aggregation pipeline
        match_stage = {}
        if entity_type and entity_id:
            match_stage.update({
                "entity_type": entity_type,
                "entity_id": entity_id,
            })

        if job_type:
            if isinstance(job_type, str):
                job_type = JobType(job_type)
            match_stage["job_type"] = job_type.value

        if status:
            if isinstance(status, str):
                status = JobStatus(status)
            match_stage["status"] = status.value

        pipeline = []
        if match_stage:
            pipeline.append({"$match": match_stage})

        pipeline.extend([
            {
                "$group": {
                    "_id": None,
                    "total_jobs": {"$sum": 1},
                    "pending_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "pending"]}, 1, 0]}
                    },
                    "running_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "running"]}, 1, 0]}
                    },
                    "completed_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "retrying_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "retrying"]}, 1, 0]}
                    },
                    "dead_letter_jobs": {
                        "$sum": {"$cond": [{"$eq": ["$status", "dead_letter"]}, 1, 0]}
                    },
                    "avg_processing_time": {"$avg": "$processing_time"},
                }
            }
        ])

        result = await collection.aggregate(pipeline).to_list(1)

        if result:
            data = result[0]
            stats = JobStats(
                total_jobs=data.get("total_jobs", 0),
                pending_jobs=data.get("pending_jobs", 0),
                running_jobs=data.get("running_jobs", 0),
                completed_jobs=data.get("completed_jobs", 0),
                failed_jobs=data.get("failed_jobs", 0),
                retrying_jobs=data.get("retrying_jobs", 0),
                dead_letter_jobs=data.get("dead_letter_jobs", 0),
                avg_processing_time=data.get("avg_processing_time"),
                avg_queue_wait_time=None,
                success_rate=None,
            )

            if stats.total_jobs > 0:
                stats.success_rate = stats.completed_jobs / stats.total_jobs

            return stats

    async def cleanup_old_jobs(
        self,
        days_old: int = 30,
        status: Optional[Union[str, JobStatus]] = None,
    ) -> int:
        """Clean up old jobs."""
        collection = await self.get_collection("trackedjobs")

        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        query = {"created_at": {"$lt": cutoff_date}}

        if status:
            if isinstance(status, str):
                status = JobStatus(status)
            query["status"] = status.value  # type: ignore

        result = await collection.delete_many(query)
        deleted_count = result.deleted_count

        logger.info(f"Cleaned up {deleted_count} old jobs from MongoDB")
        return deleted_count

    async def get_jobs_by_dependencies(
        self,
        dependency_job_ids: List[Union[str, UUID]],
    ) -> List[TrackedJob]:
        """Get jobs that depend on the specified job IDs."""
        collection = await self.get_collection("trackedjobs")

        # Convert UUIDs to strings for MongoDB query
        dependency_ids = [str(job_id) for job_id in dependency_job_ids]

        # Find jobs that have any of the dependency IDs in their depends_on list
        cursor = collection.find({"depends_on": {"$in": dependency_ids}})

        jobs = []
        async for doc in cursor:
            try:
                job = TrackedJob(**doc)
                jobs.append(job)
            except Exception as e:
                logger.warning(f"Failed to parse job document: {e}")

        logger.debug(f"Found {len(jobs)} jobs depending on {len(dependency_ids)} jobs")
        return jobs

    async def get_jobs_by_group(
        self,
        job_group: str,
    ) -> List[TrackedJob]:
        """Get all jobs in a specific job group."""
        collection = await self.get_collection("trackedjobs")

        cursor = collection.find({"metadata.job_group": job_group})

        jobs = []
        async for doc in cursor:
            try:
                job = TrackedJob(**doc)
                jobs.append(job)
            except Exception as e:
                logger.warning(f"Failed to parse job document: {e}")

        logger.debug(f"Found {len(jobs)} jobs in group {job_group}")
        return jobs

    async def get_jobs_by_filters(
        self,
        filters: Dict[str, Any],
    ) -> List[TrackedJob]:
        """Get jobs based on multiple filters."""
        collection = await self.get_collection("trackedjobs")

        # Build MongoDB query from filters
        query = {}
        for key, value in filters.items():
            if key == "status" and isinstance(value, str):
                query[key] = value
            elif key == "job_group":
                query["metadata.job_group"] = value
            elif key == "entity_type":
                query[key] = value
            elif key == "entity_id":
                query[key] = value
            elif key == "job_type" and isinstance(value, str):
                query[key] = value
            else:
                query[key] = value

        cursor = collection.find(query)

        jobs = []
        async for doc in cursor:
            try:
                job = TrackedJob(**doc)
                jobs.append(job)
            except Exception as e:
                logger.warning(f"Failed to parse job document: {e}")

        logger.debug(f"Found {len(jobs)} jobs matching filters: {filters}")
        return jobs

    # Helper methods

    async def _save_tracked_job(self, tracked_job: TrackedJob) -> None:
        """Save a tracked job to MongoDB."""
        collection = await self.get_collection("trackedjobs")

        # Convert to dict and add TTL field
        job_dict = tracked_job.dict()
        job_dict["_id"] = str(tracked_job.id)
        job_dict["created_at_ttl"] = datetime.now(timezone.utc) + timedelta(
            days=self.ttl_days
        )

        # Convert UUID fields to strings for MongoDB compatibility
        if "id" in job_dict:
            job_dict["id"] = str(job_dict["id"])
        if "parent_job_id" in job_dict and job_dict["parent_job_id"]:
            job_dict["parent_job_id"] = str(job_dict["parent_job_id"])
        if "rollback_job_id" in job_dict and job_dict["rollback_job_id"]:
            job_dict["rollback_job_id"] = str(job_dict["rollback_job_id"])
        if "depends_on" in job_dict:
            job_dict["depends_on"] = [str(uuid) for uuid in job_dict["depends_on"]]
        if "dependency_details" in job_dict:
            for dep in job_dict["dependency_details"]:
                if "job_id" in dep:
                    dep["job_id"] = str(dep["job_id"])

        try:
            await collection.replace_one(
                {"_id": str(tracked_job.id)}, job_dict, upsert=True
            )
        except DuplicateKeyError:
            # Handle race condition
            await collection.update_one(
                {"_id": str(tracked_job.id)}, {"$set": job_dict}
            )

    async def _save_dlq_job(self, dlq_job: DeadLetterJob) -> None:
        """Save a DLQ job to MongoDB."""
        collection = await self.get_collection("dlqjobs")

        # Convert to dict and add TTL field
        job_dict = dlq_job.dict()
        job_dict["_id"] = str(dlq_job.tracked_job_id)
        job_dict["moved_to_dlq_at_ttl"] = datetime.now(timezone.utc) + timedelta(
            days=self.ttl_days
        )

        try:
            await collection.replace_one(
                {"_id": str(dlq_job.tracked_job_id)}, job_dict, upsert=True
            )
        except DuplicateKeyError:
            # Handle race condition
            await collection.update_one(
                {"_id": str(dlq_job.tracked_job_id)}, {"$set": job_dict}
            )

    async def _create_indexes(self) -> None:
        """Create indexes for efficient querying."""
        # Tracked jobs collection
        tracked_jobs = await self.get_collection("trackedjobs")

        # Create indexes
        await tracked_jobs.create_index([
            ("entity_type", ASCENDING),
            ("entity_id", ASCENDING),
        ])
        await tracked_jobs.create_index([("job_type", ASCENDING)])
        await tracked_jobs.create_index([("status", ASCENDING)])
        await tracked_jobs.create_index([("created_at", DESCENDING)])
        await tracked_jobs.create_index([("updated_at", DESCENDING)])
        await tracked_jobs.create_index([("parent_job_id", ASCENDING)])
        await tracked_jobs.create_index([("queue_job_id", ASCENDING)])

        # DLQ jobs collection
        dlq_jobs = await self.get_collection("dlqjobs")
        await dlq_jobs.create_index([("tracked_job_id", ASCENDING)])
        await dlq_jobs.create_index([("moved_to_dlq_at", DESCENDING)])
        await dlq_jobs.create_index([
            ("entity_type", ASCENDING),
            ("entity_id", ASCENDING),
        ])

    async def _create_ttl_indexes(self) -> None:
        """Create TTL indexes for automatic cleanup."""
        # Tracked jobs TTL
        tracked_jobs = await self.get_collection("trackedjobs")
        await tracked_jobs.create_index("created_at_ttl", expireAfterSeconds=0)

        # DLQ jobs TTL
        dlq_jobs = await self.get_collection("dlqjobs")
        await dlq_jobs.create_index("moved_to_dlq_at_ttl", expireAfterSeconds=0)
