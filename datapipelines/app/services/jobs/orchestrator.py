"""
Job Orchestrator Service for TractionX Data Pipeline Service

This module provides sophisticated job orchestration capabilities without the complexity
of barrier sync systems. It handles job dependencies, triggers, and various orchestration patterns.
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from app.configs import get_logger
from app.models.job_tracking import Job<PERSON>tatus, JobType, TrackedJob
from app.queueing.interfaces import JobPriority
from app.services.jobs.factory import get_job_service

logger = get_logger(__name__)


class JobOrchestrator:
    """
    Sophisticated job orchestrator for managing complex job workflows.

    Provides dependency management, job triggering, and orchestration patterns
    without the complexity of barrier sync systems.
    """

    def __init__(self, queue_service=None):
        self._job_service = None
        self._queue_service = queue_service
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the orchestrator."""
        if self._initialized:
            return

        self._job_service = await get_job_service()
        # Queue service will be injected when needed
        self._initialized = True
        logger.info("Job orchestrator initialized")

    async def create_job_chain(
        self,
        entity_type: str,
        entity_id: str,
        job_configs: List[Dict[str, Any]],
        job_group: Optional[str] = None,
        orchestration_pattern: Optional[str] = None,
        queue_service=None,
    ) -> List[TrackedJob]:
        """
        Create a chain of jobs with dependencies and enqueue them.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            job_configs: List of job configurations
            job_group: Optional job group for coordination
            orchestration_pattern: Optional orchestration pattern name
            queue_service: Queue service for enqueueing jobs

        Returns:
            List of created tracked jobs
        """
        await self.initialize()

        created_jobs: List[TrackedJob] = []
        previous_job = None

        for i, config in enumerate(job_configs):
            # Create the job using centralized job creation
            if queue_service:
                job_id = await queue_service.create_job(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    job_type=config.get("job_type"),
                    payload=config.get("payload", {}),
                    metadata=config.get("metadata"),
                    config=config.get("config"),
                    priority=config.get("priority", JobPriority.NORMAL),
                    parent_job_id=previous_job.id if previous_job else None,
                    depends_on=[previous_job.id] if previous_job else None,
                    scheduled_at=config.get("scheduled_at"),
                    queue_priority=config.get("queue_priority"),
                    queue_type=config.get("queue_type", "default"),
                    queue_job_type=config.get("queue_job_type"),
                    job_group=job_group,
                    orchestration_pattern=orchestration_pattern,
                    timeout=config.get("timeout"),
                    retry=config.get("retry"),
                    triggers=config.get("triggers"),
                    wait_for=config.get("wait_for"),
                    conditions=config.get("conditions"),
                )

                # Job creation and enqueueing handled by QueueServiceV2.create_job()
                # No need to retrieve job again - it's already created and stored
                logger.debug(f"Job created and enqueued via QueueServiceV2: {job_id}")
            else:
                logger.warning("No queue service provided, skipping job creation")

        logger.info(
            f"Created job chain with {len(created_jobs)} jobs for {entity_type}:{entity_id}"
        )
        return created_jobs

    async def create_parallel_jobs(
        self,
        entity_type: str,
        entity_id: str,
        job_configs: List[Dict[str, Any]],
        job_group: Optional[str] = None,
        parent_job_id: Optional[UUID] = None,
        queue_service=None,
    ) -> List[TrackedJob]:
        """
        Create multiple jobs that can run in parallel and enqueue them.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            job_configs: List of job configurations
            job_group: Optional job group for coordination
            parent_job_id: Optional parent job ID
            queue_service: Queue service for enqueueing jobs

        Returns:
            List of created tracked jobs
        """
        await self.initialize()

        created_jobs: List[TrackedJob] = []

        for config in job_configs:
            # Create the job using centralized job creation
            if queue_service:
                job_id = await queue_service.create_job(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    job_type=config.get("job_type"),
                    payload=config.get("payload", {}),
                    metadata=config.get("metadata"),
                    config=config.get("config"),
                    priority=config.get("priority", JobPriority.NORMAL),
                    parent_job_id=parent_job_id,
                    depends_on=config.get("depends_on"),
                    scheduled_at=config.get("scheduled_at"),
                    queue_priority=config.get("queue_priority"),
                    queue_type=config.get("queue_type", "default"),
                    queue_job_type=config.get("queue_job_type"),
                    job_group=job_group,
                    orchestration_pattern=config.get("orchestration_pattern"),
                    timeout=config.get("timeout"),
                    retry=config.get("retry"),
                    triggers=config.get("triggers"),
                    wait_for=config.get("wait_for"),
                    conditions=config.get("conditions"),
                )

                # Job creation and enqueueing handled by QueueServiceV2.create_job()
                # No need to retrieve job again - it's already created and stored
                logger.debug(
                    f"Parallel job created and enqueued via QueueServiceV2: {job_id}"
                )
            else:
                logger.warning("No queue service provided, skipping job creation")

        logger.info(
            f"Created {len(created_jobs)} parallel jobs for {entity_type}:{entity_id}"
        )
        return created_jobs

    async def trigger_dependent_jobs(
        self, completed_job_id: UUID, queue_service
    ) -> List[TrackedJob]:
        """
        When a job completes, trigger its dependent jobs.

        Args:
            completed_job_id: ID of the completed job
            queue_service: Queue service for enqueueing jobs

        Returns:
            List of triggered jobs
        """
        await self.initialize()

        if not self._job_service:
            logger.error("Job service not initialized")
            return []

        # Get the completed job
        completed_job = await self._job_service.get_job(completed_job_id)
        if not completed_job:
            logger.warning(f"Completed job {completed_job_id} not found")
            return []

        # Find jobs that depend on this completed job
        dependent_jobs = await self._job_service.get_jobs_by_dependencies([
            completed_job_id
        ])

        triggered_jobs: List[TrackedJob] = []

        for dep_job in dependent_jobs:
            # Check if the dependent job is ready to execute
            if await self._is_job_ready(dep_job):
                # Mark dependency as completed
                dep_job.mark_dependency_completed(completed_job_id)

                # Update job status
                await self._job_service.update_job_status(
                    dep_job.id,
                    JobStatus.QUEUED,
                    output={"dependency_completed": str(completed_job_id)},
                )

                # Enqueue the job
                await queue_service.enqueue_job(
                    job_func=dep_job.job_type.value
                    if isinstance(dep_job.job_type, JobType)
                    else str(dep_job.job_type),
                    job_args=dep_job.payload,
                    meta={
                        "tracked_job_id": str(dep_job.id),
                        "entity_type": dep_job.entity_type,
                        "entity_id": dep_job.entity_id,
                    },
                )

                triggered_jobs.append(dep_job)
                logger.info(
                    f"Triggered dependent job {dep_job.id} ({dep_job.job_type})"
                )

        logger.info(
            f"Triggered {len(triggered_jobs)} dependent jobs for completed job {completed_job_id}"
        )
        return triggered_jobs

    async def check_job_readiness(self, job_id: UUID) -> bool:
        """
        Check if a job is ready to execute (all dependencies met).

        Args:
            job_id: Job ID to check

        Returns:
            True if job is ready to execute
        """
        await self.initialize()

        if not self._job_service:
            logger.error("Job service not initialized")
            return False

        job = await self._job_service.get_job(job_id)
        if not job:
            return False

        return await self._is_job_ready(job)

    async def _is_job_ready(self, job: TrackedJob) -> bool:
        """
        Check if a job is ready to execute.

        Args:
            job: Job to check

        Returns:
            True if job is ready to execute
        """
        # Check if job is in a valid state
        if job.status not in [JobStatus.QUEUED, JobStatus.RUNNING]:
            return False

        # Check if all dependencies are completed
        if job.total_dependencies > 0:
            if job.completed_dependencies < job.total_dependencies:
                return False

        # Legacy check for backward compatibility
        if job.depends_on and self._job_service:
            for dep_job_id in job.depends_on:
                dep_job = await self._job_service.get_job(dep_job_id)
                if not dep_job or dep_job.status != JobStatus.COMPLETED:
                    return False

        return True

    async def get_job_group_status(self, job_group: str) -> Dict[str, Any]:
        """
        Get status of all jobs in a job group.

        Args:
            job_group: Job group name

        Returns:
            Job group status information
        """
        await self.initialize()

        if not self._job_service:
            logger.error("Job service not initialized")
            return {
                "job_group": job_group,
                "total_jobs": 0,
                "completed_jobs": 0,
                "failed_jobs": 0,
                "pending_jobs": 0,
                "running_jobs": 0,
                "progress": 0.0,
                "status": "error",
            }

        jobs = await self._job_service.get_jobs_by_group(job_group)

        if not jobs:
            return {
                "job_group": job_group,
                "total_jobs": 0,
                "completed_jobs": 0,
                "failed_jobs": 0,
                "pending_jobs": 0,
                "running_jobs": 0,
                "progress": 0.0,
                "status": "not_found",
            }

        # Calculate statistics
        total_jobs = len(jobs)
        completed_jobs = len([j for j in jobs if j.status == JobStatus.COMPLETED])
        failed_jobs = len([j for j in jobs if j.status == JobStatus.FAILED])
        pending_jobs = len([j for j in jobs if j.status == JobStatus.PENDING])
        running_jobs = len([j for j in jobs if j.status == JobStatus.RUNNING])

        progress = completed_jobs / total_jobs if total_jobs > 0 else 0.0

        # Determine overall status
        if failed_jobs > 0:
            status = "failed"
        elif completed_jobs == total_jobs:
            status = "completed"
        elif running_jobs > 0 or pending_jobs > 0:
            status = "in_progress"
        else:
            status = "unknown"

        return {
            "job_group": job_group,
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "pending_jobs": pending_jobs,
            "running_jobs": running_jobs,
            "progress": progress,
            "status": status,
            "jobs": [
                {
                    "id": str(job.id),
                    "job_type": job.job_type,
                    "status": job.status,
                    "created_at": job.created_at.isoformat(),
                    "updated_at": job.updated_at.isoformat(),
                }
                for job in jobs
            ],
        }

    async def cleanup_job_group(self, job_group: str) -> bool:
        """
        Clean up all jobs in a job group.

        Args:
            job_group: Job group name

        Returns:
            True if cleanup successful
        """
        await self.initialize()

        if not self._job_service:
            logger.error("Job service not initialized")
            return False

        jobs = await self._job_service.get_jobs_by_group(job_group)

        if not jobs:
            logger.info(f"No jobs found for group {job_group}")
            return True

        # Cancel all pending/running jobs
        for job in jobs:
            if job.status in [JobStatus.PENDING, JobStatus.RUNNING]:
                await self._job_service.update_job_status(
                    job.id, JobStatus.CANCELLED, output={"reason": "job_group_cleanup"}
                )

        logger.info(f"Cleaned up {len(jobs)} jobs in group {job_group}")
        return True

    async def retry_failed_jobs(
        self,
        job_group: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
    ) -> List[TrackedJob]:
        """
        Retry failed jobs.

        Args:
            job_group: Optional job group filter
            entity_type: Optional entity type filter
            entity_id: Optional entity ID filter

        Returns:
            List of retried jobs
        """
        await self.initialize()

        if not self._job_service:
            logger.error("Job service not initialized")
            return []

        # Get failed jobs based on filters
        filters = {"status": JobStatus.FAILED.value}
        if job_group:
            filters["job_group"] = job_group
        if entity_type:
            filters["entity_type"] = entity_type
        if entity_id:
            filters["entity_id"] = entity_id

        failed_jobs = await self._job_service.get_jobs_by_filters(filters)

        retried_jobs: List[TrackedJob] = []

        for job in failed_jobs:
            # Reset job status
            await self._job_service.update_job_status(
                job.id, JobStatus.PENDING, output={"retry_attempt": job.retry_count + 1}
            )

            retried_jobs.append(job)
            logger.info(f"Retried failed job {job.id} ({job.job_type})")

        logger.info(f"Retried {len(retried_jobs)} failed jobs")
        return retried_jobs

    def _ensure_serializable(self, data: Any) -> Any:
        """
        Ensure data is serializable for Redis using the base model serialization.

        Args:
            data: Data to serialize

        Returns:
            Serializable version of the data
        """
        try:
            # Import here to avoid circular imports
            from app.models.base import TractionXModel

            if isinstance(data, dict):
                return {str(k): self._ensure_serializable(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [self._ensure_serializable(item) for item in data]
            elif isinstance(data, (str, int, float, bool, type(None))):
                return data
            elif hasattr(data, "model_dump"):  # Pydantic v2 models
                serialized = data.model_dump(mode="json", by_alias=True)
                # Recursively ensure the serialized data is also serializable
                return self._ensure_serializable(serialized)
            elif hasattr(data, "dict"):  # Pydantic v1 models
                serialized = data.dict(by_alias=True)
                # Recursively ensure the serialized data is also serializable
                return self._ensure_serializable(serialized)
            else:
                # Use base model serialization for any other type
                serialized = TractionXModel.serialize_value(data, for_db=False)
                # Recursively ensure the serialized data is also serializable
                return self._ensure_serializable(serialized)
        except Exception as e:
            logger.warning(f"Error serializing data: {e}, converting to string")
            return str(data)


# Global orchestrator instance
_orchestrator: Optional[JobOrchestrator] = None


async def get_job_orchestrator() -> JobOrchestrator:
    """Get or create the global job orchestrator instance."""
    global _orchestrator

    if _orchestrator is None:
        _orchestrator = JobOrchestrator()
        await _orchestrator.initialize()

    return _orchestrator
