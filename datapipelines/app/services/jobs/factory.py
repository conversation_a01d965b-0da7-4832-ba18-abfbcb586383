"""
Job Service Factory

This module provides factory functions for creating MongoDB job tracking services,
following clean and simple patterns.
"""

from typing import Optional

from app.configs import settings
from app.services.jobs.interfaces import JobServiceInterface
from app.services.jobs.mongo import MongoJobService

# Global service instance
_job_service: Optional[JobServiceInterface] = None


async def get_job_service() -> JobServiceInterface:
    """
    Get the global job service instance.

    Returns:
        The job service instance
    """
    global _job_service

    if _job_service is None:
        _job_service = await create_job_service()
        await _job_service.initialize()

    return _job_service


async def create_job_service(**kwargs) -> JobServiceInterface:
    """
    Create a MongoDB job service instance.

    Args:
        **kwargs: Additional configuration parameters

    Returns:
        MongoDB job service instance
    """
    return MongoJobService(
        mongo_url=kwargs.get("mongo_url", settings.mongo_connection_string),
        database_name=kwargs.get("database_name", "tx_datapipelines"),
        collection_prefix=kwargs.get("collection_prefix", "tx_datapipelines"),
        ttl_days=kwargs.get("ttl_days", 30),
    )


async def cleanup_job_service() -> None:
    """Clean up the global job service instance."""
    global _job_service

    if _job_service:
        await _job_service.cleanup()
        _job_service = None
