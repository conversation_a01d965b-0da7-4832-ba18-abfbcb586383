"""
Job Creation Service

This module provides a dedicated service for job creation, separating this responsibility
from orchestration and queueing concerns.
"""

from typing import Any, Dict, Optional, Union
from uuid import UUID

from app.configs import get_logger
from app.models.job_tracking import JobType, TrackedJob
from app.utils.jobs import JobBuilder

logger = get_logger(__name__)


class JobCreatorService:
    """
    Dedicated service for job creation.

    This service handles all job creation logic, providing a clean interface
    for creating tracked jobs with proper configuration.
    """

    async def create_job(
        self,
        entity_type: str,
        entity_id: Optional[str],
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 0,
        parent_job_id: Optional[Union[str, UUID]] = None,
        depends_on: Optional[list] = None,
        scheduled_at: Optional[int] = None,
        queue_priority: Optional[str] = None,
        queue_type: Optional[str] = None,
        queue_job_type: Optional[str] = None,
        job_group: Optional[str] = None,
        orchestration_pattern: Optional[str] = None,
        timeout: Optional[int] = None,
        retry: Optional[Dict[str, Any]] = None,
    ) -> TrackedJob:
        """
        Create a tracked job using JobBuilder.

        Args:
            entity_type: Type of entity
            entity_id: Entity ID
            job_type: Type of job to create
            payload: Job payload data
            metadata: Additional metadata
            config: Job configuration
            priority: Job priority (0-100)
            parent_job_id: Parent job ID
            depends_on: List of job dependencies
            scheduled_at: Scheduled execution time
            queue_priority: Queue priority
            queue_type: Queue type
            queue_job_type: Queue job type
            job_group: Job group for coordination
            orchestration_pattern: Orchestration pattern
            timeout: Job timeout in seconds
            retry: Retry configuration

        Returns:
            The created tracked job
        """
        job_builder = JobBuilder()

        # Apply entity information
        job_builder.for_entity(entity_type, entity_id or None)

        # Apply job type
        job_builder.with_job_type(job_type)

        # Apply payload
        job_builder.with_payload(**payload)

        # Apply metadata
        if metadata:
            job_builder.with_metadata(**metadata)

        # Apply configuration
        if config:
            job_builder.with_config(**config)

        # Apply priority
        job_builder.with_priority(priority)

        # Apply parent job
        if parent_job_id:
            job_builder.with_parent(parent_job_id)

        # Apply dependencies
        if depends_on:
            job_builder.depends_on(*depends_on)

        # Apply scheduling
        if scheduled_at:
            job_builder.scheduled_at(scheduled_at)

        # Apply queue configuration
        if any([queue_priority, queue_type, queue_job_type]):
            job_builder.with_queue_config(
                job_type=queue_job_type,
                priority=queue_priority,
                queue_type=queue_type,
            )

        # Apply job group
        if job_group:
            job_builder.with_job_group(job_group)

        # Apply orchestration pattern
        if orchestration_pattern:
            job_builder.with_orchestration_pattern(orchestration_pattern)

        # Apply timeout
        if timeout:
            job_builder.with_timeout(timeout)

        # Apply retry configuration
        if retry:
            job_builder.with_retry(**retry)

        # Generate job_id and create the job
        from time import time

        job_id = str(int(time() * 1000))
        tracked_job = await job_builder.create(job_id=job_id)

        logger.info(
            f"Created tracked job {tracked_job.id} for {entity_type}:{entity_id}"
        )

        return tracked_job


# Global service instance
_job_creator: Optional[JobCreatorService] = None


async def get_job_creator() -> JobCreatorService:
    """Get the global job creator service instance."""
    global _job_creator

    if _job_creator is None:
        _job_creator = JobCreatorService()

    return _job_creator
