"""
Crunchbase URL Resolver Service for TractionX Data Pipeline Service.

This module provides automatic resolution of company Crunchbase URLs using:
1. Serper API for Google search
2. LLM agent for link selection
3. BrightData for company data scraping
"""

import json
import re
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import httpx
from app.clients.ai.together import TogetherClient
from app.configs import get_logger, settings
from app.models.crunchbase import (
    CrunchbaseResolverInput,
    CrunchbaseResolverOutput,
    SerperSearchResponse,
)
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class CrunchbaseResolverService:
    """
    Crunchbase URL resolver service.

    Handles:
    - Serper API search for Crunchbase URLs
    - LLM-based link selection and validation
    - BrightData scraping trigger
    - S3 storage of raw data
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.CrunchbaseResolverService")

        # API clients
        self.serper_client: Optional[httpx.AsyncClient] = None
        self.brightdata_client: Optional[httpx.AsyncClient] = None
        self.together_client: Optional[TogetherClient] = None

        # Configuration
        self.serper_api_key = settings.SERPAPI_KEY
        self.brightdata_api_key = settings.BRIGHTDATA_API_KEY
        self.together_api_key = settings.TOGETHER_API_KEY
        self.brightdata_dataset_id = "gd_l1vijqt9jfj7olije"  # Crunchbase dataset ID

        # S3 storage
        self.s3_storage: Optional[S3Storage] = None

        # Timeouts and retries
        self.timeout = 30.0
        self.max_retries = 3
        self.retry_delay = 2.0

    async def initialize(self) -> None:
        """Initialize the Crunchbase resolver service."""
        try:
            self.logger.info("Starting Crunchbase resolver service initialization")

            # Check API keys
            if not self.serper_api_key:
                raise ValueError("SERPAPI_KEY not configured")
            if not self.brightdata_api_key:
                raise ValueError("BRIGHTDATA_API_KEY not configured")
            if not self.together_api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            self.logger.info("API keys validated successfully")

            # Initialize HTTP clients
            self.serper_client = httpx.AsyncClient(
                base_url="https://google.serper.dev",
                headers={
                    "X-API-KEY": self.serper_api_key,
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            self.brightdata_client = httpx.AsyncClient(
                base_url="https://api.brightdata.com",
                headers={
                    "Authorization": f"Bearer {self.brightdata_api_key}",
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            # Initialize Together AI client
            self.together_client = TogetherClient()
            await self.together_client.initialize()

            # Initialize S3 storage
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

            self.logger.info("Crunchbase resolver service initialized successfully")

        except Exception as e:
            self.logger.error(
                f"Failed to initialize Crunchbase resolver service: {str(e)}",
                exc_info=True,
            )
            raise

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.serper_client:
            await self.serper_client.aclose()
        if self.brightdata_client:
            await self.brightdata_client.aclose()
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("Crunchbase resolver service cleaned up")

    async def resolve_crunchbase_url(
        self, input_data: CrunchbaseResolverInput
    ) -> CrunchbaseResolverOutput:
        """
        Main method to resolve Crunchbase URL and trigger BrightData scraping.

        Args:
            input_data: Input data containing company domain and description

        Returns:
            CrunchbaseResolverOutput with processing results
        """
        start_time = time.time()

        try:
            self.logger.info(
                "Starting Crunchbase URL resolution",
                company_domain=input_data.company_domain,
                job_id=input_data.job_id,
            )

            # Step 1: Search for Crunchbase URLs using Serper
            serper_result = await self._search_crunchbase_urls(
                input_data.company_domain
            )
            if not serper_result["success"]:
                return CrunchbaseResolverOutput(
                    status="failed",
                    company_domain=input_data.company_domain,
                    crunchbase_url=None,
                    brightdata_snapshot_id=None,
                    log=f"Serper search failed: {serper_result['error']}",
                    error_message=serper_result["error"],
                    error_stage="serper_search",
                    processing_time=time.time() - start_time,
                    llm_decision=None,
                    serper_results_count=None,
                    completed_at=None,
                )

            serper_response = serper_result["data"]
            self.logger.info(
                "Serper search completed",
                results_count=len(serper_response.organic),
                company_domain=input_data.company_domain,
            )

            # Step 2: Use LLM to select the best Crunchbase URL
            llm_result = await self._select_crunchbase_url_with_llm(
                input_data.company_domain,
                input_data.company_description or "",
                serper_response,
            )

            if not llm_result["success"]:
                return CrunchbaseResolverOutput(
                    status="failed",
                    company_domain=input_data.company_domain,
                    crunchbase_url=None,
                    brightdata_snapshot_id=None,
                    log=f"LLM selection failed: {llm_result['error']}",
                    error_message=llm_result["error"],
                    error_stage="llm_selection",
                    processing_time=time.time() - start_time,
                    llm_decision=None,
                    serper_results_count=len(serper_response.organic),
                    completed_at=None,
                )

            selected_url = llm_result["url"]
            self.logger.info(
                f"LLM selected URL: {selected_url}",
                company_domain=input_data.company_domain,
            )

            # Step 3: Validate the selected URL
            if not self._is_valid_crunchbase_url(selected_url):
                return CrunchbaseResolverOutput(
                    status="no_match",
                    company_domain=input_data.company_domain,
                    crunchbase_url=None,
                    brightdata_snapshot_id=None,
                    log="LLM returned invalid URL format",
                    error_message=None,
                    error_stage=None,
                    processing_time=time.time() - start_time,
                    llm_decision=selected_url,
                    serper_results_count=len(serper_response.organic),
                    completed_at=None,
                )

            # Step 4: Trigger BrightData scraping
            brightdata_result = await self._trigger_brightdata_scrape(selected_url)
            if not brightdata_result["success"]:
                return CrunchbaseResolverOutput(
                    status="brightdata_failed",
                    company_domain=input_data.company_domain,
                    crunchbase_url=selected_url,
                    log=f"BrightData trigger failed: {brightdata_result['error']}",
                    error_message=brightdata_result["error"],
                    error_stage="brightdata_trigger",
                    llm_decision=selected_url,
                    brightdata_snapshot_id=None,
                    completed_at=datetime.now(timezone.utc),
                    processing_time=time.time() - start_time,
                    serper_results_count=len(serper_response.organic),
                )

            snapshot_id = brightdata_result["snapshot_id"]

            # Step 5: Store raw data to S3
            s3_key = await self._store_raw_data(
                input_data.company_domain,
                input_data.org_id,
                snapshot_id,
                {
                    "serper_response": serper_response.model_dump(),
                    "llm_decision": selected_url,
                    "brightdata_snapshot_id": snapshot_id,
                    "input_data": input_data.model_dump(),
                },
            )
            logger.info(f"Raw data stored to S3: {s3_key}")

            processing_time = time.time() - start_time

            self.logger.info(
                "Crunchbase URL resolution completed successfully",
                company_domain=input_data.company_domain,
                crunchbase_url=selected_url,
                snapshot_id=snapshot_id,
                processing_time=processing_time,
            )

            return CrunchbaseResolverOutput(
                status="completed",
                company_domain=input_data.company_domain,
                crunchbase_url=selected_url,
                brightdata_snapshot_id=snapshot_id,
                log="Scrape triggered and validated successfully",
                error_message=None,
                error_stage=None,
                processing_time=processing_time,
                llm_decision=selected_url,
                serper_results_count=len(serper_response.organic),
                completed_at=datetime.now(timezone.utc),
            )

        except Exception as e:
            error_msg = f"Crunchbase URL resolution failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return CrunchbaseResolverOutput(
                status="error",
                company_domain=input_data.company_domain,
                crunchbase_url=None,
                brightdata_snapshot_id=None,
                log=error_msg,
                error_message=str(e),
                error_stage="unknown",
                processing_time=time.time() - start_time,
                llm_decision=None,
                serper_results_count=None,
                completed_at=None,
            )

    async def _search_crunchbase_urls(self, company_domain: str) -> Dict[str, Any]:
        """Search for Crunchbase URLs using Serper API."""
        try:
            if not self.serper_client:
                return {"success": False, "error": "Serper client not initialized"}

            # Construct search query
            search_query = f"site:crunchbase.com/organization {company_domain}"

            payload = {"q": search_query}

            self.logger.info(f"Searching Serper with query: {search_query}")

            response = await self.serper_client.post("/search", json=payload)
            response.raise_for_status()

            data = response.json()

            # Convert to SerperSearchResponse model
            organic_results = []
            for i, result in enumerate(data.get("organic", [])):
                organic_results.append({
                    "title": result.get("title", ""),
                    "link": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "position": i + 1,
                })

            serper_response = SerperSearchResponse(
                organic=organic_results,
                total_results=data.get("searchInformation", {}).get("totalResults"),
                search_time=data.get("searchInformation", {}).get("searchTime"),
            )

            return {"success": True, "data": serper_response}

        except Exception as e:
            error_msg = f"Serper search failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    async def _select_crunchbase_url_with_llm(
        self, domain: str, description: str, serper_response: SerperSearchResponse
    ) -> Dict[str, Any]:
        """Use LLM to select the best Crunchbase URL from search results."""
        try:
            if not self.together_client:
                return {"success": False, "error": "Together client not initialized"}

            # Format search results for LLM
            serp_list = []
            for i, result in enumerate(serper_response.organic, start=1):
                serp_list.append({
                    "id": i,
                    "title": result.title,
                    "link": result.link,
                    "snippet": result.snippet,
                })

            formatted_json = json.dumps(serp_list, indent=2)

            # Create prompt for LLM
            prompt = f"""
            You are a Crunchbase link resolver for an investment intelligence platform.

            Your job is to select the **most relevant Crunchbase company profile link** from a given list of search results.

            ### You are given:

            - A company's domain: **"{domain}"**
            - A short company description: **"{description}"**
            - A list of Crunchbase search results as structured JSON entries (below)

            ### Your rules:

            1. ONLY choose a link that is actually present in the "link" field of the search results list. **Do not make up or hallucinate any URL.**
            2. Match using the company domain or by comparing the snippet text with the company description.
            3. If **none of the links** confidently match, return `"NO_MATCH"` as the link.
            4. Do NOT assume — return `"NO_MATCH"` if unsure, missing snippets, or the links are vague.
            5. Do not return anything except the final result (just the link or "NO_MATCH")

            ### Input:

            Company domain: "{domain}"
            Company description: "{description}"

            Search Results (JSON):
            {formatted_json}

            
            ### HARD RULES:
            - DO NOT explain anything.
            - DO NOT think out loud.
            - DO NOT guess.
            - DO NOT hallucinate links.
            
            ### OUTPUT:
            Only return one of:
            - A link from the list (only the link, no other text)
            - "NO_MATCH"
            

            """

            self.logger.info(f"Sending prompt to LLM for domain: {domain}")

            # Call Together AI
            result = await self.together_client.create_chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                temperature=0.0,
            )

            # Handle the response properly
            if result["success"]:
                output = result["content"].strip()
            else:
                output = "NO_MATCH"

            self.logger.info(f"LLM response: {output}")

            return {"success": True, "url": output}

        except Exception as e:
            error_msg = f"LLM selection failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    def _is_valid_crunchbase_url(self, url: str) -> bool:
        """Validate if the URL is a valid Crunchbase company URL or NO_MATCH."""
        url = url.strip()

        # Accept NO_MATCH as valid fallback
        if url == "NO_MATCH":
            return True

        # Basic Crunchbase company URL pattern
        pattern = r"^https://www\.crunchbase\.com/organization/[a-zA-Z0-9\-_]+$"
        return bool(re.match(pattern, url))

    async def _trigger_brightdata_scrape(self, crunchbase_url: str) -> Dict[str, Any]:
        """Trigger BrightData scraping for the given Crunchbase URL."""
        try:
            if not self.brightdata_client:
                return {"success": False, "error": "BrightData client not initialized"}

            # Parameters for query string
            params = {
                "dataset_id": self.brightdata_dataset_id,
                "include_errors": "true",
            }

            # Data for request body
            data = [{"url": crunchbase_url}]

            self.logger.info(
                "Triggering BrightData scrape",
                url=crunchbase_url,
                dataset_id=self.brightdata_dataset_id,
            )

            response = await self.brightdata_client.post(
                "/datasets/v3/trigger",
                params=params,
                json=data,
            )
            response.raise_for_status()

            result = response.json()

            # Extract snapshot ID from response
            if "snapshot_id" in result:
                snapshot_id = result["snapshot_id"]
            elif "snapshots" in result and result["snapshots"]:
                snapshot_id = result["snapshots"][0]
            else:
                return {
                    "success": False,
                    "error": "No snapshot ID in BrightData response",
                }

            self.logger.info(f"BrightData scrape triggered successfully: {snapshot_id}")

            return {"success": True, "snapshot_id": snapshot_id}

        except Exception as e:
            error_msg = f"BrightData trigger failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    async def _store_raw_data(
        self,
        company_domain: str,
        org_id: Optional[str],
        snapshot_id: str,
        data: Dict[str, Any],
    ) -> str:
        """Store raw data to S3 for debugging and audit purposes."""
        try:
            if not self.s3_storage:
                raise ValueError("S3 storage not initialized")

            # Generate S3 key
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            s3_key = f"crunchbase_resolver/{org_id or 'unknown'}/{company_domain}/{timestamp}_{snapshot_id}.json"

            # Store data
            success = await self.s3_storage.put_object(s3_key, data)
            if not success:
                raise ValueError("Failed to store data to S3")

            self.logger.info(f"Raw data stored to S3: {s3_key}")
            return s3_key

        except Exception as e:
            self.logger.error(f"Failed to store raw data: {str(e)}")
            return ""

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """Download BrightData snapshot data."""
        try:
            if not self.brightdata_client:
                return {"success": False, "error": "BrightData client not initialized"}

            self.logger.info(f"Downloading BrightData snapshot: {snapshot_id}")

            # Download snapshot data
            response = await self.brightdata_client.get(
                f"/datasets/v3/snapshot/{snapshot_id}", params={"format": "json"}
            )
            response.raise_for_status()

            data = response.json()

            self.logger.info(f"Successfully downloaded snapshot: {snapshot_id}")

            return {"success": True, "data": data}

        except Exception as e:
            error_msg = f"Failed to download BrightData snapshot: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}


# Global service instance
_crunchbase_resolver_service: Optional[CrunchbaseResolverService] = None


async def get_crunchbase_resolver_service() -> CrunchbaseResolverService:
    """
    Get or create Crunchbase resolver service instance.

    Returns:
        CrunchbaseResolverService instance
    """
    global _crunchbase_resolver_service

    if _crunchbase_resolver_service is None:
        _crunchbase_resolver_service = CrunchbaseResolverService()
        await _crunchbase_resolver_service.initialize()

    return _crunchbase_resolver_service
