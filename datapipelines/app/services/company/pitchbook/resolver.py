"""
PitchBook URL Resolver Service for TractionX Data Pipeline Service.

This module provides automatic resolution of company PitchBook URLs using:
1. Serper API for Google search
2. LLM agent for link selection
3. BrightData for company data scraping
"""

import time
from typing import Any, Dict, Optional

import httpx
from app.clients.ai.together import TogetherClient
from app.configs import get_logger, settings
from app.models.pitchbook import PitchBookResolverInput, PitchBookResolverOutput
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class PitchBookResolverService:
    """
    PitchBook URL resolver service.

    Handles:
    - Serper API search for PitchBook URLs
    - LLM-based link selection and validation
    - BrightData scraping trigger
    - S3 storage of raw data
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.PitchBookResolverService")

        # API clients
        self.serper_client: Optional[httpx.AsyncClient] = None
        self.brightdata_client: Optional[httpx.AsyncClient] = None
        self.together_client: Optional[TogetherClient] = None

        # Configuration
        self.serper_api_key = settings.SERPAPI_KEY
        self.brightdata_api_key = settings.BRIGHTDATA_API_KEY
        self.together_api_key = settings.TOGETHER_API_KEY
        self.brightdata_dataset_id = "gd_m4ijiqfp2n9oe3oluj"  # PitchBook dataset ID

        # S3 storage
        self.s3_storage: Optional[S3Storage] = None

        # Timeouts and retries
        self.timeout = 30.0
        self.max_retries = 3
        self.retry_delay = 2.0

    async def initialize(self) -> None:
        """Initialize the PitchBook resolver service."""
        try:
            self.logger.info("Starting PitchBook resolver service initialization")

            # Check API keys
            if not self.serper_api_key:
                raise ValueError("SERPAPI_KEY not configured")
            if not self.brightdata_api_key:
                raise ValueError("BRIGHTDATA_API_KEY not configured")
            if not self.together_api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            self.logger.info("API keys validated successfully")

            # Initialize HTTP clients
            self.serper_client = httpx.AsyncClient(
                base_url="https://google.serper.dev",
                headers={
                    "X-API-KEY": self.serper_api_key,
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            self.brightdata_client = httpx.AsyncClient(
                base_url="https://api.brightdata.com",
                headers={
                    "Authorization": f"Bearer {self.brightdata_api_key}",
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            # Initialize Together AI client
            self.together_client = TogetherClient()
            await self.together_client.initialize()

            # Initialize S3 storage
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

            self.logger.info("PitchBook resolver service initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize PitchBook resolver service: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up the PitchBook resolver service."""
        try:
            if self.serper_client:
                await self.serper_client.aclose()
            if self.brightdata_client:
                await self.brightdata_client.aclose()
            if self.together_client:
                await self.together_client.cleanup()
            if self.s3_storage:
                await self.s3_storage.cleanup()
            self.logger.info("PitchBook resolver service cleaned up successfully")
        except Exception as e:
            self.logger.error(f"Error cleaning up PitchBook resolver service: {e}")

    async def resolve_pitchbook_url(
        self, input_data: PitchBookResolverInput
    ) -> PitchBookResolverOutput:
        """
        Resolve PitchBook URL for a company domain.

        Args:
            input_data: Input data containing company domain and metadata

        Returns:
            PitchBookResolverOutput with resolved URL and metadata
        """
        start_time = time.time()
        self.logger.info(
            f"Starting PitchBook URL resolution for domain: {input_data.company_domain}"
        )

        try:
            # Step 1: Search for PitchBook URLs using Serper API
            serper_response = await self._search_pitchbook_urls(
                input_data.company_domain
            )
            self.logger.info(f"Serper response: {serper_response}")

            if not serper_response or not serper_response.get("organic"):
                self.logger.warning("No search results found from Serper API")
                return PitchBookResolverOutput(
                    status="no_match",
                    pitchbook_url=None,
                    brightdata_snapshot_id=None,
                    error_message="No search results found",
                    llm_decision=None,
                    serper_results_count=0,
                    confidence_score=0.0,
                    processing_time=time.time() - start_time,
                )

            # Step 2: Use LLM to select the best PitchBook URL
            pitchbook_url = await self._select_pitchbook_url_with_llm(
                input_data.company_domain,
                input_data.company_description or "",
                serper_response,
            )

            if not pitchbook_url or pitchbook_url == "NO_MATCH":
                self.logger.warning("No suitable PitchBook URL found")
                return PitchBookResolverOutput(
                    status="no_match",
                    pitchbook_url=None,
                    brightdata_snapshot_id=None,
                    error_message="No suitable PitchBook URL found",
                    llm_decision=None,
                    serper_results_count=len(serper_response.get("organic", [])),
                    confidence_score=0.0,
                    processing_time=time.time() - start_time,
                )

            self.logger.info(f"Resolved PitchBook URL: {pitchbook_url}")

            return PitchBookResolverOutput(
                status="resolved",
                pitchbook_url=pitchbook_url,
                brightdata_snapshot_id=None,
                error_message=None,
                llm_decision="URL selected successfully",
                serper_results_count=len(serper_response.get("organic", [])),
                confidence_score=0.8,
                processing_time=time.time() - start_time,
            )

        except Exception as e:
            self.logger.error(f"Error resolving PitchBook URL: {e}")
            return PitchBookResolverOutput(
                status="failed",
                pitchbook_url=None,
                brightdata_snapshot_id=None,
                error_message=str(e),
                llm_decision=None,
                serper_results_count=0,
                confidence_score=0.0,
                processing_time=time.time() - start_time,
            )

    async def _search_pitchbook_urls(self, company_domain: str) -> Dict[str, Any]:
        """Search for PitchBook URLs using Serper API."""
        try:
            if not self.serper_client:
                raise ValueError("Serper client not initialized")

            query = f"site:pitchbook.com {company_domain}"
            self.logger.info(f"Searching Serper with query: {query}")

            response = await self.serper_client.post(
                "/search",
                json={"q": query, "num": 10},
            )
            response.raise_for_status()

            data = response.json()
            self.logger.info(
                f"Serper search completed for {company_domain}, results: {len(data.get('organic', []))}"
            )

            return data

        except Exception as e:
            self.logger.error(f"Error searching Serper API: {e}")
            return {}

    async def _select_pitchbook_url_with_llm(
        self, domain: str, description: str, serper_response: Dict[str, Any]
    ) -> Optional[str]:
        """Use LLM to select the best PitchBook URL from search results."""
        try:
            if not self.together_client:
                raise ValueError("Together client not initialized")

            # Extract organic results
            organic_results = serper_response.get("organic", [])
            if not organic_results:
                return None

            # Prepare context for LLM
            context = f"""
            Company Domain: {domain}
            Company Description: {description}
            
            Search Results:
            """

            for i, result in enumerate(organic_results[:5], 1):
                title = result.get("title", "")
                link = result.get("link", "")
                snippet = result.get("snippet", "")
                context += (
                    f"\n{i}. Title: {title}\n   URL: {link}\n   Snippet: {snippet}\n"
                )

            # Create prompt for LLM
            prompt = """
            You are a PitchBook URL resolver. Given the company domain and search results, select the most relevant PitchBook URL.


            Instructions:
            1. Look for URLs that contain the company domain or company name
            2. Prefer URLs that are actual company pages (not search results or lists), sometimes the results maybe vague, and title in the search maybe misleading, so you need to be careful and use your judgement to select the most relevant URL
            3. If no suitable URL is found, return "NO_MATCH"
            4. Return only the URL, nothing else
            5. Sometimes the description is not enough to make a decision, so you need to use the search results to make a decision and check if the URL is a company page

            Selected URL:
            """

            self.logger.info(f"Sending prompt to LLM for domain: {domain}")

            # Get LLM response
            result = await self.together_client.create_chat_completion(
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": context},
                ],
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                max_tokens=2000,
                temperature=0.0,
            )
            self.logger.info("LLM Response: ", result=result)

            if not result or not result.get("success"):
                self.logger.warning("No response from LLM")
                return None

            # Extract content from the response - together_client returns content directly
            selected_url = result.get("content", "").strip()

            # Clean up the URL
            if selected_url.startswith('"') and selected_url.endswith('"'):
                selected_url = selected_url[1:-1]

            if selected_url == "NO_MATCH" or not selected_url:
                return None

            # Validate the URL
            if "pitchbook.com" not in selected_url:
                self.logger.warning(
                    f"Selected URL is not a PitchBook URL: {selected_url}"
                )
                return None

            return selected_url

        except Exception as e:
            self.logger.error(f"Error selecting PitchBook URL with LLM: {e}")
            return None

    async def trigger_brightdata_scrape(self, pitchbook_url: str) -> Dict[str, Any]:
        """
        Trigger BrightData scraping for a PitchBook URL.

        Args:
            pitchbook_url: PitchBook URL to scrape

        Returns:
            Dict with scraping result
        """
        try:
            if not self.brightdata_client:
                raise ValueError("BrightData client not initialized")

            self.logger.info(f"Triggering BrightData scraping for: {pitchbook_url}")

            # Prepare request parameters and data following BrightData API pattern
            params = {
                "dataset_id": self.brightdata_dataset_id,
                "include_errors": "true",
            }

            # Data should be an array of objects with URL
            data = [{"url": pitchbook_url}]

            self.logger.info(
                "Triggering BrightData scrape",
                url=pitchbook_url,
                dataset_id=self.brightdata_dataset_id,
            )

            # Trigger the scraping using the correct endpoint
            response = await self.brightdata_client.post(
                "/datasets/v3/trigger",
                params=params,
                json=data,
            )
            response.raise_for_status()

            result = response.json()
            self.logger.info(
                "BrightData trigger response received",
                result=result,
                pitchbook_url=pitchbook_url,
            )

            # Extract snapshot ID from response
            if "snapshot_id" in result:
                snapshot_id = result["snapshot_id"]
            elif "snapshots" in result and result["snapshots"]:
                snapshot_id = result["snapshots"][0]
            else:
                return {
                    "success": False,
                    "error": "No snapshot ID in BrightData response",
                }

            self.logger.info(
                f"BrightData scraping triggered successfully: {snapshot_id}"
            )

            return {
                "success": True,
                "snapshot_id": snapshot_id,
                "status": "triggered",
            }

        except Exception as e:
            self.logger.error(f"Error triggering BrightData scraping: {e}")
            return {
                "success": False,
                "error": str(e),
            }

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """Download BrightData snapshot data."""
        try:
            if not self.brightdata_client:
                raise ValueError("BrightData client not initialized")

            self.logger.info(f"Downloading BrightData snapshot: {snapshot_id}")

            # Download snapshot data using the correct endpoint
            response = await self.brightdata_client.get(
                f"/datasets/v3/snapshot/{snapshot_id}",
                params={"format": "json"},
            )
            response.raise_for_status()

            data = response.json()
            self.logger.info(
                "BrightData snapshot downloaded successfully",
                snapshot_id=snapshot_id,
                data_keys=list(data.keys()) if isinstance(data, dict) else "not_dict",
            )

            # Return the first item if it's a list, otherwise return the data as is
            if isinstance(data, list) and data:
                return {
                    "success": True,
                    "data": data[0],
                    "snapshot_id": snapshot_id,
                }
            else:
                return {
                    "success": True,
                    "data": data,
                    "snapshot_id": snapshot_id,
                }

        except Exception as e:
            self.logger.error(f"Error downloading BrightData snapshot: {e}")
            return {
                "success": False,
                "error": str(e),
            }


# Global service instance
_pitchbook_resolver_service: Optional[PitchBookResolverService] = None


async def get_pitchbook_resolver_service() -> PitchBookResolverService:
    """Get or create the global PitchBook resolver service instance."""
    global _pitchbook_resolver_service

    if _pitchbook_resolver_service is None:
        _pitchbook_resolver_service = PitchBookResolverService()
        await _pitchbook_resolver_service.initialize()

    return _pitchbook_resolver_service
