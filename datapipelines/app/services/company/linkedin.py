"""
LinkedIn Company Service for TractionX Data Pipeline Service.

This service handles LinkedIn company enrichment operations including:
- URL resolution
- Data scraping
- Data processing (now integrated into scraper task)
"""

import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.configs import get_logger
from app.ctx.job.linkedin.company import LinkedInCompanyJobContext
from app.models.linkedin import LinkedInResolverInput
from app.services.company.linkedin.resolver import get_linkedin_resolver_service
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class LinkedInCompanyService:
    """
    Comprehensive service for LinkedIn company enrichment operations.

    Handles:
    - LinkedIn URL resolution
    - BrightData scraping
    - Data processing (now integrated into scraper task)
    - Error handling and logging
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.LinkedInCompanyService")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.resolver_service = None

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        try:
            # Initialize storage
            if not self.rds_storage:
                self.rds_storage = RDSStorageV2()
                await self.rds_storage.initialize()

            if not self.s3_storage:
                self.s3_storage = S3Storage()
                await self.s3_storage.initialize()

            # Initialize resolver service
            if not self.resolver_service:
                self.resolver_service = await get_linkedin_resolver_service()

            self.logger.info("LinkedIn Company Service initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize LinkedIn Company Service: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up service resources."""
        try:
            if self.rds_storage:
                await self.rds_storage.cleanup()

            if self.s3_storage:
                await self.s3_storage.cleanup()

            self.logger.info("LinkedIn Company Service cleaned up")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    async def resolve_linkedin_url(
        self,
        company_domain: str,
        company_id: str,
        org_id: str,
        company_description: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Resolve LinkedIn URL for a company.

        Args:
            company_domain: Company domain
            company_id: Company ID
            org_id: Organization ID
            company_description: Optional company description

        Returns:
            Resolution result with LinkedIn URL and metadata
        """
        try:
            if not self.resolver_service:
                return {
                    "success": False,
                    "error": "Resolver service not initialized",
                    "company_id": company_id,
                    "org_id": org_id,
                }

            # Create input model
            input_data = LinkedInResolverInput(
                company_domain=company_domain,
                company_description=company_description,
                org_id=org_id,
                job_id=f"linkedin_company_{company_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            )

            # Resolve LinkedIn URL
            result = await self.resolver_service.resolve_linkedin_url(input_data)

            return {
                "success": result.status in ["triggered", "no_match", "completed"],
                "status": result.status,
                "linkedin_url": result.linkedin_url,
                "brightdata_snapshot_id": result.brightdata_snapshot_id,
                "company_id": company_id,
                "org_id": org_id,
                "processing_status": "completed",
            }

        except Exception as e:
            error_msg = f"LinkedIn URL resolution failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "company_id": company_id,
                "org_id": org_id,
            }

    async def scrape_linkedin_data(
        self, snapshot_id: str, company_domain: str, company_id: str, org_id: str
    ) -> Dict[str, Any]:
        """
        Scrape LinkedIn data for a company.

        Args:
            snapshot_id: BrightData snapshot ID
            company_domain: Company domain
            company_id: Company ID
            org_id: Organization ID

        Returns:
            Scraping result with data and metadata
        """
        try:
            if not self.resolver_service:
                return {
                    "success": False,
                    "error": "Resolver service not initialized",
                    "company_id": company_id,
                    "org_id": org_id,
                }

            # Create job context
            job_context = LinkedInCompanyJobContext(
                job_id=f"linkedin_company_scraper_{company_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
                company_id=company_id,
                org_id=org_id,
                company_domain=company_domain,
                data={},
                brightdata_snapshot_id=snapshot_id,
            )

            # Poll for snapshot completion
            poll_result = await self._poll_brightdata_snapshot_completion(snapshot_id)
            if not poll_result["success"]:
                return {
                    "success": False,
                    "error": f"Snapshot polling failed: {poll_result['error']}",
                    "brightdata_status": poll_result.get("status", "poll_failed"),
                }

            # Download snapshot data
            download_result = await self._download_brightdata_snapshot(snapshot_id)
            if not download_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to download snapshot: {download_result['error']}",
                }

            raw_data = download_result["data"]

            # Store raw data to S3
            s3_key = await self._store_raw_data(raw_data, job_context)

            # Process the data
            # The processing logic is now integrated into the scraper task,
            # so we just return the raw data and S3 key.
            return {
                "success": True,
                "status": "completed",
                "s3_key": s3_key,
                "company_id": company_id,
                "org_id": org_id,
                "data": raw_data,  # Return raw data as it's already processed
            }

        except Exception as e:
            error_msg = f"LinkedIn data scraping failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "company_id": company_id,
                "org_id": org_id,
            }

    async def _poll_brightdata_snapshot_completion(
        self, snapshot_id: str
    ) -> Dict[str, Any]:
        """Poll for BrightData snapshot completion."""
        max_poll_time = 600  # 10 minutes
        poll_interval = 10  # 10 seconds
        start_time = datetime.now(timezone.utc).timestamp()

        while datetime.now(timezone.utc).timestamp() - start_time < max_poll_time:
            try:
                if (
                    not self.resolver_service
                    or not self.resolver_service.brightdata_client
                ):
                    return {
                        "success": False,
                        "status": "error",
                        "error": "BrightData client not initialized",
                    }

                response = await self.resolver_service.brightdata_client.get(
                    f"/datasets/v3/progress/{snapshot_id}"
                )
                response.raise_for_status()
                result = response.json()

                status = result.get("status")
                self.logger.info(f"Polling status: {status} for {snapshot_id}")

                if status == "ready":
                    return {
                        "success": True,
                        "status": "ready",
                    }
                elif status in ["error", "failed"]:
                    return {
                        "success": False,
                        "status": "error",
                        "error": f"Job failed with status: {status}",
                    }

                await asyncio.sleep(poll_interval)

            except Exception as e:
                self.logger.warning(f"Poll error: {e}")
                await asyncio.sleep(poll_interval)

        return {
            "success": False,
            "status": "timeout",
            "error": f"Polling timed out after {max_poll_time} seconds",
        }

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """Download BrightData snapshot data."""
        try:
            if not self.resolver_service:
                return {
                    "success": False,
                    "error": "Resolver service not initialized",
                }

            download_result = await self.resolver_service._download_brightdata_snapshot(
                snapshot_id
            )

            if not download_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to download snapshot: {download_result['error']}",
                }

            return {
                "success": True,
                "data": download_result["data"],
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Download failed: {str(e)}",
            }

    async def _store_raw_data(
        self, raw_data: Any, job_context: LinkedInCompanyJobContext
    ) -> str:
        """Store raw data to S3."""
        if not self.s3_storage:
            raise Exception("S3 storage not initialized")

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = (
            f"brightdata_company_linkedin/{job_context.company_domain}_{timestamp}.json"
        )

        s3_data = {
            "snapshot_id": job_context.brightdata_snapshot_id,
            "company_domain": job_context.company_domain,
            "company_id": job_context.company_id,
            "org_id": job_context.org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await self.s3_storage.put_object(s3_key, s3_data)
        if not success:
            raise Exception(f"Failed to store raw data to S3: {s3_key}")

        self.logger.info(f"Stored raw data to S3: {s3_key}")
        return s3_key


# Service instance
_linkedin_company_service: Optional[LinkedInCompanyService] = None


async def get_linkedin_company_service() -> LinkedInCompanyService:
    """Get or create LinkedIn company service instance."""
    global _linkedin_company_service

    if _linkedin_company_service is None:
        _linkedin_company_service = LinkedInCompanyService()
        await _linkedin_company_service.initialize()

    return _linkedin_company_service


async def cleanup_linkedin_company_service() -> None:
    """Clean up LinkedIn company service instance."""
    global _linkedin_company_service

    if _linkedin_company_service:
        await _linkedin_company_service.cleanup()
        _linkedin_company_service = None
