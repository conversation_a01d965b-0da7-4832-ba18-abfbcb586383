"""
LinkedIn URL Resolver Service for TractionX Data Pipeline Service.

This module provides automatic resolution of company LinkedIn URLs using:
1. Serper API for Google search
2. LLM agent for link selection
3. BrightData for company data scraping
"""

import json
import re
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import httpx
from app.clients.ai.together import TogetherClient
from app.configs import get_logger, settings
from app.models.linkedin import (
    LinkedInResolverInput,
    LinkedInResolverOutput,
    SerperSearchResponse,
)
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class LinkedInResolverService:
    """
    LinkedIn URL resolver service.

    Handles:
    - Serper API search for LinkedIn URLs
    - LLM-based link selection and validation
    - BrightData scraping trigger
    - S3 storage of raw data
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.LinkedInResolverService")

        # API clients
        self.serper_client: Optional[httpx.AsyncClient] = None
        self.brightdata_client: Optional[httpx.AsyncClient] = None
        self.together_client: Optional[TogetherClient] = None

        # Configuration
        self.serper_api_key = settings.SERPAPI_KEY
        self.brightdata_api_key = settings.BRIGHTDATA_API_KEY
        self.together_api_key = settings.TOGETHER_API_KEY
        self.brightdata_dataset_id = "gd_l1vikfnt1wgvvqz95w"  # LinkedIn dataset ID

        # S3 storage
        self.s3_storage: Optional[S3Storage] = None

        # Timeouts and retries
        self.timeout = 30.0
        self.max_retries = 3
        self.retry_delay = 2.0

    async def initialize(self) -> None:
        """Initialize the LinkedIn resolver service."""
        try:
            self.logger.info("Starting LinkedIn resolver service initialization")

            # Check API keys
            if not self.serper_api_key:
                raise ValueError("SERPAPI_KEY not configured")
            if not self.brightdata_api_key:
                raise ValueError("BRIGHTDATA_API_KEY not configured")
            if not self.together_api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            self.logger.info("API keys validated successfully")

            # Initialize HTTP clients
            self.serper_client = httpx.AsyncClient(
                base_url="https://google.serper.dev",
                headers={
                    "X-API-KEY": self.serper_api_key,
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            self.brightdata_client = httpx.AsyncClient(
                base_url="https://api.brightdata.com",
                headers={
                    "Authorization": f"Bearer {self.brightdata_api_key}",
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            # Initialize Together AI client
            self.together_client = TogetherClient()
            await self.together_client.initialize()

            # Initialize S3 storage
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

            self.logger.info("LinkedIn resolver service initialized successfully")

        except Exception as e:
            self.logger.error(
                f"Failed to initialize LinkedIn resolver service: {str(e)}",
                exc_info=True,
            )
            raise

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.serper_client:
            await self.serper_client.aclose()
        if self.brightdata_client:
            await self.brightdata_client.aclose()
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("LinkedIn resolver service cleaned up")

    async def resolve_linkedin_url(
        self, input_data: LinkedInResolverInput
    ) -> LinkedInResolverOutput:
        """
        Main method to resolve LinkedIn URL (no automatic BrightData triggering).

        Args:
            input_data: Input data containing company domain and description

        Returns:
            LinkedInResolverOutput with processing results (no BrightData snapshot_id)
        """
        start_time = time.time()

        try:
            self.logger.info(
                "Starting LinkedIn URL resolution",
                company_domain=input_data.company_domain,
                job_id=input_data.job_id,
            )

            # Step 1: Search for LinkedIn URLs using Serper
            serper_result = await self._search_linkedin_urls(input_data.company_domain)
            if not serper_result["success"]:
                return LinkedInResolverOutput(
                    status="failed",
                    company_domain=input_data.company_domain,
                    linkedin_url=None,
                    brightdata_snapshot_id=None,
                    log=f"Serper search failed: {serper_result['error']}",
                    error_message=serper_result["error"],
                    error_stage="serper_search",
                    processing_time=time.time() - start_time,
                    llm_decision=None,
                    serper_results_count=None,
                    completed_at=None,
                )

            serper_response = serper_result["data"]
            self.logger.info(
                "Serper search completed",
                results_count=len(serper_response.organic),
                company_domain=input_data.company_domain,
            )

            # Step 2: Use LLM to select the best LinkedIn URL
            llm_result = await self._select_linkedin_url_with_llm(
                input_data.company_domain,
                input_data.company_description or "",
                serper_response,
            )

            if not llm_result["success"]:
                return LinkedInResolverOutput(
                    status="failed",
                    company_domain=input_data.company_domain,
                    linkedin_url=None,
                    brightdata_snapshot_id=None,
                    log=f"LLM selection failed: {llm_result['error']}",
                    error_message=llm_result["error"],
                    error_stage="llm_selection",
                    processing_time=time.time() - start_time,
                    llm_decision=None,
                    serper_results_count=len(serper_response.organic),
                    completed_at=None,
                )

            selected_url = llm_result["url"]
            self.logger.info(
                f"LLM selected URL: {selected_url}",
                company_domain=input_data.company_domain,
            )

            # Step 3: Validate the selected URL
            if not self._is_valid_linkedin_url_or_nomatch(selected_url):
                return LinkedInResolverOutput(
                    status="failed",
                    company_domain=input_data.company_domain,
                    linkedin_url=None,
                    brightdata_snapshot_id=None,
                    log=f"Invalid LinkedIn URL format: {selected_url}",
                    error_message=f"Invalid LinkedIn URL format: {selected_url}",
                    error_stage="url_validation",
                    processing_time=time.time() - start_time,
                    llm_decision=selected_url,
                    serper_results_count=len(serper_response.organic),
                    completed_at=None,
                )

            # Step 4: Handle NO_MATCH case
            if selected_url == "NO_MATCH":
                return LinkedInResolverOutput(
                    status="no_match",
                    company_domain=input_data.company_domain,
                    linkedin_url=None,
                    brightdata_snapshot_id=None,
                    log="No matching LinkedIn URL found",
                    error_message=None,
                    error_stage=None,
                    processing_time=time.time() - start_time,
                    llm_decision=selected_url,
                    serper_results_count=len(serper_response.organic),
                    completed_at=datetime.now(timezone.utc),
                )

            # Step 5: Store raw data to S3 (resolution data only)
            s3_key = await self._store_raw_data(
                input_data.company_domain,
                input_data.org_id,
                None,  # No snapshot_id for pure resolution
                {
                    "serper_results": serper_response.model_dump(),
                    "llm_decision": selected_url,
                    "resolution_status": "completed",
                },
            )

            self.logger.info(
                "LinkedIn URL resolution completed successfully",
                linkedin_url=selected_url,
                s3_key=s3_key,
                company_domain=input_data.company_domain,
            )

            return LinkedInResolverOutput(
                status="resolved",
                company_domain=input_data.company_domain,
                linkedin_url=selected_url,
                brightdata_snapshot_id=None,  # No automatic BrightData triggering
                log="LinkedIn URL resolved successfully",
                error_message=None,
                error_stage=None,
                processing_time=time.time() - start_time,
                llm_decision=selected_url,
                serper_results_count=len(serper_response.organic),
                completed_at=datetime.now(timezone.utc),
            )

        except Exception as e:
            self.logger.error(f"LinkedIn URL resolution failed: {e}")
            return LinkedInResolverOutput(
                status="failed",
                company_domain=input_data.company_domain,
                linkedin_url=None,
                brightdata_snapshot_id=None,
                log=f"LinkedIn URL resolution failed: {str(e)}",
                error_message=str(e),
                error_stage="unknown",
                processing_time=time.time() - start_time,
                llm_decision=None,
                serper_results_count=None,
                completed_at=None,
            )

    async def _search_linkedin_urls(self, company_domain: str) -> Dict[str, Any]:
        """
        Search for LinkedIn URLs using Serper API.

        Args:
            company_domain: Company domain to search for

        Returns:
            Dictionary with success status and data/error
        """
        try:
            if not self.serper_client:
                raise ValueError("Serper client not initialized")

            query = f"site:linkedin.com/company {company_domain}"
            payload = {"q": query}

            self.logger.info(
                "Searching for LinkedIn URLs",
                query=query,
                company_domain=company_domain,
            )

            response = await self.serper_client.post("/search", json=payload)
            response.raise_for_status()

            data = response.json()
            self.logger.info(
                "Serper search response received",
                organic_count=len(data.get("organic", [])),
                company_domain=company_domain,
            )

            # Parse and validate response
            organic_results = []
            for i, result in enumerate(data.get("organic", []), start=1):
                organic_results.append({
                    "title": result.get("title", ""),
                    "link": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "position": i,
                })

            serper_response = SerperSearchResponse(
                organic=organic_results,
                total_results=data.get("searchInformation", {}).get("totalResults"),
                search_time=data.get("searchInformation", {}).get("searchTime"),
            )

            return {"success": True, "data": serper_response}

        except Exception as e:
            error_msg = f"Serper search failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    async def _select_linkedin_url_with_llm(
        self, domain: str, description: str, serper_response: SerperSearchResponse
    ) -> Dict[str, Any]:
        """
        Use LLM to select the best LinkedIn URL from search results.

        Args:
            domain: Company domain
            description: Company description
            serper_response: Serper search results

        Returns:
            Dictionary with success status and selected URL/error
        """
        try:
            if not self.together_client:
                raise ValueError("Together client not initialized")

            # Format search results for LLM
            serp_list = []
            for i, result in enumerate(serper_response.organic, start=1):
                serp_list.append({
                    "id": i,
                    "title": result.title,
                    "link": result.link,
                    "snippet": result.snippet,
                })

            formatted_json = json.dumps(serp_list, indent=2)

            # Generate LLM prompt
            prompt = f"""
            You are a LinkedIn company page resolver for an investment intelligence platform.

            Your task is to find the **most relevant LinkedIn company profile URL** from the list of search results below.

            ---

            ### You are given:
            - A company's domain: **"{domain}"**
            - A short company description: **"{description}"**
            - A list of Google search results (JSON) focused on LinkedIn

            ---

            ### Rules:
            1. ONLY select a `link` that contains **/company/** after any LinkedIn domain, including:
            - https://www.linkedin.com/company/
            - https://uk.linkedin.com/company/
            - https://nl.linkedin.com/company/
            - https://in.linkedin.com/company/
            - And any other country-specific LinkedIn domains
            2. Match using either the domain name or similarity between the snippet and the description.
            3. If **none of the links** clearly match, return `"NO_MATCH"` as the result.
            4. Be cautious — do **not guess** or hallucinate links.
            5. Output must ONLY be the final URL or `"NO_MATCH"` — no explanations.

            ---

            ### Search Results (JSON):
            {formatted_json}

            ---

            ### Output:
            Just return the **LinkedIn company URL** or `"NO_MATCH"` — nothing else.
"""

            self.logger.info(
                "Sending prompt to LLM",
                domain=domain,
                results_count=len(serper_response.organic),
            )

            result = await self.together_client.create_chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                temperature=0.1,
            )

            # Handle the response properly
            if result["success"]:
                selected_url = result["content"].strip()
            else:
                selected_url = "NO_MATCH"
            self.logger.info(
                "LLM response received",
                selected_url=selected_url,
                domain=domain,
            )

            return {"success": True, "url": selected_url}

        except Exception as e:
            error_msg = f"LLM selection failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    def _is_valid_linkedin_url_or_nomatch(self, url: str) -> bool:
        """
        Validate LinkedIn URL format or NO_MATCH.

        Args:
            url: URL to validate

        Returns:
            True if valid LinkedIn URL or NO_MATCH, False otherwise
        """
        url = url.strip()
        if url == "NO_MATCH":
            return True

        # Updated pattern to handle all LinkedIn regional domains
        # Supports: www.linkedin.com, uk.linkedin.com, nl.linkedin.com, etc.
        pattern = (
            r"^https:\/\/([a-z]{2,3}\.)?linkedin\.com\/company\/[a-zA-Z0-9\-_\/]+\/?$"
        )
        return bool(re.match(pattern, url))

    async def trigger_brightdata_scrape(self, linkedin_url: str) -> Dict[str, Any]:
        """
        Public method to trigger BrightData scraping for LinkedIn URL.

        Args:
            linkedin_url: LinkedIn URL to scrape

        Returns:
            Dictionary with success status and snapshot ID/error
        """
        return await self._trigger_brightdata_scrape(linkedin_url)

    async def _trigger_brightdata_scrape(self, linkedin_url: str) -> Dict[str, Any]:
        """
        Trigger BrightData scraping for LinkedIn URL.

        Args:
            linkedin_url: LinkedIn URL to scrape

        Returns:
            Dictionary with success status and snapshot ID/error
        """
        try:
            if not self.brightdata_client:
                raise ValueError("BrightData client not initialized")

            self.logger.info(
                "Triggering BrightData scraping",
                linkedin_url=linkedin_url,
            )

            # Prepare request
            params = {
                "dataset_id": self.brightdata_dataset_id,
                "include_errors": "true",
            }
            data = [{"url": linkedin_url}]

            response = await self.brightdata_client.post(
                "/datasets/v3/trigger", params=params, json=data
            )
            response.raise_for_status()

            result = response.json()
            self.logger.info(
                "BrightData trigger response received",
                result=result,
                linkedin_url=linkedin_url,
            )

            # Extract snapshot ID
            if "snapshot_id" in result:
                snapshot_id = result["snapshot_id"]
                self.logger.info(
                    "BrightData scraping triggered successfully",
                    snapshot_id=snapshot_id,
                    linkedin_url=linkedin_url,
                )
                return {"success": True, "snapshot_id": snapshot_id, "data": result}
            else:
                error_msg = f"No snapshot_id in BrightData response: {result}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg}

        except Exception as e:
            error_msg = f"BrightData scraping trigger failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    async def _store_raw_data(
        self,
        company_domain: str,
        org_id: Optional[str],
        snapshot_id: Optional[str],
        data: Dict[str, Any],
    ) -> str:
        """
        Store raw data to S3 for audit purposes.

        Args:
            company_domain: Company domain
            org_id: Organization ID
            snapshot_id: BrightData snapshot ID (optional)
            data: Data to store

        Returns:
            S3 key where data was stored
        """
        try:
            if not self.s3_storage:
                raise ValueError("S3 storage not initialized")

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            s3_key = f"linkedin_resolver_raw/{org_id or 'unknown'}/{company_domain}/{timestamp}_raw.json"

            # Prepare data for storage
            storage_data = {
                **data,
                "company_domain": company_domain,
                "org_id": org_id,
                "snapshot_id": snapshot_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "linkedin_resolver",
            }

            success = await self.s3_storage.put_object(s3_key, storage_data)
            if success:
                self.logger.info(
                    "Raw data stored to S3 successfully",
                    s3_key=s3_key,
                    company_domain=company_domain,
                    snapshot_id=snapshot_id,
                )
                # Log data summary for debugging
                self.logger.info("S3 Data Summary:")
                self.logger.info(f"  - S3 Key: {s3_key}")
                self.logger.info(f"  - Company Domain: {company_domain}")
                self.logger.info(f"  - Snapshot ID: {snapshot_id}")
                self.logger.info(f"  - Data Keys: {list(data.keys())}")
                if "serper_results" in data:
                    self.logger.info(
                        f"  - Serper Results Count: {len(data['serper_results'].get('organic', []))}"
                    )
                if "llm_decision" in data:
                    self.logger.info(f"  - LLM Decision: {data['llm_decision']}")
            else:
                self.logger.error(
                    "Failed to store raw data to S3",
                    s3_key=s3_key,
                    company_domain=company_domain,
                )

            return s3_key

        except Exception as e:
            self.logger.error(
                f"Failed to store raw data: {str(e)}",
                company_domain=company_domain,
                snapshot_id=snapshot_id,
                exc_info=True,
            )
            return ""

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """
        Download BrightData snapshot data.

        Args:
            snapshot_id: BrightData snapshot ID

        Returns:
            Dictionary with success status and snapshot data/error
        """
        try:
            if not self.brightdata_client:
                raise ValueError("BrightData client not initialized")

            self.logger.info(
                "Downloading BrightData snapshot",
                snapshot_id=snapshot_id,
            )

            # Get snapshot data
            response = await self.brightdata_client.get(
                f"/datasets/v3/snapshot/{snapshot_id}",
                params={"format": "json"},
            )
            response.raise_for_status()

            data = response.json()
            self.logger.info(
                "BrightData snapshot downloaded successfully",
                snapshot_id=snapshot_id,
                data_keys=list(data.keys()) if isinstance(data, dict) else "not_dict",
            )

            return {"success": True, "data": data[0]}

        except Exception as e:
            error_msg = f"Failed to download BrightData snapshot: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}


# Global service instance
_linkedin_resolver_service: Optional[LinkedInResolverService] = None


async def get_linkedin_resolver_service() -> LinkedInResolverService:
    """
    Get or create LinkedIn resolver service instance.

    Returns:
        LinkedInResolverService instance
    """
    global _linkedin_resolver_service

    if _linkedin_resolver_service is None:
        _linkedin_resolver_service = LinkedInResolverService()
        await _linkedin_resolver_service.initialize()

    return _linkedin_resolver_service
