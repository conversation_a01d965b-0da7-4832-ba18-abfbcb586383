"""
Apollo Company Service for TractionX Data Pipeline Service.

This service handles Apollo company enrichment operations including:
- API data fetching
- Data processing
- Error handling and logging
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.clients.apollo import get_apollo_client
from app.configs import get_logger
from app.storage.rdsv2 import RDSStorageV2
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class ApolloCompanyService:
    """
    Comprehensive service for Apollo company enrichment operations.

    Handles:
    - Apollo API data fetching
    - Data processing
    - Error handling and logging
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.ApolloCompanyService")
        self.rds_storage: Optional[RDSStorageV2] = None
        self.s3_storage: Optional[S3Storage] = None
        self.apollo_client = None

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        try:
            # Initialize storage
            if not self.rds_storage:
                self.rds_storage = RDSStorageV2()
                await self.rds_storage.initialize()

            if not self.s3_storage:
                self.s3_storage = S3Storage()
                await self.s3_storage.initialize()

            # Initialize Apollo client
            if not self.apollo_client:
                self.apollo_client = await get_apollo_client()

            self.logger.info("Apollo Company Service initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Apollo Company Service: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up service resources."""
        try:
            if self.rds_storage:
                await self.rds_storage.cleanup()

            if self.s3_storage:
                await self.s3_storage.cleanup()

            self.logger.info("Apollo Company Service cleaned up")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    async def fetch_company_data(
        self,
        domain: str,
        company_name: Optional[str] = None,
        org_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Fetch Apollo company data for a domain.

        Args:
            domain: Company domain
            company_name: Optional company name
            org_id: Optional organization ID

        Returns:
            Fetch result with Apollo data and metadata
        """
        try:
            self.logger.info(
                f"Fetching Apollo data for domain: {domain}",
                company_name=company_name,
                org_id=org_id,
            )

            if not self.apollo_client:
                return {
                    "success": False,
                    "error": "Apollo client not initialized",
                    "data": None,
                }

            # Fetch company data from Apollo API using the correct method
            company_data = await self.apollo_client.enrich_organization(domain)

            if not company_data:
                return {
                    "success": False,
                    "error": "No company data found for domain",
                    "data": None,
                }

            # Return raw response - let ETL processor handle data conversion
            self.logger.info(
                "Successfully fetched Apollo data",
                company_domain=domain,
            )

            return {
                "success": True,
                "data": company_data["data"],
                "raw_data": company_data,
                "domain": domain,
                "fetched_at": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            error_msg = f"Failed to fetch Apollo data for domain {domain}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "data": None,
            }


# Global service instance
_apollo_service: Optional[ApolloCompanyService] = None


async def get_apollo_service() -> ApolloCompanyService:
    """Get or create the global Apollo service instance."""
    global _apollo_service

    if _apollo_service is None:
        _apollo_service = ApolloCompanyService()
        await _apollo_service.initialize()

    return _apollo_service


async def cleanup_apollo_service() -> None:
    """Clean up the global Apollo service instance."""
    global _apollo_service

    if _apollo_service:
        await _apollo_service.cleanup()
        _apollo_service = None
