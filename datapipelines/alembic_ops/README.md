# Alembic Migration Guide for TractionX Data Pipeline

## Quick Start

### Run Migrations
```bash
cd datapipelines

# Check current migration status
poetry run alembic current

# Run all pending migrations
poetry run alembic upgrade head

# Run specific migration
poetry run alembic upgrade 001

# Rollback one migration
poetry run alembic downgrade -1

# Show migration history
poetry run alembic history
```

## How It Works

### Migration Files
- **Location**: `migrations/versions/`
- **Naming**: `{revision_id}_{description}.py`
- **Example**: `001_create_founder_schema.py`

### SQL Files
- **Location**: `sql/` directory
- **Naming**: `{number}_{description}.sql`
- **Example**: `01_create_schemas.sql`

### Database Tracking
- Alembic uses `alembic_version` table to track which migrations are applied
- Each migration has a unique revision ID (e.g., `001`, `002`)

## Creating New Migrations

### Step 1: Create Migration File
```bash
poetry run alembic revision -m "add_new_table"
```

This creates: `migrations/versions/002_add_new_table.py`

### Step 2: Add SQL Files
Create your SQL files in the `sql/` directory:
```
sql/
├── 05_create_new_table.sql
├── 06_add_indexes.sql
└── 07_add_constraints.sql
```

### Step 3: Update Migration File
Edit the generated migration file:

```python
def upgrade() -> None:
    """Add new table."""
    import re
    import os

    # Execute SQL scripts directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")

    # List of SQL scripts to execute in order
    scripts = [
        "05_create_new_table.sql",
        "06_add_indexes.sql", 
        "07_add_constraints.sql",
    ]

    for script_name in scripts:
        script_path = os.path.join(sql_dir, script_name)
        if os.path.exists(script_path):
            with open(script_path, "r") as f:
                sql_content = f.read()

            # Remove comments and execute the entire script as one block
            sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

            if sql_content.strip():
                op.execute(sql_content)
        else:
            raise FileNotFoundError(f"SQL script not found: {script_path}")

def downgrade() -> None:
    """Remove new table."""
    op.execute("DROP TABLE IF EXISTS new_table CASCADE")
```

### Step 4: Run Migration
```bash
poetry run alembic upgrade head
```

## Environment Management

### Development (Neon)
- **Database**: Neon PostgreSQL
- **Connection**: `NEON_CONNECTION_STRING`
- **Usage**: Development and testing

### Production (AWS RDS)
- **Database**: AWS RDS PostgreSQL
- **Connection**: `database_connection_string`
- **Usage**: Production deployment

### Environment Detection
The system automatically detects environment:
- `ENVIRONMENT=production` → Uses AWS RDS
- `ENVIRONMENT=development` → Uses Neon

## Moving from Dev to Production

### Step 1: Test in Development
```bash
# Ensure all migrations work in dev
poetry run alembic upgrade head
poetry run alembic current
```

### Step 2: Deploy to Production
```bash
# Set production environment
export ENVIRONMENT=production

# Run migrations on production
poetry run alembic upgrade head
```

### Step 3: Verify Production
```bash
# Check production migration status
poetry run alembic current

# Verify tables exist
poetry run alembic show 001
```

## Common Commands

### Check Status
```bash
# Current migration
poetry run alembic current

# Migration history
poetry run alembic history

# Show specific migration
poetry run alembic show 001
```

### Run Migrations
```bash
# Run all pending
poetry run alembic upgrade head

# Run specific revision
poetry run alembic upgrade 001

# Run with SQL preview
poetry run alembic upgrade head --sql
```

### Rollback
```bash
# Rollback one step
poetry run alembic downgrade -1

# Rollback to specific revision
poetry run alembic downgrade 001

# Rollback to base (no migrations)
poetry run alembic downgrade base
```

### Reset Migration State
```bash
# Mark as no migrations applied
poetry run alembic stamp base

# Mark as specific migration applied (without running it)
poetry run alembic stamp 001
```

## Troubleshooting

### Migration File Deleted
If you delete a migration file but the database thinks it's applied:

```bash
# Check current version
poetry run alembic current

# If it shows a deleted revision, fix it:
# Connect to database and run:
UPDATE alembic_version SET version_num = '001';
```

### SQL Script Errors
- **DO $$ blocks**: Must be executed as complete blocks (handled automatically)
- **Comments**: Removed automatically by the migration script
- **File paths**: Ensure SQL files exist in `sql/` directory

### Database Connection Issues
```bash
# Check environment variables
echo $ENVIRONMENT
echo $NEON_CONNECTION_STRING
echo $database_connection_string

# Test connection
poetry run python -c "from app.configs.settings import settings; print(settings.NEON_CONNECTION_STRING)"
```

## Best Practices

### 1. Migration Naming
- Use descriptive names: `add_user_table`, `update_indexes`
- Keep revisions sequential: `001`, `002`, `003`

### 2. SQL File Organization
- Number files for order: `01_`, `02_`, `03_`
- Group related changes together
- Use descriptive names: `create_tables.sql`, `add_indexes.sql`

### 3. Testing
- Always test migrations in development first
- Use `--sql` flag to preview changes
- Test rollback with `downgrade` commands

### 4. Production Deployment
- Never run migrations directly on production without testing
- Use staging environment if available
- Always backup before running migrations
- Run during maintenance windows

## File Structure Example

```
datapipelines/
├── migrations/
│   ├── env.py
│   ├── script.py.mako
│   └── versions/
│       ├── 001_create_founder_schema.py
│       └── 002_add_company_schema.py
├── sql/
│   ├── 01_create_schemas.sql
│   ├── 02_create_founder_tables.sql
│   ├── 03_create_founder_indexes.sql
│   ├── 04_create_founder_constraints.sql
│   ├── 05_create_company_tables.sql
│   └── 06_create_company_indexes.sql
└── alembic.ini
```

## Quick Reference

| Command | Description |
|---------|-------------|
| `alembic current` | Show current migration |
| `alembic upgrade head` | Run all pending migrations |
| `alembic downgrade -1` | Rollback one migration |
| `alembic history` | Show migration history |
| `alembic revision -m "name"` | Create new migration |
| `alembic stamp base` | Reset migration state |
| `alembic show 001` | Show specific migration details | 