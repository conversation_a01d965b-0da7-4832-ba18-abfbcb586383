"""merge heads

Revision ID: 4e0efd965590
Revises: 0770b1d1c8c1, 76fd47fbd398
Create Date: 2025-08-06 23:27:34.575954

"""

from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "4e0efd965590"
down_revision: Union[str, Sequence[str], None] = ("0770b1d1c8c1", "76fd47fbd398")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
