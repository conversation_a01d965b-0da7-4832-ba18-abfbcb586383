"""add_updated_at_to_pipeline_errors

Revision ID: 8010c00b9ec5
Revises: 2c2392ae482e
Create Date: 2025-08-05 18:28:53.491602

"""

from typing import Sequence, Union

import sqlalchemy as sa  # type: ignore
from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "8010c00b9ec5"
down_revision: Union[str, Sequence[str], None] = "2c2392ae482e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add updated_at column to pipeline_errors table."""
    # Add updated_at column to error.pipeline_errors table
    op.add_column(
        "pipeline_errors",
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="error",
    )


def downgrade() -> None:
    """Remove updated_at column from pipeline_errors table."""
    # Remove updated_at column from error.pipeline_errors table
    op.drop_column("pipeline_errors", "updated_at", schema="error")
