"""create pb company tables

Revision ID: 7d28b2c4d74c
Revises: 9553ee0fff88
Create Date: 2025-08-07 12:52:22.300384

"""

from typing import Sequence, Union

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "7d28b2c4d74c"
down_revision: Union[str, Sequence[str], None] = "9553ee0fff88"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    import os
    import re

    # Execute SQL scripts directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")

    # List of SQL scripts to execute in order
    scripts = [
        "08_create_company_pitchbook_tables.sql",
    ]

    for script_name in scripts:
        script_path = os.path.join(sql_dir, script_name)
        if os.path.exists(script_path):
            with open(script_path, "r") as f:
                sql_content = f.read()

            # Remove comments and execute the entire script as one block
            sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

            if sql_content.strip():
                op.execute(sql_content)
        else:
            raise FileNotFoundError(f"SQL script not found: {script_path}")


def downgrade() -> None:
    """Downgrade schema."""
    pass
