"""Create company schema and tables

Revision ID: 003
Revises: 002
Create Date: 2024-01-01 00:00:00.000000

"""

import os

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision = "003"
down_revision = "002"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create company schema and tables."""
    import re

    # Execute SQL scripts directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")

    # List of SQL scripts to execute in order
    scripts = [
        "06_create_company_schema.sql",
    ]

    for script_name in scripts:
        script_path = os.path.join(sql_dir, script_name)
        if os.path.exists(script_path):
            with open(script_path, "r") as f:
                sql_content = f.read()

            # Remove comments and execute the entire script as one block
            sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

            if sql_content.strip():
                op.execute(sql_content)
        else:
            raise FileNotFoundError(f"SQL script not found: {script_path}")


def downgrade() -> None:
    """Drop company schema and tables."""
    # Drop all company tables
    op.execute("DROP TABLE IF EXISTS company.linkedin CASCADE")
    op.execute("DROP TABLE IF EXISTS company.datasources CASCADE")

    # Drop company schema
    op.execute("DROP SCHEMA IF EXISTS company CASCADE")
