"""fix_s3_raw_data_key_serialization

Revision ID: 2c2392ae482e
Revises: 1bd52fe3489b
Create Date: 2025-08-05 18:11:19.894127

"""

from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "2c2392ae482e"
down_revision: Union[str, Sequence[str], None] = "1bd52fe3489b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Fix s3_raw_data_key serialization issue.

    The s3_raw_data_key field should store a simple JSON object mapping sources to keys:
    {"linkedin": "brightdata/linkedin/123.json", "pdl": "pdl/456.json"}

    Instead of the current list format:
    [{"key": "brightdata/linkedin/123.json", "source": "linkedin"}]

    This change should be made in the data processor code, not the database schema.
    The database schema (JSONB) is correct.
    """
    # No schema changes needed - the issue is in the data serialization logic
    # The s3_raw_data_key field should be changed from:
    # [{"key": s3_key, "source": "linkedin"}]
    # to:
    # {"linkedin": s3_key}
    pass


def downgrade() -> None:
    """Downgrade schema."""
    # No schema changes to revert
    pass
