"""Create founder schema

Revision ID: 001
Revises:
Create Date: 2024-01-01 00:00:00.000000

"""

import os

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision = "001"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create founder schema and tables."""
    import re

    # Execute SQL scripts directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")

    # List of SQL scripts to execute in order
    scripts = [
        "01_create_schemas.sql",
        "02_create_founder_tables.sql",
        "03_create_founder_indexes.sql",
        "04_create_founder_constraints.sql",
    ]

    for script_name in scripts:
        script_path = os.path.join(sql_dir, script_name)
        if os.path.exists(script_path):
            with open(script_path, "r") as f:
                sql_content = f.read()

            # Remove comments and execute the entire script as one block
            sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

            if sql_content.strip():
                op.execute(sql_content)
        else:
            raise FileNotFoundError(f"SQL script not found: {script_path}")


def downgrade() -> None:
    """Drop founder schema and tables."""
    # Drop all founder tables
    op.execute("DROP TABLE IF EXISTS founder.signals CASCADE")
    op.execute("DROP TABLE IF EXISTS founder.skills CASCADE")
    op.execute("DROP TABLE IF EXISTS founder.education CASCADE")
    op.execute("DROP TABLE IF EXISTS founder.experiences CASCADE")
    op.execute("DROP TABLE IF EXISTS founder.basic CASCADE")

    # Drop founder schema
    op.execute("DROP SCHEMA IF EXISTS founder CASCADE")
