"""update cb location columns

Revision ID: 9d06a7db7ff2
Revises: 9ea7afe09960
Create Date: 2025-08-07 01:26:33.124582

"""

import os
from typing import Sequence, Union

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "9d06a7db7ff2"
down_revision: Union[str, Sequence[str], None] = "9ea7afe09960"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    import re

    # Execute SQL scripts directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")

    # List of SQL scripts to execute in order
    scripts = [
        "11_update_crunchbase_location_to_text_array.sql",
    ]

    for script_name in scripts:
        script_path = os.path.join(sql_dir, script_name)
        if os.path.exists(script_path):
            with open(script_path, "r") as f:
                sql_content = f.read()

            # Remove comments and execute the entire script as one block
            sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

            if sql_content.strip():
                op.execute(sql_content)
        else:
            raise FileNotFoundError(f"SQL script not found: {script_path}")


def downgrade() -> None:
    """Downgrade schema."""
    pass
