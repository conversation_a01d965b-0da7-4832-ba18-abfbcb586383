"""add_unique_constraints_to_founder_tables

Revision ID: 65a9c7bb8e86
Revises: 3b7d8e4a8a4d
Create Date: 2025-08-05 18:44:05.073308

"""

from typing import Sequence, Union

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "65a9c7bb8e86"
down_revision: Union[str, Sequence[str], None] = "3b7d8e4a8a4d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add unique constraints to founder tables for upsert operations."""
    # Add unique constraint to founder.experiences table
    # This allows upsert on founder_id + company_name + title (simpler, avoids NULL start_date)
    op.create_unique_constraint(
        "uq_experiences_founder_company_title",
        "experiences",
        ["founder_id", "company_name", "title"],
        schema="founder",
    )

    # Add unique constraint to founder.education table
    # This allows upsert on founder_id + school_name (simpler, avoids NULL start_date)
    op.create_unique_constraint(
        "uq_education_founder_school",
        "education",
        ["founder_id", "school_name"],
        schema="founder",
    )

    # Add unique constraint to founder.skills table
    # This allows upsert on founder_id + skill
    op.create_unique_constraint(
        "uq_skills_founder_skill", "skills", ["founder_id", "skill"], schema="founder"
    )


def downgrade() -> None:
    """Remove unique constraints from founder tables."""
    # Remove unique constraint from founder.experiences table
    op.drop_constraint(
        "uq_experiences_founder_company_title", "experiences", schema="founder"
    )

    # Remove unique constraint from founder.education table
    op.drop_constraint("uq_education_founder_school", "education", schema="founder")

    # Remove unique constraint from founder.skills table
    op.drop_constraint("uq_skills_founder_skill", "skills", schema="founder")
