"""Convert JSONB fields to arrays in company.linkedin table

Revision ID: 004
Revises: 003
Create Date: 2024-01-01 00:00:00.000000

"""

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision = "004"
down_revision = "003"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Convert JSONB fields to arrays in company.linkedin table."""

    # Create a helper function to convert JSONB arrays to TEXT arrays
    op.execute("""
        CREATE OR REPLACE FUNCTION jsonb_array_to_text_array(jsonb_data jsonb)
        RETURNS text[] AS $$
        DECLARE
            result text[];
            element jsonb;
        BEGIN
            IF jsonb_data IS NULL THEN
                RETURN NULL;
            END IF;
            
            result := ARRAY[]::text[];
            FOR element IN SELECT * FROM jsonb_array_elements(jsonb_data)
            LOOP
                result := array_append(result, element::text);
            END LOOP;
            
            RETURN result;
        END;
        $$ LANGUAGE plpgsql;
    """)

    # Convert JSONB fields to arrays using the helper function
    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN employees TYPE TEXT[] 
        USING jsonb_array_to_text_array(employees)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN executives TYPE TEXT[] 
        USING jsonb_array_to_text_array(executives)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN "similar" TYPE TEXT[] 
        USING jsonb_array_to_text_array("similar")
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN updates TYPE TEXT[] 
        USING jsonb_array_to_text_array(updates)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN investors TYPE TEXT[] 
        USING jsonb_array_to_text_array(investors)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN affiliated TYPE TEXT[] 
        USING jsonb_array_to_text_array(affiliated)
    """)

    # Drop the helper function
    op.execute("DROP FUNCTION IF EXISTS jsonb_array_to_text_array(jsonb)")

    # Keep funding and stock_info as JSONB since they are complex objects, not arrays
    # Keep linkedin_metadata as JSONB since it's a complex object


def downgrade() -> None:
    """Convert arrays back to JSONB fields in company.linkedin table."""

    # Create a helper function to convert TEXT arrays back to JSONB
    op.execute("""
        CREATE OR REPLACE FUNCTION text_array_to_jsonb_array(text_data text[])
        RETURNS jsonb AS $$
        BEGIN
            IF text_data IS NULL THEN
                RETURN NULL;
            END IF;
            
            RETURN to_jsonb(text_data);
        END;
        $$ LANGUAGE plpgsql;
    """)

    # Convert arrays back to JSONB
    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN employees TYPE JSONB 
        USING text_array_to_jsonb_array(employees)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN executives TYPE JSONB 
        USING text_array_to_jsonb_array(executives)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN "similar" TYPE JSONB 
        USING text_array_to_jsonb_array("similar")
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN updates TYPE JSONB 
        USING text_array_to_jsonb_array(updates)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN investors TYPE JSONB 
        USING text_array_to_jsonb_array(investors)
    """)

    op.execute("""
        ALTER TABLE company.linkedin 
        ALTER COLUMN affiliated TYPE JSONB 
        USING text_array_to_jsonb_array(affiliated)
    """)

    # Drop the helper function
    op.execute("DROP FUNCTION IF EXISTS text_array_to_jsonb_array(text[])")
