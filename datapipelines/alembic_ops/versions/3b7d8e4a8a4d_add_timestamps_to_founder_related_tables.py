"""add_timestamps_to_founder_related_tables

Revision ID: 3b7d8e4a8a4d
Revises: 8010c00b9ec5
Create Date: 2025-08-05 18:40:19.570357

"""

from typing import Sequence, Union

import sqlalchemy as sa  # type: ignore
from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "3b7d8e4a8a4d"
down_revision: Union[str, Sequence[str], None] = "8010c00b9ec5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add created_at and updated_at columns to founder related tables."""
    # Add timestamps to founder.experiences table
    op.add_column(
        "experiences",
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )
    op.add_column(
        "experiences",
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )

    # Add timestamps to founder.education table
    op.add_column(
        "education",
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )
    op.add_column(
        "education",
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )

    # Add timestamps to founder.skills table
    op.add_column(
        "skills",
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )
    op.add_column(
        "skills",
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        schema="founder",
    )


def downgrade() -> None:
    """Remove created_at and updated_at columns from founder related tables."""
    # Remove timestamps from founder.experiences table
    op.drop_column("experiences", "created_at", schema="founder")
    op.drop_column("experiences", "updated_at", schema="founder")

    # Remove timestamps from founder.education table
    op.drop_column("education", "created_at", schema="founder")
    op.drop_column("education", "updated_at", schema="founder")

    # Remove timestamps from founder.skills table
    op.drop_column("skills", "created_at", schema="founder")
    op.drop_column("skills", "updated_at", schema="founder")
