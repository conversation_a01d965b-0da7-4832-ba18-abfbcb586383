"""add_error_schema

Revision ID: 1bd52fe3489b
Revises: 001
Create Date: 2025-08-05 16:51:34.367087

"""

from typing import Sequence, Union

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "1bd52fe3489b"
down_revision: Union[str, Sequence[str], None] = "001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add error schema for uniform error tracking."""
    import os
    import re

    # Execute SQL script directly
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "..", "sql")
    script_path = os.path.join(sql_dir, "05_create_error_schema.sql")

    if os.path.exists(script_path):
        with open(script_path, "r") as f:
            sql_content = f.read()

        # Remove comments and execute the entire script as one block
        sql_content = re.sub(r"--.*$", "", sql_content, flags=re.MULTILINE)

        if sql_content.strip():
            op.execute(sql_content)
    else:
        raise FileNotFoundError(f"SQL script not found: {script_path}")


def downgrade() -> None:
    """Remove error schema."""
    op.execute("DROP TABLE IF EXISTS error.pipeline_errors CASCADE")
    op.execute("DROP SCHEMA IF EXISTS error CASCADE")
