"""Create company.resolved_urls table

Revision ID: 002
Revises: 001
Create Date: 2024-01-01 00:00:00.000000

"""

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision = "002"
down_revision = "001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create company schema and resolved_urls table."""

    # Create company schema if it doesn't exist
    op.execute("CREATE SCHEMA IF NOT EXISTS company")

    # Create company.resolved_urls table
    op.execute("""
        CREATE TABLE IF NOT EXISTS company.resolved_urls (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            
            -- Core identifiers
            company_id VARCHAR(255) NOT NULL,
            org_id VARCHAR(255) NOT NULL,
            company_domain VARCHAR(500) NOT NULL,
            resolver_type VARCHAR(50) NOT NULL,
            
            -- Resolution data
            resolved_url TEXT,
            confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
            status VARCHAR(50) DEFAULT 'pending',
            
            -- Data storage
            s3_raw_data_key TEXT,
            
            -- Error handling
            error_message TEXT,
            error_stage VARCHAR(100),
            
            -- Timestamps
            resolved_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
    """)

    # Create indexes for better performance
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_company_resolved_urls_company_id 
        ON company.resolved_urls(company_id)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_company_resolved_urls_org_id 
        ON company.resolved_urls(org_id)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_company_resolved_urls_resolver_type 
        ON company.resolved_urls(resolver_type)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_company_resolved_urls_status 
        ON company.resolved_urls(status)
    """)

    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_company_resolved_urls_created_at 
        ON company.resolved_urls(created_at)
    """)

    # Create unique constraint for company_id + org_id + resolver_type
    op.execute("""
        CREATE UNIQUE INDEX IF NOT EXISTS idx_company_resolved_urls_unique 
        ON company.resolved_urls(company_id, org_id, resolver_type)
    """)


def downgrade() -> None:
    """Drop company.resolved_urls table."""
    # Drop indexes
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_unique")
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_created_at")
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_status")
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_resolver_type")
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_org_id")
    op.execute("DROP INDEX IF EXISTS idx_company_resolved_urls_company_id")

    # Drop table
    op.execute("DROP TABLE IF EXISTS company.resolved_urls CASCADE")

    # Drop company schema if no other tables exist
    op.execute("""
        DROP SCHEMA IF EXISTS company CASCADE
    """)
