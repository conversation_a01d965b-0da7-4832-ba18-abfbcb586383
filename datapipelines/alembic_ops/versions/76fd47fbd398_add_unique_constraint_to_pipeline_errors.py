"""add_unique_constraint_to_pipeline_errors

Revision ID: 76fd47fbd398
Revises: 65a9c7bb8e86
Create Date: 2025-08-05 18:46:55.175984

"""

from typing import Sequence, Union

from alembic import op  # type: ignore

# revision identifiers, used by Alembic.
revision: str = "76fd47fbd398"
down_revision: Union[str, Sequence[str], None] = "65a9c7bb8e86"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add unique constraint to error.pipeline_errors table for upsert operations."""
    # Add unique constraint to error.pipeline_errors table
    # This allows upsert on pipeline_name + entity_id + error_type
    op.create_unique_constraint(
        "uq_pipeline_errors_pipeline_entity_type",
        "pipeline_errors",
        ["pipeline_name", "entity_id", "error_type"],
        schema="error",
    )


def downgrade() -> None:
    """Remove unique constraint from error.pipeline_errors table."""
    # Remove unique constraint from error.pipeline_errors table
    op.drop_constraint(
        "uq_pipeline_errors_pipeline_entity_type", "pipeline_errors", schema="error"
    )
