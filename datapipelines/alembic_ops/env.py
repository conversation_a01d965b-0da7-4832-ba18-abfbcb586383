"""
Alembic environment configuration for TractionX Data Pipeline Service.

This file configures Alembic to work with our PostgreSQL database and
execute our custom SQL migration scripts.
"""

import os

# Import our app configuration
import sys
from logging.config import fileConfig

from alembic import context  # type: ignore
from sqlalchemy import engine_from_config, pool  # type: ignore

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.configs import get_logger
from app.configs.settings import settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = None

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

logger = get_logger(__name__)


def get_database_url():
    """Get the database URL from settings."""
    # Use Neon for development/staging, AWS RDS for production
    if settings.ENVIRONMENT.lower() == "production":
        return settings.database_connection_string
    else:
        return settings.NEON_CONNECTION_STRING


def run_sql_script(connection, script_path):
    """Run a SQL script file."""
    try:
        with open(script_path, "r") as f:
            sql_content = f.read()

        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(";") if stmt.strip()]

        for statement in statements:
            if statement and not statement.startswith("--"):
                connection.execute(statement)

        logger.info(f"Successfully executed SQL script: {script_path}")

    except Exception as e:
        logger.error(f"Error executing SQL script {script_path}: {e}")
        raise


def run_versioned_sql_scripts(connection, revision):
    """Run SQL scripts based on the revision."""
    sql_dir = os.path.join(os.path.dirname(__file__), "..", "sql")

    # Map revisions to SQL scripts
    revision_scripts = {
        "001_create_founder_schema": [
            "01_create_schemas.sql",
            "02_create_founder_tables.sql",
            "04_create_founder_indexes.sql",
            "05_create_founder_constraints.sql",
        ]
    }

    if revision in revision_scripts:
        for script_name in revision_scripts[revision]:
            script_path = os.path.join(sql_dir, script_name)
            if os.path.exists(script_path):
                run_sql_script(connection, script_path)
            else:
                logger.warning(f"SQL script not found: {script_path}")


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Override the database URL with our actual connection string
    config.set_main_option("sqlalchemy.url", get_database_url())

    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            # Disable autogenerate since we're using custom SQL scripts
            compare_type=False,
            compare_server_default=False,
        )

        with context.begin_transaction():
            # Get the current revision being applied
            # revision = context.get_revision_argument()
            # if revision:
            #     run_versioned_sql_scripts(connection, revision)

            # Run the actual migration
            context.run_migrations()


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # Override the database URL with our actual connection string
    config.set_main_option("sqlalchemy.url", get_database_url())

    context.configure(
        url=config.get_main_option("sqlalchemy.url"),
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
