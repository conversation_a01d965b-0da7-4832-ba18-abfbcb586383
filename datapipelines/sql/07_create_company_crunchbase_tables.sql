-- Create company.crunchbase table
CREATE TABLE IF NOT EXISTS company.crunchbase (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    company_id VARCHAR(255) NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    
    -- Crunchbase-specific fields
    crunchbase_url TEXT,
    crunchbase_id VARCHAR(255),
    brightdata_snapshot_id VARCHAR(255),
    
    -- Basic company info()
    name VARCHA<PERSON>(500),
    legal_name VARCHAR(500),
    description TEXT,
    about TEXT,
    full_description TEXT,
    website TEXT,
    domain VARCHAR(255),
    
    -- Business details
    company_type VARCHAR(255),
    operating_status VARCHAR(255),
    ipo_status VARCHAR(255),
    founded_date VARCHAR(255),
    founded_year INTEGER,
    employee_count INTEGER,
    employee_count_range VARCHAR(255),
    
    -- Location
    headquarters TEXT,
    country VARCHAR(255),
    country_code VARCHAR(10),
    city VARCHAR(255),
    state VARCHAR(255),
    region VARCHAR(255),
    location JSONB,
    address TEXT,
    
    -- Industries and categories
    industries JSONB,
    industry VARCHAR(255),
    sub_industry VARCHAR(255),
    
    -- Financial information
    funding_total DECIMAL(15,2),
    funding_rounds JSONB,
    funding_rounds_list JSONB,
    last_funding_date VARCHAR(255),
    last_funding_amount DECIMAL(15,2),
    valuation DECIMAL(15,2),
    revenue DECIMAL(15,2),
    
    -- Social presence
    social_media_links TEXT[],
    linkedin_url TEXT,
    twitter_url TEXT,
    facebook_url TEXT,
    
    -- Contact information
    email VARCHAR(255),
    phone VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(255),
    
    -- Team and people
    founders JSONB,
    executives JSONB,
    current_employees JSONB,
    alumni JSONB,
    
    -- Company details
    technologies TEXT[],
    keywords TEXT[],
    competitors TEXT[],
    similar_companies JSONB,
    
    -- Additional Crunchbase data
    image TEXT,
    cb_rank INTEGER,
    monthly_visits INTEGER,
    semrush_visits_latest_month INTEGER,
    monthly_visits_growth DECIMAL(10,2),
    num_contacts INTEGER,
    num_contacts_linkedin INTEGER,
    num_employee_profiles INTEGER,
    num_news INTEGER,
    num_investors INTEGER,
    num_event_appearances INTEGER,
    num_acquisitions INTEGER,
    num_investments INTEGER,
    num_exits INTEGER,
    
    -- Featured lists and highlights
    featured_list JSONB,
    overview_highlights JSONB,
    people_highlights JSONB,
    technology_highlights JSONB,
    
    -- Additional data
    bombora JSONB,
    investors JSONB,
    event_appearances JSONB,
    acquisitions JSONB,
    funds_raised JSONB,
    investments JSONB,
    exits JSONB,
    news JSONB,
    
    -- Additional Crunchbase data
    crunchbase_metadata JSONB,
    enrichment_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for company.crunchbase
CREATE INDEX IF NOT EXISTS idx_company_crunchbase_company_id ON company.crunchbase(company_id);
CREATE INDEX IF NOT EXISTS idx_company_crunchbase_org_id ON company.crunchbase(org_id);
CREATE INDEX IF NOT EXISTS idx_company_crunchbase_company_org ON company.crunchbase(company_id, org_id);
CREATE INDEX IF NOT EXISTS idx_company_crunchbase_crunchbase_url ON company.crunchbase(crunchbase_url);
CREATE INDEX IF NOT EXISTS idx_company_crunchbase_brightdata_snapshot_id ON company.crunchbase(brightdata_snapshot_id);
