-- Create founder.spider_chart table for storing LLM-generated founder signals
CREATE TABLE IF NOT EXISTS founder.spider_chart (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    founder_id UUID REFERENCES founder.basic(id) ON DELETE CASCADE,
    
    -- Scoring
    score INTEGER CHECK (score >= 0 AND score <= 100),
    tags TEXT[],
    
    -- Analysis
    strengths TEXT[],
    risks TEXT[],
    
    -- Skill profile for spider chart visualization
    business_score INTEGER CHECK (business_score >= 0 AND business_score <= 10),
    operations_score INTEGER CHECK (operations_score >= 0 AND operations_score <= 10),
    fundraising_score INTEGER CHECK (fundraising_score >= 0 AND fundraising_score <= 10),
    product_score INTEGER CHECK (product_score >= 0 AND product_score <= 10),
    tech_score INTEGER CHECK (tech_score >= 0 AND tech_score <= 10),
    
    -- Metadata
    generated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    llm_model VARCHAR(255),
    prompt_version VARCHAR(50) DEFAULT 'v1',
    processing_time FLOAT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Essential indexes only (reduced from 6 to 3)
-- 1. Founder ID for lookups (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_spider_chart_founder_id ON founder.spider_chart(founder_id);

-- 2. Score for ranking/filtering (common for analytics)
CREATE INDEX IF NOT EXISTS idx_spider_chart_score ON founder.spider_chart(score);

-- 3. GIN index for tags (most commonly queried array field)
CREATE INDEX IF NOT EXISTS idx_spider_chart_tags ON founder.spider_chart USING GIN(tags);

-- Add unique constraint to prevent duplicate signals for the same founder
ALTER TABLE founder.spider_chart ADD CONSTRAINT spider_chart_founder_unique UNIQUE (founder_id);
