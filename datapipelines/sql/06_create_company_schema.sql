-- Create company schema and tables for TractionX Data Pipeline Service
-- This file creates the company schema and related tables

-- Create company schema
CREATE SCHEMA IF NOT EXISTS company;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA company TO PUBLIC;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set search path to include our schemas
SET search_path TO company, public;

-- Create company.datasources table
CREATE TABLE IF NOT EXISTS company.datasources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    company_id VARCHAR(255) NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    source VARCHAR(100) NOT NULL,
    
    -- Metadata
    source_url TEXT,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    enrichment_date TIMESTAMPTZ,
    
    -- Raw data backup
    s3_raw_data_key TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create company.linkedin table
CREATE TABLE IF NOT EXISTS company.linkedin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    company_id VARCHAR(255) NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    
    -- LinkedIn-specific fields
    linkedin_url TEXT,
    linkedin_id VARCHAR(255),
    brightdata_snapshot_id VARCHAR(255),
    
    -- Basic company info
    name VARCHAR(500),
    description TEXT,
    about TEXT,
    overview TEXT,
    website TEXT,
    domain VARCHAR(255),
    
    -- Business details
    industry VARCHAR(255),
    industries TEXT,
    company_type VARCHAR(255),
    organization_type VARCHAR(255),
    company_size VARCHAR(255),
    founded_year INTEGER,
    employee_count INTEGER,
    employees_in_linkedin INTEGER,
    employee_count_range VARCHAR(255),
    
    -- Location
    headquarters TEXT,
    country VARCHAR(255),
    country_code VARCHAR(10),
    country_codes_array TEXT[],
    city VARCHAR(255),
    state VARCHAR(255),
    locations TEXT[],
    formatted_locations TEXT[],
    
    -- Social metrics
    followers_count INTEGER,
    connections_count INTEGER,
    
    -- Company details
    specialties TEXT[],
    technologies TEXT[],
    keywords TEXT[],
    competitors TEXT[],
    
    -- Contact information
    email VARCHAR(255),
    phone VARCHAR(255),
    
    -- Employee and executive data
    employees JSONB,
    executives JSONB,
    
    -- Additional LinkedIn data
    image TEXT,
    logo TEXT,
    slogan TEXT,
    "similar" JSONB,
    updates JSONB,
    crunchbase_url TEXT,
    funding JSONB,
    investors JSONB,
    stock_info JSONB,
    affiliated JSONB,
    
    -- Additional LinkedIn data
    linkedin_metadata JSONB,
    enrichment_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for company.datasources
CREATE INDEX IF NOT EXISTS idx_company_datasources_company_id ON company.datasources(company_id);
CREATE INDEX IF NOT EXISTS idx_company_datasources_org_id ON company.datasources(org_id);
CREATE INDEX IF NOT EXISTS idx_company_datasources_source ON company.datasources(source);
CREATE INDEX IF NOT EXISTS idx_company_datasources_company_org_source ON company.datasources(company_id, org_id, source);

-- Create indexes for company.linkedin
CREATE INDEX IF NOT EXISTS idx_company_linkedin_company_id ON company.linkedin(company_id);
CREATE INDEX IF NOT EXISTS idx_company_linkedin_org_id ON company.linkedin(org_id);
CREATE INDEX IF NOT EXISTS idx_company_linkedin_company_org ON company.linkedin(company_id, org_id);
CREATE INDEX IF NOT EXISTS idx_company_linkedin_linkedin_url ON company.linkedin(linkedin_url);
CREATE INDEX IF NOT EXISTS idx_company_linkedin_brightdata_snapshot_id ON company.linkedin(brightdata_snapshot_id);

\
-- Create unique constraints
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_datasources_unique ON company.datasources(company_id, org_id, source);
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_linkedin_unique ON company.linkedin(company_id, org_id);

-- Add comments for documentation
COMMENT ON TABLE company.datasources IS 'Stores metadata about data sources for companies';
COMMENT ON TABLE company.linkedin IS 'Stores LinkedIn enrichment data for companies';
COMMENT ON COLUMN company.datasources.company_id IS 'Company identifier';
COMMENT ON COLUMN company.datasources.org_id IS 'Organization identifier';
COMMENT ON COLUMN company.datasources.source IS 'Data source (linkedin, crunchbase, apollo, etc.)';
COMMENT ON COLUMN company.linkedin.company_id IS 'Company identifier';
COMMENT ON COLUMN company.linkedin.org_id IS 'Organization identifier';
COMMENT ON COLUMN company.linkedin.linkedin_id IS 'LinkedIn company ID';
COMMENT ON COLUMN company.linkedin.brightdata_snapshot_id IS 'BrightData snapshot ID'; 