-- Create company.pitchbook table
CREATE TABLE IF NOT EXISTS company.pitchbook (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    company_id VARCHAR(255) NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    
    -- PitchBook-specific fields
    pitchbook_url TEXT,
    pitchbook_id VARCHAR(255),
    brightdata_snapshot_id VARCHAR(255),
    
    -- Basic company info
    name VARCHAR(500),
    description TEXT,
    website TEXT,
    domain VARCHAR(255),
    
    -- Business details
    company_type VARCHAR(255),
    status VARCHAR(255),
    founded_year INTEGER,
    employee_count INTEGER,
    
    -- Location
    headquarters TEXT,
    country VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    
    -- Financial information
    funding_total DECIMAL(15,2),
    funding_rounds INTEGER,
    last_funding_date VARCHAR(255),
    last_funding_amount DECIMAL(15,2),
    latest_deal_amount DECIMAL(15,2),
    latest_deal_type VARCHAR(255),
    valuation DECIMAL(15,2),
    revenue DECIMAL(15,2),
    
    -- Investment information
    investments_count INTEGER,
    investments TEXT[],
    investment_relationships TEXT[],
    
    -- Competitor information
    competitors TEXT[],
    
    -- Contact information
    contact_information TEXT[],
    email VARCHAR(255),
    phone VARCHAR(255),
    
    -- Social presence
    linkedin_url TEXT,
    twitter_url TEXT,
    facebook_url TEXT,
    
    -- Company details
    technologies TEXT[],
    keywords TEXT[],
    patents TEXT[],
    patent_activity JSONB,
    
    -- Research and analysis
    research_analysis JSONB,
    faq TEXT[],
    
    -- Additional PitchBook data
    pitchbook_metadata JSONB,
    enrichment_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for company.pitchbook table
CREATE INDEX IF NOT EXISTS idx_company_pitchbook_company_id ON company.pitchbook(company_id);
CREATE INDEX IF NOT EXISTS idx_company_pitchbook_org_id ON company.pitchbook(org_id);
CREATE INDEX IF NOT EXISTS idx_company_pitchbook_company_org ON company.pitchbook(company_id, org_id);
CREATE INDEX IF NOT EXISTS idx_company_pitchbook_pitchbook_url ON company.pitchbook(pitchbook_url);
CREATE INDEX IF NOT EXISTS idx_company_pitchbook_brightdata_snapshot_id ON company.pitchbook(brightdata_snapshot_id);

-- Create unique constraint
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_pitchbook_unique ON company.pitchbook(company_id, org_id); 