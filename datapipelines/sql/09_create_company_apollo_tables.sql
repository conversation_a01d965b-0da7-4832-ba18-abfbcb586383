-- Create company.apollo table
CREATE TABLE IF NOT EXISTS company.apollo (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    company_id VARCHAR(255) NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    
    -- Apollo-specific fields
    apollo_id VARCHAR(255),
    
    -- Basic info
    name VARCHAR(500),
    domain VARCHAR(255),
    website TEXT,
    description TEXT,
    
    -- Business details
    industry VARCHAR(255),
    sub_industry VARCHAR(255),
    employee_count INTEGER,
    employee_count_range VARCHAR(255),
    founded_year INTEGER,
    
    -- Location
    headquarters TEXT,
    country VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(255),
    
    -- Financial
    funding_total DECIMAL(15,2),
    funding_rounds INTEGER,
    last_funding_date TIMESTAMPTZ,
    last_funding_amount DECIMAL(15,2),
    valuation DECIMAL(15,2),
    revenue DECIMAL(15,2),
    
    -- Social presence
    linkedin_url TEXT,
    twitter_url TEXT,
    facebook_url TEXT,
    
    -- Contact info
    email VARCHAR(255),
    phone VARCHAR(255),
    
    -- Array fields (stored as TEXT[] for efficient querying)
    technologies TEXT[],
    keywords TEXT[],
    
    -- JSON fields for complex data
    tech_stack JSONB,
    departmental_head_count JSONB,
    
    -- Additional Apollo data
    apollo_metadata JSONB,
    enrichment_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Essential indexes only (reduced from 9 to 4)
-- 1. Composite index for most common query pattern (company_id, org_id)
CREATE INDEX IF NOT EXISTS idx_company_apollo_company_org ON company.apollo(company_id, org_id);

-- 2. Apollo ID for lookups
CREATE INDEX IF NOT EXISTS idx_company_apollo_apollo_id ON company.apollo(apollo_id);

-- 3. GIN index for technologies (most commonly queried array)
CREATE INDEX IF NOT EXISTS idx_company_apollo_technologies ON company.apollo USING GIN(technologies);

-- 4. GIN index for apollo_metadata (for JSON queries)
CREATE INDEX IF NOT EXISTS idx_company_apollo_apollo_metadata ON company.apollo USING GIN(apollo_metadata);

-- Create unique constraint
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_apollo_unique ON company.apollo(company_id, org_id); 