-- Create indexes for founder tables for optimal query performance
-- This file creates all necessary indexes for the founder schema

-- Core founder table indexes
CREATE INDEX IF NOT EXISTS idx_basic_founder_id ON founder.basic(founder_id);
CREATE INDEX IF NOT EXISTS idx_basic_org_id ON founder.basic(org_id);
CREATE INDEX IF NOT EXISTS idx_basic_company_id ON founder.basic(company_id);
CREATE INDEX IF NOT EXISTS idx_basic_org_company ON founder.basic(org_id, company_id);
CREATE INDEX IF NOT EXISTS idx_basic_source ON founder.basic(source);
CREATE INDEX IF NOT EXISTS idx_basic_confidence ON founder.basic(confidence_score);
CREATE INDEX IF NOT EXISTS idx_basic_enrichment_date ON founder.basic(enrichment_date);

-- Experiences indexes
CREATE INDEX IF NOT EXISTS idx_experiences_founder_id ON founder.experiences(founder_id);
CREATE INDEX IF NOT EXISTS idx_experiences_company ON founder.experiences(company_name);
CREATE INDEX IF NOT EXISTS idx_experiences_primary ON founder.experiences(founder_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_experiences_title ON founder.experiences(title);
CREATE INDEX IF NOT EXISTS idx_experiences_industry ON founder.experiences(industry);
CREATE INDEX IF NOT EXISTS idx_experiences_dates ON founder.experiences(start_date, end_date);

-- Education indexes
CREATE INDEX IF NOT EXISTS idx_education_founder_id ON founder.education(founder_id);
CREATE INDEX IF NOT EXISTS idx_education_school ON founder.education(school_name);
CREATE INDEX IF NOT EXISTS idx_education_dates ON founder.education(start_date, end_date);

-- Skills indexes
CREATE INDEX IF NOT EXISTS idx_skills_founder_id ON founder.skills(founder_id);
CREATE INDEX IF NOT EXISTS idx_skills_skill ON founder.skills(skill);


-- Signals indexes
CREATE INDEX IF NOT EXISTS idx_signals_founder_id ON founder.signals(founder_id);
CREATE INDEX IF NOT EXISTS idx_signals_score ON founder.signals(score);
CREATE INDEX IF NOT EXISTS idx_signals_generated_at ON founder.signals(generated_at);
