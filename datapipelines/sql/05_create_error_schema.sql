-- Create error schema for uniform error tracking across all pipelines
-- This file creates the error schema and basic error table

-- Create error schema
CREATE SCHEMA IF NOT EXISTS error;

-- Create basic error table for all pipeline errors
CREATE TABLE error.pipeline_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pipeline_name VARCHAR(100) NOT NULL,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    entity_id VARCHAR(255),
    entity_type VARCHAR(50),
    org_id VARCHAR(255),
    company_id VARCHAR(255),
    raw_data_key TEXT,
    payload_preview TEXT,
    stack_trace TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for error tracking
CREATE INDEX IF NOT EXISTS idx_error_pipeline ON error.pipeline_errors(pipeline_name);
CREATE INDEX IF NOT EXISTS idx_error_type ON error.pipeline_errors(error_type);
CREATE INDEX IF NOT EXISTS idx_error_entity ON error.pipeline_errors(entity_id, entity_type);
CREATE INDEX IF NOT EXISTS idx_error_org ON error.pipeline_errors(org_id);
CREATE INDEX IF NOT EXISTS idx_error_company ON error.pipeline_errors(company_id);
CREATE INDEX IF NOT EXISTS idx_error_created_at ON error.pipeline_errors(created_at);

-- Grant permissions
GRANT USAGE ON SCHEMA error TO PUBLIC;
GRANT ALL ON error.pipeline_errors TO PUBLIC; 