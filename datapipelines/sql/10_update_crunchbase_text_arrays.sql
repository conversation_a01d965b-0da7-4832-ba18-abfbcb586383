-- Migration to update company.crunchbase table to use TEXT[] for list fields
-- This migration converts JSONB fields to TEXT[] to match the Crunchbase data structure

-- Helper function to convert JSONB array to TEXT array
CREATE OR REPLACE FUNCTION jsonb_array_to_text_array(jsonb_data jsonb)
RETURNS text[] AS $$
DECLARE
    result text[];
    item jsonb;
BEGIN
    IF jsonb_data IS NULL THEN
        RETURN NULL;
    END IF;
    
    IF jsonb_typeof(jsonb_data) != 'array' THEN
        RETURN ARRAY[jsonb_data::text];
    END IF;
    
    result := ARRAY[]::text[];
    FOR item IN SELECT * FROM jsonb_array_elements(jsonb_data)
    LOOP
        result := array_append(result, item::text);
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Update industries field
ALTER TABLE company.crunchbase 
ALTER COLUMN industries TYPE TEXT[] USING jsonb_array_to_text_array(industries);

-- Update founders field
ALTER TABLE company.crunchbase 
ALTER COLUMN founders TYPE TEXT[] USING jsonb_array_to_text_array(founders);

-- Update executives field
ALTER TABLE company.crunchbase 
ALTER COLUMN executives TYPE TEXT[] USING jsonb_array_to_text_array(executives);

-- Update current_employees field
ALTER TABLE company.crunchbase 
ALTER COLUMN current_employees TYPE TEXT[] USING jsonb_array_to_text_array(current_employees);

-- Update alumni field
ALTER TABLE company.crunchbase 
ALTER COLUMN alumni TYPE TEXT[] USING jsonb_array_to_text_array(alumni);

-- Update similar_companies field
ALTER TABLE company.crunchbase 
ALTER COLUMN similar_companies TYPE TEXT[] USING jsonb_array_to_text_array(similar_companies);

-- Update featured_list field
ALTER TABLE company.crunchbase 
ALTER COLUMN featured_list TYPE TEXT[] USING jsonb_array_to_text_array(featured_list);

-- Update bombora field
ALTER TABLE company.crunchbase 
ALTER COLUMN bombora TYPE TEXT[] USING jsonb_array_to_text_array(bombora);

-- Update investors field
ALTER TABLE company.crunchbase 
ALTER COLUMN investors TYPE TEXT[] USING jsonb_array_to_text_array(investors);

-- Update event_appearances field
ALTER TABLE company.crunchbase 
ALTER COLUMN event_appearances TYPE TEXT[] USING jsonb_array_to_text_array(event_appearances);

-- Update acquisitions field
ALTER TABLE company.crunchbase 
ALTER COLUMN acquisitions TYPE TEXT[] USING jsonb_array_to_text_array(acquisitions);

-- Update funds_raised field
ALTER TABLE company.crunchbase 
ALTER COLUMN funds_raised TYPE TEXT[] USING jsonb_array_to_text_array(funds_raised);

-- Update investments field
ALTER TABLE company.crunchbase 
ALTER COLUMN investments TYPE TEXT[] USING jsonb_array_to_text_array(investments);

-- Update exits field
ALTER TABLE company.crunchbase 
ALTER COLUMN exits TYPE TEXT[] USING jsonb_array_to_text_array(exits);

-- Drop the helper function
DROP FUNCTION jsonb_array_to_text_array(jsonb); 