-- Create constraints for founder tables
-- This file creates unique constraints and foreign key relationships

-- Unique constraints to prevent duplicates
DO $$ BEGIN
    ALTER TABLE founder.skills ADD CONSTRAINT skills_unique UNIQUE (founder_id, skill);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE founder.signals ADD CONSTRAINT signals_unique UNIQUE (founder_id, generated_at);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add unique constraint on founder_id and company_id combination for basic table
DO $$ BEGIN
    ALTER TABLE founder.basic ADD CONSTRAINT basic_id_company_id_unique UNIQUE (id, company_id);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add check constraints for data validation
DO $$ BEGIN
    ALTER TABLE founder.basic ADD CONSTRAINT basic_confidence_check 
        CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE founder.signals ADD CONSTRAINT signals_score_check 
        CHECK (score >= 0 AND score <= 100);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add not null constraints where appropriate
ALTER TABLE founder.basic ALTER COLUMN founder_id SET NOT NULL;
ALTER TABLE founder.basic ALTER COLUMN org_id SET NOT NULL;
ALTER TABLE founder.basic ALTER COLUMN company_id SET NOT NULL;

ALTER TABLE founder.experiences ALTER COLUMN founder_id SET NOT NULL;
ALTER TABLE founder.education ALTER COLUMN founder_id SET NOT NULL;
ALTER TABLE founder.skills ALTER COLUMN founder_id SET NOT NULL;
ALTER TABLE founder.skills ALTER COLUMN skill SET NOT NULL;
ALTER TABLE founder.signals ALTER COLUMN founder_id SET NOT NULL;