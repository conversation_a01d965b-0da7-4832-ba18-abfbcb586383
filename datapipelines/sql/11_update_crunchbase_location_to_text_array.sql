-- Update company.crunchbase location field from JSONB to TEXT[]
-- This migration converts the location field to store location data as a text array

-- First, let's check if the location field exists and has data
DO $$
BEGIN
    -- Check if location column exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'company' 
        AND table_name = 'crunchbase' 
        AND column_name = 'location'
    ) THEN
        -- Convert JSONB location data to TEXT[] array
        -- Extract location names from the JSONB array and convert to text array
        ALTER TABLE company.crunchbase 
        ALTER COLUMN location TYPE TEXT[] 
        USING (
            CASE 
                WHEN location IS NULL THEN NULL
                WHEN jsonb_typeof(location) = 'array' THEN
                    ARRAY(
                        SELECT jsonb_extract_path_text(item, 'name')
                        FROM jsonb_array_elements(location) AS item
                        WHERE jsonb_extract_path_text(item, 'name') IS NOT NULL 
                        AND jsonb_extract_path_text(item, 'name') != ''
                    )
                ELSE NULL
            END
        );
        
        RAISE NOTICE 'Successfully converted location field from JSONB to TEXT[]';
    ELSE
        RAISE NOTICE 'Location column does not exist in company.crunchbase table';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error converting location field: %', SQLERRM;
        -- If conversion fails, create a new column and drop the old one
        BEGIN
            ALTER TABLE company.crunchbase ADD COLUMN location_new TEXT[];
            
            -- Convert existing data to new column
            UPDATE company.crunchbase 
            SET location_new = (
                CASE 
                    WHEN location IS NULL THEN NULL
                    WHEN jsonb_typeof(location) = 'array' THEN
                        ARRAY(
                            SELECT jsonb_extract_path_text(item, 'name')
                            FROM jsonb_array_elements(location) AS item
                            WHERE jsonb_extract_path_text(item, 'name') IS NOT NULL 
                            AND jsonb_extract_path_text(item, 'name') != ''
                        )
                    ELSE NULL
                END
            );
            
            -- Drop old column and rename new one
            ALTER TABLE company.crunchbase DROP COLUMN location;
            ALTER TABLE company.crunchbase RENAME COLUMN location_new TO location;
            
            RAISE NOTICE 'Successfully converted location field using fallback method';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Fallback conversion also failed: %', SQLERRM;
        END;
END $$;

-- Add a comment to document the change
COMMENT ON COLUMN company.crunchbase.location IS 'Location data stored as TEXT[] array of location names'; 