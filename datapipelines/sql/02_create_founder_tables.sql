-- Create founder tables for TractionX Data Pipeline Service
-- This file creates all founder-related tables in the founder schema

-- Core founder table (matches Founder<PERSON><PERSON>ord model)
CREATE TABLE founder.basic (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    founder_id VARCHAR(255) UNIQUE NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(500),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    avatar_url TEXT,
    headline TEXT,
    summary TEXT,
    location JSONB,
    current_job_title VARCHAR(255),
    current_job_company VARCHAR(255),
    social_profiles JSONB,
    org_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    source VARCHAR(100),
    confidence_score FLOAT CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    enrichment_date TIMESTAMPTZ,
    s3_raw_data_key JSONB,
    links JSONB,
    extra_data JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Founder experiences table (matches FounderExperience model)
CR<PERSON>TE TABLE founder.experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    founder_id UUID REFERENCES founder.basic(id) ON DELETE CASCADE,
    company_name VARCHAR(255),
    title VARCHAR(255),
    industry VARCHAR(255),
    company_size VARCHAR(50),
    company_url TEXT,
    company_logo_url TEXT,
    start_date DATE,
    end_date DATE,
    is_primary BOOLEAN,
    location TEXT,
    description TEXT
);

-- Founder education table (matches FounderEducation model)
CREATE TABLE founder.education (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    founder_id UUID REFERENCES founder.basic(id) ON DELETE CASCADE,
    school_name VARCHAR(255),
    degrees TEXT[],
    majors TEXT[],
    school_url TEXT,
    institute_logo_url TEXT,
    start_date DATE,
    end_date DATE,
    location TEXT,
    description TEXT
);

-- Founder skills table (matches FounderSkill model)
CREATE TABLE founder.skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    founder_id UUID REFERENCES founder.basic(id) ON DELETE CASCADE,
    skill TEXT NOT NULL
);

-- Founder signals table (matches FounderSignal model)
CREATE TABLE founder.signals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    founder_id UUID REFERENCES founder.basic(id) ON DELETE CASCADE,
    score INTEGER CHECK (score >= 0 AND score <= 100),
    tags TEXT[],
    strengths JSONB,
    risks JSONB,
    generated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
