-- Update company.crunchbase country_code field from VARCHAR(10) to TEXT
-- This migration increases the country_code field to TEXT to accommodate longer country names

-- First, let's check if the country_code field exists
DO $$
BEGIN
    -- Check if country_code column exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'company' 
        AND table_name = 'crunchbase' 
        AND column_name = 'country_code'
    ) THEN
        -- Convert country_code field from VARCHAR(10) to TEXT
        ALTER TABLE company.crunchbase 
        ALTER COLUMN country_code TYPE TEXT;
        
        RAISE NOTICE 'Successfully updated country_code field from VARCHAR(10) to TEXT';
    ELSE
        RAISE NOTICE 'country_code column does not exist in company.crunchbase table';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating country_code field: %', SQLERRM;
END $$;

-- Add a comment to document the change
COMMENT ON COLUMN company.crunchbase.country_code IS 'Country code or name (changed to TEXT to accommodate longer country names)'; 