-- Create schemas for TractionX Data Pipeline Service
-- This file creates the founder and company schemas in Neon (dev database)

-- Create founder schema
CREATE SCHEMA IF NOT EXISTS founder;


-- Grant necessary permissions
GRANT USAGE ON SCHEMA founder TO PUBLIC;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set search path to include our schemas
SET search_path TO founder, public; 