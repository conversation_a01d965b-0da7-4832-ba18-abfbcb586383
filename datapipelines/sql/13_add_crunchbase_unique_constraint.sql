-- Add unique constraint to company.crunchbase table for upsert operations
-- This migration adds a unique constraint on (company_id, org_id) to support ON CONFLICT operations

-- First, let's check if the constraint already exists
DO $$
BEGIN
    -- Check if unique constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'company_crunchbase_company_org_unique' 
        AND conrelid = 'company.crunchbase'::regclass
    ) THEN
        -- Add unique constraint on (company_id, org_id)
        ALTER TABLE company.crunchbase 
        ADD CONSTRAINT company_crunchbase_company_org_unique 
        UNIQUE (company_id, org_id);
        
        RAISE NOTICE 'Successfully added unique constraint on (company_id, org_id) for company.crunchbase table';
    ELSE
        RAISE NOTICE 'Unique constraint company_crunchbase_company_org_unique already exists on company.crunchbase table';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error adding unique constraint: %', SQLERRM;
        -- If there are duplicate records, we need to handle them first
        BEGIN
            -- Delete duplicate records keeping only the most recent one
            DELETE FROM company.crunchbase 
            WHERE id NOT IN (
                SELECT DISTINCT ON (company_id, org_id) id 
                FROM company.crunchbase 
                ORDER BY company_id, org_id, updated_at DESC
            );
            
            -- Now try to add the constraint again
            ALTER TABLE company.crunchbase 
            ADD CONSTRAINT company_crunchbase_company_org_unique 
            UNIQUE (company_id, org_id);
            
            RAISE NOTICE 'Successfully added unique constraint after cleaning duplicate records';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Failed to add unique constraint even after cleaning duplicates: %', SQLERRM;
        END;
END $$;

-- Add a comment to document the change
COMMENT ON CONSTRAINT company_crunchbase_company_org_unique ON company.crunchbase IS 'Unique constraint on (company_id, org_id) to support upsert operations'; 