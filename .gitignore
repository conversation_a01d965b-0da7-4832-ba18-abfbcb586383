# Dependencies
node_modules/
**/node_modules/
**/node_modules/*

# Next.js
.next/
**/.next/
out/
dist/

# Environment variables
.env
.env.local
.env.dev
.env.test.com
.env.main
.env.development.local
.env.test.local
.env.production.local

# Python
.venv/
__pycache__/
**/__pycache__/
*.py[cod]
.pytest_cache
.mypy_cache

# Build outputs
out/
dist/
build/
*.tsbuildinfo

# System files
.DS_Store
.DS_Store/ 
Thumbs.db

# IDE
.vscode/
.cursor/
.idea/
*.iml
*.ipr
*.iws

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
tests/
coverage/

# Scripts
scripts/

# Database files
*.db
*.ipynb
migrations/

# docker-compose
docker-compose.yml
Dockerfile

# Lock files (uncomment if you want to exclude them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
# poetry.lock

# promtail should only be active in production
promtail-config.yml
backend.log.*