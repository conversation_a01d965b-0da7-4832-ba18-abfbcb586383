# 🧾 Remote Loki Logging Setup Guide

## 🎯 Overview
This guide sets up reliable, structured log streaming from the FastAPI backend to a remote Loki server with:
- Daily log rotation (max 2 files: today + yesterday)
- Real-time tailing via Promtail
- No long-term local retention
- Structured, JSON-readable logs (via structlog)
- Clean integration with Grafana dashboards
- **Cross-platform support** (macOS development + Linux production)

## 📦 Components

| Component | Location | Description |
|-----------|----------|-------------|
| structlog | Backend | Handles structured logging for FastAPI |
| TimedRotatingFileHandler | Backend | Rotates logs daily, avoids local bloat |
| Promtail | Backend | Tails rotated log files and streams to Loki |
| Loki | External server | Receives and stores logs |
| Grafana | External server | Queries logs from Loki and visualizes them |

## 🛠️ Setup Steps

### 1. Backend Logging Configuration ✅
The `backend/app/core/logging.py` has been updated to:
- **Cross-platform**: Automatically detects macOS vs Linux
- **macOS**: Logs to `./logs/backend.log` (local development)
- **Linux**: Logs to `/home/<USER>/logs/backend.log` (production)
- Rotate at midnight, keep only 1 backup
- Stream logs to both stdout and file
- Use ReadableJSONRenderer for consistent formatting

### 2. Universal Setup (Recommended)

```bash
# Make universal setup script executable
chmod +x /path/to/tx-app/backend/scripts/setup-promtail-universal.sh

# Run the universal setup (auto-detects platform)
/path/to/tx-app/backend/scripts/setup-promtail-universal.sh
```

### 3. Platform-Specific Setup

#### macOS (Development)
```bash
chmod +x /path/to/tx-app/backend/scripts/setup-promtail-mac.sh
/path/to/tx-app/backend/scripts/setup-promtail-mac.sh
```

#### Linux (Production)
```bash
chmod +x /path/to/tx-app/backend/scripts/setup-promtail.sh
/path/to/tx-app/backend/scripts/setup-promtail.sh
```

### 4. Configuration Details

#### macOS Configuration
- **Log Path**: `./logs/backend.log`
- **Installation**: Direct download from GitHub releases (ARM64/AMD64 auto-detected)
- **Config**: Local `./promtail-config.yml`
- **Environment**: `development`

#### Linux Configuration
- **Log Path**: `/home/<USER>/logs/backend.log`
- **Installation**: Direct download from GitHub releases
- **Config**: `/home/<USER>/promtail-config.yml`
- **Environment**: `production`

### 5. Start Promtail

#### macOS (Development)
```bash
# Test run
promtail -config.file=./promtail-config.yml

# Background run
promtail -config.file=./promtail-config.yml &
```

#### Linux (Production)
```bash
# Quick test
promtail -config.file=/home/<USER>/promtail-config.yml

# Systemd service (recommended)
sudo cp /home/<USER>/tx-app/backend/scripts/promtail.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable promtail
sudo systemctl start promtail
```

## 🧪 Validation Checklist

| Check | macOS Command | Linux Command |
|-------|---------------|---------------|
| Log file created | `ls -lh ./logs/backend.log` | `ls -lh /home/<USER>/logs/backend.log` |
| Log file rotates at midnight | Check `./logs/backend.log.1` next day | Check `/home/<USER>/logs/backend.log.1` next day |
| Promtail running | `ps aux \| grep promtail` | `ps aux \| grep promtail` |
| Promtail targets | `curl http://localhost:9080/targets` | `curl http://localhost:9080/targets` |
| Service status | N/A | `sudo systemctl status promtail` |
| Log appears in Grafana | `{job="backend", environment="development"}` | `{job="backend", environment="production"}` |

## 📊 Log Format

The logs will be structured JSON with the following format:
```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "level": "info",
  "logger": "app.services.deal",
  "event": "Deal created successfully",
  "deal_id": "12345",
  "user_id": "67890"
}
```

## 🔧 Troubleshooting

### Promtail not starting
```bash
# Check logs (Linux)
sudo journalctl -u promtail -f

# Check config syntax
promtail -config.file=./promtail-config.yml -dry-run
```

### Logs not appearing in Loki
```bash
# Check Promtail targets
curl http://localhost:9080/targets

# Check file permissions
ls -la ./logs/backend.log  # macOS
ls -la /home/<USER>/logs/backend.log  # Linux

# Test Loki connectivity
curl -v http://localhost:3100/ready
```

### Platform-specific issues

#### macOS
- **Architecture detection**: Script automatically detects Apple Silicon vs Intel
- **Permission issues**: Check file permissions in `./logs/`
- **Port conflicts**: Ensure port 9080 is available
- **wget not found**: Install with `brew install wget` if needed

#### Linux
- **Service not starting**: Check systemd logs
- **Permission issues**: Ensure ubuntu user owns log directory
- **Network issues**: Check firewall settings

## 📌 Best Practices

✅ **Cross-platform compatibility** (macOS + Linux)  
✅ **Environment-specific configurations**  
✅ **Logs stored to disk only temporarily** (max 2 days)  
✅ **Local file structured for clean log tailing**  
✅ **Promtail runs independently of app**  
✅ **ReadableJSONRenderer ensures logs are human-readable in Grafana**  
✅ **Handles future scale** (multi-service support, can add job and service labels later)

## 🚀 Next Steps

1. **Update Loki URL**: Replace `localhost:3100` in the Promtail config if Loki is remote
2. **Test Logging**: Generate some logs and verify they appear in Grafana
3. **Monitor**: Set up alerts for log ingestion failures
4. **Scale**: Add more services by extending the scrape_configs

## 📝 Notes

- **macOS**: Perfect for local development and testing
- **Linux**: Production-ready with systemd service management
- **Automatic Detection**: Backend automatically chooses correct log directory
- **Environment Labels**: Logs are tagged with `development` or `production`
- **Log Rotation**: Happens at midnight UTC on both platforms
- **Real-time Streaming**: Promtail tails logs and pushes to Loki immediately 