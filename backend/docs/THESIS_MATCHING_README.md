# Investment Thesis Matching Handler

A comprehensive queue handler that evaluates how well deals align with an organization's investment thesis using hybrid static and LLM-powered analysis.

## Features

- **Fast Static Matching**: Uses RapidFuzz for efficient fuzzy string matching across thesis dimensions
- **LLM Enhancement**: Optional OpenAI-powered analysis for nuanced investor-grade insights
- **Comprehensive Scoring**: Dimension-level breakdowns with explanations and overall weighted scoring
- **Feature Flagged**: LLM enhancement controlled by `llm_thesis_matcher` organization feature flag
- **Idempotent**: Safe to re-run, replaces existing results
- **Error Handling**: Comprehensive error tracking with deal-level error storage

## How It Works

### 1. Static Matching Engine

The handler performs fuzzy matching across four key dimensions:

- **Sector** (30% weight): Technology categories, industries
- **Geography** (25% weight): Geographic regions, countries
- **Stage** (25% weight): Investment stages (Seed, Series A, etc.)
- **Business Model** (20% weight): Revenue models (B2B SaaS, Marketplace, etc.)

#### Matching Thresholds:
- **90%+ similarity**: Exact match (100 points)
- **85-89% similarity**: Fuzzy match (75-90 points)  
- **70-84% similarity**: Weak match (50-75 points)
- **<70% similarity**: No match (0 points)

### 2. LLM Enhancement Layer

When enabled via feature flag and for moderate scores (50-85%), the handler:
- Calls OpenAI GPT-4o-mini for cost-effective analysis
- Generates investor-grade 2-3 sentence summaries
- Provides strategic context beyond static scoring
- Maintains static scores while enhancing explanations

### 3. Result Storage

Results are stored in `Deal.scoring.thesis`:

```json
{
  "match_percent": 87.5,
  "match_breakdown": {
    "sector": {
      "score": 100.0,
      "reason": "Exact match: 'ai'",
      "matches": [...]
    },
    "geography": {
      "score": 75.0,
      "reason": "Fuzzy match: 'united states' ≈ 'north america'",
      "matches": [...]
    }
  },
  "summary": "Strong alignment with AI focus and US market preference...",
  "enhanced_by_llm": true,
  "updated_at": 1703123456
}
```

## Usage

### Triggering Jobs

The handler responds to these job types:
- `thesis_matching`
- `investment_thesis_matching`

**Job Payload:**
```json
{
  "org_id": "ObjectId",
  "deal_id": "ObjectId"
}
```

### Programmatic Usage

```python
from app.services.factory import get_queue_service

# Enqueue thesis matching job
queue_service = await get_queue_service()
job = await queue_service.enqueue_job(
    job_type="thesis_matching",
    payload={
        "org_id": "675a1b2c3d4e5f6789012345",
        "deal_id": "675a1b2c3d4e5f6789012346"
    }
)
```

### Manual Testing

1. **Create Organization with Thesis Config:**
```python
from app.models.organization import Organization, ThesisConfig

org = Organization(
    name="Test VC",
    subdomain="test-vc",
    thesis_config=ThesisConfig(
        sector=["AI", "FinTech"],
        geography=["North America", "Europe"],
        stage=["Seed", "Series A"],
        business_model=["B2B SaaS"]
    )
)
await org.save()
```

2. **Create Deal:**
```python
from app.models.deal import Deal, DealStatus

deal = Deal(
    org_id=org.id,
    company_name="AI Startup",
    sector=["Artificial Intelligence"],
    geography="United States", 
    stage="Seed",
    business_model="B2B Software",
    status=DealStatus.NEW
)
await deal.save()
```

3. **Trigger Job:**
```python
await queue_service.enqueue_job("thesis_matching", {
    "org_id": str(org.id),
    "deal_id": str(deal.id)
})
```

4. **Check Results:**
```python
# After job processes
updated_deal = await Deal.find_one({"_id": deal.id})
print(updated_deal.scoring.get("thesis", {}))
```

## Configuration

### Feature Flags

Enable LLM enhancement in organization settings:

```python
org.settings.features["llm_thesis_matcher"] = True
```

### Dimension Weights

Current weights (customizable in future versions):
- Sector: 30%
- Geography: 25% 
- Stage: 25%
- Business Model: 20%

### Dependencies

- **RapidFuzz**: For fuzzy string matching
- **OpenAI**: For LLM enhancement (optional)
- **Deal Service**: For deal updates
- **Organization Model**: For thesis config access

## Performance

- **Static mode**: <100ms typical processing time
- **LLM enhanced**: 1-3s processing time
- **Memory efficient**: Processes one deal at a time
- **Retry logic**: Built-in OpenAI retry with exponential backoff

## Error Handling

Errors are stored in `Deal.thesis_matching_error`:

- Missing org/deal: `"Organization not found: {org_id}"`
- No thesis config: `"Organization has no thesis configuration"`
- Empty thesis: `"Thesis configuration is empty"`
- Service errors: Detailed error messages with context

## Monitoring

Key metrics logged:
- Deal ID and Organization ID
- Match percentage achieved
- Whether LLM was used
- Processing time in milliseconds
- Error details if any

## Integration Points

### Automatic Triggers
- New deal creation (via submission or pitch upload)
- Enrichment pipeline completion
- Organization thesis config updates

### Manual Triggers
- API endpoints for re-scoring
- Admin panels for bulk re-processing
- Development testing scripts

## Future Enhancements

- [ ] Custom dimension weights per organization
- [ ] Confidence scoring for match quality
- [ ] Historical match trend analysis
- [ ] Batch processing for multiple deals
- [ ] Advanced synonym handling
- [ ] Multi-language thesis support

## Troubleshooting

### Common Issues

1. **RapidFuzz not installed**: Handler falls back to simple string matching
2. **OpenAI API key missing**: LLM enhancement disabled, static only
3. **Empty thesis config**: Job fails with clear error message
4. **Deal not found**: Validates deal existence and org membership

### Debug Mode

Enable debug logging to see detailed matching process:

```python
import logging
logging.getLogger("app.services.queue.handlers.thesis_matching").setLevel(logging.DEBUG)
```

## Testing Scenarios

### Perfect Match (Expected: ~95-100%)
- Thesis: `["AI"]`, Deal: `["AI"]`

### Fuzzy Match (Expected: ~75-90%)  
- Thesis: `["North America"]`, Deal: `["United States"]`

### Weak Match (Expected: ~50-75%)
- Thesis: `["FinTech"]`, Deal: `["Financial Services"]`

### No Match (Expected: 0%)
- Thesis: `["AI"]`, Deal: `["Agriculture"]`

### Agnostic Thesis (Expected: 100%)
- Thesis: `[]`, Deal: `["Any Sector"]` 