import json
import logging
import os
import sys
from logging.handlers import TimedRotatingFileHandler
from typing import Any, Dict

import structlog  # type: ignore
from structlog.stdlib import ProcessorFormatter  # type: ignore

from app.core.config import settings


# Cross-platform log directory configuration
def get_log_directory() -> str:
    """Get the appropriate log directory based on platform and environment."""
    # Check for environment variable override first
    env_log_dir = os.getenv("LOG_DIR")
    if env_log_dir:
        log_dir = env_log_dir
        print(f"Using LOG_DIR from environment: {log_dir}")
        return log_dir

    # Check if we're in a Docker container
    if os.path.exists("/.dockerenv") or os.getenv("DOCKER_ENV"):
        # Docker environment - use a standard location
        log_dir = "/app/logs"
        print(f"Docker environment detected, using: {log_dir}")
    elif sys.platform == "darwin":  # macOS
        # Use a local logs directory in the project
        log_dir = os.path.join(os.getcwd(), "logs")
        print(f"macOS detected, using: {log_dir}")
    else:  # Linux (EC2)
        # Use the production logs directory
        log_dir = "/home/<USER>/logs"
        print(f"Linux detected, using: {log_dir}")

    # Create directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    return log_dir


# Log directory and file configuration
LOG_DIR = get_log_directory()
LOG_FILE = os.path.join(LOG_DIR, "backend.log")

print(f"Final log directory: {LOG_DIR}")
print(f"Log file path: {LOG_FILE}")


class ReadableJSONRenderer:
    """Custom JSON renderer that produces more readable output."""

    def __init__(self, indent: int = 2):
        self.indent = indent

    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: Dict[str, Any]
    ) -> str:
        # Extract the actual event if it's a JSON string
        if isinstance(event_dict.get("event"), str):
            try:
                # Try to parse if it's a JSON string
                event = json.loads(event_dict["event"])
                if isinstance(event, dict) and "event" in event:
                    # If it's a nested JSON, use the inner event
                    event_dict["event"] = event["event"]
                    # Merge other fields if they exist
                    for key, value in event.items():
                        if key != "event" and key not in event_dict:
                            event_dict[key] = value
            except json.JSONDecodeError:
                # If it's not JSON, keep it as is
                pass

        # Remove redundant logger name if it's the same as the parent
        if "logger" in event_dict and event_dict["logger"].startswith(
            "app.services.queue"
        ):
            event_dict["logger"] = event_dict["logger"].replace(
                "app.services.queue.", ""
            )

        # Format the output
        return json.dumps(event_dict, indent=self.indent, ensure_ascii=False)


def configure_logging() -> None:
    """Configure structured logging with file rotation for production."""
    # Basic logging configuration
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=settings.LOG_LEVEL,
    )

    # Common formatter chain for both handlers
    formatter_chain = [
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
    ]

    # File handler with daily rotation
    file_handler = TimedRotatingFileHandler(
        filename=LOG_FILE,
        when="midnight",
        interval=1,
        backupCount=1,  # keeps just one old file (yesterday)
        encoding="utf-8",
    )
    file_handler.setFormatter(
        ProcessorFormatter(
            processor=ReadableJSONRenderer(),  # type: ignore
            foreign_pre_chain=formatter_chain,
        )
    )

    # Stream handler for stdout
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(
        ProcessorFormatter(
            processor=ReadableJSONRenderer(),  # type: ignore
            foreign_pre_chain=formatter_chain,
        )
    )

    # Configure root logger with both handlers
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)
    root_logger.addHandler(stream_handler)

    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            ReadableJSONRenderer(),  # type: ignore
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)
