"""
Job Utilities

This module provides utility functions for working with the job tracking system.
"""

from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from app.models.job import EntityType, JobStatus, JobType, TrackedJob
from app.services.factory import get_job_service
from app.services.queue import JobPriority, QueueType
from bson import ObjectId


async def create_tracked_job(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_type: Union[str, JobType],
    payload: Dict[str, Any],
    queue_job_type: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    config: Optional[Dict[str, Any]] = None,
    priority: int = 0,
    parent_job_id: Optional[Union[str, ObjectId]] = None,
    depends_on: Optional[List[Union[str, ObjectId]]] = None,
    scheduled_at: Optional[int] = None,
    queue_priority: JobPriority = JobPriority.NORMAL,
    queue_type: QueueType = QueueType.DEFAULT,
) -> TrackedJob:
    """
    Create a tracked job with comprehensive configuration.

    Args:
        entity_type: Type of entity this job is associated with
        entity_id: ID of the entity
        job_type: Type of job to create
        payload: Job payload data
        queue_job_type: Optional queue-specific job type
        metadata: Additional metadata for the job
        config: Job configuration
        priority: Job priority (0-100)
        parent_job_id: ID of parent job if this is a child job
        depends_on: List of job IDs this job depends on
        scheduled_at: Unix timestamp for scheduled execution
        queue_priority: Queue priority level
        queue_type: Type of queue to use

    Returns:
        The created tracked job
    """
    # Get job service if not provided
    from app.services.job.interfaces import JobServiceInterface

    job_service: JobServiceInterface = await get_job_service()  # type: ignore

    # Create the tracked job
    tracked_job = await job_service.create_job(
        entity_type=entity_type,
        entity_id=entity_id,
        job_type=job_type,
        payload=payload,
        metadata=metadata or {},
        config=config or {},
        priority=priority,
        parent_job_id=parent_job_id,
        depends_on=depends_on or [],
        scheduled_at=scheduled_at,
    )

    # Enqueue the job in the appropriate queue
    from app.services.factory import get_queue_service

    queue_service = await get_queue_service()
    await queue_service.enqueue_job(  # type: ignore
        job_type=queue_job_type or job_type,
        payload=payload,
        queue_type=queue_type,
        priority=queue_priority,
        metadata={
            "tracked_job_id": str(tracked_job.id),
            **(metadata or {}),
        },
    )

    return tracked_job


class JobBuilder:
    """
    Sophisticated job builder for creating tracked jobs with fluent interface.

    Supports job chaining, dependencies, scheduling, and complex configurations.
    """

    def __init__(self):
        self._entity_type: Optional[EntityType] = None
        self._entity_id: Optional[Union[str, ObjectId]] = None
        self._job_type: Optional[JobType] = None
        self._payload: Dict[str, Any] = {}
        self._metadata: Dict[str, Any] = {}
        self._config: Dict[str, Any] = {}
        self._queue_job_type: Optional[str] = None
        self._queue_priority: JobPriority = JobPriority.NORMAL
        self._queue_type: QueueType = QueueType.DEFAULT
        self._priority: int = 0
        self._parent_job_id: Optional[Union[str, ObjectId]] = None
        self._depends_on: List[Union[str, ObjectId]] = []
        self._scheduled_at: Optional[int] = None
        self._retry_config: Optional[Dict[str, Any]] = None
        self._timeout: Optional[int] = None

    def for_entity(
        self, entity_type: Union[str, EntityType], entity_id: Union[str, ObjectId]
    ) -> "JobBuilder":
        """Set the entity this job is associated with."""
        self._entity_type = (
            EntityType(entity_type) if isinstance(entity_type, str) else entity_type
        )
        self._entity_id = entity_id
        return self

    def of_type(self, job_type: Union[str, JobType]) -> "JobBuilder":
        """Set the job type."""
        self._job_type = JobType(job_type) if isinstance(job_type, str) else job_type
        return self

    def with_payload(self, **payload) -> "JobBuilder":
        """Add payload data."""
        self._payload.update(payload)
        return self

    def with_metadata(self, **metadata) -> "JobBuilder":
        """Add metadata."""
        self._metadata.update(metadata)
        return self

    def with_config(self, **config) -> "JobBuilder":
        """Add configuration."""
        self._config.update(config)
        return self

    def with_queue_config(
        self,
        job_type: Optional[str] = None,
        priority: JobPriority = JobPriority.NORMAL,
        queue_type: QueueType = QueueType.DEFAULT,
    ) -> "JobBuilder":
        """Configure queue settings."""
        self._queue_job_type = job_type
        self._queue_priority = priority
        self._queue_type = queue_type
        return self

    def with_priority(self, priority: int) -> "JobBuilder":
        """Set job priority (0-100)."""
        self._priority = max(0, min(100, priority))
        return self

    def with_parent(self, parent_job_id: Union[str, ObjectId]) -> "JobBuilder":
        """Set parent job ID for job chaining."""
        self._parent_job_id = parent_job_id
        return self

    def depends_on(self, *job_ids: Union[str, ObjectId]) -> "JobBuilder":
        """Add job dependencies."""
        self._depends_on.extend(job_ids)
        return self

    def scheduled_at(self, timestamp: int) -> "JobBuilder":
        """Schedule job for future execution."""
        self._scheduled_at = timestamp
        return self

    def with_retry(
        self, max_attempts: int = 3, backoff_factor: int = 2, max_backoff: int = 3600
    ) -> "JobBuilder":
        """Configure retry behavior."""
        self._retry_config = {
            "max_attempts": max_attempts,
            "backoff_factor": backoff_factor,
            "max_backoff": max_backoff,
        }
        return self

    def with_timeout(self, timeout_seconds: int) -> "JobBuilder":
        """Set job timeout."""
        self._timeout = timeout_seconds
        return self

    def for_deal(self, deal_id: Union[str, ObjectId]) -> "JobBuilder":
        """Convenience method for deal-related jobs."""
        return self.for_entity(EntityType.DEAL, deal_id)

    def for_form_submission(self, submission_id: Union[str, ObjectId]) -> "JobBuilder":
        """Convenience method for form submission jobs."""
        return self.for_entity(EntityType.FORM_SUBMISSION, submission_id)

    def for_pitch_deck(self, temp_id: str) -> "JobBuilder":
        """Convenience method for pitch deck jobs."""
        return self.for_entity(EntityType.PITCH_DECK, temp_id)

    def for_integration(self, execution_id: str) -> "JobBuilder":
        """Convenience method for integration jobs."""
        return self.for_entity(EntityType.INTEGRATION, execution_id)

    def for_import(self, import_id: str) -> "JobBuilder":
        """Convenience method for import jobs."""
        return self.for_entity(EntityType.IMPORT, import_id)

    async def create(self) -> TrackedJob:
        """Create and enqueue the tracked job."""
        if not all([self._entity_type, self._entity_id, self._job_type]):
            raise ValueError("entity_type, entity_id, and job_type are required")

        # Add retry config to metadata if specified
        if self._retry_config:
            self._metadata["retry_config"] = self._retry_config

        # Add timeout to metadata if specified
        if self._timeout:
            self._metadata["timeout"] = self._timeout

        return await create_tracked_job(
            entity_type=self._entity_type,  # type: ignore
            entity_id=self._entity_id,  # type: ignore
            job_type=self._job_type,  # type: ignore
            payload=self._payload,
            queue_job_type=self._queue_job_type,
            metadata=self._metadata,
            config=self._config,
            priority=self._priority,
            parent_job_id=self._parent_job_id,
            depends_on=self._depends_on,
            scheduled_at=self._scheduled_at,
            queue_priority=self._queue_priority,
            queue_type=self._queue_type,
        )


class JobChain:
    """
    Sophisticated job chaining system for complex workflows.

    Supports sequential, parallel, and conditional job execution.
    """

    def __init__(
        self, entity_type: Union[str, EntityType], entity_id: Union[str, ObjectId]
    ):
        self.entity_type = (
            EntityType(entity_type) if isinstance(entity_type, str) else entity_type
        )
        self.entity_id = entity_id
        self.jobs: List[JobBuilder] = []
        self.parallel_groups: List[List[JobBuilder]] = []
        self.conditions: Dict[str, Callable[[Dict[str, Any]], bool]] = {}

    def add_job(self, job_builder: JobBuilder) -> "JobChain":
        """Add a job to the chain."""
        job_builder.for_entity(self.entity_type, self.entity_id)
        self.jobs.append(job_builder)
        return self

    def add_parallel_group(self, *job_builders: JobBuilder) -> "JobChain":
        """Add jobs that should run in parallel."""
        for job_builder in job_builders:
            job_builder.for_entity(self.entity_type, self.entity_id)
        self.parallel_groups.append(list(job_builders))
        return self

    def add_condition(
        self, condition_name: str, condition_func: Callable[[Dict[str, Any]], bool]
    ) -> "JobChain":
        """Add a conditional execution rule."""
        self.conditions[condition_name] = condition_func
        return self

    async def execute(
        self, context: Optional[Dict[str, Any]] = None
    ) -> List[TrackedJob]:
        """Execute the job chain."""
        created_jobs: List[TrackedJob] = []
        context = context or {}

        # Execute sequential jobs
        for i, job_builder in enumerate(self.jobs):
            if i > 0:
                # Make this job depend on the previous one
                job_builder.depends_on(created_jobs[-1].id)
            created_jobs.append(await job_builder.create())

        # Execute parallel groups
        for parallel_group in self.parallel_groups:
            # All jobs in a parallel group depend on the last sequential job
            parent_job_id = created_jobs[-1].id if created_jobs else None

            parallel_jobs = []
            for job_builder in parallel_group:
                if parent_job_id:
                    job_builder.depends_on(parent_job_id)
                parallel_jobs.append(await job_builder.create())

            created_jobs.extend(parallel_jobs)

        return created_jobs


# Convenience functions for common job patterns
async def create_deal_job(
    deal_id: Union[str, ObjectId], job_type: Union[str, JobType], **kwargs
) -> TrackedJob:
    """Create a job for a deal entity."""
    builder = JobBuilder().for_deal(deal_id).of_type(job_type)

    # Apply any additional configuration
    if "payload" in kwargs:
        builder.with_payload(**kwargs["payload"])
    if "metadata" in kwargs:
        builder.with_metadata(**kwargs["metadata"])
    if "priority" in kwargs:
        builder.with_priority(kwargs["priority"])
    if "queue_priority" in kwargs:
        builder.with_queue_config(priority=kwargs["queue_priority"])

    return await builder.create()


async def create_form_job(
    submission_id: Union[str, ObjectId], job_type: Union[str, JobType], **kwargs
) -> TrackedJob:
    """Create a job for a form submission entity."""
    builder = JobBuilder().for_form_submission(submission_id).of_type(job_type)

    # Apply any additional configuration
    if "payload" in kwargs:
        builder.with_payload(**kwargs["payload"])
    if "metadata" in kwargs:
        builder.with_metadata(**kwargs["metadata"])
    if "priority" in kwargs:
        builder.with_priority(kwargs["priority"])

    return await builder.create()


async def create_pitch_job(
    temp_id: str, job_type: Union[str, JobType], **kwargs
) -> TrackedJob:
    """Create a job for a pitch deck entity."""
    builder = JobBuilder().for_pitch_deck(temp_id).of_type(job_type)

    # Apply any additional configuration
    if "payload" in kwargs:
        builder.with_payload(**kwargs["payload"])
    if "metadata" in kwargs:
        builder.with_metadata(**kwargs["metadata"])
    if "priority" in kwargs:
        builder.with_priority(kwargs["priority"])

    return await builder.create()


async def create_integration_job(
    execution_id: str, job_type: Union[str, JobType], **kwargs
) -> TrackedJob:
    """Create a job for an integration entity."""
    builder = JobBuilder().for_integration(execution_id).of_type(job_type)

    # Apply any additional configuration
    if "payload" in kwargs:
        builder.with_payload(**kwargs["payload"])
    if "metadata" in kwargs:
        builder.with_metadata(**kwargs["metadata"])
    if "priority" in kwargs:
        builder.with_priority(kwargs["priority"])

    return await builder.create()


# Advanced job patterns
async def create_job_chain(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_configs: List[Dict[str, Any]],
    metadata: Optional[Dict[str, Any]] = None,
) -> List[TrackedJob]:
    """
    Create a chain of related jobs.

    Args:
        entity_type: Type of entity
        entity_id: Entity ID
        job_configs: List of job configurations
        metadata: Additional metadata for all jobs

    Returns:
        List of created tracked jobs
    """
    chain = JobChain(entity_type, entity_id)

    for config in job_configs:
        job_type = config.get("job_type")
        if not job_type:
            raise ValueError("job_type is required in job config")

        builder = JobBuilder().of_type(job_type)

        # Apply configuration
        if "payload" in config:
            builder.with_payload(**config["payload"])
        if "priority" in config:
            builder.with_priority(config["priority"])
        if "queue_priority" in config:
            builder.with_queue_config(priority=config["queue_priority"])
        if "queue_type" in config:
            builder.with_queue_config(queue_type=config["queue_type"])
        if "retry_config" in config:
            builder.with_retry(**config["retry_config"])
        if "timeout" in config:
            builder.with_timeout(config["timeout"])

        # Add metadata
        job_metadata = metadata or {}
        if "metadata" in config:
            job_metadata.update(config["metadata"])
        builder.with_metadata(**job_metadata)

        chain.add_job(builder)

    return await chain.execute()


async def create_job_group(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_configs: List[Dict[str, Any]],
    metadata: Optional[Dict[str, Any]] = None,
    parent_job: Optional[TrackedJob] = None,
) -> List[TrackedJob]:
    """
    Create a group of parallel jobs.

    Args:
        entity_type: Type of entity
        entity_id: Entity ID
        job_configs: List of job configurations
        metadata: Additional metadata for all jobs
        parent_job: Optional parent job to depend on

    Returns:
        List of created tracked jobs
    """
    created_jobs = []

    for config in job_configs:
        job_type = config.get("job_type")
        if not job_type:
            raise ValueError("job_type is required in job config")

        builder = JobBuilder().of_type(job_type)

        # Apply configuration
        if "payload" in config:
            builder.with_payload(**config["payload"])
        if "priority" in config:
            builder.with_priority(config["priority"])
        if "queue_priority" in config:
            builder.with_queue_config(priority=config["queue_priority"])
        if "queue_type" in config:
            builder.with_queue_config(queue_type=config["queue_type"])
        if "retry_config" in config:
            builder.with_retry(**config["retry_config"])
        if "timeout" in config:
            builder.with_timeout(config["timeout"])

        # Add parent dependency if specified
        if parent_job:
            builder.depends_on(parent_job.id)

        # Add metadata
        job_metadata = metadata or {}
        if "metadata" in config:
            job_metadata.update(config["metadata"])
        builder.with_metadata(**job_metadata)

        created_jobs.append(await builder.create())

    return created_jobs


async def with_job_tracking(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_type: Union[str, JobType],
    func: Callable[..., Awaitable[Any]],
    *args,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> Any:
    """
    Execute a function with job tracking.

    Args:
        entity_type: Type of entity
        entity_id: Entity ID
        job_type: Type of job
        func: Function to execute
        *args: Function arguments
        metadata: Additional metadata
        **kwargs: Function keyword arguments

    Returns:
        Function result
    """
    # Create job
    job = await create_tracked_job(
        entity_type=entity_type,
        entity_id=entity_id,
        job_type=job_type,
        payload={"args": args, "kwargs": kwargs},
        metadata=metadata or {},
    )

    try:
        # Execute function
        result = await func(*args, **kwargs)

        # Update job status
        from app.services.factory import get_job_service

        job_service = await get_job_service()
        await job_service.update_job_status(  # type: ignore
            job.id, JobStatus.COMPLETED, {"result": result}
        )

        return result

    except Exception as e:
        # Update job status on error
        from app.services.factory import get_job_service

        job_service = await get_job_service()
        await job_service.update_job_status(  # type: ignore
            job.id, JobStatus.FAILED, {"error": str(e)}
        )
        raise
