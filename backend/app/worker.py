"""
Worker script for processing jobs from the queue.

This script starts a worker pool that processes jobs from the queue.
It can be run as a separate process from the main application.

Example:
    $ python -m app.worker  # Run directly
    $ python -m app.worker --no-watch  # Run without watching (used by bash script)
"""

import asyncio
import signal
import sys

from app.core.config import settings
from app.core.logging import configure_logging, get_logger
from app.services.factory import get_queue_service
from app.services.queue import QueueFactory, QueueType
from app.services.queue.handlers import HANDLERS

# Configure logging
configure_logging()
logger = get_logger(__name__)



async def run_worker():
    """Run the worker process."""
    logger.info("Starting worker pool")

    try:
        # Create queue service
        queue_service = await get_queue_service()
        await queue_service.initialize()

        # Create worker pool
        worker_pool = QueueFactory.create_worker_pool(
            queue_service=queue_service,  # type: ignore
            queue_types=[QueueType.DEFAULT, QueueType.HIGH_PRIORITY],
            poll_interval=7.0,
            shutdown_timeout=60,
        )

        # Register handlers
        for job_type, handler in HANDLERS.items():
            worker_pool.register_handler(job_type, handler)

        # Initialize worker pool
        await worker_pool.initialize()

        # Determine number of workers
        worker_count = int(settings.WORKER_CONCURRENCY)
        logger.info("Starting worker pool", worker_count=worker_count)

        # Start worker pool
        await worker_pool.start(num_workers=worker_count)

        try:
            # Keep the worker pool running
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received KeyboardInterrupt, shutting down worker pool")
        except Exception as e:
            logger.error(f"Worker pool error: {e}")
            raise
        finally:
            # Shutdown worker pool
            logger.info("Shutting down worker pool")
            await worker_pool.stop()
            await queue_service.cleanup()
            logger.info("Worker pool shutdown complete")

    except Exception as e:
        logger.error(f"Failed to start worker: {e}")
        raise


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""

    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        # Raise KeyboardInterrupt to trigger graceful shutdown
        raise KeyboardInterrupt

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main entry point."""
    # Setup signal handlers for graceful shutdown
    setup_signal_handlers()

    # Log startup info
    if "--no-watch" in sys.argv:
        logger.info("Starting worker without file watching (controlled by bash script)")
    else:
        logger.info("Starting worker")

    # Always run the worker directly since file watching is handled by bash script
    await run_worker()


if __name__ == "__main__":
    asyncio.run(main())
