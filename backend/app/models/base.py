from datetime import datetime, timezone
from typing import (
    Annotated,
    Any,
    ClassVar,
    Dict,
    List,
    Optional,
    Type,
    TypeVar,
    Union,
    get_args,
    get_origin,
    get_type_hints,
)

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection
from pydantic import BaseModel, ConfigDict, Field, create_model, field_validator
from pydantic.fields import FieldInfo
from pymongo.results import UpdateResult

from app.core.database import get_database
from app.core.logging import get_logger
from app.utils.common import ObjectIdField

logger = get_logger(__name__)

T = TypeVar("T", bound="TractionXModel")


class TractionXModel(BaseModel):
    """
    Base document class with advanced MongoDB features for TractionX.

    Usage for modular config:
    ----------------------------------------
    # In your subclass, define only extra/override config:
    class Question(TractionXModel):
        extra_model_config = {
            'json_schema_extra': {
                'example': {...}
            }
        }
        ...
    # The merged config will be used as model_config for Pydantic.
    ----------------------------------------
    """

    # Default config for all models. Subclasses can add/override by defining 'extra_model_config'.
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectIdField: str, datetime: lambda v: v.isoformat()},  # type: ignore
        from_attributes=True,
    )

    # Subclasses can define this as a dict or ConfigDict to add/override config (e.g., json_schema_extra)
    extra_model_config: ClassVar[dict] = {}

    def __init_subclass__(cls, **kwargs):
        """
        Merge base model_config with any extra_model_config defined in subclasses.
        This allows subclasses to specify only the config they need to add or override
        (e.g., json_schema_extra) without redefining the entire config.
        """
        super().__init_subclass__(**kwargs)
        base_config = dict(TractionXModel.model_config)
        extra = getattr(cls, "extra_model_config", {}) or {}
        # Merge, with subclass config taking precedence
        merged = {**base_config, **extra}
        cls.model_config = ConfigDict(**merged)

    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")  # type: ignore

    @field_validator("id", mode="before")
    def validate_id(cls, v):
        """
        Pydantic validator for the 'id' field.
        Ensures that if the id is provided as a string, it is converted to a MongoDB ObjectId.
        This allows seamless usage of both string and ObjectId representations in the API and DB.
        """
        if isinstance(v, str):
            return ObjectId(v)
        return v

    @staticmethod
    def serialize_value(value: Any, for_db: bool) -> Any:
        """
        Serialize a value for database storage or API response.

        Args:
            value: The value to serialize
            for_db: If True, serialize for database storage. If False, serialize for API response.

        Returns:
            Serialized value
        """
        if isinstance(value, BaseModel):
            return value.model_dump(mode="json" if for_db else "python", by_alias=True)
        elif isinstance(value, ObjectId):
            return str(value) if not for_db else value
        elif isinstance(value, str) and len(value) == 24 and for_db:
            # Check if this is a string that looks like an ObjectId and we're serializing for DB
            try:
                return ObjectId(value)
            except Exception:
                # logger.error(f"Error converting string to ObjectId: {e}")
                return value
        elif isinstance(value, datetime):
            return value.isoformat() if not for_db else value
        elif isinstance(value, list):
            return [TractionXModel.serialize_value(v, for_db) for v in value]
        elif isinstance(value, dict):
            return {
                k: TractionXModel.serialize_value(v, for_db) for k, v in value.items()
            }
        return value

    @classmethod
    def get_collection_name(cls) -> str:
        """
        Returns the MongoDB collection name for this model.
        By default, it uses the lowercase class name with an 's' appended (e.g., 'User' -> 'users').
        Override this method if you want a custom collection name.
        """
        return cls.__name__.lower() + "s"

    @classmethod
    async def get_collection(cls) -> AsyncIOMotorCollection:  # type: ignore
        """
        Asynchronously retrieves the MongoDB collection object for this model.
        Uses the configured database connection and the collection name from get_collection_name().
        """
        db = await get_database()
        return db[cls.get_collection_name()]

    @classmethod
    async def get_by_id(
        cls: Type[T],
        id: Union[str, ObjectId],
        include_deleted: bool = False,
        projection: Optional[Dict[str, int]] = None,
    ) -> Optional[T]:
        """
        Retrieve a single document by its MongoDB _id.
        Args:
            id: The document's _id (string or ObjectId).
            include_deleted: If True, include soft-deleted documents in the search.
            projection: Optional dict specifying fields to include/exclude.
        Returns:
            An instance of the model if found, else None.
        """
        collection = await cls.get_collection()
        query = {}
        if isinstance(id, str):
            query["_id"] = ObjectId(id)
        elif isinstance(id, ObjectId):
            query["_id"] = id
        else:
            raise ValueError(f"Invalid id type: {type(id)}")

        # Only add is_deleted check if the model has this field
        if not include_deleted and hasattr(cls, "is_deleted"):
            query["is_deleted"] = False

        doc = await collection.find_one(query, projection)
        return cls(**doc) if doc else None

    @classmethod
    async def find_one(
        cls: Type[T],
        query: Dict[str, Any],
        projection: Optional[Dict[str, int]] = None,
        include_deleted: bool = False,
    ) -> Optional[T]:
        """
        Find and return the first document matching the provided query.
        Dynamically converts query values to the correct type based on the model's field type.
        """
        try:
            from app.utils.common import ObjectIdField, PyObjectId
        except ImportError:
            PyObjectId = ObjectId
            ObjectIdField = ObjectId

        # Get model field types
        model_fields = {}
        if hasattr(cls, "model_fields"):
            model_fields = cls.model_fields
        elif hasattr(cls, "__fields__"):
            model_fields = cls.__fields__
        elif hasattr(cls, "__annotations__"):
            model_fields = cls.__annotations__

        def is_object_id_type(field_type):
            """Check if a field type is an ObjectId type."""
            origin = get_origin(field_type)
            args = get_args(field_type)

            # Direct match
            if field_type in (ObjectId, ObjectIdField, PyObjectId):
                return True

            # Annotated[ObjectId, ...]
            if origin is Annotated and args:
                return args[0] in (ObjectId, ObjectIdField, PyObjectId)

            # List[ObjectId]
            if origin is list and args:
                return is_object_id_type(args[0])

            # Union[ObjectId, None, ...]
            if origin is Union and args:
                return any(is_object_id_type(arg) for arg in args)

            return False

        def convert_value(field_type, value):
            origin = get_origin(field_type)
            args = get_args(field_type)

            if origin is list and args:
                item_type = args[0]
                if isinstance(value, list):
                    return [convert_value(item_type, v) for v in value]
                return [convert_value(item_type, value)]

            if is_object_id_type(field_type):
                if isinstance(value, ObjectId):
                    return value
                if isinstance(value, str):
                    return ObjectId(value)
                if isinstance(value, bytes):
                    return ObjectId(value)
                raise ValueError(f"Cannot convert {value} to ObjectId")

            return value

        def convert_query(q):
            if not isinstance(q, dict):
                return q
            new_q = {}
            for k, v in q.items():
                field_type = None
                field_key = k

                if k.startswith("$"):
                    new_q[k] = convert_query(v)
                    continue

                if field_key == "_id":
                    if isinstance(v, str):
                        v = ObjectId(v)
                    elif isinstance(v, dict):
                        v = {
                            op: ObjectId(op_val) if isinstance(op_val, str) else op_val
                            for op, op_val in v.items()
                        }
                    new_q[k] = v
                    continue

                if field_key in model_fields:
                    field_type = model_fields[field_key]

                if isinstance(v, dict):
                    new_v = {}
                    for op, op_val in v.items():
                        if op.startswith("$"):
                            new_v[op] = convert_value(field_type, op_val)
                        else:
                            new_v[op] = convert_query(op_val)
                    new_q[k] = new_v
                else:
                    new_q[k] = convert_value(field_type, v)
            return new_q

        converted_query = convert_query(query)
        # # logger.info(f"Converted query: {converted_query}")
        if not include_deleted and hasattr(cls, "is_deleted"):
            converted_query["is_deleted"] = False

        collection = await cls.get_collection()
        doc = await collection.find_one(converted_query, projection)
        return cls(**doc) if doc else None

    @classmethod
    async def find_many(
        cls: Type[T],
        query: Dict[str, Any] = {},
        projection: Optional[Dict[str, int]] = None,
        skip: int = 0,
        limit: int = 100,
        sort: Optional[List[tuple]] = None,
        include_deleted: bool = False,
    ) -> List[T]:
        """
        Find and return a list of documents matching the provided query, with support for pagination and sorting.
        Args:
            query: MongoDB filter dict.
            projection: Optional dict specifying fields to include/exclude.
            skip: Number of documents to skip (for pagination).
            limit: Maximum number of documents to return.
            sort: Optional list of (field, direction) tuples for sorting.
            include_deleted: If True, include soft-deleted documents.
        Returns:
            List of model instances.
        """
        try:
            from app.utils.common import ObjectIdField, PyObjectId
        except ImportError:
            PyObjectId = ObjectId
            ObjectIdField = ObjectId

        # Get model field types
        model_fields = {}
        if hasattr(cls, "model_fields"):
            model_fields = cls.model_fields
        elif hasattr(cls, "__fields__"):
            model_fields = cls.__fields__
        elif hasattr(cls, "__annotations__"):
            model_fields = cls.__annotations__

        def is_object_id_type(field_type):
            """Check if a field type is an ObjectId type."""
            origin = get_origin(field_type)
            args = get_args(field_type)

            # Direct match
            if field_type in (ObjectId, ObjectIdField, PyObjectId):
                return True

            # Annotated[ObjectId, ...]
            if origin is Annotated and args:
                return args[0] in (ObjectId, ObjectIdField, PyObjectId)

            # List[ObjectId]
            if origin is list and args:
                return is_object_id_type(args[0])

            # Union[ObjectId, None, ...]
            if origin is Union and args:
                return any(is_object_id_type(arg) for arg in args)

            return False

        def convert_value(field_type, value):
            origin = get_origin(field_type)
            args = get_args(field_type)

            if origin is list and args:
                item_type = args[0]
                if isinstance(value, list):
                    return [convert_value(item_type, v) for v in value]
                return [convert_value(item_type, value)]

            if is_object_id_type(field_type):
                if isinstance(value, ObjectId):
                    return value
                if isinstance(value, str):
                    return ObjectId(value)
                if isinstance(value, bytes):
                    return ObjectId(value)
                raise ValueError(f"Cannot convert {value} to ObjectId")

            return value

        def convert_query(q):
            if not isinstance(q, dict):
                return q
            new_q = {}
            for k, v in q.items():
                field_type = None
                field_key = k

                if k.startswith("$"):
                    new_q[k] = convert_query(v)
                    continue

                if field_key == "_id":
                    if isinstance(v, str):
                        v = ObjectId(v)
                    elif isinstance(v, dict):
                        v = {
                            op: ObjectId(op_val) if isinstance(op_val, str) else op_val
                            for op, op_val in v.items()
                        }
                    new_q[k] = v
                    continue

                if field_key in model_fields:
                    field_type = model_fields[field_key]

                if isinstance(v, dict):
                    new_v = {}
                    for op, op_val in v.items():
                        if op.startswith("$"):
                            new_v[op] = convert_value(field_type, op_val)
                        else:
                            new_v[op] = convert_query(op_val)
                    new_q[k] = new_v
                else:
                    new_q[k] = convert_value(field_type, v)
            return new_q

        converted_query = convert_query(query)

        collection = await cls.get_collection()
        if not include_deleted and hasattr(cls, "is_deleted"):
            # Only add is_deleted check if the model has this field
            converted_query["is_deleted"] = False
        logger.info(f"Converted query: {converted_query}")
        if projection:
            cursor = collection.find(converted_query, projection)
        else:
            cursor = collection.find(converted_query)
        # logger.info(f"Cursor: {cursor}")
        if sort:
            cursor = cursor.sort(sort)

        cursor = cursor.skip(skip).limit(limit)
        docs = await cursor.to_list(length=limit)
        logger.info(f"Docs: {docs}")
        return [cls(**doc) for doc in docs]

    async def save(
        self,
        actor_id: Optional[ObjectId] = None,
        use_locking: bool = False,
        is_update: bool = False,
    ) -> T:  # type: ignore
        """
        Save the document to the database. Handles both insert and update operations.
        - On insert: sets created_at, created_by fields and initializes version.
        - On update: sets updated_at, updated_by fields, and optionally performs optimistic locking.
        - Maintains audit trail if actor_id is provided.
        Args:
            actor_id: Optional ObjectId of the user performing the operation (for audit).
            use_locking: Whether to use optimistic locking for updates (default: False).
            is_update: Whether this is an update operation (default: False).
        Returns:
            The saved model instance (self).
        Raises:
            ValueError: If optimistic locking fails (document was modified concurrently).
        """
        collection = await self.__class__.get_collection()

        # Update audit fields
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
        # if actor_id:
        #     self.updated_by = actor_id

        if not is_update:
            # Insert new document
            # # logger.info("Inserting new document")
            self.created_at = int(datetime.now(timezone.utc).timestamp())
            if actor_id:
                self.created_by = actor_id
            if use_locking:
                self.version = 1
            result = await collection.insert_one(
                self.model_dump(by_alias=True, for_db=True)
            )
            self.id = result.inserted_id
            # # logger.info(f"Inserted document with id: {self.id}")
        else:
            # Update existing document
            # logger.info(f"Updating existing document with id: {self.id}")
            if use_locking:
                # Optimistic locking
                current_version = self.version
                self.version += 1

                result = await collection.update_one(
                    {"_id": self.id, "version": current_version},
                    {"$set": self.model_dump(by_alias=True, for_db=True)},
                )

                if result.modified_count == 0:
                    # Version mismatch, fetch latest version
                    latest = await collection.find_one({"_id": self.id})
                    if latest:
                        self.version = latest["version"]
                    raise ValueError("Document was modified by another operation")
            else:
                # Simple update without locking
                update_data = self.model_dump(by_alias=True, for_db=True)
                logger.info(f"Performing simple update without locking: {update_data}")
                result = await collection.update_one(
                    {"_id": self.id},
                    {"$set": update_data},
                )
                logger.info(
                    f"Update result: {result}, Modified count: {result.modified_count}"
                )

        return self  # type: ignore

    async def soft_delete(self, actor_id: Optional[ObjectId] = None) -> bool:
        """
        Soft delete the document (mark as deleted without removing from DB).
        Sets is_deleted=True and updates audit fields.
        Args:
            actor_id: Optional ObjectId of the user performing the operation.
        Returns:
            True if the document was successfully marked as deleted, else False.
        """
        if not self.id:
            return False

        self.is_deleted = True
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
        if actor_id:
            self.updated_by = actor_id

        collection = await self.get_collection()
        result = await collection.update_one(
            {"_id": self.id}, {"$set": self.model_dump(by_alias=True)}
        )
        return result.modified_count > 0

    async def delete(self, actor_id: Optional[ObjectId] = None) -> bool:
        """
        Delete the document from the database.
        By default, this performs a soft delete (sets is_deleted=True).

        Args:
            actor_id: Optional ObjectId of the user performing the operation (for audit).

        Returns:
            True if the document was successfully deleted, else False.
        """

        if hasattr(self, "is_deleted"):
            # Perform soft delete if the model has is_deleted field
            return await self.soft_delete(actor_id)
        else:
            # Fall back to hard delete if the model doesn't support soft delete
            return await self.hard_delete()

    async def hard_delete(self) -> bool:
        """
        Permanently delete the document from the database.
        Returns:
            True if the document was deleted, else False.
        """
        if not self.id:
            return False

        collection = await self.get_collection()
        result = await collection.delete_one({"_id": self.id})
        return result.deleted_count > 0

    async def restore(self, actor_id: Optional[ObjectId] = None) -> bool:
        """
        Restore a soft-deleted document (set is_deleted=False).
        Args:
            actor_id: Optional ObjectId of the user performing the operation.
        Returns:
            True if the document was restored, else False.
        """
        if not self.id:
            return False

        self.is_deleted = False
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
        if actor_id:
            self.updated_by = actor_id

        collection = await self.get_collection()
        result = await collection.update_one(
            {"_id": self.id}, {"$set": self.model_dump(by_alias=True)}
        )
        return result.modified_count > 0

    async def populate(
        self,
        fields: Union[str, List[str]],
        depth: int = 1,
        projection: Optional[Dict[str, int]] = None,
    ) -> T:  # type: ignore
        """
        Populate referenced fields for this document.
        Supports both single and list references to other TractionXModel subclasses.
        Args:
            fields: Field name(s) to populate. Can be a string or list of strings.
            depth: Recursion depth for nested population (default: 1).
            projection: Optional projection for referenced documents.
        Returns:
            The model instance with populated fields.
        """
        if isinstance(fields, str):
            fields = [fields]

        # Get full type hints with annotations
        type_hints = get_type_hints(self.__class__, include_extras=True)

        for field in fields:
            if not hasattr(self, field):
                continue

            field_value = getattr(self, field)
            if not field_value:
                continue

            if field not in type_hints:
                continue

            field_type = type_hints[field]
            # logger.info(f"Populating field {field} with type {field_type}")

            model_class = None
            is_list = False

            # Handle Annotated fields (with populate_reference marker)
            if get_origin(field_type) is Annotated:
                base_type, *annotations = get_args(field_type)
                field_type = base_type  # override base type

                for annotation in annotations:
                    # Check for PopulateReference instance
                    if (
                        hasattr(annotation, "__class__")
                        and annotation.__class__.__name__ == "PopulateReference"
                    ):
                        model_class_ref = annotation.model_class
                        is_forward_ref = annotation.is_forward_ref

                        if is_forward_ref:
                            # Handle string reference by importing the model dynamically
                            model_class = await self._import_model_class(
                                model_class_ref
                            )
                        else:
                            # Direct class reference
                            model_class = model_class_ref
                        break

                    # Legacy dict format support
                    elif (
                        isinstance(annotation, dict) and "populate_model" in annotation
                    ):
                        model_class_ref = annotation["populate_model"]
                        is_forward_ref = annotation.get("is_forward_ref", False)

                        if is_forward_ref:
                            # Handle string reference by importing the model dynamically
                            model_class = await self._import_model_class(
                                model_class_ref
                            )
                        else:
                            model_class = model_class_ref
                        break

            # Handle lists (List[ObjectIdField] or List[Annotated[...]])
            if get_origin(field_type) is list:
                is_list = True
                item_type = get_args(field_type)[0]

                if get_origin(item_type) is Annotated:
                    _, *annotations = get_args(
                        item_type
                    )  # We don't need the base_item_type

                    for annotation in annotations:
                        # Check for PopulateReference instance
                        if (
                            hasattr(annotation, "__class__")
                            and annotation.__class__.__name__ == "PopulateReference"
                        ):
                            model_class_ref = annotation.model_class
                            is_forward_ref = annotation.is_forward_ref

                            if is_forward_ref:
                                # Handle string reference by importing the model dynamically
                                model_class = await self._import_model_class(
                                    model_class_ref
                                )
                            else:
                                # Direct class reference
                                model_class = model_class_ref
                            break

                        # Legacy dict format support
                        elif (
                            isinstance(annotation, dict)
                            and "populate_model" in annotation
                        ):
                            model_class_ref = annotation["populate_model"]
                            is_forward_ref = annotation.get("is_forward_ref", False)

                            if is_forward_ref:
                                # Handle string reference by importing the model dynamically
                                model_class = await self._import_model_class(
                                    model_class_ref
                                )
                            else:
                                model_class = model_class_ref
                            break

            # Fallback: If it's a TractionXModel subclass
            if model_class is None and isinstance(field_value, (str, ObjectId)):
                if issubclass(field_type, TractionXModel):
                    model_class = field_type

            if model_class is None:
                # logger.warning(f"Field {field} has no model class to populate.")
                continue

            # Check if data is already populated
            if is_list:
                if all(isinstance(item, model_class) for item in field_value):  # type: ignore
                    # logger.info(f"Field {field} is already populated")
                    continue
                populated_items = []
                for item_id in field_value:  # type: ignore
                    doc = await model_class.find_one({"_id": item_id}, projection)
                    if doc and depth > 1:
                        await doc.populate(fields, depth - 1, projection)
                    populated_items.append(doc)
                setattr(self, field, populated_items)
            else:
                if isinstance(field_value, model_class):
                    # logger.info(f"Field {field} is already populated")
                    continue
                doc = await model_class.find_one({"_id": field_value}, projection)
                if doc and depth > 1:
                    await doc.populate(fields, depth - 1, projection)
                setattr(self, field, doc)

        return self  # type: ignore

    @staticmethod
    async def _import_model_class(model_class_name: str):
        """
        Dynamically import a model class by name.

        Args:
            model_class_name: The name of the model class to import

        Returns:
            The imported model class
        """
        # logger.info(f"Dynamically importing model class: {model_class_name}")

        # Map of common model names to their modules
        model_modules = {
            "Organization": "app.models.organization",
            "User": "app.models.user",
            "Role": "app.models.role",
            "Form": "app.models.form",
            "Section": "app.models.form",
            "Question": "app.models.form",
            "ScoringRule": "app.models.thesis",
            "MatchRule": "app.models.thesis",
            "Thesis": "app.models.thesis",
        }

        # Try to find the module for this model
        module_path = model_modules.get(
            model_class_name, f"app.models.{model_class_name.lower()}"
        )

        try:
            # Import the module
            module = __import__(module_path, fromlist=[model_class_name])
            # Get the class from the module
            return getattr(module, model_class_name)
        except (ImportError, AttributeError):
            # Fallback to form module for common form-related models
            try:
                from app.models import form

                return getattr(form, model_class_name)
            except AttributeError:
                # logger.error(
                #     f"Failed to import model class {model_class_name}: {str(e)}"
                # )
                return None

    def model_dump(self, *, for_db=False, **kwargs) -> Dict[str, Any]:
        """Override model_dump to:
        - Exclude internal/config fields (e.g., extra_model_config)
        - Only persist declared fields
        - Ensure values are of correct types as per model schema
        - Only convert ObjectId to str for API/JSON, not for DB
        """
        raw_data = super().model_dump(**kwargs)
        output = {}

        # Prefer model_fields (Pydantic v2), fallback to __fields__ (v1 compat)
        fields = getattr(self, "model_fields", getattr(self, "__fields__", {}))
        field_names = set(fields.keys())
        if "id" in field_names:
            field_names.add("_id")

        exclude_fields = {"extra_model_config", "model_config"}
        field_names -= exclude_fields

        for field_name in field_names:
            alias = None
            if kwargs.get("by_alias", False):
                if field_name in fields and hasattr(fields[field_name], "alias"):
                    alias = fields[field_name].alias
            key = alias or field_name
            if key in raw_data:
                value = raw_data[key]
                output[key] = TractionXModel.serialize_value(value, for_db)

        return output

    @classmethod
    def create_partial_model(
        cls: Type[T], name: str, fields: Dict[str, FieldInfo]
    ) -> Type[T]:
        """Create a partial model with selected fields."""
        return create_model(name, __base__=cls, **fields)  # type: ignore

    @classmethod
    async def aggregate(
        cls: Type[T], pipeline: List[Dict[str, Any]], include_deleted: bool = False
    ) -> List[Dict[str, Any]]:
        """Run aggregation pipeline with advanced options."""
        if not include_deleted and hasattr(cls, "is_deleted"):
            pipeline.insert(0, {"$match": {"is_deleted": False}})

        collection = await cls.get_collection()
        cursor = collection.aggregate(pipeline)
        return await cursor.to_list(length=None)

    @classmethod
    async def count(
        cls: Type[T], query: Dict[str, Any], include_deleted: bool = False
    ) -> int:
        """Count documents matching query."""
        if not include_deleted and hasattr(cls, "is_deleted"):
            query["is_deleted"] = False

        collection = await cls.get_collection()
        return await collection.count_documents(query)

    @classmethod
    async def distinct(
        cls: Type[T],
        field: str,
        query: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False,
    ) -> List[Any]:
        """Get distinct values for field."""
        if query is None:
            query = {}
        if not include_deleted and hasattr(cls, "is_deleted"):
            query["is_deleted"] = False

        collection = await cls.get_collection()
        return await collection.distinct(field, query)

    @classmethod
    async def delete_by_id(
        cls: Type[T],
        id: Union[str, ObjectId],
        actor_id: Optional[ObjectId] = None,
        hard_delete: bool = True,
    ) -> bool:
        """
        Delete a document by its ID.

        By default, performs a soft delete (sets is_deleted=True) if the model
        has an is_deleted field. Otherwise, performs a hard delete.

        Args:
            id: The ID of the document to delete
            actor_id: Optional ObjectId of the user performing the operation (for audit)
            hard_delete: If True, permanently removes the document instead of soft delete

        Returns:
            True if the document was deleted, False otherwise
        """
        # Convert string ID to ObjectId if needed
        if isinstance(id, str):
            id = ObjectId(id)

        # Get the document first
        doc = await cls.get_by_id(id, include_deleted=True)
        if not doc:
            return False

        # Perform delete operation
        if hard_delete:
            return await doc.hard_delete()
        else:
            return await doc.delete(actor_id)

    @classmethod
    async def delete_many(
        cls: Type[T],
        query: Dict[str, Any],
        actor_id: Optional[ObjectId] = None,
        hard_delete: bool = False,
    ) -> int:
        """
        Delete many documents matching query.

        By default, performs a soft delete (sets is_deleted=True) if the model
        has an is_deleted field. Otherwise, performs a hard delete.

        Args:
            query: Query to match documents to delete
            actor_id: Optional ObjectId of the user performing the operation (for audit)
            hard_delete: If True, permanently removes documents instead of soft delete

        Returns:
            Number of documents deleted
        """
        collection = await cls.get_collection()

        # Check if model has is_deleted field and we're not forcing hard delete
        if hasattr(cls, "is_deleted") and not hard_delete:
            # Perform soft delete
            update = {
                "$set": {
                    "is_deleted": True,
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                }
            }
            if actor_id:
                update["$set"]["updated_by"] = actor_id

            result = await collection.update_many(query, update)
            return result.modified_count
        else:
            # Perform hard delete
            result = await collection.delete_many(query)
            return result.deleted_count

    @classmethod
    async def update_one(
        cls: Type[T],
        query: Dict[str, Any],
        update: Dict[str, Any],
        actor_id: Optional[ObjectId] = None,
    ) -> UpdateResult:
        """Update one document matching query with audit trail."""
        return await cls.update_many(query, update, actor_id)

    @classmethod
    async def update_many(
        cls: Type[T],
        query: Dict[str, Any],
        update: Dict[str, Any],
        actor_id: Optional[ObjectId] = None,
    ) -> UpdateResult:
        """Update many documents matching query with audit trail."""
        if not query.get("is_deleted") and hasattr(cls, "is_deleted"):
            query["is_deleted"] = False

        # Add audit fields to update
        update["$set"] = update.get("$set", {})
        update["$set"]["updated_at"] = int(datetime.now(timezone.utc).timestamp())
        if actor_id:
            update["$set"]["updated_by"] = actor_id

        collection = await cls.get_collection()
        result = await collection.update_many(query, update)
        return result

    @classmethod
    async def bulk_write(
        cls: Type[T],
        operations: List[Dict[str, Any]],
        actor_id: Optional[ObjectId] = None,
    ) -> Dict[str, int]:
        """Perform bulk write operations with audit trail."""
        collection = await cls.get_collection()

        # Add audit fields to operations
        for op in operations:
            if "update" in op:
                op["update"]["$set"] = op["update"].get("$set", {})
                op["update"]["$set"]["updated_at"] = int(
                    datetime.now(timezone.utc).timestamp()
                )
                if actor_id:
                    op["update"]["$set"]["updated_by"] = actor_id

        result = await collection.bulk_write(operations)  # type: ignore
        return {
            "inserted": result.inserted_count,
            "modified": result.modified_count,
            "deleted": result.deleted_count,
        }

    # @classmethod
    # async def create_indexes(cls) -> None:
    #     """Create indexes for the collection."""
    #     collection = await cls.get_collection()

    #     # Create default indexes
    #     await collection.create_index("created_at")
    #     await collection.create_index("updated_at")
    #     await collection.create_index("is_deleted")
    #     await collection.create_index("is_active")
    #     await collection.create_index("created_by")
    #     await collection.create_index("updated_by")
    #     await collection.create_index("version")

    #     # Create compound indexes for common queries
    #     await collection.create_index([
    #         ("is_deleted", 1),
    #         ("is_active", 1),
    #         ("created_at", -1)
    #     ])

    #     await collection.create_index([
    #         ("is_deleted", 1),
    #         ("updated_at", -1)
    #     ])

    @classmethod
    async def get_stats(cls) -> Dict[str, Any]:
        """Get collection statistics."""
        collection = await cls.get_collection()
        return await collection.aggregate([
            {
                "$group": {
                    "_id": None,
                    "total": {"$sum": 1},
                    "active": {
                        "$sum": {"$cond": [{"$eq": ["$is_active", True]}, 1, 0]}
                    },
                    "deleted": {
                        "$sum": {"$cond": [{"$eq": ["$is_deleted", True]}, 1, 0]}
                    },
                }
            }
        ]).to_list(length=1)  # type: ignore

    @classmethod
    async def get_audit_trail(
        cls: Type[T], id: Union[str, ObjectId], limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get audit trail for a document."""
        collection = await cls.get_collection()
        return await collection.find(
            {"_id": ObjectId(str(id))}, {"audit_trail": {"$slice": [-limit]}}
        ).to_list(length=1)
