"""
Job Tracking Models

This module defines the data models for the centralized job tracking system,
which tracks asynchronous jobs across the application.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from pydantic import Field, field_validator, model_validator

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class JobStatus(str, Enum):
    """Status of a tracked job."""

    QUEUED = "queued"
    PENDING = "pending"  # Alias for QUEUED (queue compatibility)
    IN_PROGRESS = "in_progress"
    PROCESSING = "processing"  # Alias for IN_PROGRESS (queue compatibility)
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    SCHEDULED = "scheduled"


class JobType(str, Enum):
    """
    Types of jobs that can be tracked.

    This is an extensible enum - new job types can be added as needed.
    The CUSTOM type can be used with a custom job_type_name in the metadata.
    """

    SUBMISSION_PROCESSING = "submission_processing"
    SCORING = "scoring"
    AI_SUMMARY = "ai_summary"
    AI_ANALYSIS = "ai_analysis"
    DATA_ENRICHMENT = "data_enrichment"
    EXPORT = "export"
    IMPORT = "import"
    NOTIFICATION = "notification"
    INTEGRATION = "integration"
    REPORT_GENERATION = "report_generation"
    PITCH_DECK_PARSING = "parse_deal_excel_upload"
    DATA_SYNC = "data_sync"

    # Pitch processing jobs
    PARSE_PITCH_UPLOAD = "parse_pitch_upload"
    PARSE_PITCH_UPLOAD_EXISTING_DEAL = "parse_pitch_upload_existing_deal"

    # Research and analysis jobs
    EXTERNAL_SIGNALS_RESEARCH = "external_signals_research"
    THESIS_MATCHING = "thesis_matching"
    CONTEXT_BLOCK_GENERATION = "generate_context_block"

    # Deal processing jobs
    DEAL_ENRICHMENT = "deal_enrichment"

    # Form processing jobs
    PROCESS_RESOURCE_EVENT = "process_resource_event"
    PROCESS_FORM_SUBMISSION = "process_form_submission"

    # Trigger jobs
    EXECUTE_TRIGGER = "execute_trigger"

    # Chat jobs
    CHAT_COMPLETION = "chat_completion"

    CUSTOM = "custom"  # For custom job types, specify job_type_name in metadata


class EntityType(str, Enum):
    """
    Types of entities that can have associated jobs.

    This is an extensible enum - new entity types can be added as needed.
    The CUSTOM type can be used with a custom entity_type_name in the metadata.
    """

    FORM_SUBMISSION = "form_submission"
    FORM = "form"
    THESIS = "thesis"
    ORGANIZATION = "organization"
    USER = "user"
    SECTION = "section"
    QUESTION = "question"
    REPORT = "report"
    INTEGRATION = "integration"
    EXPORT = "export"

    # Deal-related entities
    DEAL = "deal"
    PITCH_DECK = "pitch_deck"
    CONTEXT_BLOCK = "context_block"
    RESEARCH = "research"

    # Import/Export entities
    IMPORT = "import"

    CUSTOM = "custom"  # For custom entity types, specify entity_type_name in metadata


class TrackedJob(TractionXModel):
    """
    Represents a tracked job in the system.

    This model is used to track the status and results of asynchronous jobs
    across the application, including AI processing, scoring, data enrichment,
    and any other asynchronous operations.

    The model is designed to be flexible and extensible, supporting various
    job types, entity types, and metadata configurations.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")

    # Job identification
    job_id: str = Field(
        ..., description="Unique identifier for the job (usually from queue system)"
    )
    entity_type: EntityType = Field(
        ..., description="Type of entity this job is associated with"
    )
    entity_id: Union[ObjectIdField, str] = Field(
        ...,
        description="ID of the entity this job is associated with (ObjectId or string)",
    )

    @field_validator("entity_id", mode="before")
    @classmethod
    def validate_entity_id(cls, v):
        """
        Pydantic validator for the 'entity_id' field.
        Converts string to ObjectId only if it's a valid ObjectId format (24 hex chars).
        Otherwise, keeps it as a string (for UUIDs, etc.).
        """
        if isinstance(v, str):
            # Check if it's a valid ObjectId format (24 hex chars)
            if len(v) == 24 and all(c in "0123456789abcdef" for c in v.lower()):
                return ObjectId(v)
            # Otherwise, keep it as a string (for UUIDs, etc.)
            return v
        return v

    # Job details
    job_type: JobType = Field(..., description="Type of job being performed")
    status: JobStatus = Field(
        default=JobStatus.QUEUED, description="Current status of the job"
    )
    progress: float = Field(default=0.0, description="Progress of the job (0.0 to 1.0)")
    error: Optional[str] = Field(
        default=None, description="Error message if the job failed"
    )

    # Job results
    output: Optional[Dict[str, Any]] = Field(
        default=None, description="Output of the job (results, data, etc.)"
    )

    # Additional metadata
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata about the job"
    )

    # Configuration
    config: Dict[str, Any] = Field(
        default_factory=dict,
        description="Configuration for the job (parameters, settings)",
    )

    # Priority and dependencies
    priority: int = Field(
        default=0, description="Priority of the job (higher values = higher priority)"
    )
    parent_job_id: Optional[ObjectIdField] = Field(
        default=None, description="ID of the parent job if this is a child job"
    )
    depends_on: List[ObjectIdField] = Field(
        default_factory=list, description="IDs of jobs this job depends on"
    )

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    completed_at: Optional[int] = Field(
        default=None, description="When the job was completed"
    )
    started_at: Optional[int] = Field(
        default=None, description="When the job was started"
    )
    scheduled_at: Optional[int] = Field(
        default=None, description="When the job is scheduled to run"
    )

    @field_validator("progress")
    @classmethod
    def validate_progress(cls, v: float) -> float:
        """Validate that progress is between 0 and 1."""
        if v < 0.0 or v > 1.0:
            raise ValueError("Progress must be between 0.0 and 1.0")
        return v

    @model_validator(mode="after")  # type: ignore
    def validate_timestamps(self) -> "TrackedJob":
        """Set timestamps based on job status."""
        now = int(datetime.now(timezone.utc).timestamp())

        # Set completed_at if status is COMPLETED or FAILED
        if (
            self.status in [JobStatus.COMPLETED, JobStatus.FAILED]
            and self.completed_at is None
        ):
            self.completed_at = now

        # Set started_at if status is IN_PROGRESS and started_at is None
        if self.status == JobStatus.IN_PROGRESS and self.started_at is None:
            self.started_at = now

        return self


class EntityWithJobs(TractionXModel):
    """
    Mixin for entities that have associated jobs.

    This mixin can be used to add job tracking capabilities to any entity model.
    """

    active_jobs: List[ObjectIdField] = Field(
        default_factory=list,
        description="List of active job IDs associated with this entity",
    )

    def add_job(self, job_id: Union[str, ObjectId]) -> None:
        """Add a job ID to the active jobs list."""
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        if job_id not in self.active_jobs:
            self.active_jobs.append(PyObjectId(job_id))

    def remove_job(self, job_id: Union[str, ObjectId]) -> None:
        """Remove a job ID from the active jobs list."""
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        if job_id in self.active_jobs:
            self.active_jobs.remove(job_id)
