from datetime import datetime
from typing import List, Optional

from bson import ObjectId
from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import BaseModel

from app.api.base import BaseAPIRouter
from app.core import logging
from app.core.database import get_database
from app.dependencies.auth import get_current_user
from app.models.audit import AuditLog
from app.models.user import TourProgress, User
from app.services.audit.interfaces import AuditServiceInterface
from app.services.auth.interface import IAuthService
from app.services.factory import (
    get_audit_service,
    get_auth_service,
    get_rbac_service,
    get_user_service,
)
from app.services.rbac.interfaces import RBACServiceInterface
from app.services.user.interfaces import UserServiceInterface
from app.services.user.mongo import UserService
from app.utils.rbac.rbac import rbac_register

logger = logging.get_logger(__name__)
router = BaseAPIRouter(prefix="/users", tags=["users"])


class SuperUserCreate(BaseModel):
    email: str
    password: str
    name: str


# Tour-related schemas
class TourProgressResponse(BaseModel):
    """Response schema for tour progress."""

    completed: List[str]
    last_seen_prompt: Optional[int]
    completed_at: Optional[int]


class CompleteTourRequest(BaseModel):
    """Request schema for marking a tour as completed."""

    tour_id: str


class TourCompletionResponse(BaseModel):
    """Response schema for tour completion."""

    success: bool
    message: Optional[str] = None


# Tour system constants
AVAILABLE_TOURS = [
    "onboarding",
    "dashboard",
    "deals",
    "forms",
    "theses",
    "deal-detail",
    "thesis-builder",
    "form-builder",
    "settings",
]  # Added "onboarding" as main tour
MAIN_ONBOARDING_TOUR = "onboarding"  # The unified master tour
PRODUCT_TOURS = ["dashboard", "deals", "forms", "theses"]  # Individual feature tours
TOUR_SYSTEM_LAUNCH_TIMESTAMP = **********  # January 21, 2025 (approximate launch date)


@router.post("/create-superuser")
@rbac_register(resource="users", action="create", hidden=True)
async def create_superuser(
    user_data: SuperUserCreate,
    db: AsyncIOMotorDatabase = Depends(get_database),
    auth_service: IAuthService = Depends(get_auth_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Create the first superuser account. This endpoint can only be used if no superusers exist."""
    # Check if any superuser exists
    existing_superuser = await db.users.find_one({"is_superuser": True})
    if existing_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="A superuser already exists. This endpoint can only be used to create the first superuser.",
        )

    # Check if user with this email exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists",
        )

    # Hash password
    password_hash = auth_service.get_password_hash(user_data.password)  # type: ignore

    user = User(
        email=user_data.email,
        password_hash=password_hash,
        name=user_data.name,
        is_superuser=True,
        status="active",
    )  # type: ignore
    result = await db.users.insert_one(user.dict(by_alias=True))
    user.id = result.inserted_id

    # log action
    audit_log = AuditLog(
        user_id=user.id,
        action="create_superuser",
        entity_type="user",
        entity_id=user.id,
    )
    await audit_service.log_action(audit_log)

    return {"message": "Superuser created successfully", "user_id": str(user.id)}


@router.put("/{user_id}/role")
@rbac_register(
    resource="users", action="edit", group="Users", description="Update user role"
)
async def update_user_role(
    user_id: str,
    role_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Update user's role."""
    # Check if user has permission to update roles
    user_service = UserService(db)
    # has_permission = await user_service.check_permission(
    #     current_user.role_id, "users", "edit", current_user.org_id
    # )
    # if not has_permission:
    #     raise HTTPException(
    #         status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
    #     )

    # Update role
    result = await user_service.update_user(
        {"_id": ObjectId(user_id), "org_id": current_user.org_id},
        {"$set": {"role_id": ObjectId(role_id)}},
    )

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Log role update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_role",
        entity_type="user",
        entity_id=ObjectId(user_id),  # type: ignore
        metadata={"new_role_id": role_id},
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    return {"message": "Role updated successfully"}


@router.put("/{user_id}/status")
@rbac_register(
    resource="users", action="edit", group="Users", description="Update user status"
)
async def update_user_status(
    user_id: str,
    status: str,
    current_user: User = Depends(get_current_user),
    rbac_service: RBACServiceInterface = Depends(get_rbac_service),
    user_service: UserServiceInterface = Depends(get_user_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Update user's status (active/suspended)."""
    # Check permissions
    # has_permission = await rbac_service.check_permission(
    #     current_user.id, "users", "edit"
    # )
    # if not has_permission:
    #     raise HTTPException(
    #         status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
    #     )

    if status not in ["active", "suspended"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,  # type: ignore
            detail="Invalid status",
        )

    # Update status
    result = await user_service.update_user(
        {"_id": ObjectId(user_id)}, {"$set": {"status": status}}
    )

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,  # type: ignore
            detail="User not found",
        )

    # Log status update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_status",
        entity_type="user",
        entity_id=ObjectId(user_id),  # type: ignore
        metadata={"new_status": status},
    )
    # log action
    await audit_service.log_action(audit_log)

    return {"message": "Status updated successfully"}


@router.get("/me")
@rbac_register(resource="users", action="view", hidden=True)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
):
    """Get current user's information including tour progress and tour prompt flags."""
    logger.info(f"Getting current user info for: {current_user.email}")

    # Determine if user should see tour prompt
    is_first_login = (
        not current_user.last_login
        or current_user.last_login < TOUR_SYSTEM_LAUNCH_TIMESTAMP
    )
    tour_progress = current_user.tour_progress

    # Enhanced logic focused on main onboarding tour
    if tour_progress:
        completed_tours = tour_progress.completed
        has_completed_main_onboarding = MAIN_ONBOARDING_TOUR in completed_tours
        has_seen_prompt = bool(tour_progress.last_seen_prompt)

        # Should prompt if:
        # 1. Main onboarding tour not completed, OR
        # 2. First-time users who haven't seen prompt yet
        should_prompt_tour = (not has_completed_main_onboarding) or (
            is_first_login and not has_seen_prompt
        )

        logger.info(
            f"User {current_user.email} tour status: main_onboarding_completed={has_completed_main_onboarding}, should_prompt: {should_prompt_tour}"
        )
    else:
        # No tour progress at all - should definitely prompt for main onboarding
        should_prompt_tour = True
        logger.info(
            f"User {current_user.email} has no tour progress, should_prompt: {should_prompt_tour}"
        )

    return {
        "user_id": str(current_user.id),
        "email": current_user.email,
        "name": current_user.name,
        "org_id": str(current_user.org_id) if current_user.org_id else None,
        "role_id": str(current_user.role_id) if current_user.role_id else None,
        "status": current_user.status,
        "tour_progress": tour_progress.model_dump() if tour_progress else None,
        "should_prompt_tour": should_prompt_tour,
        "is_first_login": is_first_login,
    }


# TODO: Implement user update endpoint
# @router.put("")
# @rbac_register(resource="users", action="edit", hidden=True)
# async def update_user(
#     user_data: UserUpdate,
#     current_user: User = Depends(get_current_user),
# ):
#     """Update user."""
#     return {"message": "User updated successfully"}


@router.get("/all")
@rbac_register(resource="users", action="view", hidden=True)
async def get_all_users(
    current_user: User = Depends(get_current_user),
):
    """Get all users."""
    org_id = current_user.org_id
    users = await User.find_many({"org_id": org_id})
    # remove password_hash from the response
    return [user.model_dump(by_alias=True, exclude={"password_hash"}) for user in users]


@router.get("/tours", response_model=TourProgressResponse)
@rbac_register(resource="users", action="view", hidden=True)
async def get_tour_progress(
    current_user: User = Depends(get_current_user),
):
    """Get current user's tour progress."""
    tour_progress = current_user.tour_progress
    if not tour_progress:
        # Return default empty progress for users without tour data
        return TourProgressResponse(
            completed=[], last_seen_prompt=None, completed_at=None
        )

    return TourProgressResponse(
        completed=tour_progress.completed,
        last_seen_prompt=tour_progress.last_seen_prompt,
        completed_at=tour_progress.completed_at,
    )


@router.post("/tours/complete", response_model=TourCompletionResponse)
@rbac_register(resource="users", action="edit", hidden=True)
async def complete_tour(
    request: CompleteTourRequest,
    current_user: User = Depends(get_current_user),
    user_service: UserServiceInterface = Depends(get_user_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Mark a tour as completed for the current user."""
    # Validate tour ID
    if request.tour_id not in AVAILABLE_TOURS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid tour ID. Available tours: {', '.join(AVAILABLE_TOURS)}",
        )

    # Get current tour progress or create new one
    tour_progress = current_user.tour_progress
    if not tour_progress:
        tour_progress = TourProgress(
            completed=[], last_seen_prompt=None, completed_at=None
        )

    # Add tour to completed list if not already there
    if request.tour_id not in tour_progress.completed:
        tour_progress.completed.append(request.tour_id)

        # Check if all tours are completed
        if len(tour_progress.completed) >= len(AVAILABLE_TOURS):
            tour_progress.completed_at = int(datetime.utcnow().timestamp())

        # Update user in database
        update_result = await user_service.update_user(
            {"_id": current_user.id},
            {"$set": {"tour_progress": tour_progress.model_dump()}},
        )

        if not update_result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update tour progress",
            )

        # Log the action
        audit_log = AuditLog(
            user_id=current_user.id,
            action="complete_tour",
            entity_type="user",
            entity_id=current_user.id,
            metadata={
                "tour_id": request.tour_id,
                "total_completed": len(tour_progress.completed),
            },
        )
        await audit_service.log_action(audit_log)

        message = f"Tour '{request.tour_id}' marked as completed"
        if tour_progress.completed_at:
            message += ". All tours completed!"
    else:
        message = f"Tour '{request.tour_id}' was already completed"

    return TourCompletionResponse(success=True, message=message)


@router.post("/tours/prompt_seen", response_model=TourCompletionResponse)
@rbac_register(resource="users", action="edit", hidden=True)
async def mark_tour_prompt_seen(
    current_user: User = Depends(get_current_user),
    user_service: UserServiceInterface = Depends(get_user_service),
    audit_service: AuditServiceInterface = Depends(get_audit_service),
):
    """Mark that the user has seen the tour prompt."""
    # Get current tour progress or create new one
    tour_progress = current_user.tour_progress
    if not tour_progress:
        tour_progress = TourProgress(
            completed=[], last_seen_prompt=None, completed_at=None
        )

    # Update last_seen_prompt timestamp
    tour_progress.last_seen_prompt = int(datetime.utcnow().timestamp())

    # Update user in database
    update_result = await user_service.update_user(
        {"_id": current_user.id},
        {"$set": {"tour_progress": tour_progress.model_dump()}},
    )

    if not update_result:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tour prompt timestamp",
        )

    # Log the action
    audit_log = AuditLog(
        user_id=current_user.id,
        action="tour_prompt_seen",
        entity_type="user",
        entity_id=current_user.id,
        metadata={"timestamp": tour_progress.last_seen_prompt},
    )
    await audit_service.log_action(audit_log)

    return TourCompletionResponse(
        success=True, message="Tour prompt seen timestamp updated"
    )
