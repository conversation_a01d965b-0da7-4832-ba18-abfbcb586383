"""
Deal API Endpoints

This module provides REST API endpoints for managing deals in the startup investing platform.
Supports full CRUD operations, filtering, search, and dashboard statistics.
"""

import json
import uuid
from datetime import datetime
from http import HTTPStatus
from typing import Any, Dict, List, Optional

from bson import ObjectId
from fastapi import Depends, File, Form, HTTPException, Query, UploadFile, status
from pydantic import Field

from app.api.base import BaseAPIRouter
from app.core.config import settings
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.base import TractionXModel
from app.models.deal import Deal, DealStatus
from app.models.public_submission import PublicSubmission
from app.models.user import User
from app.schemas.deal import (
    AddTimelineEventRequest,
    BulkDealUpdateRequest,
    DealAssignRequest,
    DealCreate,
    DealNotesRequest,
    DealResponse,
    DealSearchRequest,
    DealStatusUpdateRequest,
    DealSubmissionPreviewResponse,
    DealSummaryResponse,
    DealUpdate,
    DealUpdateAssignmentsRequest,
    UpdateUserPreferencesRequest,
)
from app.schemas.deal_document import (
    DealDocumentListResponse,
    DealDocumentResponse,
    DocumentDeleteResponse,
    DocumentDownloadResponse,
)
from app.services.company.interfaces import CompanyServiceInterface

# from app.services.factory import get_submission_service
from app.services.deal.interfaces import DealServiceInterface
from app.services.factory import (
    get_company_service,
    get_deal_document_service,
    get_deal_service,
    get_founder_service,
    get_queue_service,
    get_research_service,
)
from app.services.founder.interfaces import FounderServiceInterface
from app.utils.common import get_root_domain
from app.utils.jobs.job_utils import (
    JobBuilder,
    JobPriority,
    JobType,
)
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/deals", tags=["deals"])


# Scoring-related schemas
class ScoreOverrideRequest(TractionXModel):
    """Request schema for score override."""

    signal_type: str = Field(
        ...,
        description="Type of signal to override (team_strength, market_signals, thesis_match)",
    )
    new_score: int = Field(..., ge=0, le=100, description="New score (0-100)")
    reason: str = Field(..., min_length=10, description="Reason for override")


class FullAnalysisResponse(TractionXModel):
    """Response schema for full analysis."""

    deal_id: str
    overall_score: int
    signal_breakdown: Dict[str, Any]
    thesis_breakdown: Optional[Dict[str, Any]] = None
    comprehensive_scoring: Optional[Dict[str, Any]] = None


def _deal_to_response(deal: Deal) -> Dict[str, Any]:
    """Convert Deal model to DealResponse schema."""
    return deal.model_dump(by_alias=True)


# Core CRUD Endpoints


@router.post("", response_model=DealResponse, status_code=status.HTTP_201_CREATED)
@rbac_register(
    resource="deal", action="create", group="Deals", description="Create new deal"
)
async def create_deal(
    request: DealCreate,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Optional[Dict[str, Any]]:
    """
    Create a new deal.

    Creates a deal with the provided information. Core fields can be provided directly
    or will be extracted from the associated submission if available.
    """
    try:
        org_id, _ = org_context

        # Prepare deal data
        # convert request to dict
        deal_dict = request.model_dump()
        deal_dict["org_id"] = org_id
        deal = Deal(**deal_dict)
        await deal.save()

        if not deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create deal"
            )

        # Trigger company enrichment if company_website is available
        if deal.company_website:
            try:
                # Import the concrete service to access the enrichment method
                from app.services.deal.mongo import DealService

                deal_service_instance = DealService()
                await deal_service_instance._trigger_company_enrichment(deal)
                logger.info(
                    f"Triggered company enrichment for newly created deal {deal.id}"
                )
            except Exception as enrichment_error:
                logger.warning(
                    f"Failed to trigger company enrichment for deal {deal.id}: {enrichment_error}"
                )
                # Don't fail deal creation if enrichment fails

        logger.info(f"Created deal {deal.id} for organization {org_id}")
        return deal.model_dump(by_alias=True)

    except Exception as e:
        logger.error(f"Error creating deal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Preferences endpoints - must be defined before /{deal_id} routes to avoid routing conflicts
@router.get("/preferences", response_model=dict)
@rbac_register(
    resource="deal", action="view", group="Deals", description="Get deal preferences"
)
async def get_deal_preferences(
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Get user's deal dashboard preferences.

    Returns the saved filter and sorting preferences.
    """
    try:
        # Get current user from database to access preferences
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        preferences = user.preferences.get("deal_dashboard_filters", {})

        return {"deal_dashboard_filters": preferences}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deal preferences: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/preferences", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Update deal preferences"
)
async def update_deal_preferences(
    request: UpdateUserPreferencesRequest,
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Update user's deal dashboard preferences.

    Saves filter and sorting preferences that will be used as fallbacks
    when no query parameters are provided to the list_deals endpoint.
    """
    try:
        # Get current user from database to access preferences
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Update preferences
        update_data = {}
        if request.deal_dashboard_filters is not None:
            # Convert to dict for storage
            filters_dict = request.deal_dashboard_filters.model_dump(exclude_none=True)
            if filters_dict:
                update_data["preferences.deal_dashboard_filters"] = filters_dict

        if update_data:
            await User.update_one({"_id": current_user.id}, {"$set": update_data})

        logger.info(f"Updated deal preferences for user {current_user.id}")
        return {
            "message": "Preferences updated successfully",
            "preferences": {
                "deal_dashboard_filters": request.deal_dashboard_filters.model_dump(
                    exclude_none=True
                )
                if request.deal_dashboard_filters
                else None
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deal preferences: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{deal_id}", response_model=dict)
@rbac_register(resource="deal", action="view", group="Deals", description="View deal")
async def get_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Optional[Dict[str, Any]]:
    """
    Get a deal by ID.

    Returns the full deal information including timeline, scoring, and metadata.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        return deal.model_dump(by_alias=True)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("", response_model=Dict[str, Any])
@rbac_register(resource="deal", action="view", group="Deals", description="List deals")
async def list_deals(
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of deals to return"
    ),
    status: Optional[str] = Query(
        None, description="Filter by deal status (comma-separated)"
    ),
    form_id: Optional[str] = Query(None, description="Filter by form ID"),
    search: Optional[str] = Query(None, description="Search by company name"),
    stage: Optional[str] = Query(None, description="Filter by company stage"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    assigned_to_me: Optional[bool] = Query(
        None, description="Filter to show only deals assigned to current user"
    ),
    created_at_start: Optional[str] = Query(
        None, description="Deals created after this date (ISO string)"
    ),
    created_at_end: Optional[str] = Query(
        None, description="Deals created before this date (ISO string)"
    ),
    sort_by: str = Query("updated_at", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    favourites_only: Optional[bool] = Query(
        False, description="Filter to only favourite deals for current user"
    ),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    List deals with optional filtering and pagination.

    Supports filtering by status, form, stage, sector, tags, assignment, date ranges, and text search.
    Results are paginated and can be sorted by various fields.

    Filter resolution priority:
    1. Query parameters (highest priority)
    2. User preferences (fallback)
    3. Default values (lowest priority)
    """
    try:
        org_id, _ = org_context

        # Parse comma-separated values
        status_list = None
        if status:
            status_list = [s.strip() for s in status.split(",") if s.strip()]

        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

        # Resolve filters with preference fallback
        resolved_filters = await _resolve_deal_filters(
            current_user=current_user,
            query_status=status_list,
            query_assigned_to_me=assigned_to_me,
            query_created_at_start=created_at_start,
            query_created_at_end=created_at_end,
            query_sort_by=sort_by,
            query_sort_order=sort_order,
        )

        # Get deals with resolved filters
        deals, total = await deal_service.list_deals(
            org_id=org_id,
            skip=skip,
            limit=limit,
            status=resolved_filters["status"],
            form_id=form_id,
            search=search,
            stage=stage,
            sector=sector,
            tags=tag_list,
            assigned_to_me=resolved_filters["assigned_to_me"],
            created_at_start=resolved_filters["created_at_start"],
            created_at_end=resolved_filters["created_at_end"],
            sort_by=resolved_filters["sort_by"],
            sort_order=resolved_filters["sort_order"],
        )

        # Apply assigned_to_me filter if needed
        if resolved_filters["assigned_to_me"]:
            # Show only deals assigned to current user
            deals = [
                deal
                for deal in deals
                if str(current_user.id) in [str(uid) for uid in deal.assigned_user_ids]
            ]
            # Recalculate total for filtered deals only
            total = len(deals)
        # If assigned_to_me is False, show all deals (no additional filtering needed)

        # After fetching deals, filter for favourites if needed
        if favourites_only:
            deals = [
                deal
                for deal in deals
                if any(
                    str(fav.get("user_id")) == str(current_user.id)
                    for fav in (deal.favourites or [])
                )
            ]
            total = len(deals)

        # Convert to response format
        deals_data = [deal.model_dump(by_alias=True) for deal in deals]
        logger.info(f"🔍 DEBUG: deals_data: {deals_data}")
        return {
            "deals": deals_data,
            "total": total,
            "skip": skip,
            "limit": limit,
            "has_more": skip + len(deals) < total,
            "applied_filters": resolved_filters,  # Return applied filters for frontend
        }

    except Exception as e:
        logger.error(f"Error listing deals: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{deal_id}", response_model=dict)
@rbac_register(resource="deal", action="edit", group="Deals", description="Update deal")
async def update_deal(
    deal_id: str,
    request: DealUpdate,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Optional[Dict[str, Any]]:
    """
    Update a deal.

    Supports updating all deal fields including status, notes, tags, and core information.
    Status changes are automatically tracked in the timeline.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Prepare update data
        update_data = {}
        for field, value in request.model_dump(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value

        # Update deal
        updated_deal = await deal_service.update_deal(deal_id, update_data)
        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update deal"
            )

        # Trigger company enrichment if company_website was added/updated
        if "company_website" in update_data and updated_deal.company_website:
            try:
                # Import the concrete service to access the enrichment method
                from app.services.deal.mongo import DealService

                if isinstance(deal_service, DealService):
                    await deal_service._trigger_company_enrichment(updated_deal)
                    logger.info(
                        f"Triggered company enrichment for deal {deal_id} after update"
                    )
            except Exception as enrichment_error:
                logger.warning(
                    f"Failed to trigger company enrichment for deal {deal_id}: {enrichment_error}"
                )
                # Don't fail the update if enrichment fails

        logger.info(f"Updated deal {deal_id} for organization {org_id}")
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{deal_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(
    resource="deal", action="delete", group="Deals", description="Delete deal"
)
async def delete_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
):
    """
    Delete a deal.

    Permanently removes the deal from the system. This action cannot be undone.
    """
    try:
        org_id, _ = org_context
        logger.info(f"Deleting deal {deal_id} for organization {org_id}")
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get existing deal to verify access
        existing_deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Delete deal
        success = await deal_service.delete_deal(deal_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to delete deal"
            )

        logger.info(f"Deleted deal {deal_id} for organization {org_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Auxiliary Endpoints


@router.get("/by_submission/{submission_id}", response_model=List[Dict[str, Any]])
@rbac_register(
    resource="deal", action="view", group="Deals", description="Get deals by submission"
)
async def get_deals_by_submission(
    submission_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> List[Optional[Dict[str, Any]]]:
    """
    Get deals associated with a specific submission.

    Returns all deals that were created from or associated with the given submission.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(submission_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid submission ID format",
            )

        # Get deals
        deals = await deal_service.get_deals_by_submission(submission_id, org_id)

        # Convert to response format
        return [_deal_to_response(deal) for deal in deals]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deals by submission {submission_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/by_form/{form_id}", response_model=List[Dict[str, Any]])
@rbac_register(
    resource="deal", action="view", group="Deals", description="Get deals by form"
)
async def get_deals_by_form(
    form_id: str,
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of deals to return"
    ),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> List[Dict[str, Any]]:
    """
    Get deals associated with a specific form.

    Returns paginated list of deals that were created from the given form.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(form_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid form ID format"
            )

        # Get deals
        deals, total = await deal_service.get_deals_by_form(
            form_id, org_id, skip, limit
        )

        # Convert to response format
        deal_responses = [_deal_to_response(deal) for deal in deals]

        return deal_responses
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deals by form {form_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/summary", response_model=DealSummaryResponse)
@rbac_register(
    resource="deal", action="view", group="Deals", description="Get deal summary"
)
async def get_deal_summary(
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> DealSummaryResponse:
    """
    Get deal summary statistics for dashboard.

    Returns aggregated counts by status, stage, sector, and recent activity metrics.
    """
    try:
        org_id, _ = org_context

        # Get summary
        summary = await deal_service.get_deal_summary(org_id)

        return DealSummaryResponse(**summary)

    except Exception as e:
        logger.error(f"Error getting deal summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/{deal_id}/timeline", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Add timeline event"
)
async def add_timeline_event(
    deal_id: str,
    request: AddTimelineEventRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Optional[Dict[str, Any]]:
    """
    Add a timeline event to a deal.

    Records an event in the deal's timeline with timestamp and optional notes.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Add timeline event
        updated_deal = await deal_service.add_timeline_event(
            deal_id, request.event, request.notes, current_user.id
        )

        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add timeline event",
            )

        logger.info(f"Added timeline event to deal {deal_id}")
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding timeline event to deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{deal_id}/notes", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Update deal notes"
)
async def update_deal_notes(
    deal_id: str,
    request: DealNotesRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Optional[Dict[str, Any]]:
    """
    Update deal notes.

    Replaces the existing notes with the provided content.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Update notes
        updated_deal = await deal_service.update_deal_notes(deal_id, request.notes)

        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update deal notes",
            )

        logger.info(f"Updated notes for deal {deal_id}")
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating notes for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/bulk_update", response_model=List[Dict[str, Any]])
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Bulk update deals"
)
async def bulk_update_deals(
    request: BulkDealUpdateRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> List[Dict[str, Any]]:
    """
    Bulk update multiple deals.

    Applies the same updates to multiple deals in a single operation.
    Only deals belonging to the current organization will be updated.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        for deal_id in request.deal_ids:
            try:
                ObjectId(deal_id)
            except Exception:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid deal ID format: {deal_id}",
                )

        # Prepare update data
        update_data = {}
        for field, value in request.updates.model_dump(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No updates provided"
            )

        # Bulk update
        updated_deals = await deal_service.bulk_update_deals(
            request.deal_ids,  # type: ignore
            update_data,
            org_id,  # type: ignore
        )

        logger.info(
            f"Bulk updated {len(updated_deals)} deals for organization {org_id}"
        )
        return [_deal_to_response(deal) for deal in updated_deals]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bulk updating deals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/search", response_model=dict)
@rbac_register(
    resource="deal", action="view", group="Deals", description="Advanced deal search"
)
async def search_deals(
    request: DealSearchRequest,
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of deals to return"
    ),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Advanced search for deals.

    Supports complex filtering with multiple criteria, date ranges, and custom sorting.
    """
    try:
        org_id, _ = org_context

        # Search deals
        deals, total = await deal_service.search_deals(
            org_id=org_id,
            query=request.query,
            filters=request.filters,
            date_range=request.date_range,
            sort_by=request.sort_by or "created_at",
            sort_order=request.sort_order or "desc",
            skip=skip,
            limit=limit,
        )

        # Convert to response format
        deal_responses = [_deal_to_response(deal) for deal in deals]

        return {
            "deals": deal_responses,
            "total": total,
            "skip": skip,
            "limit": limit,
            "has_more": skip + len(deals) < total,
        }

    except Exception as e:
        logger.error(f"Error searching deals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Scoring Endpoints


@router.get("/{deal_id}/full-analysis", response_model=FullAnalysisResponse)
@rbac_register(
    resource="deal", action="read", group="Deals", description="Get full deal analysis"
)
async def get_full_analysis(
    deal_id: str,
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> FullAnalysisResponse:
    """
    Get comprehensive scoring analysis for a deal.

    Returns detailed scoring breakdown including thesis matching,
    question-wise analysis, and AI explanations.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal from database
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        deal_data = deal.model_dump(by_alias=True)

        # Extract comprehensive scoring from deal
        comprehensive_scoring = deal_data["scoring"] if deal_data["scoring"] else {}

        # Build response from comprehensive scoring data
        if comprehensive_scoring and isinstance(comprehensive_scoring, dict):
            # Extract scoring components
            thesis_data = comprehensive_scoring.get("thesis", {})
            founders_data = comprehensive_scoring.get("founders", {})
            market_data = comprehensive_scoring.get("market", {})

            # Calculate overall score (weighted average)
            thesis_score = thesis_data.get("normalized_score", 0)
            founders_score = founders_data.get("normalized_score", 0)
            market_score = market_data.get("normalized_score", 0)

            # Weight: thesis 50%, founders 30%, market 20%
            overall_score = (
                (thesis_score * 0.5) + (founders_score * 0.3) + (market_score * 0.2)
            )

            # Build signal breakdown
            signal_breakdown = {
                "thesis_match": {
                    "score": int(thesis_score),
                    "explanation": f"Thesis matching score based on {len(thesis_data.get('question_scores', {}))} evaluated criteria",
                    "ai_insights": f"Scored against {thesis_data.get('thesis_name', 'investment thesis')} with detailed rule evaluation",
                    "sources": [],
                    "sub_scores": {},
                },
                "team_strength": {
                    "score": int(founders_score),
                    "explanation": founders_data.get(
                        "ai_analysis", "Founder analysis pending"
                    ),
                    "ai_insights": f"Founder evaluation score: {founders_score:.1f}",
                    "sources": [],
                    "sub_scores": {},
                },
                "market_signals": {
                    "score": int(market_score),
                    "explanation": market_data.get(
                        "ai_analysis", "Market analysis pending"
                    ),
                    "ai_insights": f"Market evaluation score: {market_score:.1f}",
                    "sources": [],
                    "sub_scores": {},
                },
            }

            # Build thesis breakdown from detailed scoring
            thesis_breakdown = None
            if thesis_data.get("question_scores"):
                rules = []
                for question_id, score_data in thesis_data["question_scores"].items():
                    rules.append({
                        "question_id": question_id,
                        "question_label": f"Question {question_id}",
                        "raw_score": score_data["raw_score"],
                        "weight": score_data["weight"],
                        "weighted_score": score_data["weighted_score"],
                        "explanation": score_data.get("explanation", ""),
                        "ai_generated": score_data.get("ai_generated", False),
                    })

                thesis_breakdown = {
                    "thesis_id": thesis_data.get("thesis_id", ""),
                    "thesis_name": thesis_data.get("thesis_name", ""),
                    "rules": rules,
                    "total_score": thesis_data.get("total_score", 0),
                    "max_possible_score": thesis_data.get("max_possible_score", 0),
                }

            return FullAnalysisResponse(
                deal_id=deal_id,
                overall_score=int(overall_score),
                signal_breakdown=signal_breakdown,
                thesis_breakdown=thesis_breakdown,
                comprehensive_scoring=comprehensive_scoring,
            )

        # Fallback for deals without comprehensive scoring
        return FullAnalysisResponse(
            deal_id=deal_id,
            overall_score=0,
            signal_breakdown={
                "thesis_match": {
                    "score": 0,
                    "explanation": "No thesis scoring available",
                    "ai_insights": "",
                },
                "team_strength": {
                    "score": 0,
                    "explanation": "No founder analysis available",
                    "ai_insights": "",
                },
                "market_signals": {
                    "score": 0,
                    "explanation": "No market analysis available",
                    "ai_insights": "",
                },
            },
            thesis_breakdown=None,
            comprehensive_scoring=None,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting full analysis for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get full analysis",
        )


@router.post("/{deal_id}/score-override")
@rbac_register(
    resource="deal", action="update", group="Deals", description="Override deal score"
)
async def override_score(
    deal_id: str,
    override_request: ScoreOverrideRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Override a specific signal score for a deal.

    Updates the score for a specific signal type and adds an audit trail entry.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # TODO: Implement actual score override logic
        # For now, return success response
        return {
            "success": True,
            "message": f"Score override applied for {override_request.signal_type}",
            "new_score": override_request.new_score,
            "reason": override_request.reason,
            "overridden_by": current_user.email,
            "timestamp": datetime.now().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error overriding score for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to override score",
        )


# Deal Document Endpoints


@router.get("/{deal_id}/documents", response_model=DealDocumentListResponse)
@rbac_register(
    resource="deal", action="view", group="Deals", description="List deal documents"
)
async def list_deal_documents(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
) -> DealDocumentListResponse:
    """
    List all documents for a deal.

    Returns both investor-uploaded documents and startup submission files.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await Deal.find_one({
            "_id": ObjectId(deal_id),
        })
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

            # Get documents
        doc_service = await get_deal_document_service()
        documents = await doc_service.list_deal_documents(deal_id)  # type: ignore

        # Convert to response format
        document_responses = []
        total_size = 0

        for doc in documents:
            total_size += doc.file_size

            # Check if user can delete this document
            can_delete = doc.can_be_deleted_by(current_user.email, "investor")

            doc_response = DealDocumentResponse(
                id=str(doc.id),
                deal_id=str(doc.deal_id),
                org_id=str(doc.org_id),
                source=doc.source,
                filename=doc.filename,
                document_type=doc.document_type,
                file_size=doc.file_size,
                status=doc.status,
                uploaded_by_email=doc.uploaded_by_email,
                uploaded_by_name=doc.uploaded_by_name,
                uploaded_by_role=doc.uploaded_by_role,
                download_url=doc.download_url,
                preview_url=doc.preview_url,
                tags=doc.tags,
                download_count=doc.download_count,
                last_accessed_at=datetime.fromtimestamp(doc.last_accessed_at)
                if doc.last_accessed_at
                else None,
                last_accessed_by=doc.last_accessed_by,
                created_at=datetime.fromtimestamp(doc.created_at),
                updated_at=datetime.fromtimestamp(doc.updated_at),
                can_delete=can_delete,
            )
            document_responses.append(doc_response)

        return DealDocumentListResponse(
            documents=document_responses,
            total_count=len(document_responses),
            total_size=total_size,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing documents for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list documents",
        )


@router.post("/{deal_id}/documents", response_model=DealDocumentResponse)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Upload deal document"
)
async def upload_deal_document(
    deal_id: str,
    file: UploadFile = File(...),
    tags: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> DealDocumentResponse:
    """
    Upload a new document to a deal.

    Only authenticated organization members can upload documents.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No file provided"
            )

        # Check file extension
        allowed_extensions = [
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".csv",
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
        ]
        file_ext = (
            "." + file.filename.split(".")[-1].lower() if "." in file.filename else ""
        )

        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Supported types: {', '.join(allowed_extensions)}",
            )

        # Read file content
        file_content = await file.read()

        # Parse tags
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

            # Upload document
        from app.core.database import get_database
        from app.services.deal_document import DealDocumentService

        db = await get_database()
        doc_service = DealDocumentService(db)

        document = await doc_service.upload_document(
            deal_id=deal_id,
            org_id=org_id,
            file_content=file_content,
            filename=file.filename,
            mime_type=file.content_type or "application/octet-stream",
            user_id=current_user.id,
            user_email=current_user.email,
            user_name=current_user.name,  # Use name instead of first_name + last_name
            user_role="investor",  # Default role for now
            tags=tag_list,
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload document",
            )

        # Add timeline event
        await deal_service.add_timeline_event(
            deal_id=deal_id,
            event="Document uploaded",
            notes=f"Document '{file.filename}' uploaded by {current_user.email}",
            user_id=current_user.id,
        )

        # Return response
        return DealDocumentResponse(
            id=str(document.id),
            deal_id=str(document.deal_id),
            org_id=str(document.org_id),
            source=document.source,
            filename=document.filename,
            document_type=document.document_type,
            file_size=document.file_size,
            status=document.status,
            uploaded_by_email=document.uploaded_by_email,
            uploaded_by_name=document.uploaded_by_name,
            uploaded_by_role=document.uploaded_by_role,
            download_url=document.download_url,
            preview_url=document.preview_url,
            tags=document.tags,
            download_count=document.download_count,
            last_accessed_at=datetime.fromtimestamp(document.last_accessed_at)
            if document.last_accessed_at
            else None,
            last_accessed_by=document.last_accessed_by,
            created_at=datetime.fromtimestamp(document.created_at),
            updated_at=datetime.fromtimestamp(document.updated_at),
            can_delete=document.can_be_deleted_by(current_user.email, "investor"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload document",
        )


@router.delete(
    "/{deal_id}/documents/{document_id}", response_model=DocumentDeleteResponse
)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Delete deal document"
)
async def delete_deal_document(
    deal_id: str,
    document_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> DocumentDeleteResponse:
    """
    Delete a deal document.

    Only the uploader or admin can delete investor-uploaded documents.
    Startup submission documents can only be deleted by admins.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(document_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

            # Delete document
        from app.core.database import get_database
        from app.services.deal_document import DealDocumentService

        db = await get_database()
        doc_service = DealDocumentService(db)

        success, error_message = await doc_service.delete_document(
            document_id=document_id,
            user_email=current_user.email,
            user_role="investor",
        )

        if not success:
            if "not found" in error_message.lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=error_message,
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=error_message,
                )

        # Add timeline event
        await deal_service.add_timeline_event(
            deal_id=deal_id,
            event="Document deleted",
            notes=f"Document deleted by {current_user.email}",
            user_id=current_user.id,
        )

        return DocumentDeleteResponse(
            success=True, message="Document deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id} for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document",
        )


@router.get(
    "/{deal_id}/documents/{document_id}/download",
    response_model=DocumentDownloadResponse,
)
@rbac_register(
    resource="deal", action="view", group="Deals", description="Download deal document"
)
async def download_deal_document(
    deal_id: str,
    document_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> DocumentDownloadResponse:
    """
    Get a presigned download URL for a deal document.

    All users with deal access can download documents.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(document_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

            # Get download URL
        from app.core.database import get_database
        from app.services.deal_document import DealDocumentService

        db = await get_database()
        doc_service = DealDocumentService(db)

        download_url = await doc_service.get_document_download_url(
            document_id=document_id,
            user_email=current_user.email,
        )

        if not download_url:
            raise HTTPException(status_code=404, detail="Document not found")

        return DocumentDownloadResponse(
            download_url=download_url,
            expires_in=600,  # 10 minutes
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting download URL for document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get download URL",
        )


# Deal Submission Preview Endpoint


@router.get(
    "/{deal_id}/submissions/preview", response_model=DealSubmissionPreviewResponse
)
@rbac_register(
    resource="deal",
    action="view",
    group="Deals",
    description="Get deal submission preview",
)
async def get_deal_submission_preview(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Get a comprehensive preview of all submissions for a deal.

    Returns structured, frontend-ready data with:
    - All form answers mapped to human-readable labels
    - Repeatable sections properly handled
    - Form structure and metadata
    - Dropdown options for multiple submissions

    Optimized for fast rendering with no business logic required on frontend.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Get submission preview data
        preview_data = await deal_service.get_deal_submission_preview(deal_id)

        if not preview_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate submission preview",
            )

        logger.info(f"Generated submission preview for deal {deal_id}")
        return preview_data

    except Exception as e:
        logger.error(f"Error getting submission preview for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get submission preview",
        )


# External Signals Research Endpoints


@router.get("/{deal_id}/external-signals")
@rbac_register(
    resource="deal",
    action="view",
    group="Deals",
    description="Get external signals research",
)
async def get_external_signals(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    research_service=Depends(get_research_service),
) -> Dict[str, Any]:
    """
    Get external signals research for a deal.

    Returns all 4 research cards (competitors, market, news, summary) if available.
    If research doesn't exist, returns empty structure.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Get existing research
        external_signals = await research_service.get_external_signals(deal_id)

        if external_signals:
            return external_signals.model_dump()
        else:
            # Return empty structure if no research exists
            return {
                "deal_id": deal_id,
                "competitors": None,
                "market": None,
                "news": None,
                "summary": None,
                "generated_at": None,
                "version": "v1.0",
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting external signals for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get external signals",
        )


@router.post("/{deal_id}/external-signals/refresh")
@rbac_register(
    resource="deal",
    action="edit",
    group="Deals",
    description="Refresh external signals research",
)
async def refresh_external_signals(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    queue_service=Depends(get_queue_service),
) -> Dict[str, Any]:
    """
    Force refresh external signals research for a deal.

    Triggers a new research job that will overwrite existing research.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Check if context block exists
        if not deal.context_block_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Context block not available for this deal",
            )

        # Enqueue research job with force refresh
        from app.utils.jobs.job_utils import JobBuilder

        job = await (
            JobBuilder()
            .for_deal(deal_id)
            .of_type(JobType.EXTERNAL_SIGNALS_RESEARCH)
            .with_payload(
                deal_id=deal_id,
                org_id=org_id,
                context_block_url=deal.context_block_url,
                force_refresh=True,
            )
            .with_metadata(
                endpoint="refresh_external_signals",
                deal_id=deal_id,
                triggered_by=current_user.email,
            )
            .create()
        )

        logger.info(f"Enqueued external signals refresh job {job} for deal {deal_id}")

        return {
            "success": True,
            "message": "External signals research refresh initiated",
            "job_id": str(job.id),
            "deal_id": deal_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing external signals for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh external signals",
        )


# Deal Assignment & Status Update Endpoints


@router.patch("/{deal_id}/assign", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Assign users to deal"
)
async def assign_deal(
    deal_id: str,
    request: DealAssignRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Assign users to a deal.

    Validates that all users belong to the same organization as the deal,
    updates the assignment, logs the action in the timeline, and sends
    email notifications to the assigned users.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            for user_id in request.user_ids:
                ObjectId(user_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Assign users to deal
        updated_deal = await deal_service.assign_users_to_deal(
            deal_id=deal_id, user_ids=request.user_ids, acting_user_id=current_user.id
        )

        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to assign users to deal",
            )

        logger.info(f"Assigned users {request.user_ids} to deal {deal_id}")
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning users to deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{deal_id}/assignments", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Update deal assignments"
)
async def update_deal_assignments(
    deal_id: str,
    request: DealUpdateAssignmentsRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Update assigned users on a deal.

    Supports three actions:
    - 'replace': Replace all assigned users with the provided list
    - 'add': Add new users to existing assignments
    - 'remove': Remove specified users from assignments

    Validates that all users belong to the same organization as the deal,
    updates the assignment, logs the action in the timeline, and sends
    email notifications to newly assigned users.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            for user_id in request.user_ids:
                ObjectId(user_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Update deal assignments
        updated_deal = await deal_service.update_deal_assignments(
            deal_id=deal_id,
            user_ids=request.user_ids,
            action=request.action,
            acting_user_id=current_user.id,
        )

        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update deal assignments",
            )

        logger.info(
            f"Updated deal {deal_id} assignments with action '{request.action}'"
        )
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deal assignments {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{deal_id}/status", response_model=dict)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Update deal status"
)
async def update_deal_status(
    deal_id: str,
    request: DealStatusUpdateRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Update the status of a deal.

    Updates the deal status and logs the change in the timeline with
    an optional note explaining the status change.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Update deal status
        updated_deal = await deal_service.update_deal_status(
            deal_id=deal_id,
            new_status=request.status,
            note=request.note,
            acting_user_id=current_user.id,
        )

        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update deal status",
            )

        logger.info(f"Updated deal {deal_id} status to {request.status}")
        return _deal_to_response(updated_deal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deal status {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Helper Functions


async def _resolve_deal_filters(
    current_user: User,
    query_status: Optional[List[str]] = None,
    query_assigned_to_me: Optional[bool] = None,
    query_created_at_start: Optional[str] = None,
    query_created_at_end: Optional[str] = None,
    query_sort_by: Optional[str] = None,
    query_sort_order: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Resolve deal filters with preference fallback.

    Priority order:
    1. Query parameters (highest priority)
    2. User preferences (fallback)
    3. Default values (lowest priority)
    """
    # Get user preferences
    user_preferences = current_user.preferences.get("deal_dashboard_filters", {})

    # Resolve each filter with priority
    resolved_filters = {
        "status": query_status
        or user_preferences.get("status")
        or [status.value for status in DealStatus],
        "assigned_to_me": query_assigned_to_me
        if query_assigned_to_me is not None
        else user_preferences.get("assigned_to_me", False),
        "created_at_start": query_created_at_start
        or user_preferences.get("created_at_start"),
        "created_at_end": query_created_at_end
        or user_preferences.get("created_at_end"),
        "sort_by": query_sort_by or user_preferences.get("sort_by", "updated_at"),
        "sort_order": query_sort_order or user_preferences.get("sort_dir", "desc"),
    }

    # Validate status values
    if resolved_filters["status"]:
        valid_statuses = [s.value for s in DealStatus]
        resolved_filters["status"] = [
            status for status in resolved_filters["status"] if status in valid_statuses
        ]
        # If no valid statuses, use defaults
        if not resolved_filters["status"]:
            resolved_filters["status"] = [status.value for status in DealStatus]

    # Validate sort_by
    valid_sort_fields = ["created_at", "updated_at", "status_updated_at"]
    if resolved_filters["sort_by"] not in valid_sort_fields:
        resolved_filters["sort_by"] = "updated_at"

    # Validate sort_order
    if resolved_filters["sort_order"] not in ["asc", "desc"]:
        resolved_filters["sort_order"] = "desc"

    logger.info(f"Resolved filters: {resolved_filters}")

    return resolved_filters


@router.post("/{deal_id}/favourite", response_model=DealResponse)
@rbac_register(
    resource="deal", action="edit", group="Deals", description="Mark deal as favourite"
)
async def favourite_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
):
    """Mark a deal as favourite for the current user."""
    org_id, _ = org_context
    deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
    if not deal:
        raise HTTPException(status_code=404, detail="Deal not found")
    # Add favourite if not already present
    if not any(fav.get("user_id") == str(current_user.id) for fav in deal.favourites):
        deal.favourites.append({
            "user_id": str(current_user.id),
            "timestamp": int(datetime.utcnow().timestamp()),
        })
        await deal.save(is_update=True)
    return deal.model_dump(by_alias=True)


@router.delete("/{deal_id}/favourite", response_model=DealResponse)
@rbac_register(
    resource="deal",
    action="edit",
    group="Deals",
    description="Remove deal from favourites",
)
async def unfavourite_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
):
    """Remove a deal from favourites for the current user."""
    org_id, _ = org_context
    deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
    if not deal:
        raise HTTPException(status_code=404, detail="Deal not found")
    deal.favourites = [
        fav for fav in deal.favourites if fav.get("user_id") != str(current_user.id)
    ]
    await deal.save(is_update=True)
    return deal.model_dump(by_alias=True)


# Founder Analysis Endpoints


@router.get("/{deal_id}/founders", response_model=Dict[str, Any])
@rbac_register(
    resource="deal", action="view", group="Deals", description="Get deal founders"
)
async def get_deal_founders(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    founder_service: FounderServiceInterface = Depends(get_founder_service),
) -> Dict[str, Any]:
    """
    Get all enriched founder data tied to a deal.

    This endpoint:
    1. Takes a deal_id
    2. Resolves the company_website from the deal
    3. Fetches all founders tied to that company
    4. Returns structured founder data including profile, experience, education, skills, profiles, and signals

    Returns:
        Dictionary containing founders array with all enriched data
    """
    try:
        org_id, _ = org_context
        logger.info(f"Getting founders for deal {deal_id}")

        # Get deal
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Check if deal has company_website
        if not deal.company_website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company website not found for deal",
            )
        logger.info(
            f"Company Website: {deal.company_website}, looking up domain: {get_root_domain(deal.company_website)}"
        )
        # Get founders using company_website as company_id
        founders = await founder_service.get_founders_by_company_id(
            get_root_domain(deal.company_website)
        )

        logger.info(f"Found {len(founders)} founders for deal {deal_id}")

        return {"founders": founders}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting founders for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Executive Summary Endpoint


@router.get("/{deal_id}/executive-summary")
@rbac_register(
    resource="deal",
    action="view",
    group="Deals",
    description="Get executive summary JSON",
)
async def get_executive_summary(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """
    Get executive summary JSON for a deal.

    Fetches the executive summary JSON from S3 and returns it to the frontend.
    The executive summary contains structured sections with titles and content.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Check if executive summary JSON URL exists
        if not deal.executive_summary_json_url:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Executive summary not available for this deal",
            )

        # Fetch executive summary from S3
        import boto3

        from app.core.config import settings

        try:
            # Initialize S3 client
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION,
            )

            # Extract S3 key and bucket from URL - handle both s3:// and https:// formats
            s3_key = None
            s3_bucket = None

            if deal.executive_summary_json_url.startswith("s3://"):
                # Handle s3:// format: s3://bucket/key
                url_parts = deal.executive_summary_json_url.replace("s3://", "").split(
                    "/", 1
                )
                s3_bucket = url_parts[0]
                s3_key = url_parts[1] if len(url_parts) > 1 else None
            elif deal.executive_summary_json_url.startswith("https://"):
                # Handle https:// format: https://bucket.s3.region.amazonaws.com/key
                from urllib.parse import urlparse

                parsed_url = urlparse(deal.executive_summary_json_url)
                # Extract bucket from hostname: bucket.s3.region.amazonaws.com
                if not parsed_url.hostname:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid URL: missing hostname",
                    )
                hostname_parts = parsed_url.hostname.split(".")
                s3_bucket = hostname_parts[0]  # First part is the bucket name
                # Remove leading slash and get the path
                s3_key = parsed_url.path.lstrip("/")
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid executive summary URL format",
                )

            if not s3_key:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Could not extract S3 key from URL",
                )

            logger.info(f"Using S3 bucket: {s3_bucket}, key: {s3_key}")

            # Read JSON content from S3
            response = s3_client.get_object(Bucket=s3_bucket, Key=s3_key)

            json_content = response["Body"].read().decode("utf-8")

            # Parse JSON
            import json

            executive_summary_data = json.loads(json_content)

            logger.info(f"Successfully fetched executive summary for deal {deal_id}")
            return executive_summary_data

        except Exception as e:
            logger.error(f"Error fetching executive summary from S3: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch executive summary",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting executive summary for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{deal_id}/company/enriched")
async def get_company_enriched(
    deal_id: str,
    org_context: tuple[str, bool] = Depends(get_org_context),
    company_service: CompanyServiceInterface = Depends(get_company_service),
) -> Dict[str, Any]:
    """
    Get enriched company data with all related information.

    This endpoint:
    1. Takes a domain query parameter
    2. Fetches company enrichment data from the database
    3. Returns structured company data including keywords, technologies, and department counts

    Args:
        domain: Company domain (e.g., "kubegrade.com")

    Returns:
        Dictionary containing company data with all enriched information
    """
    try:
        org_id, _ = org_context
        deal = await Deal.find_one({"_id": ObjectId(deal_id)})

        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        website = deal.company_website

        if not website:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Deal does not have a company website",
            )

        domain = get_root_domain(website)
        logger.info(f"Domain Looked Up for Company Data: {domain}")
        # Validate domain parameter
        if not domain or not domain.strip():
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Domain parameter is required and cannot be empty",
            )

        # Get company with all relations
        company_data = await company_service.get_company_with_all_relations(domain)

        if not company_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No enrichment data found for domain: {domain}",
            )

        logger.info(f"Found enriched data for company: {domain}")

        return company_data

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting enriched company data for {domain}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching company data",
        )


@router.post("/upload_excel", response_model=Dict[str, Any])
@rbac_register(
    resource="deal",
    action="create",
    group="Deals",
    description="Upload Excel/CSV file for bulk deal creation",
)
async def upload_excel_deals(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    queue_service=Depends(get_queue_service),
) -> Dict[str, Any]:
    """
    Upload Excel/CSV file for bulk deal creation.

    Accepts .xlsx or .csv files, stores them in S3, and queues processing.
    Returns upload_id and job_id for tracking.
    """
    try:
        org_id, _ = org_context

        # Validate file type
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No file provided"
            )

        file_extension = file.filename.lower().split(".")[-1]
        if file_extension not in ["xlsx", "csv"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only .xlsx and .csv files are supported",
            )

        # Validate file size (2MB limit)
        file_size = 0
        file_content = b""
        while chunk := await file.read(8192):
            file_size += len(chunk)
            file_content += chunk
            if file_size > 2 * 1024 * 1024:  # 2MB
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="File size exceeds 2MB limit",
                )

        # Generate upload ID
        upload_id = str(uuid.uuid4())

        # Store file in S3
        import boto3

        try:
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION,
            )

            s3_key = f"org_{org_id}/deal_uploads/{upload_id}.{file_extension}"

            s3_client.put_object(
                Bucket=settings.S3_BUCKET_ASSETS,
                Key=s3_key,
                Body=file_content,
                ContentType=file.content_type or "application/octet-stream",
            )
        except Exception as e:
            logger.error(f"Failed to upload file to S3: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to store file",
            )

        # Parse metadata
        metadata_dict = {}
        if metadata:
            try:
                metadata_dict = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning(f"Invalid metadata JSON for upload {upload_id}")

        # Queue the processing job
        job = await (
            JobBuilder()
            .for_import(
                upload_id
            )  # Use upload_id as entity_id for tracking Excel import processing
            .of_type(JobType.PITCH_DECK_PARSING)
            .with_payload(
                upload_id=upload_id,
                org_id=org_id,
                user_id=str(current_user.id),
                s3_key=f"s3://{settings.S3_BUCKET_ASSETS}/{s3_key}",
                file_extension=file_extension,
                metadata=metadata_dict,
            )
            .with_metadata(
                endpoint="upload_excel_deals",
                triggered_by=current_user.email,
            )
            .with_queue_config(priority=JobPriority.HIGH)
            .create()
        )

        logger.info(
            f"Queued Excel upload processing: upload_id={upload_id}, job_id={job}"
        )

        return {
            "upload_id": upload_id,
            "status": "queued",
            "message": "Excel uploaded and processing queued",
            "job": job.model_dump(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading Excel file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/{deal_id}/company/enrich", response_model=dict)
@rbac_register(
    resource="deal",
    action="edit",
    group="Deals",
    description="Trigger company enrichment for a deal",
)
async def trigger_company_enrichment(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
):
    """
    Trigger company enrichment pipeline for a deal.
    """
    try:
        org_id, _ = org_context
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )
        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )
        # Trigger enrichment
        try:
            # Import the concrete service to access the enrichment method
            from app.services.deal.mongo import DealService

            if isinstance(deal_service, DealService):
                await deal_service._trigger_company_enrichment(deal)
            else:
                # fallback: create a new instance
                from app.services.deal.mongo import DealService as ConcreteDealService

                concrete_service = ConcreteDealService()
                await concrete_service._trigger_company_enrichment(deal)
            return {"success": True, "message": "Company enrichment triggered."}
        except Exception as e:
            logger.error(f"Error triggering company enrichment for deal {deal_id}: {e}")
            return {"success": False, "message": f"Failed to trigger enrichment: {e}"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in enrichment endpoint for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger company enrichment",
        )


@router.get("/{deal_id}/company/enriched/v2")
async def get_company_enriched_v2(
    deal_id: str,
    include: Optional[str] = Query(None, description="Include company fields"),
    org_context: tuple[str, bool] = Depends(get_org_context),
    company_service: CompanyServiceInterface = Depends(get_company_service),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """Get enriched company data v2 with optional company fields."""
    try:
        deal = await Deal.find_one({"_id": ObjectId(deal_id)})

        if not deal:
            raise HTTPException(status_code=404, detail="Deal not found")

        if not deal.company_website:
            raise HTTPException(status_code=422, detail="Deal has no company website")

        domain = get_root_domain(deal.company_website)

        data = await deal_service.fetch_v2_enrichment_data(domain, include)

        data = data[0]
        del data["id"]
        del data["company_id"]
        del data["org_id"]
        return data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in enriched v2: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{deal_id}/poc")
@rbac_register(
    resource="deal",
    action="view",
    group="Deals",
    description="Get deal POC information",
)
async def get_deal_poc(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
) -> Dict[str, Any]:
    """Get Point of Contact (POC) information for a deal."""
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Get deal and verify access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # First try to get invited email (for invite-first deals)
        if deal.invited_email:
            return {
                "email": deal.invited_email,
                "name": deal.company_name,
                "source": "invited_email",
            }

        # Fallback to submission email (for form-submitted deals)
        if deal.submission_ids:
            submission_id = deal.submission_ids[0]
            submission = await PublicSubmission.find_one({
                "submission_id": submission_id
            })

            if submission and submission.public_user_email:
                return {
                    "email": submission.public_user_email,
                    "name": deal.company_name,
                    "source": "submission",
                }

        # No POC found
        return {"email": None, "name": deal.company_name, "source": "none"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting POC for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get deal POC information",
        )
