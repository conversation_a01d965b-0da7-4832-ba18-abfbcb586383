from datetime import datetime
from typing import List, Optional

from app.core.database import get_database
from app.models.audit import AuditLog
from app.services.audit.interfaces import AuditServiceInterface
from app.services.base import BaseService
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase


class AuditService(BaseService, AuditServiceInterface):
    """MongoDB implementation of AuditService."""

    def __init__(self, db: AsyncIOMotorDatabase = None):
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize database connection."""
        if self.db is None:
            async for db in get_database():
                self.db = db
                break

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def log_action(self, audit_log: AuditLog) -> AuditLog:
        """Log an action."""
        try:
            result = await self.db.audit_logs.insert_one(audit_log.dict(by_alias=True))
            audit_log.id = result.inserted_id
            return audit_log
        except Exception as e:
            await self.handle_error(e, {"audit_log": audit_log})

    async def get_user_logs(
        self, user_id: str, skip: int = 0, limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs for a user."""
        try:
            cursor = (
                self.db.audit_logs.find({"user_id": ObjectId(user_id)})
                .skip(skip)
                .limit(limit)
            )
            logs = await cursor.to_list(length=limit)
            return [AuditLog(**log) for log in logs]
        except Exception as e:
            await self.handle_error(
                e, {"user_id": user_id, "skip": skip, "limit": limit}
            )

    async def get_entity_logs(
        self, entity_type: str, entity_id: str, skip: int = 0, limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs for an entity."""
        try:
            cursor = (
                self.db.audit_logs.find({
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                })
                .skip(skip)
                .limit(limit)
            )
            logs = await cursor.to_list(length=limit)
            return [AuditLog(**log) for log in logs]
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                    "skip": skip,
                    "limit": limit,
                },
            )

    async def search_logs(
        self,
        action: Optional[str] = None,
        entity_type: Optional[str] = None,
        user_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[AuditLog]:
        """Search audit logs with filters."""
        try:
            query = {}
            if action:
                query["action"] = action
            if entity_type:
                query["entity_type"] = entity_type
            if user_id:
                query["user_id"] = ObjectId(user_id)
            if start_date or end_date:
                date_query = {}
                if start_date:
                    date_query["$gte"] = datetime.fromisoformat(start_date)
                if end_date:
                    date_query["$lte"] = datetime.fromisoformat(end_date)
                query["created_at"] = date_query

            cursor = self.db.audit_logs.find(query).skip(skip).limit(limit)
            logs = await cursor.to_list(length=limit)
            return [AuditLog(**log) for log in logs]
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "action": action,
                    "entity_type": entity_type,
                    "user_id": user_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "skip": skip,
                    "limit": limit,
                },
            )
