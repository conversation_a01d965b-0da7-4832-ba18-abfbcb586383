"""
OpenAI API Client

Handles chat completions with OpenAI GPT models for Chat mode.
Provides fast, context-aware, human-like responses.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional

import httpx

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class OpenAIError(Exception):
    """Base exception for OpenAI API errors."""

    pass


class OpenAIRateLimitError(OpenAIError):
    """Raised when rate limit is exceeded."""

    pass


class OpenAITimeoutError(OpenAIError):
    """Raised when request times out."""

    pass


class OpenAIClient:
    """
    Client for OpenAI Chat Completions API.
    Handles fast, context-aware responses for Chat mode.
    """

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or getattr(settings, "OPENAI_API_KEY", None)
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        self.base_url = "https://api.openai.com/v1"
        self.timeout = 600.0  # Increased timeout to 2 minutes for complex requests
        self.max_retries = 20
        self.retry_delay = 1.0  # Initial delay in seconds

        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a chat completion request with retry logic.

        Returns:
            completion_data: Full completion response
        """
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                return await self._make_completion_request(
                    messages, model, max_tokens, temperature, stream, attempt
                )
            except OpenAITimeoutError as e:
                last_exception = e
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2**attempt)  # Exponential backoff
                    logger.warning(
                        f"OpenAI request timed out (attempt {attempt + 1}/{self.max_retries + 1}). Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"OpenAI request failed after {self.max_retries + 1} attempts due to timeout"
                    )
                    raise
            except OpenAIRateLimitError as e:
                last_exception = e
                if attempt < self.max_retries:
                    delay = (
                        self.retry_delay * (2**attempt) * 2
                    )  # Longer delay for rate limits
                    logger.warning(
                        f"OpenAI rate limit hit (attempt {attempt + 1}/{self.max_retries + 1}). Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"OpenAI request failed after {self.max_retries + 1} attempts due to rate limit"
                    )
                    raise
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2**attempt)
                    logger.warning(
                        f"OpenAI request failed (attempt {attempt + 1}/{self.max_retries + 1}): {str(e)}. Retrying in {delay}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"OpenAI request failed after {self.max_retries + 1} attempts: {str(e)}"
                    )
                    raise

        # This should never be reached, but just in case
        raise last_exception or OpenAIError("Unknown error occurred")

    async def _make_completion_request(
        self,
        messages: List[Dict[str, str]],
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool,
        attempt: int,
    ) -> Dict[str, Any]:
        """Make a single completion request."""
        start_time = time.time()

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            "presence_penalty": 0,
            "frequency_penalty": 0,
        }

        try:
            # Use a longer timeout for complex requests like pitch parsing
            request_timeout = self.timeout * 2 if max_tokens > 2000 else self.timeout

            async with httpx.AsyncClient(timeout=request_timeout) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                )

                if response.status_code == 429:
                    raise OpenAIRateLimitError(
                        "Rate limit exceeded. Please try again later."
                    )
                elif response.status_code == 400:
                    error_detail = response.text
                    raise OpenAIError(f"Bad request: {error_detail}")
                elif response.status_code == 401:
                    raise OpenAIError("Invalid API key")
                elif response.status_code == 403:
                    raise OpenAIError("Access forbidden. Check your API permissions.")
                elif response.status_code >= 500:
                    raise OpenAIError(
                        f"OpenAI server error ({response.status_code}). Please try again."
                    )
                elif response.status_code >= 400:
                    error_detail = response.text
                    raise OpenAIError(
                        f"API error {response.status_code}: {error_detail}"
                    )

                result = response.json()

                if not result.get("choices"):
                    raise OpenAIError("No response choices returned from OpenAI API")

                # Add performance metadata
                response_time_ms = int((time.time() - start_time) * 1000)
                result["_performance"] = {
                    "response_time_ms": response_time_ms,
                    "model": model,
                    "attempt": attempt + 1,
                }

                logger.info(
                    f"Completed OpenAI chat completion with model: {model} in {response_time_ms}ms (attempt {attempt + 1})"
                )
                return result

        except httpx.TimeoutException:
            raise OpenAITimeoutError("Request timed out. Please try again.")
        except httpx.RequestError as e:
            logger.error(f"Network error during completion: {e}")
            raise OpenAIError(
                "Network error. Please check your connection and try again."
            )
        except Exception as e:
            logger.error(f"Error creating chat completion: {e}")
            if isinstance(e, (OpenAIError, OpenAIRateLimitError, OpenAITimeoutError)):
                raise
            raise OpenAIError(f"Failed to create completion: {str(e)}")

    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from OpenAI completion response.
        """
        try:
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content

            return "No response generated"

        except Exception as e:
            logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"

    def extract_performance_metadata(
        self, completion_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract performance metadata from completion response.
        """
        try:
            performance = completion_data.get("_performance", {})
            usage = completion_data.get("usage", {})

            return {
                "response_time_ms": performance.get("response_time_ms"),
                "token_count": usage.get("total_tokens"),
                "model": performance.get("model"),
            }

        except Exception as e:
            logger.error(f"Error extracting performance metadata: {e}")
            return {}

    def build_investment_prompt(
        self,
        user_message: str,
        deal_context: Optional[Dict[str, Any]] = None,
        chat_history: Optional[List[Dict[str, str]]] = None,
        mode: str = "chat",
    ) -> List[Dict[str, str]]:
        """
        Build a context-aware prompt for investment analysis in Chat mode.
        Uses a hybrid approach: formatted essential fields + complete deal context data.
        """
        messages = []

        # Base system instructions
        system_lines = [
            # Identity & purpose
            "You are Orbit, TractionX's AI investment assistant.",
            "Your mission: deliver fast, conversational, human-like guidance that helps early-stage investors and founders make smarter, more strategic decisions.",
            # Tone & style
            "Tone & style:",
            "  • Professional yet approachable—speak like a trusted colleague.",
            "  • Concise and clear—prioritize actionable insights over fluff.",
            "  • Stay focused—avoid generic or off-topic commentary.",
            # Context usage
            "Context rules:",
            "  • Leverage the provided deal context (company, website, stage, sector, founders, form answers) to tailor every response.",
            "  • When quoting any metrics or names, mirror the exact formatting from context (e.g. 'Pre-Seed', 'SaaS / AI-ML').",
            "  • You have access to both formatted essential fields and complete deal context data - use the complete data for detailed analysis.",
            "  • Dont limit yourself with the context use your search the web to find more information, about the deal or market or even the user request.",
            # Research & citation
            "Research guidance:",
            "  • If you make any factual claim or need detailed data, suggest: 'Switch to Deep Research mode' for sources and citations.",
            # Clarification & follow-ups
            "Clarifications:",
            "  • Only ask follow-up questions when essential to answering the user's current request.",
            "  • Otherwise, respond directly and confidently.",
            # Token & length management
            "Performance hints:",
            "  • Keep system message under 200 tokens; excessive length can impact model responsiveness.",
            "  • If context is growing, summarize or truncate less-critical details before sending.",
            # Final cue
            "Begin each response by acknowledging the user's ask, then dive into your advice.",
        ]

        messages.append({"role": "system", "content": "\n".join(system_lines)})

        # Add complete deal context as a separate message for comprehensive data access
        if deal_context:
            # Clean sensitive data if needed
            safe_deal_context = self._sanitize_deal_context(deal_context)
            context_message = {
                "role": "system",
                "content": f"Complete deal context data (use for detailed analysis):\n{str(safe_deal_context)}",
            }
            messages.append(context_message)

        # Append recent chat history if available
        if chat_history:
            messages.extend(chat_history[-8:])

        # Append the user's current message
        messages.append({"role": "user", "content": user_message})
        return messages

    def _sanitize_deal_context(self, deal_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize deal context data to remove sensitive information before sending to AI.
        """
        # Create a copy to avoid modifying the original
        sanitized = deal_context.copy()

        # Remove potentially sensitive fields
        sensitive_fields = [
            "invited_email",
            "contact_email",
            "email",
            "phone",
            "pitch_deck_url",
            "context_block_url",
            "executive_summary_url",
            "enrichment_job_id",
            "submission_ids",
            "form_id",
        ]

        def remove_sensitive_recursive(obj, path=""):
            if isinstance(obj, dict):
                for key in list(obj.keys()):
                    current_path = f"{path}.{key}" if path else key
                    if key in sensitive_fields:
                        obj[key] = "[REDACTED]"
                    elif isinstance(obj[key], (dict, list)):
                        remove_sensitive_recursive(obj[key], current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]"
                    remove_sensitive_recursive(item, current_path)

        remove_sensitive_recursive(sanitized)
        return sanitized
