from typing import Any, Dict, Optional, Union

from app.core.auth_exceptions import (
    InvalidTokenError,
    TokenExpiredError,
    TokenTypeError,
)
from app.core.config import settings
from app.core.logging import get_logger
from app.core.security import get_password_hash, verify_password
from app.models.organization import Organization
from app.models.token import Token, TokenType
from app.models.user import <PERSON>User, User, UserStatus
from app.schemas.auth import TokenResponse
from app.services.auth.interface import IAuthService
from app.services.base import BaseService
from app.services.factory import get_token_service
from app.services.token.service import TokenService
from app.utils.common import PyObjectId
from fastapi import HTTPException, status
from jose import jwt  # type: ignore
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = get_logger(__name__)


class AuthService(BaseService, IAuthService):
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db

    async def initialize(self) -> None:
        """Initialize the service."""
        # Use the factory function to get the token service
        self.token_service: TokenService = await get_token_service(db=self.db)  # type: ignore

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        # No cleanup needed for AuthService as it doesn't maintain any state
        pass

    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate a user with email and password."""
        try:
            user = await User.find_one({"email": email})
            if not user:
                # Don't reveal if user exists
                logger.warning(f"Login attempt for non-existent email: {email}")
                return None
            if not verify_password(password, user.password_hash):
                logger.warning(f"Invalid password attempt for user: {user.id}")
                return None
            return user
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service unavailable",
            )

    async def create_tokens(
        self, user: User, token_type: Optional[TokenType] = None
    ) -> TokenResponse:
        """
        Create access and/or refresh tokens for a user.
        Args:
            user: The user to create tokens for
            token_type: Optional token type to create. If None, creates both tokens.
        Returns:
            TokenResponse with the requested tokens
        """
        try:
            # Get user's organization and plan
            user = await user.populate("org_id")
            org: Organization = user.org_id  # type: ignore
            if not org:
                logger.error(f"User {user.id} has no organization")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User is not associated with any organization",
                )

            access_token = None
            refresh_token = None

            # Create requested tokens
            if token_type is None or token_type == TokenType.ACCESS:
                access_token = await self.token_service.create_token(
                    user_id=str(user.id),
                    token_type=TokenType.ACCESS,
                    tenant=org.subdomain,
                    tier=org.settings.plan,
                    expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                    metadata={
                        "org_id": str(org.id),
                        "role_id": str(user.role_id),
                        "plan": org.settings.plan,
                    },
                )

            if token_type is None or token_type == TokenType.REFRESH:
                refresh_token = await self.token_service.create_token(
                    user_id=str(user.id),
                    token_type=TokenType.REFRESH,
                    tenant=org.subdomain,
                    tier=org.settings.plan,
                    expires_in=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
                    metadata={
                        "org_id": str(org.id),
                        "role_id": str(user.role_id),
                        "plan": org.settings.plan,
                    },
                )

            return TokenResponse(
                access_token=access_token or "",
                refresh_token=refresh_token or "",
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                requires_password_reset=user.status == "invited",
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create authentication tokens",
            )

    async def create_public_user_tokens(
        self, public_user: PublicUser, token_type: Optional[TokenType] = None
    ) -> TokenResponse:
        """
        Create access and/or refresh tokens for a public user.
        Public users have limited access and don't belong to organizations.
        """
        try:
            access_token = None
            refresh_token = None

            # Create requested tokens for public user
            if token_type is None or token_type == TokenType.ACCESS:
                access_token = await self.token_service.create_token(
                    user_id=str(public_user.id),
                    token_type=TokenType.ACCESS,
                    tenant="public",  # Public users use "public" tenant
                    tier="public",  # Public users have "public" tier
                    expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                    metadata={
                        "user_type": "public",
                        "email": public_user.email,
                        "name": public_user.name,
                    },
                )

            if token_type is None or token_type == TokenType.REFRESH:
                refresh_token = await self.token_service.create_token(
                    user_id=str(public_user.id),
                    token_type=TokenType.REFRESH,
                    tenant="public",
                    tier="public",
                    expires_in=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
                    metadata={
                        "user_type": "public",
                        "email": public_user.email,
                        "name": public_user.name,
                    },
                )

            return TokenResponse(
                access_token=access_token or "",
                refresh_token=refresh_token or "",
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                requires_password_reset=False,  # Public users don't need password reset
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Public user token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create public user authentication tokens",
            )

    async def get_redirect_url(self, org) -> str:
        """Get the appropriate redirect URL based on organization type."""
        try:
            if org.settings.plan == "advanced":
                return f"https://{org.subdomain}.{settings.DOMAIN}/app"
            return settings.BASIC_PORTAL_URL
        except Exception as e:
            logger.error(f"Redirect URL error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to determine redirect URL",
            )

    async def refresh_tokens(self, refresh_token: str) -> TokenResponse:
        """Refresh access token using refresh token."""
        try:
            logger.info("Starting token refresh process")

            # Verify refresh token
            token_data = await self.token_service.verify_token(
                refresh_token, TokenType.REFRESH
            )
            logger.info(f"Refresh token verified for user: {token_data.sub}")

            # Get user
            user = await User.find_one({"_id": token_data.sub})
            if not user:
                logger.error(f"User not found for token refresh: {token_data.sub}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token",
                )

            if not user.is_active:
                logger.error(f"User is inactive: {token_data.sub}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="User is inactive"
                )

            logger.info(f"Creating new tokens for user: {user.id}")

            # Revoke the old refresh token to prevent reuse
            await self.token_service.revoke_token(refresh_token)
            logger.info("Old refresh token revoked")

            # Create new tokens
            new_tokens = await self.create_tokens(user)
            logger.info("New tokens created successfully")

            return new_tokens

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to refresh tokens",
            )

    async def verify_token(
        self, token: str, token_type: TokenType = TokenType.ACCESS
    ) -> Union[Token, None]:
        """Verify an access token."""
        try:
            return await self.token_service.verify_token(token, token_type)
        except (TokenExpiredError, InvalidTokenError, TokenTypeError):
            # Pass through our custom auth exceptions
            raise
        except jwt.ExpiredSignatureError:  # type: ignore
            # Explicitly handle token expiration
            raise TokenExpiredError(token_preview=token[:10] if token else None)
        except Exception as e:
            logger.error(f"Token verification error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify token",
            )

    async def revoke_refresh_token(self, refresh_token: str) -> None:
        """Revoke a refresh token."""
        try:
            await self.token_service.revoke_token(refresh_token)
        except Exception as e:
            logger.error(f"Token revocation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to revoke token",
            )

    async def revoke_all_user_tokens(self, user_id: str) -> None:
        """Revoke all tokens for a user."""
        try:
            # Revoke all tokens for the user
            await self.token_service.revoke_all_user_tokens(user_id)

            # Also revoke any active sessions
            await Token.update_many(
                {"user_id": user_id, "is_active": True}, {"$set": {"is_active": False}}
            )

            logger.info(
                f"Successfully revoked all tokens and sessions for user {user_id}"
            )
        except Exception as e:
            logger.error(f"Token revocation error for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to revoke tokens",
            )

    async def send_password_reset_email(self, email: str) -> None:
        from app.services.factory import get_email_service

        email_service = await get_email_service()
        """Send password reset email."""
        try:
            user = await User.find_one({"email": email})
            if not user:
                # Don't reveal if user exists
                logger.info(f"Password reset requested for non-existent email: {email}")
                return

            # Create password reset token
            token = await self.token_service.create_password_reset_token(str(user.id))

            # Send email
            return await email_service.send_password_reset_email(
                email=user.email, token=token
            )

        except Exception as e:
            logger.error(f"Password reset email error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email",
            )

    async def reset_password(self, token: str, new_password: str) -> None:
        """Reset password using token."""
        try:
            # Verify token
            token_data = await self.token_service.verify_password_reset_token(token)

            # Update password
            user = await User.find_one({"_id": token_data.sub})
            if not user:
                logger.error(f"User not found for password reset: {token_data.sub}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
                )

            user.password_hash = get_password_hash(new_password)
            await user.save(is_update=True)

            # Revoke all user tokens
            await self.revoke_all_user_tokens(str(user.id))
            logger.info(f"Password reset successful for user {user.id}")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Password reset error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reset password",
            )

    async def invite_user(
        self, name: str, email: str, role_id: str, org_id: str, invited_by: str
    ) -> dict:
        """Invite a new user to the organization."""
        try:
            # Create or get user
            user = await User.find_one(query={"email": email})
            if not user:
                user = User(
                    name=name,
                    email=email,
                    role_id=PyObjectId(role_id),
                    org_id=PyObjectId(org_id),
                    password_hash="",
                    status=UserStatus.INVITED,
                    tour_progress=None,
                )
                await user.save()
            else:
                # update user
                return {"message": f"User {email} already exists"}

            # Create invitation token
            token = await self.token_service.create_invitation_token(
                user_id=str(user.id), org_id=org_id, role_id=role_id
            )

            # add user to organization
            org = await Organization.find_one({"_id": org_id})
            if org:
                org.user_ids.append(user.id)
                await org.save(is_update=True)

            from app.services.factory import get_email_service

            email_service = await get_email_service()
            # Send invitation email
            await email_service.send_invitation_email(email=email, token=token)

            # Log the invitation
            logger.info(f"Invitation sent to {email} for org {org_id}")

            # send the accepting url back to response
            return {
                "message": f"Invitation sent to {email} for org {org_id}",
                "accepting_url": f"{settings.FRONTEND_URL}/accept-invitation?token={token}",
            }
        except Exception as e:
            logger.error(f"User invitation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send invitation",
            )

    async def accept_invitation(self, token: str, password: str) -> None:
        """Accept user invitation and set password."""
        try:
            # Verify token
            token_data = await self.token_service.verify_invitation_token(token)

            # Get user
            user = await User.find_one({"_id": token_data.sub})
            if not user:
                logger.error(
                    f"User not found for invitation acceptance: {token_data.sub}"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
                )

            # Update user
            user.password_hash = get_password_hash(password)
            user.status = UserStatus.ACTIVE

            # Update with with Role Id
            # user.role = token_data.metadata["role"]
            logger.info(f"User: {user}")
            await user.save(is_update=True)

            # Add user to organization
            logger.info(f"Token data: {token_data}")
            # org = await Organization.find_one({"_id": token_data.metadata["org_id"]})
            # if org:
            #     if org.members is None:
            #         org.members = []
            #     logger.info(
            #         f"Adding user {user.id}, type {type(user.id)} to organization {org.id}, type {type(org.id)}")
            #     org.members.append(user.id)
            #     await org.save()

            # Revoke invitation token
            await self.token_service.revoke_token(token)
            logger.info(f"Invitation accepted for user {user.id}")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Invitation acceptance error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to accept invitation",
            )

    async def create_magic_link_token(
        self, user_id: str, email: str, redirect_url: Optional[str] = None
    ) -> str:
        """Create a magic link token for passwordless authentication."""
        try:
            return await self.token_service.create_token(
                user_id=user_id,
                token_type=TokenType.MAGIC_LINK,
                tenant="system",
                tier="system",
                expires_in=30 * 24 * 60 * 60,  # 30 days
                metadata={"email": email, "redirect_url": redirect_url},
            )
        except Exception as e:
            logger.error(f"Magic link token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create magic link token",
            )

    async def verify_magic_link_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify a magic link token."""
        try:
            token_data = await self.token_service.verify_token(
                token, TokenType.MAGIC_LINK
            )
            if not token_data:
                return None

            return {
                "user_id": token_data.sub,
                "email": token_data.metadata.get("email")
                if token_data.metadata
                else None,
                "redirect_url": token_data.metadata.get("redirect_url")
                if token_data.metadata
                else None,
            }
        except (TokenExpiredError, InvalidTokenError, TokenTypeError):
            return None
        except Exception as e:
            logger.error(f"Magic link token verification error: {str(e)}")
            return None

    async def send_magic_link_email(
        self, email: str, token: str, redirect_url: Optional[str] = None
    ) -> None:
        """Send magic link email to user."""

        try:
            # For now, just log the magic link. In production, this would send an actual email
            magic_link_url = f"{settings.FRONTEND_URL}/auth/magic-link/verify/{token}"
            if redirect_url:
                magic_link_url += f"?redirect={redirect_url}"

            logger.info(f"Magic link for {email}: {magic_link_url}")

            from app.services.factory import get_email_service

            email_service = await get_email_service()
            await email_service.send_magic_link_email(email, magic_link_url)

        except Exception as e:
            logger.error(f"Magic link email sending error: {str(e)}")
            # Don't raise exception here to avoid breaking the flow
            # The user will still get the success message
