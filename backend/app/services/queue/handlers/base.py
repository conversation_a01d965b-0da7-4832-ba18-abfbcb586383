"""
Base job handler.

This module provides a base class for job handlers.
"""

from abc import abstractmethod
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.models.queue import Job
from app.services.queue.worker_interface import JobHandlerInterface

logger = get_logger(__name__)


class BaseJobHandler(JobHandlerInterface):
    """Base class for job handlers."""

    def __init__(self):
        """Initialize the job handler."""
        self.logger = logger
        self._initialized = False
        self.job_service = None
        self.logger.debug(f"Initializing {self.__class__.__name__}")

    async def initialize(self) -> None:
        """
        Initialize the handler.

        This method should be called before processing any jobs.
        Subclasses should override this method to initialize their specific services.
        """
        self.logger.debug(f"Starting initialization for {self.__class__.__name__}")
        if not self._initialized:
            try:
                self.logger.debug(
                    f"Calling _initialize_services for {self.__class__.__name__}"
                )
                await self._initialize_services()
                self._initialized = True
                self.logger.debug(f"Successfully initialized {self.__class__.__name__}")
            except Exception as e:
                self.logger.error(
                    f"Failed to initialize {self.__class__.__name__}: {str(e)}",
                    exc_info=True,
                )
                raise
        else:
            self.logger.debug(f"{self.__class__.__name__} already initialized")

    async def _initialize_services(self) -> None:
        """
        Initialize handler services.

        This method should be implemented by subclasses to initialize their specific services.
        """
        self.logger.debug(
            f"Base _initialize_services called for {self.__class__.__name__}"
        )

        # Initialize job service for centralized tracking
        try:
            from app.services.factory import get_job_service

            self.job_service = await get_job_service()
            self.logger.debug(f"Job service initialized for {self.__class__.__name__}")
        except Exception as e:
            self.logger.warning(f"Failed to initialize job service: {str(e)}")
            self.job_service = None

    async def handle(self, job: Job) -> Dict[str, Any]:
        """
        Handle a job with centralized job tracking.

        This method ensures the handler is initialized before processing the job
        and automatically updates the tracked job status.

        Args:
            job: The job to handle

        Returns:
            Result of job processing
        """
        self.logger.info(
            f"Handling job {job.id} of type {job.type} with {self.__class__.__name__}"
        )

        # Extract tracked job ID for status updates
        tracked_job_id = job.payload.get("tracked_job_id")

        try:
            # Ensure handler is initialized
            self.logger.debug(f"Ensuring initialization for job {job.id}")
            await self.initialize()

            # Update job status to in progress if we have a tracked job
            if tracked_job_id and self.job_service:
                try:
                    from app.models.job import JobStatus

                    await self.job_service.update_job_status(  # type: ignore
                        job_id=tracked_job_id,
                        status=JobStatus.IN_PROGRESS,
                        progress=0.1,
                    )
                    self.logger.info(
                        f"Updated job status to in progress for {tracked_job_id}"
                    )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status to in progress: {str(e)}",
                        exc_info=True,
                    )

            # Call the process method
            self.logger.debug(f"Processing job {job.id} with {self.__class__.__name__}")
            result = await self.process(job)

            # Update job status to completed if we have a tracked job
            if tracked_job_id and self.job_service:
                try:
                    await self.job_service.update_job_status(  # type: ignore
                        job_id=tracked_job_id,
                        status=JobStatus.COMPLETED,
                        progress=1.0,
                        output=result,
                    )
                    self.logger.info(
                        f"Updated job status to completed for {tracked_job_id}"
                    )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status to completed: {str(e)}",
                        exc_info=True,
                    )

            # Return the result
            self.logger.debug(f"Job {job.id} completed successfully")
            return result or {}
        except Exception as e:
            # Update job status to failed if we have a tracked job
            if tracked_job_id and self.job_service:
                try:
                    await self.job_service.update_job_status(  # type: ignore
                        job_id=tracked_job_id,
                        status=JobStatus.FAILED,
                        error=str(e),
                    )
                    self.logger.info(
                        f"Updated job status to failed for {tracked_job_id}"
                    )
                except Exception as update_error:
                    self.logger.error(
                        f"Failed to update job status to failed: {str(update_error)}",
                        exc_info=True,
                    )

            self.logger.error(
                f"Error handling job {job.id} with {self.__class__.__name__}: {str(e)}",
                exc_info=True,
                extra={
                    "job_id": job.id,
                    "job_type": job.type,
                    "handler_class": self.__class__.__name__,
                },
            )
            raise

    @abstractmethod
    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a job.

        This method should be implemented by subclasses.

        Args:
            job: The job to process

        Returns:
            Optional result of job processing
        """
        pass
