"""
Investment Thesis Matching Queue Handler

This module handles the investment thesis matching task that evaluates how well
a deal aligns with an organization's investment thesis configuration.

Features:
- Static fuzzy matching with RapidFuzz for fast evaluation
- LLM enhancement layer for detailed analysis (feature-flagged)
- Comprehensive scoring with dimension-level breakdowns
- Idempotent operation with proper error handling
- Integration with existing deal scoring structure
"""

import time
from typing import Any, Dict, List, Optional, Tuple, Union

from bson import ObjectId

from app.core.logging import get_logger
from app.models.deal import Deal
from app.models.organization import Organization
from app.models.queue import Job
from app.services.deal.interfaces import DealServiceInterface
from app.services.factory import get_deal_service
from app.services.openai.client import OpenAIClient
from app.services.queue.handlers.base import BaseJobHandler

# Import RapidFuzz for fuzzy matching
try:
    from rapidfuzz import fuzz, utils  # type: ignore

    RAPIDFUZZ_AVAILABLE = True
except ImportError:
    RAPIDFUZZ_AVAILABLE = False
    print("Warning: RapidFuzz not available. Install with: pip install rapidfuzz")

logger = get_logger(__name__)


class ThesisMatchingHandler(BaseJobHandler):
    """
    Handler for investment thesis matching jobs.

    Evaluates deal alignment with organization thesis using:
    1. Fast static matching with fuzzy logic
    2. Optional LLM enhancement for detailed analysis
    3. Structured scoring with explanations
    """

    def __init__(self):
        super().__init__()
        self.deal_service: Optional[DealServiceInterface] = None
        self.openai_client: Optional[OpenAIClient] = None

        # Fuzzy matching thresholds
        self.EXACT_MATCH_THRESHOLD = 90  # 90%+ = exact match (score 1.0)
        self.FUZZY_MATCH_THRESHOLD = 85  # 85%+ = fuzzy match (score 0.75-0.9)
        self.MIN_MATCH_THRESHOLD = 70  # 70%+ = weak match (score 0.5-0.75)

        # Weights for overall scoring (customizable per org in future)
        self.DIMENSION_WEIGHTS = {
            "sector": 0.20,
            "geography": 0.20,
            "stage": 0.20,
            "business_model": 0.20,
        }

    async def _initialize_services(self) -> None:
        """Initialize required services."""
        self.logger.debug("Initializing services for ThesisMatchingHandler")

        # Initialize deal service
        self.deal_service = await get_deal_service()  # type: ignore
        if self.deal_service:
            await self.deal_service.initialize()

        # Initialize OpenAI client (for LLM enhancement)
        try:
            self.openai_client = OpenAIClient()
            self.logger.debug("OpenAI client initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize OpenAI client: {e}")
            self.openai_client = None

        self.logger.debug("ThesisMatchingHandler services initialized")

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a thesis matching job.

        Expected payload:
        {
            "org_id": ObjectId,
            "deal_id": ObjectId
        }

        Returns:
            Dict containing matching results and metadata
        """
        payload = job.payload
        self.logger.info(f"Processing thesis matching job: {job.id}")

        start_time = time.time()

        try:
            # Extract and validate input
            org_id = payload.get("org_id")
            deal_id = payload.get("deal_id")

            if not org_id or not deal_id:
                raise ValueError(
                    "Missing required parameters: org_id and deal_id are required"
                )

            # Convert to ObjectIds if needed
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            self.logger.info(
                f"Processing thesis matching for deal {deal_id} in org {org_id}"
            )

            # Step 1: Fetch required data
            organization, deal = await self._fetch_required_data(org_id, deal_id)

            # Step 2: Validate thesis configuration
            thesis_config = self._validate_thesis_config(organization)

            # Step 3: Extract and normalize deal fields
            deal_fields = self._extract_and_normalize_deal_fields(deal)

            # Step 4: Perform static matching
            # static_result = await self._perform_static_matching(
            #     thesis_config, deal_fields
            # )
            # TODO: Until we have accurate matching, provide simple fallback structure
            static_result = {
                "overall": {
                    "match_percent": 0.0,
                    "summary": {
                        "assessment": "Pending",
                        "description": "Analysis pending",
                        "investment_rationale": "Analysis will be provided by AI evaluation",
                    },
                }
            }

            # Step 5: Determine if LLM enhancement is needed
            # should_use_llm = await self._should_use_llm_enhancement(
            #     organization, static_result["overall"]["match_percent"]
            # )
            should_use_llm = True

            # Step 6: Optional LLM enhancement
            final_result = static_result
            if should_use_llm and self.openai_client:
                try:
                    enhanced_result = await self._enhance_with_llm(
                        deal, thesis_config, deal_fields, static_result
                    )
                    final_result = enhanced_result
                except Exception as e:
                    self.logger.warning(
                        f"LLM enhancement failed, using static result: {e}"
                    )

            # Step 7: Store results in deal (separate from scoring structure)
            await self._store_thesis_matching_results(deal, final_result)

            # Step 8: Calculate performance metrics
            processing_time_ms = int((time.time() - start_time) * 1000)

            self.logger.info(
                f"Completed thesis matching for deal {deal_id}: "
                f"{final_result['overall']['match_percent']:.1f}% match in {processing_time_ms}ms"
            )

            return {
                "success": True,
                "deal_id": str(deal_id),
                "org_id": str(org_id),
                "match_percent": final_result["overall"]["match_percent"],
                "used_llm": should_use_llm and self.openai_client is not None,
                "processing_time_ms": processing_time_ms,
                "dimension_scores": {
                    dim: result["score"]
                    for dim, result in final_result.items()
                    if dim != "overall"
                },
            }

        except Exception as e:
            processing_time_ms = int((time.time() - start_time) * 1000)
            self.logger.error(
                f"Error processing thesis matching for deal {payload.get('deal_id')}: {str(e)}",
                exc_info=True,
            )

            # Store error in deal if possible
            if payload.get("deal_id"):
                await self._store_thesis_matching_error(payload.get("deal_id"), str(e))  # type: ignore

            return {
                "success": False,
                "deal_id": str(payload.get("deal_id"))
                if payload.get("deal_id")
                else None,
                "error": str(e),
                "processing_time_ms": processing_time_ms,
            }

    async def _fetch_required_data(
        self, org_id: ObjectId, deal_id: ObjectId
    ) -> Tuple[Organization, Deal]:
        """Fetch organization and deal data."""
        self.logger.debug(f"Fetching organization {org_id} and deal {deal_id}")

        # Fetch organization
        organization = await Organization.find_one(query={"_id": org_id})
        if not organization:
            raise ValueError(f"Organization not found: {org_id}")

        # Fetch deal
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        deal = await self.deal_service.get_deal(deal_id)
        if not deal:
            raise ValueError(f"Deal not found: {deal_id}")

        # Verify deal belongs to organization
        if deal.org_id != org_id:
            raise ValueError(f"Deal {deal_id} does not belong to organization {org_id}")

        return organization, deal

    def _validate_thesis_config(
        self, organization: Organization
    ) -> Dict[str, List[str]]:
        """Validate and extract thesis configuration."""
        if not organization.thesis_config:
            raise ValueError("Organization has no thesis configuration")

        thesis_config = {
            "sector": organization.thesis_config.sector or [],
            "geography": organization.thesis_config.geography or [],
            "stage": organization.thesis_config.stage or [],
            "business_model": organization.thesis_config.business_model or [],
        }

        # Check if thesis has any criteria
        total_criteria = sum(len(values) for values in thesis_config.values())
        if total_criteria == 0:
            raise ValueError("Thesis configuration is empty")

        self.logger.debug(f"Thesis config: {thesis_config}")
        return thesis_config

    def _extract_and_normalize_deal_fields(self, deal: Deal) -> Dict[str, List[str]]:
        """Extract and normalize deal fields for matching."""
        deal_fields = {}

        # Extract sector
        sector = deal.sector
        if isinstance(sector, str):
            deal_fields["sector"] = [sector.strip().lower()]
        elif isinstance(sector, list):
            deal_fields["sector"] = [
                s.strip().lower() for s in sector if s and s.strip()
            ]
        else:
            deal_fields["sector"] = []

        # Extract geography
        geography = deal.geography
        if isinstance(geography, str):
            deal_fields["geography"] = [geography.strip().lower()]
        elif isinstance(geography, list):
            deal_fields["geography"] = [
                g.strip().lower()
                for g in geography  # type: ignore
                if g and g.strip()
            ]
        else:
            deal_fields["geography"] = []

        # Extract stage
        stage = deal.stage
        if isinstance(stage, str):
            deal_fields["stage"] = [stage.strip().lower()]
        else:
            deal_fields["stage"] = []

        # Extract business model
        business_model = deal.business_model
        if isinstance(business_model, str):
            deal_fields["business_model"] = [business_model.strip().lower()]
        elif isinstance(business_model, list):
            deal_fields["business_model"] = [
                bm.strip().lower()
                for bm in business_model  # type: ignore
                if bm and bm.strip()
            ]
        else:
            deal_fields["business_model"] = []

        self.logger.debug(f"Normalized deal fields: {deal_fields}")
        return deal_fields

    async def _perform_static_matching(
        self, thesis_config: Dict[str, List[str]], deal_fields: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Perform static fuzzy matching across all dimensions."""
        results = {}
        total_score = 0.0
        dimension_count = 0

        for dimension in ["sector", "geography", "stage", "business_model"]:
            thesis_values = [
                v.strip().lower()
                for v in thesis_config.get(dimension, [])
                if v and v.strip()
            ]
            deal_values = deal_fields.get(dimension, [])

            if not thesis_values:
                # Thesis is agnostic for this dimension
                results[dimension] = {
                    "score": 100.0,
                    "reason": f"Thesis is agnostic on {dimension}",
                    "matches": [],
                }
                total_score += 100.0
            elif not deal_values:
                # Deal has no data for this dimension
                results[dimension] = {
                    "score": 0.0,
                    "reason": f"Deal has no {dimension} information",
                    "matches": [],
                }
                total_score += 0.0
            else:
                # Perform fuzzy matching
                match_result = self._fuzzy_match_dimension(
                    thesis_values, deal_values, dimension
                )
                results[dimension] = match_result
                total_score += match_result["score"]

            dimension_count += 1

        # Calculate overall score using weighted average
        weighted_score = 0.0
        total_weight = 0.0
        for dimension, weight in self.DIMENSION_WEIGHTS.items():
            if dimension in results:
                weighted_score += results[dimension]["score"] * weight
                total_weight += weight

        overall_score = weighted_score / total_weight if total_weight > 0 else 0.0

        # Generate structured summary
        strong_matches = [
            dim for dim, result in results.items() if result["score"] >= 75
        ]
        weak_matches = [
            dim for dim, result in results.items() if 50 <= result["score"] < 75
        ]
        poor_matches = [dim for dim, result in results.items() if result["score"] < 50]

        # Create structured summary
        structured_summary = self._create_structured_summary(
            overall_score, results, strong_matches, weak_matches, poor_matches
        )

        results["overall"] = {
            "match_percent": round(overall_score, 1),
            "summary": structured_summary,
            "strong_dimensions": strong_matches,
            "weak_dimensions": weak_matches,
            "poor_dimensions": poor_matches,
        }

        return results

    def _fuzzy_match_dimension(
        self, thesis_values: List[str], deal_values: List[str], dimension: str
    ) -> Dict[str, Any]:
        """Perform fuzzy matching for a single dimension."""
        if not RAPIDFUZZ_AVAILABLE:
            # Fallback to simple string matching
            return self._simple_string_match(thesis_values, deal_values, dimension)

        best_score = 0.0
        best_matches = []

        for deal_value in deal_values:
            for thesis_value in thesis_values:
                # Use RapidFuzz for fuzzy matching
                similarity = fuzz.ratio(
                    utils.default_process(deal_value),
                    utils.default_process(thesis_value),
                )

                if similarity >= self.EXACT_MATCH_THRESHOLD:
                    score = 100.0
                    match_type = "exact"
                elif similarity >= self.FUZZY_MATCH_THRESHOLD:
                    # Scale between 75-90 based on similarity
                    score = 75.0 + (similarity - self.FUZZY_MATCH_THRESHOLD) * 0.6
                    match_type = "fuzzy"
                elif similarity >= self.MIN_MATCH_THRESHOLD:
                    # Scale between 50-75 based on similarity
                    score = 50.0 + (similarity - self.MIN_MATCH_THRESHOLD) * 1.67
                    match_type = "weak"
                else:
                    continue  # No match

                if score > best_score:
                    best_score = score
                    best_matches = [
                        {
                            "deal_value": deal_value,
                            "thesis_value": thesis_value,
                            "similarity": similarity,
                            "match_type": match_type,
                        }
                    ]
                elif score == best_score:
                    best_matches.append({
                        "deal_value": deal_value,
                        "thesis_value": thesis_value,
                        "similarity": similarity,
                        "match_type": match_type,
                    })

        if best_score > 0:
            best_match = best_matches[0]
            if best_match["match_type"] == "exact":
                reason = f"Exact match: '{best_match['deal_value']}'"
            elif best_match["match_type"] == "fuzzy":
                reason = f"Fuzzy match: '{best_match['deal_value']}' ≈ '{best_match['thesis_value']}'"
            else:
                reason = f"Weak match: '{best_match['deal_value']}' ~ '{best_match['thesis_value']}'"
        else:
            reason = f"No match found for {dimension}"

        return {
            "score": round(best_score, 1),
            "reason": reason,
            "matches": best_matches,
        }

    def _simple_string_match(
        self, thesis_values: List[str], deal_values: List[str], dimension: str
    ) -> Dict[str, Any]:
        """Fallback simple string matching when RapidFuzz is not available."""
        for deal_value in deal_values:
            for thesis_value in thesis_values:
                if deal_value == thesis_value:
                    return {
                        "score": 100.0,
                        "reason": f"Exact match: '{deal_value}'",
                        "matches": [
                            {
                                "deal_value": deal_value,
                                "thesis_value": thesis_value,
                                "similarity": 100.0,
                                "match_type": "exact",
                            }
                        ],
                    }
                elif deal_value in thesis_value or thesis_value in deal_value:
                    return {
                        "score": 75.0,
                        "reason": f"Partial match: '{deal_value}' ≈ '{thesis_value}'",
                        "matches": [
                            {
                                "deal_value": deal_value,
                                "thesis_value": thesis_value,
                                "similarity": 75.0,
                                "match_type": "partial",
                            }
                        ],
                    }

        return {
            "score": 0.0,
            "reason": f"No match found for {dimension}",
            "matches": [],
        }

    def _create_structured_summary(
        self,
        overall_score: float,
        results: Dict[str, Any],
        strong_matches: List[str],
        weak_matches: List[str],
        poor_matches: List[str],
    ) -> Dict[str, Any]:
        """Create a structured summary for easy frontend rendering."""

        # Determine overall assessment
        if overall_score >= 85:
            assessment = "Excellent"
            description = "Strong alignment across multiple dimensions"
        elif overall_score >= 75:
            assessment = "Good"
            description = "Solid alignment with minor gaps"
        elif overall_score >= 60:
            assessment = "Moderate"
            description = "Reasonable fit with some misalignments"
        elif overall_score >= 40:
            assessment = "Weak"
            description = "Limited alignment requiring evaluation"
        else:
            assessment = "Poor"
            description = "Significant misalignment with thesis"

        # Build matches breakdown
        matches = []
        partial_matches = []
        mismatches = []
        neutral_items = []

        for dimension in ["sector", "geography", "stage", "business_model"]:
            if dimension not in results:
                continue

            result = results[dimension]
            score = result["score"]
            reason = result["reason"]

            dimension_display = dimension.replace("_", " ").title()

            item = {
                "dimension": dimension_display,
                "score": score,
                "reason": reason,
                "matches": result.get("matches", []),
            }

            if score >= 90:
                matches.append(item)
            elif score >= 70:
                partial_matches.append(item)
            elif score >= 30:
                neutral_items.append(item)
            else:
                mismatches.append(item)

        # Create investment rationale based on matches
        rationale_parts = []

        if matches:
            match_dimensions = [item["dimension"] for item in matches]
            rationale_parts.append(f"Strong alignment in {', '.join(match_dimensions)}")

        if partial_matches:
            partial_dimensions = [item["dimension"] for item in partial_matches]
            rationale_parts.append(f"Reasonable fit in {', '.join(partial_dimensions)}")

        if mismatches:
            mismatch_dimensions = [item["dimension"] for item in mismatches]
            rationale_parts.append(
                f"Gaps in {', '.join(mismatch_dimensions)} require evaluation"
            )

        investment_rationale = (
            ". ".join(rationale_parts)
            if rationale_parts
            else "Limited alignment with investment thesis"
        )

        return {
            "score": round(overall_score, 1),
            "assessment": assessment,
            "description": description,
            "investment_rationale": investment_rationale,
            "breakdown": {
                "strong_matches": matches,
                "partial_matches": partial_matches,
                "neutral_items": neutral_items,
                "mismatches": mismatches,
            },
            "summary_stats": {
                "total_dimensions": len(results) - 1,  # Exclude 'overall'
                "strong_count": len(matches),
                "partial_count": len(partial_matches),
                "neutral_count": len(neutral_items),
                "mismatch_count": len(mismatches),
            },
        }

    async def _should_use_llm_enhancement(
        self, organization: Organization, static_score: float
    ) -> bool:
        """Determine if LLM enhancement should be used."""
        # Check feature flag
        features = organization.settings.features if organization.settings else {}
        llm_enabled = features.get("llm_thesis_matcher", False)

        if not llm_enabled:
            self.logger.debug("LLM thesis matcher feature flag disabled")
            return False

        # Use LLM for moderate scores (50-85%) where enhancement adds value
        if 50.0 <= static_score <= 85.0:
            self.logger.debug(f"LLM enhancement triggered for score {static_score}")
            return True

        self.logger.debug(f"LLM enhancement skipped for score {static_score}")
        return False

    async def _enhance_with_llm(
        self,
        deal: Deal,
        thesis_config: Dict[str, List[str]],
        deal_fields: Dict[str, List[str]],
        static_result: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Enhance static matching with LLM analysis."""
        if not self.openai_client:
            return static_result

        self.logger.info(f"Enhancing thesis matching with LLM for deal {deal.id}")

        # Create LLM prompt
        prompt = self._create_llm_enhancement_prompt(
            deal, thesis_config, deal_fields, static_result
        )

        try:
            # Call OpenAI with structured prompt for both score and analysis
            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert investment analyst evaluating startup-investor fit. You must provide both a numerical match percentage (0-100) and detailed analysis.",
                    },
                    {
                        "role": "system",
                        "content": 'Respond in this exact JSON format: {"match_percentage": <number>, "analysis": "<detailed analysis>"}. The match_percentage should be a number between 0-100 based on how well the deal fits the investment thesis.',
                    },
                    {
                        "role": "system",
                        "content": "If the investor targets ANY/AGNOSTIC in a dimension (empty list), give full points for that dimension. Only penalize for actual mismatches between specified preferences and deal characteristics.",
                    },
                    {
                        "role": "user",
                        "content": prompt,
                    },
                ],
                model="gpt-4o-mini",  # Cost-effective model for analysis
                max_tokens=800,
                temperature=0.2,  # Low temperature for consistent analysis
            )

            # Extract and parse LLM response
            llm_content = self.openai_client.extract_content_from_completion(response)

            try:
                import json

                llm_result = json.loads(llm_content.strip())
                match_percentage = float(llm_result.get("match_percentage", 0))
                analysis = llm_result.get("analysis", llm_content.strip())
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.warning(
                    f"Failed to parse LLM JSON response, using fallback: {e}"
                )
                # Fallback: try to extract percentage from text
                import re

                percentage_match = re.search(r"(\d+(?:\.\d+)?)%", llm_content)
                match_percentage = (
                    float(percentage_match.group(1)) if percentage_match else 50.0
                )
                analysis = llm_content.strip()

            # Create enhanced result with LLM-generated score and analysis
            enhanced_result = static_result.copy()
            enhanced_result["overall"]["match_percent"] = round(match_percentage, 1)

            if isinstance(enhanced_result["overall"]["summary"], dict):
                # Update structured summary with LLM insights
                enhanced_result["overall"]["summary"]["score"] = round(
                    match_percentage, 1
                )
                enhanced_result["overall"]["summary"]["investment_rationale"] = analysis
                enhanced_result["overall"]["summary"]["enhanced_by_llm"] = True

                # Update assessment based on LLM score
                if match_percentage >= 85:
                    enhanced_result["overall"]["summary"]["assessment"] = "Excellent"
                    enhanced_result["overall"]["summary"]["description"] = (
                        "Strong alignment with investment thesis"
                    )
                elif match_percentage >= 75:
                    enhanced_result["overall"]["summary"]["assessment"] = "Good"
                    enhanced_result["overall"]["summary"]["description"] = (
                        "Good fit with minor considerations"
                    )
                elif match_percentage >= 60:
                    enhanced_result["overall"]["summary"]["assessment"] = "Moderate"
                    enhanced_result["overall"]["summary"]["description"] = (
                        "Reasonable fit requiring evaluation"
                    )
                elif match_percentage >= 40:
                    enhanced_result["overall"]["summary"]["assessment"] = "Weak"
                    enhanced_result["overall"]["summary"]["description"] = (
                        "Limited alignment"
                    )
                else:
                    enhanced_result["overall"]["summary"]["assessment"] = "Poor"
                    enhanced_result["overall"]["summary"]["description"] = (
                        "Significant misalignment"
                    )
            else:
                # Fallback for backwards compatibility
                enhanced_result["overall"]["summary"] = analysis

            enhanced_result["overall"]["enhanced_by_llm"] = True

            self.logger.info(
                f"Successfully enhanced thesis matching with LLM: {match_percentage}% match"
            )
            return enhanced_result

        except Exception as e:
            self.logger.error(f"LLM enhancement failed: {e}")
            return static_result

    def _create_llm_enhancement_prompt(
        self,
        deal: Deal,
        thesis_config: Dict[str, List[str]],
        deal_fields: Dict[str, List[str]],
        static_result: Dict[str, Any],
    ) -> str:
        """Create prompt for LLM enhancement."""
        company_name = deal.company_name or "Company"

        prompt = f"""Analyze the investment fit between {company_name} and our investment thesis.

COMPANY PROFILE:
- Sector: {", ".join(deal_fields.get("sector", ["Not specified"]))}
- Geography: {", ".join(deal_fields.get("geography", ["Not specified"]))}
- Stage: {", ".join(deal_fields.get("stage", ["Not specified"]))}
- Business Model: {", ".join(deal_fields.get("business_model", ["Not specified"]))}

OUR INVESTMENT THESIS:
- Target Sectors: {", ".join(thesis_config.get("sector", ["Any (agnostic)"]) if thesis_config.get("sector") else ["Any (agnostic)"])}
- Target Geography: {", ".join(thesis_config.get("geography", ["Any (agnostic)"]) if thesis_config.get("geography") else ["Any (agnostic)"])}
- Target Stages: {", ".join(thesis_config.get("stage", ["Any (agnostic)"]) if thesis_config.get("stage") else ["Any (agnostic)"])}
- Target Business Models: {", ".join(thesis_config.get("business_model", ["Any (agnostic)"]) if thesis_config.get("business_model") else ["Any (agnostic)"])}

Provide:
1. A match percentage (0-100) based on overall fit
2. A 2-3 sentence investor-grade analysis explaining the fit, highlighting key alignments or gaps

Remember: If we target "Any (agnostic)" in a dimension, that's a perfect match for that dimension. Only penalize for actual conflicts between our specified preferences and the company profile."""

        return prompt

    async def _store_thesis_matching_results(
        self, deal: Deal, results: Dict[str, Any]
    ) -> None:
        """Store thesis matching results in deal's org_thesis_matching field (separate from scoring)."""
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        # Prepare thesis matching data (4 essential fields only)
        summary = results["overall"]["summary"]
        thesis_matching = {
            "score": results["overall"]["match_percent"],
            "assessment": summary.get("assessment", "Unknown")
            if isinstance(summary, dict)
            else "Unknown",
            "investment_rationale": summary.get("investment_rationale", summary)
            if isinstance(summary, dict)
            else summary,
            "description": summary.get("description", "")
            if isinstance(summary, dict)
            else "",
        }

        # Update deal with thesis matching results (separate field, not in scoring)
        update_data = {
            "org_thesis_matching": thesis_matching,
            "thesis_matching_error": None,  # Clear any previous errors
        }

        await self.deal_service.update_deal(str(deal.id), update_data)

        # Add timeline event
        await self.deal_service.add_timeline_event(
            deal_id=str(deal.id),
            event="Investment thesis analysis completed",
            notes=f"Match score: {results['overall']['match_percent']}%",
        )

        self.logger.info(
            f"Stored thesis matching results in org_thesis_matching for deal {deal.id}"
        )

    async def _store_thesis_matching_error(
        self, deal_id: Union[str, ObjectId], error: str
    ) -> None:
        """Store thesis matching error in the deal."""
        if not self.deal_service:
            return

        try:
            update_data = {
                "thesis_matching_error": error,
            }
            await self.deal_service.update_deal(str(deal_id), update_data)
            self.logger.info(f"Stored thesis matching error for deal {deal_id}")
        except Exception as e:
            self.logger.error(f"Failed to store thesis matching error: {e}")


# Handler factory function
def create_thesis_matching_handler() -> ThesisMatchingHandler:
    """Create a thesis matching handler instance."""
    return ThesisMatchingHandler()


# Export handlers for registration
HANDLERS = {
    "thesis_matching": create_thesis_matching_handler,
    "investment_thesis_matching": create_thesis_matching_handler,  # Alternative name
}
